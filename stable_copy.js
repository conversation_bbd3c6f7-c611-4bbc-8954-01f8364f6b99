/*
 * 高德地图数据提取器 - 成功模式复制版
 * 专注于人类可读数据，基于成功案例
 * 版本: Frida 12.9.7 兼容
 */

console.log("[Data Extractor] 启动数据提取器...");

var extractCount = 0;
var maxExtracts = 3;
var fileReadCount = 0;

function extractReadableData(dataPtr, size, source) {
    if (extractCount >= maxExtracts) return;
    
    try {
        console.log("\n 分析 " + source + " 数据: " + size + " 字节");
        
        var data = dataPtr.readByteArray(Math.min(256, size));
        var bytes = new Uint8Array(data);
        
        // 转换为字符串预览
        var textPreview = "";
        for (var i = 0; i < Math.min(100, bytes.length); i++) {
            if (bytes[i] >= 32 && bytes[i] < 127) {
                textPreview += String.fromCharCode(bytes[i]);
            } else {
                textPreview += ".";
            }
        }
        
        console.log("预览: " + textPreview);
        
        // 检测人类可读数据类型
        if (textPreview.indexOf('<?xml') >= 0) {
            extractCount++;
            console.log(" 发现XML配置数据");
            console.log(" 类型: 地图配置文件");
        } else if (textPreview.indexOf('{"') >= 0 || textPreview.indexOf('"res_list"') >= 0) {
            extractCount++;
            console.log(" 发现JSON数据");
            console.log(" 类型: 资源配置");
        } else if (textPreview.indexOf("DICE-AM") >= 0) {
            extractCount++;
            console.log(" 发现DICE-AM地图数据");
            console.log(" 类型: 矢量地图瓦片");
        } else {
            // 检查中文字符
            var hasChinese = false;
            for (var j = 0; j < Math.min(50, bytes.length); j++) {
                if (bytes[j] >= 0xE4 && bytes[j] <= 0xE9) {
                    hasChinese = true;
                    break;
                }
            }
            if (hasChinese) {
                extractCount++;
                console.log(" 发现中文文本数据");
                console.log(" 类型: 地名或标注信息");
            }
        }
        
        console.log("========================");
        
    } catch (e) {
        console.log("[提取错误] " + e.message);
    }
}

function isSystemFile(charStr) {
    return charStr.indexOf("VmFlags") >= 0 ||
           charStr.indexOf("kB.") >= 0 ||
           charStr.indexOf("/proc/") >= 0 ||
           charStr.indexOf("/dev/") >= 0 ||
           charStr.indexOf("Referenced") >= 0 ||
           charStr.indexOf("Anonymous") >= 0 ||
           charStr.indexOf("Shared_") >= 0 ||
           charStr.indexOf("Private_") >= 0;
}

function setupDataHooks() {
    setTimeout(function() {
        console.log("[Setup] 开始设置数据Hook...");
        
        try {
            var lib = Module.findBaseAddress("libamapnsq.so");
            if (!lib) {
                console.log("[Error] 未找到libamapnsq.so");
                return;
            }
            
            console.log("[Library] 库基址: " + lib);
            
            // Hook文件读取 - 精确复制成功模式
            var readPtr = Module.findExportByName("libc.so", "read");
            if (readPtr) {
                Interceptor.attach(readPtr, {
                    onEnter: function(args) {
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                        this.isMapFile = (this.size > 5000 && this.size < 1024*1024);
                    },
                    onLeave: function(retval) {
                        if (!this.isMapFile) return;
                        
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 0 && extractCount < maxExtracts) {
                            fileReadCount++;
                            
                            try {
                                var previewData = this.buffer.readByteArray(Math.min(32, bytesRead));
                                var previewBytes = new Uint8Array(previewData);
                                var previewStr = "";
                                
                                for (var i = 0; i < Math.min(16, previewBytes.length); i++) {
                                    if (previewBytes[i] >= 32 && previewBytes[i] < 127) {
                                        previewStr += String.fromCharCode(previewBytes[i]);
                                    }
                                }
                                
                                if (!isSystemFile(previewStr)) {
                                    console.log("[File Read #" + fileReadCount + "] 非系统文件，大小: " + bytesRead);
                                    extractReadableData(this.buffer, bytesRead, "FileRead");
                                } else if (fileReadCount <= 2) {
                                    console.log("[File Read #" + fileReadCount + "] 跳过系统文件");
                                }
                                
                            } catch (e) {
                                console.log("[File Preview Error] " + e.message);
                            }
                        }
                    }
                });
                console.log(" File read() Hook设置成功");
            }
            
            // SQLite Hook - 精确复制成功模式
            var sqliteHookSuccess = false;
            try {
                var sqliteImplAddr = lib.add(0x15000);
                Interceptor.attach(sqliteImplAddr, {
                    onEnter: function(args) {
                        if (extractCount < maxExtracts) {
                            console.log("[SQLite Impl] bind_blob实现调用");
                            try {
                                var stmt = args[0];
                                var index = args[1];
                                var dataPtr = args[2];
                                var sizePtr = args[3];
                                
                                if (dataPtr && !dataPtr.isNull() && sizePtr) {
                                    var size = sizePtr.toInt32();
                                    if (size > 16 && size < 50000) {
                                        console.log("[SQLite Data] 发现数据: " + size + " 字节");
                                        extractReadableData(dataPtr, size, "SQLite-Impl");
                                    }
                                }
                            } catch (e) {
                                console.log("[SQLite Param Error] " + e.message);
                            }
                        }
                    }
                });
                console.log("[Hook] SQLite实现Hook设置成功");
                sqliteHookSuccess = true;
            } catch (e) {
                console.log("[Hook] SQLite实现Hook失败: " + e.message);
            }
            
            if (!sqliteHookSuccess) {
                try {
                    var altSqliteAddr = lib.add(0x13B24);
                    Interceptor.attach(altSqliteAddr, {
                        onEnter: function(args) {
                            if (extractCount < maxExtracts) {
                                console.log("[SQLite Alt] sub_13B24调用");
                                try {
                                    if (args[1] && !args[1].isNull() && args[2]) {
                                        var size = args[2].toInt32();
                                        if (size > 16 && size < 50000) {
                                            extractReadableData(args[1], size, "SQLite-Alt");
                                        }
                                    }
                                } catch (e) {
                                    console.log("[SQLite Alt Error] " + e.message);
                                }
                            }
                        }
                    });
                    console.log("[Hook] SQLite备用Hook设置成功");
                } catch (e) {
                    console.log("[Hook] SQLite备用Hook也失败: " + e.message);
                }
            }
            
            console.log("[Ready] 所有Hook设置完成，请移动地图到新区域");
            
        } catch (e) {
            console.log("[Setup Error] " + e.message);
        }
    }, 3000);
}

function statusReport() {
    console.log("\n === 人类可读数据报告 ===");
    console.log(" 文件读取: " + fileReadCount + " 次");
    console.log(" 发现数据: " + extractCount + "/" + maxExtracts);
    
    if (extractCount >= maxExtracts) {
        console.log(" 人类可读数据提取完成");
    } else if (extractCount > 0) {
        console.log(" 已发现部分数据，继续移动地图");
    } else {
        console.log(" 请移动地图到新区域触发数据加载");
    }
    console.log("============================\n");
}

setupDataHooks();
setInterval(statusReport, 20000);

console.log("[Data Extractor] 脚本加载完成，3秒后开始Hook设置"); 