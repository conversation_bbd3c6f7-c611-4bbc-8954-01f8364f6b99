(function () {
    'use strict';

    // 配置
    var MODULE_NAME = 'libamapnsq.so';
    var ENABLE_LIBAMAPNSQ_HOOKS = true; // 若无偏移，可设为 false
    var ENABLE_LIBC_FILEIO = true;      // 是否启用 libc 文件 I/O 钩子
    var ENABLE_MMAP_HOOKS = false;      // 默认关闭 mmap/munmap，避免启动期开销
    var ENABLE_ASSET_HOOKS = false;     // 默认关闭 APK 资产读取钩子
    var ENABLE_SQLITE_HOOKS = false;    // 默认关闭 sqlite 钩子
    var ENABLE_ZLIB_UNCOMPRESS = false; // 启动期默认关闭，必要时再开
    var ENABLE_ZLIB_INFLATE = false;    // 启动期默认关闭，必要时再开

    var OFFSETS = {
        sub_C654: 0xC654,      // 入口/解压触发（示例，需以 IDA 校验）
        sub_5C394: 0x5C394     // 解析调度（示例，需以 IDA 校验）
    };

    var MAX_HEX_PREVIEW = 128;      // 预览 hexdump 字节数（降到 128）
    var READ_HEXDUMP_LIMIT = 64;    // 对 read/pread64/fread 的缓冲区预览（降到 64）
    var CHECK_INTERVAL_MS = 500;
    var STARTUP_DELAY_MS = 0;       // 如 spawn 卡顿可调大（例如 5000-8000ms）；attach 模式设 0

    // 记录 fd/FILE* 到路径的映射，用于判断离线来源
    var fdToPath = {};
    var filePtrToPath = {};

    function nowMs() { return (new Date()).getTime(); }

    function safeCString(ptr) {
        try { return ptr ? Memory.readCString(ptr) : null; } catch (e) { return null; }
    }

    function safeHexdump(ptrValue, length) {
        try {
            console.log(hexdump(ptrValue, { length: length, header: true, ansi: false }));
        } catch (e) {
            // 降噪：仅简短提示
            console.log('  [!] 无法读取内存: ' + e.message);
        }
    }

    function looksLikeOfflinePath(path) {
        if (!path) return false;
        var p = path.toLowerCase();
        // 更严格的离线判定：优先 .ans、典型离线路径
        if (p.indexOf('.ans') >= 0) return true;
        if (p.indexOf('/offline') >= 0) return true;
        if (p.indexOf('/offline_map') >= 0) return true;
        if (p.indexOf('/offlinemap') >= 0) return true;
        if (p.indexOf('/maps/') >= 0 && (p.indexOf('/data') >= 0 || p.indexOf('/tiles') >= 0)) return true;
        if (p.indexOf('/autonavi') >= 0 && (p.indexOf('/files') >= 0 || p.indexOf('/amap') >= 0)) return true;
        if (p.indexOf('/android/data/com.autonavi.minimap/files') >= 0) return true;
        return false;
    }

    function logHeader(title) {
        console.log('\n==================== ' + title + ' ====================');
    }

    function hookLibcFileIO() {
        if (!ENABLE_LIBC_FILEIO) return;
        var libc = 'libc.so';

        function tryHook(name, impl) {
            var addr = Module.findExportByName(libc, name);
            if (!addr) return;
            try {
                Interceptor.attach(addr, impl);
                console.log('[libc] Hooked ' + name + ' @ ' + addr);
            } catch (e) {
                console.log('[libc] Failed hook ' + name + ': ' + e.message);
            }
        }

        // open/open64
        var openImpl = {
            onEnter: function (args) { this.path = safeCString(args[0]); },
            onLeave: function (retval) {
                var fd = retval.toInt32();
                if (fd >= 0 && this.path) {
                    fdToPath[fd] = this.path;
                    if (looksLikeOfflinePath(this.path)) {
                        logHeader('open (OFFLINE CANDIDATE)');
                        console.log('[open] fd=' + fd + ' path=' + this.path);
                    }
                }
            }
        };
        tryHook('open', openImpl);
        tryHook('open64', openImpl);

        // openat/openat64
        var openatImpl = {
            onEnter: function (args) {
                this.dirfd = args[0].toInt32();
                this.path = safeCString(args[1]);
            },
            onLeave: function (retval) {
                var fd = retval.toInt32();
                if (fd >= 0 && this.path) {
                    fdToPath[fd] = this.path;
                    if (looksLikeOfflinePath(this.path)) {
                        logHeader('openat (OFFLINE CANDIDATE)');
                        console.log('[openat] dirfd=' + this.dirfd + ' fd=' + fd + ' path=' + this.path);
                    }
                }
            }
        };
        tryHook('openat', openatImpl);
        tryHook('openat64', openatImpl);

        // read
        tryHook('read', {
            onEnter: function (args) {
                this.fd = args[0].toInt32();
                this.buf = args[1];
                this.count = args[2].toInt32();
                this.path = fdToPath[this.fd];
                this.ts = nowMs();
            },
            onLeave: function (retval) {
                var n = retval.toInt32();
                if (n > 0 && looksLikeOfflinePath(this.path)) {
                        logHeader('read (OFFLINE)');
                    console.log('[read] fd=' + this.fd + ' path=' + this.path + ' bytes=' + n + ' ts=' + this.ts);
                        safeHexdump(this.buf, Math.min(READ_HEXDUMP_LIMIT, n));
                }
            }
        });

        // pread64
        tryHook('pread64', {
            onEnter: function (args) {
                this.fd = args[0].toInt32();
                this.buf = args[1];
                this.count = args[2].toInt32();
                this.offset = args[3].toInt32 ? args[3].toInt32() : 0;
                this.path = fdToPath[this.fd];
                this.ts = nowMs();
            },
            onLeave: function (retval) {
                var n = retval.toInt32();
                if (n > 0 && looksLikeOfflinePath(this.path)) {
                        logHeader('pread64 (OFFLINE)');
                    console.log('[pread64] fd=' + this.fd + ' off=' + this.offset + ' path=' + this.path + ' bytes=' + n + ' ts=' + this.ts);
                        safeHexdump(this.buf, Math.min(READ_HEXDUMP_LIMIT, n));
                }
            }
        });

        // 关闭/清理
        tryHook('close', {
            onEnter: function (args) { this.fd = args[0].toInt32(); this.path = fdToPath[this.fd]; },
            onLeave: function (retval) { if (this.path) delete fdToPath[this.fd]; }
        });

        // 可选：mmap/munmap（默认关闭）
        if (ENABLE_MMAP_HOOKS) {
        tryHook('mmap', {
            onEnter: function (args) {
                this.length = args[1].toInt32();
                this.fd = args[4].toInt32();
                this.off = args[5].toInt32 ? args[5].toInt32() : 0;
                this.path = fdToPath[this.fd];
            },
            onLeave: function (retval) {
                    var tag = this.path ? this.path : ('fd:' + this.fd);
                    if (looksLikeOfflinePath(this.path)) {
                        logHeader('mmap (OFFLINE)');
                        console.log('[mmap] ret=' + retval + ' len=' + this.length + ' fd=' + this.fd + ' off=' + this.off + ' path=' + tag);
                    }
                }
            });
            tryHook('munmap', {
                onEnter: function (args) { this.addr = args[0]; this.length = args[1].toInt32(); },
                onLeave: function (retval) { /* 降噪，不打印 */ }
            });
        }
    }

    // libandroid 资产读取（默认关闭）
    function hookLibandroidAssets() {
        if (!ENABLE_ASSET_HOOKS) return;
        var lib = 'libandroid.so';
        function tryHook(name, impl) {
            var addr = Module.findExportByName(lib, name);
            if (!addr) return;
            try { Interceptor.attach(addr, impl); console.log('[libandroid] Hooked ' + name + ' @ ' + addr); } catch (e) { console.log('[libandroid] Failed hook ' + name + ': ' + e.message); }
        }
        tryHook('AAssetManager_open', {
            onEnter: function (args) { this.name = safeCString(args[1]); },
            onLeave: function (retval) {
                if (!retval.isNull()) {
                    var path = this.name || '<asset>';
                    if (looksLikeOfflinePath(path)) {
                        logHeader('AAssetManager_open (OFFLINE CANDIDATE)');
                        console.log('[asset open] asset=' + path + ' assetPtr=' + retval);
                    }
                }
            }
        });
        tryHook('AAsset_read', {
            onEnter: function (args) { this.asset = args[0]; this.buf = args[1]; this.size = args[2].toInt32(); },
            onLeave: function (retval) { var n = retval.toInt32(); if (n > 0) { logHeader('AAsset_read'); safeHexdump(this.buf, Math.min(READ_HEXDUMP_LIMIT, n)); } }
        });
        tryHook('AAsset_close', { onEnter: function (args) { this.asset = args[0]; }, onLeave: function (retval) { /* 降噪 */ } });
    }

    // SQLite（默认关闭）
    function hookSqlite() {
        if (!ENABLE_SQLITE_HOOKS) return;
        var lib = 'libsqlite.so';
        function tryHook(name, impl) {
            var addr = Module.findExportByName(lib, name);
            if (!addr) return;
            try { Interceptor.attach(addr, impl); console.log('[sqlite] Hooked ' + name + ' @ ' + addr); } catch (e) { console.log('[sqlite] Failed hook ' + name + ': ' + e.message); }
        }
        tryHook('sqlite3_open', { onEnter: function (args) { this.path = safeCString(args[0]); }, onLeave: function (retval) { console.log('[sqlite3_open] path=' + this.path + ' ret=' + retval); } });
        tryHook('sqlite3_open_v2', { onEnter: function (args) { this.path = safeCString(args[0]); }, onLeave: function (retval) { console.log('[sqlite3_open_v2] path=' + this.path + ' ret=' + retval); } });
    }

    // zlib inflate 系列（默认关闭）
    function hookZlibInflate() {
        if (!ENABLE_ZLIB_INFLATE) return;
        var lib = 'libz.so';
        function hook(name, impl) {
            var addr = Module.findExportByName(lib, name);
            if (!addr) return;
            try { Interceptor.attach(addr, impl); console.log('[zlib] Hooked ' + name + ' @ ' + addr); } catch (e) { console.log('[zlib] Failed hook ' + name + ': ' + e.message); }
        }
        hook('inflateInit2_', { onEnter: function () { /* 降噪 */ }, onLeave: function (retval) { /* 降噪 */ } });
        hook('inflate', {
            onEnter: function (args) { this.z = args[0]; this.flush = args[1].toInt32(); },
            onLeave: function (retval) { /* 降噪：仅保留必要信息 */ }
        });
        hook('inflateEnd', { onEnter: function () { /* 降噪 */ }, onLeave: function () { /* 降噪 */ } });
    }

    // zlib uncompress（默认关闭）
    function hookZlibUncompress() {
        if (!ENABLE_ZLIB_UNCOMPRESS) return;
        var addr = Module.findExportByName('libz.so', 'uncompress');
        if (!addr) { console.log('[zlib] uncompress not found'); return; }
        Interceptor.attach(addr, {
            onEnter: function (args) {
                this.dest = args[0];
                this.destLenPtr = args[1];
                this.src = args[2];
                this.srcLen = args[3].toInt32();
                // 降噪：不在启动期 hexdump 大块数据
            },
            onLeave: function (retval) { /* 降噪 */ }
        });
        console.log('[zlib] Hooked uncompress @ ' + addr);
    }

    function hookLibamapnsq(module) {
        if (!ENABLE_LIBAMAPNSQ_HOOKS) return;
        function hookAt(offset, name) {
            try {
                var addr = module.base.add(offset);
                Interceptor.attach(addr, {
                    onEnter: function (args) {
                        logHeader('libamapnsq ' + name + ' ENTER');
                        console.log('[' + name + '] addr=' + addr + ' arg0=' + args[0] + ' arg1=' + args[1]);
                        if (args[0]) safeHexdump(args[0], Math.min(MAX_HEX_PREVIEW, 64));
                    },
                    onLeave: function (retval) { /* 降噪 */ }
                });
                console.log('[libamapnsq] Hooked ' + name + ' @ ' + addr);
            } catch (e) {
                console.log('[libamapnsq] Failed to hook ' + name + ': ' + e.message);
            }
        }
        hookAt(OFFSETS.sub_C654, 'sub_C654');
        hookAt(OFFSETS.sub_5C394, 'sub_5C394');
    }

    function main() {
        console.log('[离线追踪] 脚本启动中... (Frida 12.9.7 / ES5)');
        // 启动期默认不装载重型钩子，避免卡死/闪退
        hookZlibUncompress();
        hookZlibInflate();
        hookLibandroidAssets();
        hookSqlite();

        var didHookFileIO = false;
        var hooked = false;
        var timer = setInterval(function () {
            var mod = Process.findModuleByName(MODULE_NAME);
            if (!hooked && mod) {
            hooked = true;
            console.log('[离线追踪] 目标模块已加载: ' + MODULE_NAME + ', 基址: ' + mod.base);
            try {
                Java.perform(function () {
                    console.log('[离线追踪] Java 环境就绪。');
                    hookLibamapnsq(mod);
                        // 延迟再装 libc 文件 I/O 钩子，避开启动期敏感阶段
                        setTimeout(function () { if (!didHookFileIO) { hookLibcFileIO(); didHookFileIO = true; } }, STARTUP_DELAY_MS);
                });
            } catch (e) {
                    console.log('[离线追踪] Java.perform 失败，直接 Hook Native: ' + e.message);
                hookLibamapnsq(mod);
                    setTimeout(function () { if (!didHookFileIO) { hookLibcFileIO(); didHookFileIO = true; } }, STARTUP_DELAY_MS);
                }
            }
        }, CHECK_INTERVAL_MS);
    }

    setImmediate(main);
})(); 