// 高德地图 nativeAddMapGestureMsg 动态流程追踪脚本 (V52 - Stalker版)
// 适用于 Frida 12.9.7，使用 ES5 语法
// 使用Stalker动态追踪，无需硬编码下游函数地址

(function() {
    'use-strict';

    // 全局配置
    var config = {
        nativeAddMapGestureMsg_offset: 0x6ee70c,
        stalkerMaxEvents: 5000 // 限制Stalker事件数量，防止内存溢出
    };

    // 全局变量
    var libamapr = null;
    var stalkerEvents = [];

    // 工具函数 - 日志输出
    function log(message) {
        console.log(message);
    }

    function logError(message) {
        console.log("[-] " + message);
    }

    // 工具函数 - 格式化地址为模块+偏移格式
    function formatAddressWithModule(address) {
        if (!address || address.isNull()) return "0x0";
        try {
            var module = Process.findModuleByAddress(address);
            if (module) {
                var offset = address.sub(module.base);
                return module.name + "!0x" + offset.toString(16);
            }
        } catch (e) {}
        return "0x" + address.toString(16);
    }

    // 1. 钩住 Java 层的 nativeAddMapGestureMsg 方法 (作为启动Stalker的触发器)
    function hookJavaGestureMethod() {
        log("[+] 准备监控 Java 层 nativeAddMapGestureMsg 方法...");
        Java.perform(function() {
            try {
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                        log("\n==================== 新手势事件 (类型: " + type + ") ====================");
                        var result = this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                        log("==================== 手势事件处理结束 ====================\n");
                        return result;
                    };
                    log("[+] 成功钩住 Java 层 nativeAddMapGestureMsg 方法");
                } else {
                    logError("未找到 nativeAddMapGestureMsg 方法");
                }
            } catch (e) {
                logError("钩住 Java 层方法失败: " + e);
            }
        });
    }

    // 2. 钩住 Native 函数并启动Stalker
    function hookAndStalkNative() {
        log("[+] 准备钩住并追踪 Native 函数...");
        libamapr = Process.findModuleByName("libamapr.so");
        if (!libamapr) {
            logError("未找到 libamapr.so 模块，无法追踪");
            return;
        }

        var targetAddr = libamapr.base.add(config.nativeAddMapGestureMsg_offset);
        log("[+] 目标函数地址: " + formatAddressWithModule(targetAddr));

        try {
            Interceptor.attach(targetAddr, {
                onEnter: function(args) {
                    log("---> 进入 Native 函数: nativeAddMapGestureMsg");
                    stalkerEvents = []; // 清空上一次的事件
                    try {
                        Stalker.follow(this.threadId, {
                            events: {
                                call: true, // 只关心函数调用
                                ret: false, // 不关心返回，简化分析
                                exec: false
                            },
                            onReceive: function(events) {
                                // 批量接收事件
                                stalkerEvents = stalkerEvents.concat(Stalker.parse(events));
                            }
                        });
                    } catch (e) {
                        logError("启动Stalker失败: " + e.message);
                    }
                },
                onLeave: function(retval) {
                    try {
                        Stalker.unfollow(this.threadId);
                        Stalker.flush();
                        log("<--- 退出 Native 函数: nativeAddMapGestureMsg");

                        // 分析并输出结果
                        processStalkerEvents();

                    } catch (e) {
                        logError("停止Stalker或分析事件失败: " + e.message);
                    }
                }
            });
            log("[+] 成功钩住 Native 函数并设置Stalker");
        } catch (e) {
            logError("钩住 Native 函数失败: " + e);
        }
    }

    // 3. 分析和处理Stalker捕获的事件
    function processStalkerEvents() {
        if (stalkerEvents.length === 0) {
            log("[!] Stalker 未捕获到任何函数调用事件。");
            return;
        }

        log("\n--- Stalker 捕获的下游函数调用链 (共 " + stalkerEvents.length + " 个调用) ---");

        var callDepth = 0;
        var uniqueCalls = {};

        stalkerEvents.forEach(function(event) {
            var location = event[1];
            var module = Process.findModuleByAddress(location);

            // 只显示目标模块内的调用，过滤掉系统库和无关库的调用
            if (module && (module.name === 'libamapr.so' || module.name === 'libamapnsq.so')) {
                var functionName = formatAddressWithModule(location);
                
                // 避免重复打印完全相同的连续调用
                if (!uniqueCalls[functionName]) {
                    var indent = "  ".repeat(callDepth);
                    log(indent + "-> " + functionName);
                    uniqueCalls[functionName] = true;
                }
            }
        });
        log("--- 调用链分析结束 ---\n");
    }

    // 主函数
    function main() {
        log("高德地图手势下游流程动态追踪脚本 (V52) 启动");
        
        // 延迟执行，确保应用完全初始化
        setTimeout(function() {
            try {
                // 1. 先钩住Java层，作为流程起点
                hookJavaGestureMethod();
                
                // 2. 再钩住Native层函数并设置Stalker
                hookAndStalkNative();
                
                log("[+] 脚本设置完成，等待手势事件...");
            } catch (e) {
                logError("脚本初始化失败: " + e);
            }
        }, 4000); // 增加延迟以提高稳定性
    }
    
    // 启动脚本
    main();
})();