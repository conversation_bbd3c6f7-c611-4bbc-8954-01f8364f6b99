     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
[Real Data Format Analyzer] 启动真实数据格式分析器...
[Java] Java环境已准备就绪
[Main] 等待应用初始化完成...
[Remote::com.autonavi.minimap]-> [Main] 开始初始化真实数据格式分析器...
[Library] libamapnsq.so 已加载，基址: 0x7f761be000
[Real Data] 设置真实数据格式分析器...
[Real Data] 真实数据格式分析器已设置
[Real Data Format Analyzer] 真实数据格式分析器已启动!
现在移动地图以触发数据处理，观察真实数据格式...
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.i...\0\0\0'
  - 首字节: 0x88
  - 估计格式: custom
  - 压缩标识: 否
  - 可能的大小: 1976975496
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x-1
[Execution] 耗时: 15ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.i...\0\0\0'
  - 首字节: 0x88
  - 估计格式: custom
  - 压缩标识: 否
  - 可能的大小: 1976975496
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4009
[Execution] 耗时: 11ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.i...\0\0\0'
  - 首字节: 0x88
  - 估计格式: custom
  - 压缩标识: 否
  - 可能的大小: 1976975496
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:2_arg2:0x4000
[Execution] 耗时: 3ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.i...\0\0\0'
  - 首字节: 0x88
  - 估计格式: custom
  - 压缩标识: 否
  - 可能的大小: 1976975496
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 2ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.i...\0\0\0'
  - 首字节: 0x88
  - 估计格式: custom
  - 压缩标识: 否
  - 可能的大小: 1976975496
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 4ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 4ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 13ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 14ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 6ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 5ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 8ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 17ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 0ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 3ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 5ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 3ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 7ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 2ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 0ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 8ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 13ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 3ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 8ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 1ms, 返回码: 0

=== 真实数据格式分析报告 ===
总调用次数: 31

数据类型分布:
  type1: 0 次 (0.0%)
  type2: 0 次 (0.0%)
  type3: 0 次 (0.0%)
  unknown: 31 次 (100.0%)

参数模式频率:
  arg1:0_arg2:0x4000: 16 次
  arg1:0_arg2:0x4001: 12 次
  arg1:0_arg2:0x-1: 1 次
  arg1:2_arg2:0x4000: 1 次
  arg1:0_arg2:0x4009: 1 次

执行时间统计:
  平均耗时: 5.23ms
  最大耗时: 17ms

返回码分布:
  返回码 0: 31 次
==========================

[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 2ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 2ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 3ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 4ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 3ms, 返回码: 0
[Execution] 耗时: 2ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 2ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 2ms, 返回码: 0
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 3ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 2ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 5ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 7ms, 返回码: 0
[Execution] 耗时: 12ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 2ms, 返回码: 0
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 3ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 1ms, 返回码: 0
[Execution] 耗时: 5ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 2ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 3ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 13ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 0ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 1ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4000
[Execution] 耗时: 1ms, 返回码: 0
[Execution] 耗时: 4ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '. .f.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: -1814776952
  - 可能的标志: 0x7f
  - 可能的版本: 0
[Param Pattern] arg1:0_arg2:0x4001
[Execution] 耗时: 7ms, 返回码: 0
[Data Format] 检测到数据类型: unknown
  - 头部模式: '.C.U.\0\0\0'
  - 首字节: 0x8
  - 估计格式: custom
  - 压缩标识: 是
  - 可能的大小: 1607743592
Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=00000120967B8570)

Thread 0x000041b8 (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x00000dec (most recent call first):
<no Python frame>
