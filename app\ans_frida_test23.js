


// 添加到脚本中
Java.perform(function() {
    try {
      // 确认实例指针
      var nativePtr = ptr("0x7f56433b00");
      
      // 读取前几个指针，寻找vtable
      console.log("[分析] Native实例内存结构:");
      for (var i = 0; i < 5; i++) {
        try {
          var ptrValue = Memory.readPointer(nativePtr.add(i * Process.pointerSize));
          console.log("  偏移 +" + (i * Process.pointerSize) + ": " + ptrValue);
          
          // 第一个指针通常是vtable
          if (i == 0) {
            console.log("[分析] 可能的vtable: " + ptrValue);
            // 读取vtable的前10个函数
            for (var j = 0; j < 10; j++) {
              try {
                var funcPtr = Memory.readPointer(ptrValue.add(j * Process.pointerSize));
                console.log("    函数[" + j + "]: " + funcPtr);
              } catch(e) {}
            }
          }
        } catch(e) {
          console.log("  读取偏移 +" + (i * Process.pointerSize) + " 失败");
        }
      }
    } catch(e) {
      console.log("[错误] " + e);
    }
  });