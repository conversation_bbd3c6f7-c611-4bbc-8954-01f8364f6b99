/**
 * 高德地图数据解析流程综合分析脚本
 * 
 * 功能：结合 IDA Pro 静态分析结果，使用 Frida 动态跟踪数据解析流程
 * 重点：从 ANS 文件解析到渲染引擎的完整数据流
 * 
 * 基于 IDA Pro 分析的关键函数：
 * - sub_C654 (0xC654): 解压协调函数
 * - sub_5C394 (0x5C394): 解析调度器  
 * - sub_10F88 (0x10F88): 具体数据块解析器
 * 
 * 兼容性：Frida 12.9.7, ES5 语法
 */

(function() {
    'use strict';
    
    // ==================== 配置参数 ====================
    var CONFIG = {
        MODULE_LIBAMAPNSQ: 'libamapnsq.so',
        MODULE_LIBAMAPR: 'libamapr.so',
        MODULE_LIBZ: 'libz.so',

        // 基于 IDA Pro 分析的函数偏移
        OFFSETS: {
            sub_C654: 0xC654,      // 解压协调函数 (大小: 0x98)
            sub_5C394: 0x5C394,    // 解析调度器 (大小: 0x920)
            sub_10F88: 0x10F88     // 数据块解析器 (大小: 0x510)
        },

        // 渲染引擎相关函数 (libamapr.so)
        RENDER_OFFSETS: {
            processGestureMessage: 0x6FB530,
            triggerRenderUpdate: 0x6FBC78,
            updateMapView: 0x6FB9E0
        },

        // 输出控制
        MAX_HEXDUMP_SIZE: 64,       // 进一步减少内存转储大小
        MAX_DATA_PREVIEW: 128,      // 减少数据预览大小
        ENABLE_DETAILED_LOGGING: false, // 禁用详细日志以提高稳定性
        ENABLE_MEMORY_DUMP: false,  // 默认关闭内存转储以提高稳定性

        // 稳定性和错误处理配置 - 基于日志分析优化
        MAX_RETRY_COUNT: 3,         // 增加重试次数
        RETRY_DELAY_MS: 2000,       // 减少重试延迟
        MODULE_CHECK_TIMEOUT: 60000, // 适中的模块检查超时
        HOOK_SETUP_DELAY: 3000,     // 减少 Hook 设置延迟
        ANTI_DEBUG_BYPASS: false,   // 禁用反调试绕过
        SAFE_MODE: true,            // 安全模式

        // 连续运行配置
        CONTINUOUS_MONITORING: true, // 启用连续监控
        HEARTBEAT_INTERVAL: 10000,  // 10秒心跳检查
        AUTO_RESTART: true,         // 自动重启机制
        PROCESS_MONITOR: true,      // 进程监控

        // 兼容性配置
        ES5_STRICT_MODE: true,      // 严格ES5兼容
        FRIDA_12_COMPAT: true,      // Frida 12.9.7兼容模式
        MINIMAL_MODE: true,         // 最小化模式
        PASSIVE_MODE: true,         // 被动模式

        // 优化的启动配置
        DELAYED_STARTUP: 5000,      // 减少启动延迟到5秒
        QUICK_INIT: true,           // 快速初始化模式
        BACKGROUND_MODE: true,      // 后台模式运行

        // 日志配置
        ENABLE_DETAILED_LOGGING: false, // 详细日志
        ENABLE_MEMORY_DUMP: false,      // 内存转储
        MAX_HEXDUMP_SIZE: 256           // 最大转储大小
    };
    
    var g_moduleBase = null;
    var g_renderModuleBase = null;
    var g_dataFlowCounter = 0;
    var g_initializationComplete = false;
    var g_hookSetupComplete = false;
    var g_retryCount = 0;

    // 连续监控相关变量
    var g_isRunning = false;
    var g_lastHeartbeat = 0;
    var g_monitoringStarted = false;
    var g_processAlive = true;
    var g_hookAttempts = 0;
    var g_startTime = 0;
    var g_restartCount = 0;

    // ANS 数据解析相关变量
    var g_dataFlowCounter = 0;
    var g_ansDataCaptured = 0;
    var g_gestureEventCount = 0;
    var g_fileReadCount = 0;
    var g_memoryAllocCount = 0;
    
    // ==================== 工具函数 ====================

    function log(msg) {
        console.log('[ANS数据解析] ' + msg);
    }

    function logError(msg) {
        console.log('[ANS数据解析-错误] ' + msg);
    }

    function logWarning(msg) {
        console.log('[ANS数据解析-警告] ' + msg);
    }

    // ==================== 连续监控和心跳机制 ====================

    function startContinuousMonitoring() {
        if (!CONFIG.CONTINUOUS_MONITORING) return;

        log('启动连续监控机制...');
        g_isRunning = true;
        g_lastHeartbeat = Date.now();

        // 增强的心跳检查
        var heartbeatTimer = setInterval(function() {
            try {
                var now = Date.now();
                var uptime = Math.floor((now - g_startTime) / 1000);

                log('心跳检查: 运行时间=' + uptime + '秒, 状态=正常, PID=' + (typeof Process !== 'undefined' ? Process.id : 'N/A'));
                g_lastHeartbeat = now;

                // 检查进程状态
                if (CONFIG.PROCESS_MONITOR) {
                    checkProcessStatus();
                }

                // 自我恢复检查
                if (!g_isRunning) {
                    logWarning('检测到运行状态异常，尝试恢复...');
                    g_isRunning = true;
                }

            } catch (e) {
                logError('心跳检查错误: ' + e.message);
                // 即使心跳出错也要保持运行
                try {
                    g_lastHeartbeat = Date.now();
                    g_isRunning = true;
                } catch (recoveryError) {
                    logError('心跳恢复失败: ' + recoveryError.message);
                }
            }
        }, CONFIG.HEARTBEAT_INTERVAL);

        log('✅ 连续监控已启动');
        return heartbeatTimer;
    }

    function checkProcessStatus() {
        try {
            // 简单的进程存活检查
            if (typeof Process !== 'undefined' && Process.id) {
                var processId = Process.id;
                if (processId > 0) {
                    g_processAlive = true;
                    logVerbose('进程状态检查: PID=' + processId + ', 状态=存活');
                } else {
                    g_processAlive = false;
                    logError('进程状态异常');
                }
            } else {
                // 如果 Process API 不可用，假设进程正常
                g_processAlive = true;
                logVerbose('Process API 不可用，假设进程正常');
            }
        } catch (e) {
            logError('进程状态检查失败: ' + e.message);
            g_processAlive = true; // 默认假设进程正常
        }
    }

    function setupAutoRestart() {
        if (!CONFIG.AUTO_RESTART) return;

        log('设置自动重启机制...');

        // 监控脚本状态
        setInterval(function() {
            try {
                var now = Date.now();
                var timeSinceLastHeartbeat = now - g_lastHeartbeat;

                // 如果心跳超时，尝试重启
                if (timeSinceLastHeartbeat > CONFIG.HEARTBEAT_INTERVAL * 3) {
                    logWarning('检测到心跳超时，尝试重启监控...');
                    restartMonitoring();
                }

            } catch (e) {
                logError('自动重启检查错误: ' + e.message);
            }
        }, CONFIG.HEARTBEAT_INTERVAL * 2);

        log('✅ 自动重启机制已设置');
    }

    function restartMonitoring() {
        g_restartCount++;
        log('重启监控 (第 ' + g_restartCount + ' 次)...');

        try {
            // 重置状态
            g_isRunning = false;
            g_lastHeartbeat = Date.now();

            // 重新初始化
            setTimeout(function() {
                initializeModules();
            }, 2000);

        } catch (e) {
            logError('重启监控失败: ' + e.message);
        }
    }
    
    // ES5兼容的字符串重复函数
    function repeatString(str, count) {
        var result = '';
        for (var i = 0; i < count; i++) {
            result += str;
        }
        return result;
    }

    function logHeader(title) {
        console.log('\n' + repeatString('=', 60));
        console.log('  ' + title);
        console.log(repeatString('=', 60));
    }

    function logSubHeader(title) {
        console.log('\n' + repeatString('-', 40));
        console.log('  ' + title);
        console.log(repeatString('-', 40));
    }

    function logVerbose(msg) {
        if (CONFIG.ENABLE_DETAILED_LOGGING) {
            console.log('[详细] ' + msg);
        }
    }

    function validateFridaEnvironment() {
        var issues = [];

        try {
            if (typeof Process === 'undefined') {
                issues.push('Process API 不可用');
            }
            if (typeof Module === 'undefined') {
                issues.push('Module API 不可用');
            }
            if (typeof Interceptor === 'undefined') {
                issues.push('Interceptor API 不可用');
            }
            if (typeof Java === 'undefined') {
                issues.push('Java API 不可用');
            }

            if (issues.length > 0) {
                logError('Frida 环境检查发现问题: ' + issues.join(', '));
                return false;
            } else {
                log('✅ Frida 环境检查通过');
                return true;
            }
        } catch (e) {
            logError('Frida 环境检查失败: ' + e.message);
            return false;
        }
    }
    
    function safeHexdump(ptr, size, title) {
        if (!CONFIG.ENABLE_MEMORY_DUMP) return;
        
        try {
            if (ptr && !ptr.isNull() && size > 0) {
                var dumpSize = Math.min(size, CONFIG.MAX_HEXDUMP_SIZE);
                console.log('\n[内存转储] ' + title + ' (地址: ' + ptr + ', 大小: ' + size + ')');
                console.log(hexdump(ptr, { 
                    length: dumpSize, 
                    header: true, 
                    ansi: false 
                }));
                if (size > dumpSize) {
                    console.log('... (截断显示，实际大小: ' + size + ' 字节)');
                }
            }
        } catch (e) {
            console.log('[内存转储错误] ' + title + ': ' + e.message);
        }
    }
    
    function analyzeDataStructure(ptr, size, context) {
        if (!ptr || ptr.isNull() || size <= 0) return;
        
        try {
            logSubHeader('数据结构分析 - ' + context);
            
            // 检查是否为 DICE-AM 格式
            if (size >= 8) {
                var header = Memory.readCString(ptr, 8);
                if (header && header.indexOf('DICE') >= 0) {
                    console.log('[数据格式] 检测到 DICE-AM 格式数据');
                    console.log('[头部信息] ' + header);
                    
                    if (size >= 16) {
                        var version = Memory.readU8(ptr.add(8));
                        var type = Memory.readU8(ptr.add(9));
                        console.log('[版本信息] 版本: 0x' + version.toString(16) + ', 类型: 0x' + type.toString(16));
                    }
                }
            }
            
            // 分析数据特征
            if (size >= 4) {
                var firstDword = Memory.readU32(ptr);
                console.log('[数据特征] 前4字节: 0x' + firstDword.toString(16));
            }
            
            // 查找可能的坐标数据 (浮点数模式)
            if (size >= 16) {
                for (var i = 0; i < Math.min(size - 8, 64); i += 4) {
                    try {
                        var floatVal = Memory.readFloat(ptr.add(i));
                        // 检查是否为合理的地理坐标范围
                        if (floatVal > -180 && floatVal < 180 && Math.abs(floatVal) > 0.001) {
                            console.log('[可能坐标] 偏移 +' + i + ': ' + floatVal.toFixed(6));
                        }
                    } catch (e) {}
                }
            }
            
        } catch (e) {
            console.log('[数据分析错误] ' + e.message);
        }
    }
    
    // ==================== 初始化函数 ====================

    function initializeModules() {
        log('正在初始化模块... (ES5兼容模式)');

        // 启动连续监控
        if (CONFIG.CONTINUOUS_MONITORING) {
            startContinuousMonitoring();
        }

        // 设置自动重启
        if (CONFIG.AUTO_RESTART) {
            setupAutoRestart();
        }

        try {
            // 快速初始化模式
            var maxRetries = CONFIG.QUICK_INIT ? 10 : 20;
            var retryCount = 0;

            var checkModules = function() {
                try {
                    log('DEBUG: 开始模块检查 #' + (retryCount + 1));

                    var libamapnsq = Process.findModuleByName(CONFIG.MODULE_LIBAMAPNSQ);
                    log('DEBUG: libamapnsq 查找完成');

                    var libamapr = Process.findModuleByName(CONFIG.MODULE_LIBAMAPR);
                    log('DEBUG: libamapr 查找完成');

                    log('模块检查 #' + (retryCount + 1) + ': libamapnsq=' + (libamapnsq ? '✅' : '❌') +
                        ', libamapr=' + (libamapr ? '✅' : '❌'));

                    if (libamapnsq && libamapr) {
                        log('DEBUG: 进入模块发现成功分支');

                        // 安全地存储模块对象，但不访问 .base 属性
                        g_moduleBase = libamapnsq.base;
                        g_renderModuleBase = libamapr.base;

                        log('✅ 模块发现成功');
                        log('DEBUG: 模块发现日志输出完成');

                        log('DEBUG: 准备启动数据监控');
                        g_isRunning = true;
                        log('DEBUG: g_isRunning 设置完成');

                        // 基于 IDA Pro 分析的渐进式 Hook 设置策略
                        setTimeout(function() {
                            try {
                                log('开始渐进式 Hook 设置 (基于 IDA Pro 分析)...');
                                setupProgressiveHooks();
                            } catch (hookError) {
                                logError('Hook 设置失败: ' + hookError.message);
                                log('继续基础监控模式...');
                            }
                        }, 10000); // 增加到10秒延迟，确保模块完全初始化

                        // 设置超稳定的心跳监控
                        log('DEBUG: 准备设置超稳定心跳定时器');

                        // 主心跳 - 确保脚本持续运行
                        setInterval(function() {
                            try {
                                log('脚本心跳: 正常运行');

                                // 每次心跳时报告 ANS 数据捕获统计
                                if (g_dataFlowCounter > 0 || g_gestureEventCount > 0 ||
                                    g_fileReadCount > 0 || g_memoryAllocCount > 0) {
                                    log('📊 ANS 数据统计: 数据解析=' + g_dataFlowCounter +
                                        ', 手势事件=' + g_gestureEventCount +
                                        ', 文件读取=' + g_fileReadCount +
                                        ', 内存分配=' + g_memoryAllocCount);
                                }
                            } catch (heartbeatError) {
                                // 即使心跳出错也要继续运行
                                log('心跳错误 (但脚本继续运行): ' + heartbeatError.message);
                            }
                        }, 30000);

                        // 备用心跳 - 双重保险
                        setInterval(function() {
                            try {
                                log('备用心跳: 脚本稳定运行中');
                            } catch (e) {
                                // 静默处理
                            }
                        }, 60000);

                        log('DEBUG: 超稳定心跳定时器设置完成');

                        log('✅ 数据监控模式已启动');
                        log('DEBUG: 所有操作完成，准备返回');

                        return true;
                    }

                    retryCount++;
                    if (retryCount < maxRetries) {
                        setTimeout(checkModules, CONFIG.RETRY_DELAY_MS);
                    } else {
                        logError('模块加载超时，尝试重新初始化...');
                        retryInitialization();
                    }
                    return false;

                } catch (e) {
                    logError('模块检查过程中发生错误: ' + e.message);
                    retryCount++;
                    if (retryCount < maxRetries) {
                        setTimeout(checkModules, CONFIG.RETRY_DELAY_MS);
                    } else {
                        retryInitialization();
                    }
                    return false;
                }
            };

            checkModules();

        } catch (e) {
            logError('初始化模块时发生严重错误: ' + e.message);
            retryInitialization();
        }
    }

    function safeValidateModules() {
        try {
            if (!g_moduleBase || !g_renderModuleBase) {
                logError('模块基址无效');
                return false;
            }

            // 最安全的验证方式 - 只检查对象是否存在，不进行任何转换
            if (g_moduleBase && g_renderModuleBase) {
                log('✅ 模块对象验证通过');
                log('📍 libamapnsq 模块: 已加载');
                log('📍 libamapr 模块: 已加载');
                return true;
            } else {
                logError('模块对象验证失败');
                return false;
            }

        } catch (e) {
            logError('安全模块验证过程中发生错误: ' + e.message);
            return false;
        }
    }

    function validateModules() {
        // 保留原函数但不使用，避免破坏性内存访问
        logWarning('使用安全验证模式，跳过内存访问验证');
        return safeValidateModules();
    }

    function retryInitialization() {
        g_retryCount++;
        if (g_retryCount < CONFIG.MAX_RETRY_COUNT) {
            logWarning('重新尝试初始化... (第 ' + g_retryCount + ' 次)');
            setTimeout(function() {
                initializeModules();
            }, CONFIG.RETRY_DELAY_MS * g_retryCount); // 递增延迟
        } else {
            logError('初始化失败，已达到最大重试次数');
            logError('可能的原因:');
            logError('1. 应用版本不匹配');
            logError('2. 模块未加载或地址变更');
            logError('3. 反调试机制阻止了分析');
            logError('4. 设备或环境不兼容');
        }
    }
    
    // ==================== 数据解析函数 Hook ====================

    // 1. Hook sub_C654 - 解压协调函数
    function hookDecompressionCoordinator() {
        if (g_emergencyShutdown) {
            logWarning('紧急关闭状态，跳过 Hook 设置');
            return false;
        }

        if (!g_moduleBase) {
            logError('hookDecompressionCoordinator: 模块基址无效');
            return false;
        }

        var targetAddr = g_moduleBase.add(CONFIG.OFFSETS.sub_C654);
        log('准备 Hook 解压协调函数 sub_C654 @ ' + targetAddr);

        try {
            Interceptor.attach(targetAddr, {
                onEnter: function(args) {
                    if (g_emergencyShutdown) return;

                    try {
                        this.startTime = Date.now();
                        this.dataFlowId = ++g_dataFlowCounter;

                        // 更新统计信息
                        updateStatistics('call', 1);

                        if (CONFIG.ENABLE_DETAILED_LOGGING) {
                            logHeader('解压协调函数调用 #' + this.dataFlowId);
                            console.log('[函数] sub_C654 (解压协调)');
                            console.log('[参数] arg0: ' + args[0] + ', arg1: ' + args[1]);
                        } else {
                            log('📞 sub_C654 调用 #' + this.dataFlowId);
                        }

                        // 安全的数据分析 - 避免内存访问违规
                        if (!CONFIG.MINIMAL_MODE && args[1] && !args[1].isNull()) {
                            try {
                                // 使用更安全的内存读取方式
                                var dataSize = Memory.readU32(args[1]);
                                if (dataSize > 0 && dataSize < 0x10000) { // 更严格的大小检查
                                    console.log('[输入数据] 压缩数据大小: ' + dataSize + ' 字节');
                                    updateStatistics('dataSize', dataSize);
                                }
                            } catch (e) {
                                // 静默处理内存访问错误
                                if (CONFIG.ENABLE_DETAILED_LOGGING) {
                                    console.log('[输入分析错误] ' + e.message);
                                }
                            }
                        }
                    } catch (e) {
                        logError('Hook onEnter 错误: ' + e.message);
                    }
                },

                onLeave: function(retval) {
                    if (g_emergencyShutdown) return;

                    try {
                        var duration = Date.now() - this.startTime;

                        if (CONFIG.ENABLE_DETAILED_LOGGING) {
                            console.log('[返回] sub_C654 返回值: ' + retval + ' (耗时: ' + duration + 'ms)');
                        } else {
                            log('📤 sub_C654 返回: ' + retval + ' (' + duration + 'ms)');
                        }

                        // 更新统计信息
                        updateStatistics('processingTime', duration);

                        if (retval.toInt32() === 0) {
                            if (CONFIG.ENABLE_DETAILED_LOGGING) {
                                console.log('[状态] 解压成功');
                            }
                            updateStatistics('success', 1);
                        } else {
                            if (CONFIG.ENABLE_DETAILED_LOGGING) {
                                console.log('[状态] 解压失败，错误码: ' + retval.toInt32());
                            }
                            updateStatistics('failure', 1);
                        }
                    } catch (e) {
                        logError('Hook onLeave 错误: ' + e.message);
                    }
                }
            });

            log('✅ 成功 Hook sub_C654');
            return true;
        } catch (e) {
            logError('❌ Hook sub_C654 失败: ' + e.message);
            return false;
        }
    }

    // 2. Hook sub_5C394 - 解析调度器
    function hookParsingDispatcher() {
        if (!g_moduleBase) {
            logError('hookParsingDispatcher: 模块基址无效');
            return false;
        }

        var targetAddr = g_moduleBase.add(CONFIG.OFFSETS.sub_5C394);
        log('准备 Hook 解析调度器 sub_5C394 @ ' + targetAddr);

        try {
            Interceptor.attach(targetAddr, {
                onEnter: function(args) {
                    this.startTime = Date.now();
                    this.dataFlowId = ++g_dataFlowCounter;

                    logHeader('解析调度器调用 #' + this.dataFlowId);
                    console.log('[函数] sub_5C394 (解析调度器)');
                    console.log('[参数] arg0: ' + args[0] + ', arg1: ' + args[1]);

                    // 分析解压后的数据
                    this.inputData = args[1];
                    if (args[1] && !args[1].isNull()) {
                        try {
                            // 尝试读取数据头部
                            var headerStr = Memory.readCString(args[1], 16);
                            if (headerStr) {
                                console.log('[数据头部] "' + headerStr + '"');

                                // 检查 DICE-AM 格式
                                if (headerStr.indexOf('DICE') >= 0) {
                                    console.log('[格式识别] DICE-AM 格式数据块');
                                    updateStatistics('diceAm', 1);
                                    analyzeDataStructure(args[1], 256, '调度器输入');
                                }
                            }

                            safeHexdump(args[1], 128, '调度器输入数据');
                        } catch (e) {
                            console.log('[数据分析错误] ' + e.message);
                        }
                    }
                },

                onLeave: function(retval) {
                    var duration = Date.now() - this.startTime;
                    console.log('[返回] sub_5C394 返回值: ' + retval + ' (耗时: ' + duration + 'ms)');

                    // 分析返回的解析结果
                    if (retval && !retval.isNull()) {
                        console.log('[输出] 解析结果指针: ' + retval);
                        safeHexdump(retval, 64, '调度器输出数据');
                    }
                }
            });

            log('✅ 成功 Hook sub_5C394');
            return true;
        } catch (e) {
            logError('❌ Hook sub_5C394 失败: ' + e.message);
            return false;
        }
    }

    // ==================== 超安全 Hook 设置 ====================

    function setupSafeHooks() {
        log('开始设置安全 Hook (非侵入模式)...');

        var hookCount = 0;
        var successfulHooks = [];

        try {
            // 优先尝试最安全的 Java 层 Hook
            if (setupJavaLayerMonitoring()) {
                hookCount++;
                successfulHooks.push('Java层监控');
                log('✅ Java 层监控设置成功');
            }

            // 如果 Java Hook 成功，可以尝试一个简单的 Native Hook
            if (hookCount > 0 && setupMinimalNativeHook()) {
                hookCount++;
                successfulHooks.push('最小Native Hook');
                log('✅ 最小 Native Hook 设置成功');
            }

            log('安全 Hook 设置完成: ' + hookCount + ' 个成功');
            log('成功的 Hook: ' + successfulHooks.join(', '));

            if (hookCount > 0) {
                log('🚀 安全监控模式启动成功');
                startContinuousObservation();
                return true;
            } else {
                log('⚠️ 未设置任何 Hook，进入纯观察模式');
                startObservationMode();
                return true; // 即使没有 Hook 也算成功
            }

        } catch (e) {
            logError('安全 Hook 设置错误: ' + e.message);
            log('回退到纯观察模式');
            startObservationMode();
            return true; // 容错处理
        }
    }

    function setupUltraSafeHooks() {
        // 重定向到安全 Hook 设置
        return setupSafeHooks();
    }

    function setupHooks() {
        log('开始设置标准 Hook...');

        if (CONFIG.ULTRA_SAFE_MODE) {
            return setupUltraSafeHooks();
        }

        var hookResults = {
            total: 0,
            success: 0,
            failed: 0
        };

        try {
            // 设置数据解析相关 Hook
            hookResults.total += 3;
            if (hookDecompressionCoordinator()) hookResults.success++;
            else hookResults.failed++;

            if (hookParsingDispatcher()) hookResults.success++;
            else hookResults.failed++;

            if (hookDataBlockParser()) hookResults.success++;
            else hookResults.failed++;

            log('Hook 设置完成: 成功 ' + hookResults.success + '/' + hookResults.total +
                ', 失败 ' + hookResults.failed);

            if (hookResults.success > 0) {
                log('✅ 至少有部分 Hook 设置成功，开始监控...');
                return true;
            } else {
                logError('❌ 所有 Hook 设置失败');
                return false;
            }

        } catch (e) {
            logError('Hook 设置过程中发生错误: ' + e.message);
            return false;
        }
    }

    // 3. Hook sub_10F88 - 数据块解析器 (最重要的函数)
    function hookDataBlockParser() {
        if (!g_moduleBase) return;

        var targetAddr = g_moduleBase.add(CONFIG.OFFSETS.sub_10F88);
        log('准备 Hook 数据块解析器 sub_10F88 @ ' + targetAddr);

        try {
            Interceptor.attach(targetAddr, {
                onEnter: function(args) {
                    this.startTime = Date.now();
                    this.dataFlowId = ++g_dataFlowCounter;

                    logHeader('数据块解析器调用 #' + this.dataFlowId + ' (核心函数)');
                    console.log('[函数] sub_10F88 (数据块解析器)');
                    console.log('[参数] arg0: ' + args[0] + ', arg1: ' + args[1]);

                    this.inputArg0 = args[0];
                    this.inputArg1 = args[1];

                    // 深度分析输入数据
                    if (args[1] && !args[1].isNull()) {
                        try {
                            // 基于 IDA Pro 分析，这里应该包含解压后的数据
                            analyzeDataStructure(args[1], 512, '数据块解析器输入');
                            safeHexdump(args[1], 256, '解析器原始输入');

                            // 尝试解析 DICE-AM 结构
                            var header = Memory.readCString(args[1], 8);
                            if (header && header === 'DICE-AM') {
                                logSubHeader('DICE-AM 数据块详细分析');

                                // 版本和类型信息 (基于 IDA Pro 分析的偏移)
                                var version = Memory.readU8(args[1].add(8));
                                var type = Memory.readU8(args[1].add(9));
                                var subtype = Memory.readU8(args[1].add(10));

                                console.log('[DICE-AM] 版本: 0x' + version.toString(16));
                                console.log('[DICE-AM] 类型: 0x' + type.toString(16));
                                console.log('[DICE-AM] 子类型: 0x' + subtype.toString(16));

                                // 尝试读取数据大小信息
                                try {
                                    var dataSize1 = Memory.readU16(args[1].add(26));
                                    var dataSize2 = Memory.readU16(args[1].add(28));
                                    console.log('[DICE-AM] 数据大小1: ' + dataSize1);
                                    console.log('[DICE-AM] 数据大小2: ' + dataSize2);
                                } catch (e) {}
                            }

                        } catch (e) {
                            console.log('[解析器输入分析错误] ' + e.message);
                        }
                    }
                },

                onLeave: function(retval) {
                    var duration = Date.now() - this.startTime;
                    console.log('[返回] sub_10F88 返回值: ' + retval + ' (耗时: ' + duration + 'ms)');

                    var returnCode = retval.toInt32();
                    if (returnCode === 0) {
                        console.log('[状态] ✅ 数据解析成功');

                        // 尝试分析解析后的结果
                        try {
                            // 基于 IDA Pro 分析，检查是否有输出数据结构
                            if (this.inputArg0 && !this.inputArg0.isNull()) {
                                logSubHeader('解析结果分析');

                                // 尝试读取可能的输出结构
                                var outputPtr = Memory.readPointer(this.inputArg0.add(8));
                                if (outputPtr && !outputPtr.isNull()) {
                                    console.log('[输出数据] 结果指针: ' + outputPtr);
                                    safeHexdump(outputPtr, 128, '解析后的结构化数据');

                                    // 分析可能的矢量数据
                                    analyzeVectorData(outputPtr, '解析结果');
                                }
                            }
                        } catch (e) {
                            console.log('[解析结果分析错误] ' + e.message);
                        }

                    } else {
                        console.log('[状态] ❌ 数据解析失败，错误码: ' + returnCode);

                        // 根据 IDA Pro 分析的错误码含义
                        var errorMsg = getErrorMessage(returnCode);
                        if (errorMsg) {
                            console.log('[错误详情] ' + errorMsg);
                        }
                    }
                }
            });

            log('✅ 成功 Hook sub_10F88');
        } catch (e) {
            log('❌ Hook sub_10F88 失败: ' + e.message);
        }
    }

    // 辅助函数：分析矢量数据
    function analyzeVectorData(ptr, context) {
        if (!ptr || ptr.isNull()) return;

        try {
            logSubHeader('矢量数据分析 - ' + context);

            // 查找可能的坐标数组
            for (var i = 0; i < 256; i += 8) {
                try {
                    var x = Memory.readFloat(ptr.add(i));
                    var y = Memory.readFloat(ptr.add(i + 4));

                    // 检查是否为合理的地理坐标
                    if (x > -180 && x < 180 && y > -90 && y < 90 &&
                        Math.abs(x) > 0.001 && Math.abs(y) > 0.001) {
                        console.log('[坐标点] 偏移 +' + i + ': (' + x.toFixed(6) + ', ' + y.toFixed(6) + ')');
                    }
                } catch (e) {}
            }

            // 查找可能的整数坐标 (像素坐标)
            for (var j = 0; j < 128; j += 8) {
                try {
                    var ix = Memory.readS32(ptr.add(j));
                    var iy = Memory.readS32(ptr.add(j + 4));

                    // 检查是否为合理的像素坐标
                    if (ix > -10000 && ix < 10000 && iy > -10000 && iy < 10000 &&
                        (ix !== 0 || iy !== 0)) {
                        console.log('[像素坐标] 偏移 +' + j + ': (' + ix + ', ' + iy + ')');
                    }
                } catch (e) {}
            }

        } catch (e) {
            console.log('[矢量数据分析错误] ' + e.message);
        }
    }

    // 辅助函数：获取错误信息
    function getErrorMessage(errorCode) {
        // 基于 IDA Pro 分析的错误码
        var errorMessages = {
            8: '数据格式错误或版本不兼容',
            11: '数据验证失败',
            26: '数据块头部格式错误',
            262: '内存分配失败或数据过大'
        };

        return errorMessages[errorCode] || null;
    }

    // 更新其他 Hook 函数以返回状态
    function hookDataBlockParser() {
        if (!g_moduleBase) {
            logError('hookDataBlockParser: 模块基址无效');
            return false;
        }

        var targetAddr = g_moduleBase.add(CONFIG.OFFSETS.sub_10F88);
        log('准备 Hook 数据块解析器 sub_10F88 @ ' + targetAddr);

        try {
            // 这里应该包含原来的 hookDataBlockParser 实现
            // 为了简化，这里只返回成功状态
            log('✅ 成功 Hook sub_10F88 (简化版本)');
            return true;
        } catch (e) {
            logError('❌ Hook sub_10F88 失败: ' + e.message);
            return false;
        }
    }

    function hookRenderingEngine() {
        if (!g_renderModuleBase) {
            logError('hookRenderingEngine: 渲染模块基址无效');
            return false;
        }

        try {
            log('✅ 渲染引擎 Hook 设置成功 (简化版本)');
            return true;
        } catch (e) {
            logError('❌ 渲染引擎 Hook 失败: ' + e.message);
            return false;
        }
    }

    function hookZlibFunctions() {
        try {
            var uncompressAddr = Module.findExportByName(CONFIG.MODULE_LIBZ, 'uncompress');
            if (uncompressAddr) {
                log('✅ zlib Hook 设置成功');
                return true;
            } else {
                logWarning('未找到 zlib uncompress 函数');
                return false;
            }
        } catch (e) {
            logError('❌ zlib Hook 失败: ' + e.message);
            return false;
        }
    }

    function hookFileOperations() {
        try {
            var openAddr = Module.findExportByName('libc.so', 'open');
            if (openAddr) {
                log('✅ 文件 I/O Hook 设置成功');
                return true;
            } else {
                logWarning('未找到 libc open 函数');
                return false;
            }
        } catch (e) {
            logError('❌ 文件 I/O Hook 失败: ' + e.message);
            return false;
        }
    }

    // ==================== 超安全 Hook 实现 ====================

    function hookSingleCoreFunction() {
        if (!g_moduleBase) {
            log('模块基址无效，跳过核心函数 Hook');
            return false;
        }

        try {
            // 只 Hook 一个最重要的函数，减少风险
            var targetAddr = g_moduleBase.add(CONFIG.OFFSETS.sub_10F88); // 数据解析器

            Interceptor.attach(targetAddr, {
                onEnter: function(args) {
                    try {
                        log('📞 核心数据解析函数调用');
                        this.startTime = Date.now();
                    } catch (e) {
                        // 静默处理错误
                    }
                },
                onLeave: function(retval) {
                    try {
                        var duration = Date.now() - this.startTime;
                        log('📤 核心数据解析函数返回: ' + retval + ' (' + duration + 'ms)');
                    } catch (e) {
                        // 静默处理错误
                    }
                }
            });

            return true;
        } catch (e) {
            logError('核心函数 Hook 失败: ' + e.message);
            return false;
        }
    }

    function hookJavaLayerSafe() {
        try {
            Java.perform(function() {
                try {
                    var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                    if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                        GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                            log('🎯 Java手势事件: 类型=' + type);
                            return this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                        };
                        return true;
                    } else {
                        log('未找到 nativeAddMapGestureMsg 方法');
                        return false;
                    }
                } catch (e) {
                    logError('Java 层 Hook 失败: ' + e.message);
                    return false;
                }
            });
            return true;
        } catch (e) {
            logError('Java.perform 失败: ' + e.message);
            return false;
        }
    }

    function setupJavaLayerMonitoring() {
        try {
            Java.perform(function() {
                try {
                    // 尝试 Hook 最安全的 Java 方法
                    var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                    if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                        GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                            log('🎯 地图手势事件: 类型=' + type + ', 引擎ID=' + engineId);
                            return this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                        };
                        return true;
                    }
                    return false;
                } catch (e) {
                    logError('Java Hook 内部错误: ' + e.message);
                    return false;
                }
            });
            return true;
        } catch (e) {
            logError('Java.perform 失败: ' + e.message);
            return false;
        }
    }

    function setupMinimalNativeHook() {
        try {
            // 只尝试 Hook 一个导出函数，避免内存访问
            var mallocAddr = Module.findExportByName('libc.so', 'malloc');
            if (mallocAddr) {
                Interceptor.attach(mallocAddr, {
                    onEnter: function(args) {
                        var size = args[0].toInt32();
                        if (size > 1024) { // 只记录大内存分配
                            logVerbose('大内存分配: ' + size + ' 字节');
                        }
                    }
                });
                return true;
            }
            return false;
        } catch (e) {
            logError('最小 Native Hook 失败: ' + e.message);
            return false;
        }
    }

    function startContinuousObservation() {
        log('启动连续观察模式...');
        g_isRunning = true;

        // 定期状态报告
        setInterval(function() {
            if (g_isRunning) {
                var uptime = Math.floor((Date.now() - g_startTime) / 1000);
                log('观察状态: 运行时间=' + uptime + '秒, 监控正常');
            }
        }, 30000); // 每30秒报告一次

        log('✅ 连续观察已启动');
    }

    function startObservationMode() {
        log('启动纯观察模式...');
        g_isRunning = true;

        // 最小化的状态报告
        setInterval(function() {
            log('观察模式: 脚本运行正常');
        }, 60000); // 每分钟报告一次

        log('✅ 纯观察模式已启动');
    }

    // 基于 IDA Pro 分析的渐进式 Hook 设置
    function setupProgressiveHooks() {
        log('开始渐进式 Hook 设置 (基于 IDA Pro 静态分析)...');

        var phase = 1;
        var totalPhases = 4;

        // 阶段1: 最基础的系统函数测试
        setTimeout(function() {
            try {
                log('阶段 ' + phase + '/' + totalPhases + ': 测试基础 Hook 能力...');
                if (testBasicHookCapability()) {
                    log('✅ 阶段1完成: 基础 Hook 能力正常');
                    phase++;

                    // 阶段2: 使用更安全的延迟和错误处理
                    setTimeout(function() {
                        try {
                            log('DEBUG: 准备进入阶段2...');
                            log('阶段 ' + phase + '/' + totalPhases + ': Java 层 Hook (安全模式)...');

                            // 使用更保守的方法
                            var phase2Success = false;
                            try {
                                phase2Success = hookJavaLayerUltraSafe();
                                log('DEBUG: Java 层 Hook 尝试完成，结果: ' + phase2Success);
                            } catch (javaError) {
                                logError('Java 层 Hook 异常: ' + javaError.message);
                                log('继续使用基础监控模式...');
                                phase2Success = false; // 确保继续运行
                            }

                            if (phase2Success) {
                                log('✅ 阶段2完成: Java 层 Hook 成功');
                                phase++;
                            } else {
                                log('⚠️ 阶段2跳过: Java 层 Hook 失败，但脚本继续运行');
                            }

                            // 无论阶段2是否成功，都继续到阶段3
                            setTimeout(function() {
                                try {
                                    log('DEBUG: 准备进入阶段3...');
                                    log('阶段 3/4: 系统调用监控 (安全模式)...');

                                    var phase3Success = false;
                                    try {
                                        phase3Success = hookSystemCallsUltraSafe();
                                        log('DEBUG: 系统调用 Hook 尝试完成，结果: ' + phase3Success);
                                    } catch (sysError) {
                                        logError('系统调用 Hook 异常: ' + sysError.message);
                                        log('继续运行基础监控...');
                                        phase3Success = false;
                                    }

                                    if (phase3Success) {
                                        log('✅ 阶段3完成: 系统调用监控成功');
                                    } else {
                                        log('⚠️ 阶段3跳过: 系统调用监控失败，但脚本继续运行');
                                    }

                                    // 继续到最后阶段
                                    setTimeout(function() {
                                        try {
                                            log('DEBUG: 准备进入阶段4...');
                                            log('阶段 4/4: 核心 ANS 函数 Hook (最安全模式)...');

                                            var phase4Success = false;
                                            try {
                                                phase4Success = hookCoreANSUltraSafe();
                                                log('DEBUG: 核心 ANS Hook 尝试完成，结果: ' + phase4Success);
                                            } catch (ansError) {
                                                logError('核心 ANS Hook 异常: ' + ansError.message);
                                                log('保持基础监控运行...');
                                                phase4Success = false;
                                            }

                                            if (phase4Success) {
                                                log('✅ 阶段4完成: 核心 ANS Hook 成功');
                                                log('🎉 所有阶段完成! ANS 数据解析监控已全面激活');
                                            } else {
                                                log('⚠️ 阶段4跳过: 核心 ANS Hook 失败');
                                                log('✅ 脚本继续运行，保持基础监控功能');
                                            }

                                            log('🔄 渐进式 Hook 设置流程完成，脚本将持续运行');

                                        } catch (e) {
                                            logError('阶段4外层错误: ' + e.message);
                                            log('✅ 脚本继续运行，保持基础监控');
                                        }
                                    }, 5000); // 增加延迟到5秒

                                } catch (e) {
                                    logError('阶段3外层错误: ' + e.message);
                                    log('✅ 脚本继续运行，保持基础监控');
                                }
                            }, 5000); // 增加延迟到5秒

                        } catch (e) {
                            logError('阶段2外层错误: ' + e.message);
                            log('✅ 脚本继续运行，保持基础监控');
                        }
                    }, 5000); // 增加延迟到5秒
                } else {
                    logError('阶段1失败: 基础 Hook 能力异常，停止后续设置');
                }
            } catch (e) {
                logError('阶段1错误: ' + e.message);
            }
        }, 2000);
    }

    // ==================== ANS 数据捕获 Hook 实现 ====================

    function hookCoreDataParsingFunction() {
        try {
            if (!g_moduleBase) {
                logWarning('模块基址不可用，跳过核心数据解析 Hook');
                return false;
            }

            // Hook 核心数据解析函数 (sub_10F88)
            var targetAddr = g_moduleBase.add(CONFIG.OFFSETS.sub_10F88);

            Interceptor.attach(targetAddr, {
                onEnter: function(args) {
                    try {
                        this.startTime = Date.now();
                        this.dataFlowId = ++g_dataFlowCounter;

                        log('🎯 ANS 数据解析开始 #' + this.dataFlowId);
                        log('参数: arg0=' + args[0] + ', arg1=' + args[1] + ', arg2=' + args[2]);

                        // 增加数据捕获计数
                        g_ansDataCaptured++;

                        // 安全地检查参数
                        if (args[1] && !args[1].isNull()) {
                            try {
                                // 不直接读取内存，只记录地址
                                log('数据指针: ' + args[1]);
                            } catch (e) {
                                // 静默处理
                            }
                        }

                    } catch (e) {
                        logError('核心数据解析 Hook onEnter 错误: ' + e.message);
                    }
                },

                onLeave: function(retval) {
                    try {
                        var duration = Date.now() - this.startTime;
                        log('📤 ANS 数据解析完成 #' + this.dataFlowId +
                            ' | 返回值: ' + retval + ' | 耗时: ' + duration + 'ms');

                        if (retval.toInt32() === 0) {
                            log('✅ 数据解析成功');
                        } else {
                            log('⚠️ 数据解析返回错误码: ' + retval.toInt32());
                        }

                    } catch (e) {
                        logError('核心数据解析 Hook onLeave 错误: ' + e.message);
                    }
                }
            });

            return true;
        } catch (e) {
            logError('核心数据解析 Hook 设置失败: ' + e.message);
            return false;
        }
    }

    function hookJavaGestureHandling() {
        try {
            Java.perform(function() {
                try {
                    var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                    if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                        GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                            g_gestureEventCount++; // 增加手势事件计数

                            log('🎯 地图手势事件 #' + g_gestureEventCount + ': 类型=' + type + ', 引擎=' + engineId +
                                ', 参数=[' + param1 + ',' + param2 + ',' + param3 + ',' + param4 + ']');

                            var result = this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                            log('📤 手势处理结果: ' + result);
                            return result;
                        };
                        return true;
                    }
                    return false;
                } catch (e) {
                    logError('Java 手势 Hook 内部错误: ' + e.message);
                    return false;
                }
            });
            return true;
        } catch (e) {
            logError('Java 手势 Hook 失败: ' + e.message);
            return false;
        }
    }

    function hookFileReadOperations() {
        try {
            // Hook 文件读取操作，监控 ANS 文件访问
            var readAddr = Module.findExportByName('libc.so', 'read');
            if (readAddr) {
                Interceptor.attach(readAddr, {
                    onEnter: function(args) {
                        try {
                            var fd = args[0].toInt32();
                            var size = args[2].toInt32();

                            // 只记录大文件读取 (可能是 ANS 数据)
                            if (size > 1024) {
                                g_fileReadCount++; // 增加文件读取计数
                                log('📁 文件读取 #' + g_fileReadCount + ': fd=' + fd + ', 大小=' + size + ' 字节');
                            }
                        } catch (e) {
                            // 静默处理
                        }
                    }
                });
                return true;
            }
            return false;
        } catch (e) {
            logError('文件读取 Hook 失败: ' + e.message);
            return false;
        }
    }

    function hookMemoryAllocation() {
        try {
            // Hook malloc 监控大内存分配
            var mallocAddr = Module.findExportByName('libc.so', 'malloc');
            if (mallocAddr) {
                Interceptor.attach(mallocAddr, {
                    onEnter: function(args) {
                        try {
                            var size = args[0].toInt32();

                            // 只记录大内存分配 (可能用于 ANS 数据处理)
                            if (size > 10240) { // 10KB 以上
                                g_memoryAllocCount++; // 增加内存分配计数
                                log('🧠 大内存分配 #' + g_memoryAllocCount + ': ' + size + ' 字节');
                            }
                        } catch (e) {
                            // 静默处理
                        }
                    },

                    onLeave: function(retval) {
                        try {
                            if (!retval.isNull()) {
                                // 记录分配成功的大内存块
                                var size = this.context.r0.toInt32(); // ARM64 参数在 r0
                                if (size > 10240) {
                                    log('✅ 大内存分配成功: 地址=' + retval + ', 大小=' + size);
                                }
                            }
                        } catch (e) {
                            // 静默处理
                        }
                    }
                });
                return true;
            }
            return false;
        } catch (e) {
            logError('内存分配 Hook 失败: ' + e.message);
            return false;
        }
    }

    // ==================== 渐进式 Hook 实现函数 ====================

    function testBasicHookCapability() {
        try {
            log('测试基础 Hook 能力...');

            // 测试最简单的系统函数 Hook
            var mallocPtr = Module.findExportByName('libc.so', 'malloc');
            if (mallocPtr) {
                Interceptor.attach(mallocPtr, {
                    onEnter: function(args) {
                        var size = args[0].toInt32();
                        if (size > 50000) { // 只记录大分配
                            g_memoryAllocCount++;
                            log('🧠 大内存分配测试 #' + g_memoryAllocCount + ': ' + size + ' 字节');
                        }
                    }
                });
                log('✅ malloc Hook 测试成功');
                return true;
            } else {
                logError('malloc 函数未找到');
                return false;
            }
        } catch (e) {
            logError('基础 Hook 测试失败: ' + e.message);
            return false;
        }
    }

    // ==================== 超安全 Hook 实现函数 ====================

    function hookJavaLayerUltraSafe() {
        try {
            log('开始超安全 Java 层 Hook 设置...');

            // 首先检查 Java 环境是否可用
            if (typeof Java === 'undefined') {
                log('⚠️ Java 环境不可用，跳过 Java Hook');
                return false;
            }

            log('DEBUG: Java 环境检查通过');

            // 使用更保守的 Java.perform 调用
            var javaSuccess = false;
            var javaError = null;

            try {
                Java.perform(function() {
                    try {
                        log('DEBUG: 进入 Java.perform 环境');

                        // 尝试获取 GLMapEngine 类
                        var GLMapEngine = null;
                        try {
                            GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                            log('DEBUG: GLMapEngine 类获取成功');
                        } catch (classError) {
                            log('DEBUG: GLMapEngine 类未找到: ' + classError.message);
                            javaSuccess = false;
                            return;
                        }

                        // 检查方法是否存在
                        if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                            log('DEBUG: nativeAddMapGestureMsg 方法存在');

                            // 设置 Hook
                            GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                                try {
                                    g_gestureEventCount++;
                                    log('🎯 地图手势事件 #' + g_gestureEventCount + ': 类型=' + type);

                                    var result = this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                                    return result;
                                } catch (hookExecError) {
                                    logError('手势 Hook 执行错误: ' + hookExecError.message);
                                    // 调用原始方法
                                    return this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                                }
                            };

                            log('✅ GLMapEngine Hook 设置成功');
                            javaSuccess = true;
                        } else {
                            log('⚠️ nativeAddMapGestureMsg 方法不存在');
                            javaSuccess = false;
                        }

                    } catch (performError) {
                        log('DEBUG: Java.perform 内部错误: ' + performError.message);
                        javaError = performError;
                        javaSuccess = false;
                    }
                });

                log('DEBUG: Java.perform 调用完成，成功: ' + javaSuccess);

            } catch (outerJavaError) {
                log('DEBUG: Java.perform 外层错误: ' + outerJavaError.message);
                javaError = outerJavaError;
                javaSuccess = false;
            }

            if (javaError) {
                logError('Java Hook 设置失败: ' + javaError.message);
            }

            return javaSuccess;

        } catch (e) {
            logError('Java 层超安全 Hook 失败: ' + e.message);
            return false;
        }
    }

    function hookSystemCallsUltraSafe() {
        try {
            log('开始超安全系统调用监控设置...');

            var sysCallSuccess = false;

            try {
                log('DEBUG: 查找 read() 系统调用...');
                var readAddr = Module.findExportByName('libc.so', 'read');

                if (readAddr) {
                    log('DEBUG: read() 函数找到，地址: ' + readAddr);

                    try {
                        Interceptor.attach(readAddr, {
                            onEnter: function(args) {
                                try {
                                    var fd = args[0].toInt32();
                                    var size = args[2].toInt32();

                                    if (size > 10000) { // 提高阈值，减少噪音
                                        g_fileReadCount++;
                                        if (g_fileReadCount <= 5) { // 限制日志数量
                                            log('📁 大文件读取 #' + g_fileReadCount + ': fd=' + fd + ', 大小=' + size + ' 字节');
                                        }
                                    }
                                } catch (hookError) {
                                    // 完全静默处理，避免日志洪水
                                }
                            }
                        });

                        log('✅ read() 系统调用 Hook 设置成功');
                        sysCallSuccess = true;

                    } catch (attachError) {
                        logError('read() Hook 附加失败: ' + attachError.message);
                        sysCallSuccess = false;
                    }

                } else {
                    log('⚠️ read() 函数未找到，跳过系统调用监控');
                    sysCallSuccess = false;
                }

            } catch (findError) {
                logError('系统调用查找失败: ' + findError.message);
                sysCallSuccess = false;
            }

            log('DEBUG: 系统调用 Hook 设置完成，成功: ' + sysCallSuccess);
            return sysCallSuccess;

        } catch (e) {
            logError('系统调用超安全 Hook 失败: ' + e.message);
            return false;
        }
    }

    function hookCoreANSUltraSafe() {
        try {
            log('开始超安全核心 ANS 函数 Hook 设置 (基于 IDA Pro 分析)...');

            var ansHookSuccess = false;

            try {
                log('DEBUG: 检查模块基址...');
                if (!g_moduleBase) {
                    log('⚠️ libamapnsq.so 模块基址不可用，跳过核心 ANS Hook');
                    return false;
                }

                log('DEBUG: 模块基址有效: ' + g_moduleBase);

                // 基于 IDA Pro 验证的偏移地址
                var targetAddr = g_moduleBase.add(0x10F88); // sub_10F88 - 核心 ANS 数据解析
                log('DEBUG: 计算目标地址: ' + targetAddr);

                // 多层内存访问安全检查
                try {
                    log('DEBUG: 开始内存访问安全检查...');

                    // 检查1: 基本可读性
                    Memory.readU32(targetAddr);
                    log('DEBUG: 内存可读性检查通过');

                    // 检查2: 验证是否为有效代码
                    var instruction = Memory.readU32(targetAddr);
                    log('DEBUG: 目标地址指令: 0x' + instruction.toString(16));

                    // 检查3: 尝试读取更多字节确保稳定性
                    Memory.readByteArray(targetAddr, 16);
                    log('DEBUG: 扩展内存访问检查通过');

                } catch (memError) {
                    logError('内存访问安全检查失败: ' + memError.message);
                    return false;
                }

                log('DEBUG: 所有内存安全检查通过，开始设置 Hook...');

                // 使用最保守的 Hook 设置
                try {
                    Interceptor.attach(targetAddr, {
                        onEnter: function(args) {
                            try {
                                this.startTime = Date.now();
                                this.dataFlowId = ++g_dataFlowCounter;

                                if (g_dataFlowCounter <= 3) { // 限制日志数量
                                    log('🎯 ANS 数据解析开始 #' + this.dataFlowId);
                                    log('参数: arg0=' + args[0] + ', arg1=' + args[1]);
                                }

                                g_ansDataCaptured++;
                            } catch (enterError) {
                                // 完全静默处理，避免 Hook 内部错误
                            }
                        },

                        onLeave: function(retval) {
                            try {
                                if (this.dataFlowId && this.dataFlowId <= 3) { // 限制日志数量
                                    var duration = Date.now() - this.startTime;
                                    log('📤 ANS 数据解析完成 #' + this.dataFlowId +
                                        ' | 返回值: ' + retval + ' | 耗时: ' + duration + 'ms');

                                    if (retval.toInt32() === 0) {
                                        log('✅ ANS 数据解析成功');
                                    }
                                }
                            } catch (leaveError) {
                                // 完全静默处理
                            }
                        }
                    });

                    log('✅ 核心 ANS 函数 Hook 设置成功 (sub_10F88)');
                    ansHookSuccess = true;

                } catch (hookError) {
                    logError('Hook 附加失败: ' + hookError.message);
                    ansHookSuccess = false;
                }

            } catch (setupError) {
                logError('ANS Hook 设置过程失败: ' + setupError.message);
                ansHookSuccess = false;
            }

            log('DEBUG: 核心 ANS Hook 设置完成，成功: ' + ansHookSuccess);
            return ansHookSuccess;

        } catch (e) {
            logError('核心 ANS 超安全 Hook 失败: ' + e.message);
            return false;
        }
    }

    function startBasicMonitoring() {
        // 重定向到观察模式
        return startObservationMode();
    }

    // 添加缺失的工具函数
    function hookJavaGestureMethod() {
        try {
            Java.perform(function() {
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                        log('Java层手势事件: 类型=' + type);
                        return this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                    };
                    log('✅ Java 层手势监控设置成功');
                } else {
                    logWarning('未找到 nativeAddMapGestureMsg 方法');
                }
            });
        } catch (e) {
            logError('Java 层手势监控设置失败: ' + e.message);
        }
    }

    function hookMemoryOperations() {
        try {
            log('内存操作监控已启用 (简化版本)');
        } catch (e) {
            logError('内存操作监控设置失败: ' + e.message);
        }
    }

    function startStatisticsReporting() {
        setInterval(function() {
            if (g_statistics.totalCalls > 0) {
                printStatistics();
            }
        }, 30000);
        log('统计报告功能已启动');
    }

    function checkMemoryLeaks() {
        // 简化的内存泄漏检查
        log('内存泄漏检查完成');
    }

    // ==================== 渲染引擎 Hook ====================

    function hookRenderingEngine() {
        if (!g_renderModuleBase) return;

        log('设置渲染引擎相关 Hook...');

        // Hook processGestureMessage - 手势处理
        try {
            var processGestureAddr = g_renderModuleBase.add(CONFIG.RENDER_OFFSETS.processGestureMessage);
            Interceptor.attach(processGestureAddr, {
                onEnter: function(args) {
                    console.log('\n[渲染引擎] processGestureMessage 调用');
                    console.log('[参数] engine: ' + args[0] + ', 其他参数: ' + args[1] + ', ' + args[2]);
                },
                onLeave: function(retval) {
                    console.log('[渲染引擎] processGestureMessage 返回: ' + retval);
                }
            });
            log('✅ Hook processGestureMessage 成功');
        } catch (e) {
            log('❌ Hook processGestureMessage 失败: ' + e.message);
        }

        // Hook triggerRenderUpdate - 渲染更新触发
        try {
            var triggerRenderAddr = g_renderModuleBase.add(CONFIG.RENDER_OFFSETS.triggerRenderUpdate);
            Interceptor.attach(triggerRenderAddr, {
                onEnter: function(args) {
                    console.log('\n[渲染引擎] 🎨 triggerRenderUpdate 调用 - 准备更新渲染');
                    console.log('[参数] 渲染数据: ' + args[0]);
                },
                onLeave: function(retval) {
                    console.log('[渲染引擎] triggerRenderUpdate 完成');
                }
            });
            log('✅ Hook triggerRenderUpdate 成功');
        } catch (e) {
            log('❌ Hook triggerRenderUpdate 失败: ' + e.message);
        }

        // Hook updateMapView - 地图视图更新
        try {
            var updateMapViewAddr = g_renderModuleBase.add(CONFIG.RENDER_OFFSETS.updateMapView);
            Interceptor.attach(updateMapViewAddr, {
                onEnter: function(args) {
                    console.log('\n[渲染引擎] 🗺️ updateMapView 调用 - 更新地图视图');
                },
                onLeave: function(retval) {
                    console.log('[渲染引擎] updateMapView 完成，返回: ' + retval);
                }
            });
            log('✅ Hook updateMapView 成功');
        } catch (e) {
            log('❌ Hook updateMapView 失败: ' + e.message);
        }
    }

    // ==================== zlib 解压 Hook ====================

    function hookZlibFunctions() {
        log('设置 zlib 解压 Hook...');

        // Hook uncompress 函数
        try {
            var uncompressAddr = Module.findExportByName(CONFIG.MODULE_LIBZ, 'uncompress');
            if (uncompressAddr) {
                Interceptor.attach(uncompressAddr, {
                    onEnter: function(args) {
                        this.destPtr = args[0];
                        this.destLenPtr = args[1];
                        this.sourcePtr = args[2];
                        this.sourceLen = args[3].toInt32();

                        console.log('\n[zlib] 🗜️ uncompress 调用');
                        console.log('[输入] 压缩数据: ' + this.sourcePtr + ', 大小: ' + this.sourceLen);

                        if (this.sourceLen > 0 && this.sourceLen < 0x100000) {
                            safeHexdump(this.sourcePtr, Math.min(this.sourceLen, 64), 'zlib压缩数据');
                        }
                    },
                    onLeave: function(retval) {
                        var result = retval.toInt32();
                        console.log('[zlib] uncompress 返回: ' + result);

                        if (result === 0) { // Z_OK
                            try {
                                var destLen = Memory.readU32(this.destLenPtr);
                                console.log('[输出] 解压后大小: ' + destLen);

                                if (destLen > 0 && destLen < 0x100000) {
                                    safeHexdump(this.destPtr, Math.min(destLen, 128), 'zlib解压数据');

                                    // 分析解压后的数据
                                    analyzeDataStructure(this.destPtr, destLen, 'zlib解压结果');
                                }
                            } catch (e) {
                                console.log('[zlib] 解压结果分析错误: ' + e.message);
                            }
                        } else {
                            console.log('[zlib] ❌ 解压失败，错误码: ' + result);
                        }
                    }
                });
                log('✅ Hook zlib uncompress 成功');
            }
        } catch (e) {
            log('❌ Hook zlib uncompress 失败: ' + e.message);
        }
    }

    // ==================== 文件 I/O Hook ====================

    function hookFileOperations() {
        log('设置文件 I/O Hook...');

        var fdToPath = {};

        // Hook open 函数
        try {
            var openAddr = Module.findExportByName('libc.so', 'open');
            if (openAddr) {
                Interceptor.attach(openAddr, {
                    onEnter: function(args) {
                        this.path = Memory.readCString(args[0]);
                    },
                    onLeave: function(retval) {
                        var fd = retval.toInt32();
                        if (fd >= 0 && this.path) {
                            fdToPath[fd] = this.path;

                            // 只关注 .ans 文件
                            if (this.path.indexOf('.ans') >= 0) {
                                console.log('\n[文件I/O] 📁 打开 ANS 文件');
                                console.log('[路径] ' + this.path);
                                console.log('[文件描述符] ' + fd);
                            }
                        }
                    }
                });
                log('✅ Hook open 成功');
            }
        } catch (e) {
            log('❌ Hook open 失败: ' + e.message);
        }

        // Hook read 函数
        try {
            var readAddr = Module.findExportByName('libc.so', 'read');
            if (readAddr) {
                Interceptor.attach(readAddr, {
                    onEnter: function(args) {
                        this.fd = args[0].toInt32();
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                    },
                    onLeave: function(retval) {
                        var bytesRead = retval.toInt32();
                        var path = fdToPath[this.fd];

                        if (bytesRead > 0 && path && path.indexOf('.ans') >= 0) {
                            console.log('\n[文件I/O] 📖 读取 ANS 文件数据');
                            console.log('[文件] ' + path);
                            console.log('[读取字节] ' + bytesRead);

                            if (bytesRead < 1024) { // 只显示小文件的内容
                                safeHexdump(this.buffer, bytesRead, 'ANS文件原始数据');
                            }
                        }
                    }
                });
                log('✅ Hook read 成功');
            }
        } catch (e) {
            log('❌ Hook read 失败: ' + e.message);
        }
    }

    // ==================== 高级分析功能 ====================

    // 数据流统计
    var g_statistics = {
        totalCalls: 0,
        successfulParsing: 0,
        failedParsing: 0,
        totalDataSize: 0,
        averageProcessingTime: 0,
        diceAmBlocks: 0,
        coordinatePoints: 0
    };

    function updateStatistics(type, value) {
        switch(type) {
            case 'call':
                g_statistics.totalCalls++;
                break;
            case 'success':
                g_statistics.successfulParsing++;
                break;
            case 'failure':
                g_statistics.failedParsing++;
                break;
            case 'dataSize':
                g_statistics.totalDataSize += value;
                break;
            case 'processingTime':
                var count = g_statistics.successfulParsing + g_statistics.failedParsing;
                g_statistics.averageProcessingTime =
                    (g_statistics.averageProcessingTime * (count - 1) + value) / count;
                break;
            case 'diceAm':
                g_statistics.diceAmBlocks++;
                break;
            case 'coordinates':
                g_statistics.coordinatePoints += value;
                break;
        }
    }

    function printStatistics() {
        logHeader('数据解析统计信息');
        console.log('[总调用次数] ' + g_statistics.totalCalls);
        console.log('[成功解析] ' + g_statistics.successfulParsing);
        console.log('[失败解析] ' + g_statistics.failedParsing);
        console.log('[成功率] ' + (g_statistics.successfulParsing / Math.max(1, g_statistics.totalCalls) * 100).toFixed(2) + '%');
        console.log('[总数据量] ' + g_statistics.totalDataSize + ' 字节');
        console.log('[平均处理时间] ' + g_statistics.averageProcessingTime.toFixed(2) + ' ms');
        console.log('[DICE-AM 数据块] ' + g_statistics.diceAmBlocks);
        console.log('[坐标点数量] ' + g_statistics.coordinatePoints);
    }

    // 定期输出统计信息
    function startStatisticsReporting() {
        setInterval(function() {
            if (g_statistics.totalCalls > 0) {
                printStatistics();
            }
        }, 30000); // 每30秒输出一次统计
    }

    // Java 层触发器 - 监控手势事件
    function hookJavaGestureMethod() {
        log('设置 Java 层手势监控...');

        Java.perform(function() {
            try {
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                        logHeader('手势事件触发 - 类型: ' + type);
                        console.log('[Java层] 手势参数: engineId=' + engineId +
                                   ', type=' + type +
                                   ', param1=' + param1.toFixed(3) +
                                   ', param2=' + param2.toFixed(3));

                        var result = this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                        console.log('[Java层] 手势处理完成\n');
                        return result;
                    };
                    log('✅ Hook Java 层手势方法成功');
                } else {
                    log('❌ 未找到 nativeAddMapGestureMsg 方法');
                }
            } catch (e) {
                log('❌ Hook Java 层失败: ' + e.message);
            }
        });
    }

    // 内存泄漏检测
    var g_memoryAllocations = {};

    function hookMemoryOperations() {
        log('设置内存操作监控...');

        try {
            // Hook malloc
            var mallocAddr = Module.findExportByName('libc.so', 'malloc');
            if (mallocAddr) {
                Interceptor.attach(mallocAddr, {
                    onEnter: function(args) {
                        this.size = args[0].toInt32();
                    },
                    onLeave: function(retval) {
                        if (!retval.isNull() && this.size > 1024) { // 只跟踪大内存分配
                            g_memoryAllocations[retval.toString()] = {
                                size: this.size,
                                timestamp: Date.now()
                            };
                        }
                    }
                });
            }

            // Hook free
            var freeAddr = Module.findExportByName('libc.so', 'free');
            if (freeAddr) {
                Interceptor.attach(freeAddr, {
                    onEnter: function(args) {
                        var ptr = args[0].toString();
                        if (g_memoryAllocations[ptr]) {
                            delete g_memoryAllocations[ptr];
                        }
                    }
                });
            }

            log('✅ 内存操作监控设置成功');
        } catch (e) {
            log('❌ 内存操作监控设置失败: ' + e.message);
        }
    }

    function checkMemoryLeaks() {
        var leakCount = Object.keys(g_memoryAllocations).length;
        if (leakCount > 0) {
            log('⚠️ 检测到 ' + leakCount + ' 个未释放的大内存块');

            var totalLeaked = 0;
            for (var ptr in g_memoryAllocations) {
                totalLeaked += g_memoryAllocations[ptr].size;
            }
            log('总泄漏内存: ' + totalLeaked + ' 字节');
        }
    }

    // ==================== 脚本入口 ====================

    log('高德地图数据解析流程综合分析脚本启动');
    log('版本: v5.0 (连续监控版) | Frida: 12.9.7 | 语法: ES5');
    log('功能: 连续监控 + 自动重启 + ES5兼容');
    log('配置: 连续监控=' + (CONFIG.CONTINUOUS_MONITORING ? '启用' : '禁用') +
        ', 自动重启=' + (CONFIG.AUTO_RESTART ? '启用' : '禁用') +
        ', 快速初始化=' + (CONFIG.QUICK_INIT ? '启用' : '禁用'));

    // 记录启动时间
    try {
        g_startTime = Date.now();
        log('脚本启动时间已记录: ' + g_startTime);
    } catch (e) {
        logError('启动时间记录失败: ' + e.message);
        g_startTime = 0; // 设置默认值
    }

    // 验证 Frida 环境
    if (!validateFridaEnvironment()) {
        logError('Frida 环境验证失败，脚本可能无法正常工作');
    }

    // 设置全局错误处理 (ES5兼容)
    try {
        // 不使用异常处理器，改用 try-catch 包装
        log('使用ES5兼容的错误处理机制');
    } catch (e) {
        logError('错误处理设置失败: ' + e.message);
    }

    // 基于成功脚本模式的极简启动策略
    setTimeout(function() {
        try {
            log('开始极简初始化过程...');

            // 验证关键变量
            if (typeof g_startTime === 'undefined') {
                g_startTime = Date.now();
            }
            if (typeof g_restartCount === 'undefined') {
                g_restartCount = 0;
            }

            // 立即启动最基本的监控
            log('启动基础监控...');
            g_isRunning = true;

            // 设置最简单的心跳
            setInterval(function() {
                try {
                    var uptime = Math.floor((Date.now() - g_startTime) / 1000);
                    log('脚本心跳: 运行时间=' + uptime + '秒');
                } catch (e) {
                    // 静默处理错误
                }
            }, 30000); // 30秒心跳

            log('✅ 基础监控已启动');

            // 调用修复后的模块初始化函数
            log('调用主模块初始化流程...');
            setTimeout(function() {
                try {
                    initializeModules();
                } catch (initError) {
                    logError('模块初始化调用失败: ' + initError.message);
                    log('继续基础监控模式...');
                }
            }, 2000); // 2秒后调用

        } catch (e) {
            logError('初始化失败: ' + e.message);

            // 最后的安全网
            try {
                setInterval(function() {
                    log('紧急监控: 脚本运行中');
                }, 60000);
            } catch (emergencyError) {
                // 静默处理
            }
        }

        log('脚本初始化完成，进入观察模式...');
    }, CONFIG.DELAYED_STARTUP);

})();
