(function() {
    'use strict';
    
    // 配置
    var config = {
        libName: "libamapr.so",
        offsets: {
            // 已有的手势处理函数
            processGestureMessage: 0x6fb530,
            processGesture_sub: 0x713690,
            
            // 新增渲染相关函数
            triggerRenderUpdate: 0x71b230,     // 负责触发渲染更新
            updateMapView: 0x72a480,           // 更新地图视图
            calculateVisibleTiles: 0x736920,   // 计算可见瓦片
            loadMapTiles: 0x748c10,            // 加载地图瓦片
            prepareRender: 0x75d340            // 准备渲染
        },
        verboseLogging: true,
        stackTraceEnabled: true,
        memoryAnalysisEnabled: true
    };
    
    var baseAddr = null;
    var addresses = {};
    var lastGesturePtr = null;    // 记录最后处理的手势指针
    var renderCallSequence = [];  // 记录渲染调用序列
    
    function log(message) {
        console.log("[渲染流程分析] " + message);
    }
    
    function logError(message) {
        console.log("[渲染流程分析-错误] " + message);
    }
    
    // 通用工具函数
    function safeReadInt(address) {
        try {
            if (!address || address.isNull()) return 0;
            return Memory.readInt(address);
        } catch (e) {
            return 0;
        }
    }
    
    function safeReadFloat(address) {
        try {
            if (!address || address.isNull()) return 0.0;
            return Memory.readFloat(address);
        } catch (e) {
            return 0.0;
        }
    }
    
    function safeReadPointer(address) {
        try {
            if (!address || address.isNull()) return null;
            return Memory.readPointer(address);
        } catch (e) {
            return null;
        }
    }
    
    // 获取调用堆栈
    function getStackTrace() {
        if (!config.stackTraceEnabled) return [];
        
        try {
            return Thread.backtrace(this.context, Backtracer.ACCURATE)
                .map(DebugSymbol.fromAddress)
                .map(function(sym) {
                    return sym.moduleName + "+" + sym.address.sub(sym.moduleBase).toString(16);
                });
        } catch (e) {
            return ["获取堆栈失败: " + e.message];
        }
    }
    
    // 分析地图视图状态
    function analyzeMapViewState(viewPtr) {
        if (!viewPtr || viewPtr.isNull()) return {};
        
        var result = {
            viewPointer: viewPtr.toString()
        };
        
        try {
            // 解析视图坐标
            result.centerX = safeReadFloat(viewPtr.add(8));
            result.centerY = safeReadFloat(viewPtr.add(12));
            result.zoomLevel = safeReadFloat(viewPtr.add(16));
            
            // 解析视口尺寸
            result.viewportWidth = safeReadInt(viewPtr.add(24));
            result.viewportHeight = safeReadInt(viewPtr.add(28));
            
            // 解析视图标志位
            var flags = safeReadInt(viewPtr.add(32));
            result.isDirty = (flags & 0x1) !== 0;
            result.isAnimating = (flags & 0x2) !== 0;
            result.needsUpdate = (flags & 0x4) !== 0;
        } catch (e) {
            result.error = "解析视图状态失败: " + e.message;
        }
        
        return result;
    }
    
    // 分析瓦片信息
    function analyzeTileData(tilePtr) {
        if (!tilePtr || tilePtr.isNull()) return {};
        
        var result = {
            tilePointer: tilePtr.toString()
        };
        
        try {
            // 解析瓦片坐标
            result.x = safeReadInt(tilePtr.add(4));
            result.y = safeReadInt(tilePtr.add(8));
            result.zoom = safeReadInt(tilePtr.add(12));
            
            // 解析瓦片状态
            var status = safeReadInt(tilePtr.add(16));
            result.isLoaded = (status & 0x1) !== 0;
            result.isLoading = (status & 0x2) !== 0;
            result.hasError = (status & 0x4) !== 0;
            
            // 解析瓦片数据指针
            var dataPtr = safeReadPointer(tilePtr.add(24));
            result.hasData = !dataPtr.isNull();
            
            // 解析瓦片优先级
            result.priority = safeReadInt(tilePtr.add(32));
        } catch (e) {
            result.error = "解析瓦片数据失败: " + e.message;
        }
        
        return result;
    }
    
    // 安全地钩住函数
    function hookFunctionSafely(functionName, address, callbacks) {
        if (!address) {
            logError(functionName + " 地址未找到");
            return false;
        }
        
        log("尝试钩住 " + functionName + " @ " + address);
        
        try {
            Interceptor.attach(address, callbacks);
            log("成功钩住 " + functionName);
            return true;
        } catch (e) {
            logError("钩住 " + functionName + " 失败: " + e.message);
            return false;
        }
    }
    
    // 初始化地址
    function initAddresses() {
        var modules = Process.enumerateModules();
        var found = false;
        
        for (var i = 0; i < modules.length; i++) {
            if (modules[i].name === config.libName) {
                baseAddr = modules[i].base;
                log("找到 " + config.libName + " 模块，基地址: " + baseAddr);
                found = true;
                break;
            }
        }
        
        if (!found) {
            logError("未找到模块: " + config.libName);
            return false;
        }
        
        // 计算函数地址
        for (var name in config.offsets) {
            addresses[name] = baseAddr.add(config.offsets[name]);
            log("函数 " + name + " @ " + addresses[name]);
        }
        
        return true;
    }
    
    // 钩住触发渲染更新函数
    function hookTriggerRenderUpdate() {
        return hookFunctionSafely("triggerRenderUpdate", addresses.triggerRenderUpdate, {
            onEnter: function(args) {
                this.startTime = new Date().getTime();
                this.enginePtr = args[0];
                this.flags = args[1];
                
                log("\n==== triggerRenderUpdate 被调用 ====");
                log("引擎指针: " + (this.enginePtr ? this.enginePtr : "null"));
                log("更新标志: 0x" + (this.flags ? this.flags.toInt32().toString(16) : "0"));
                
                if (config.stackTraceEnabled) {
                    var stacktrace = getStackTrace.call(this);
                    log("调用堆栈:");
                    for (var i = 0; i < stacktrace.length && i < 5; i++) {
                        log("  " + i + ": " + stacktrace[i]);
                    }
                }
                
                // 分析渲染标志
                if (this.flags) {
                    var flags = this.flags.toInt32();
                    log("渲染标志分析:");
                    log("  - 需要刷新: " + ((flags & 0x1) !== 0));
                    log("  - 有动画: " + ((flags & 0x2) !== 0));
                    log("  - 强制重绘: " + ((flags & 0x4) !== 0));
                    log("  - 高优先级: " + ((flags & 0x8) !== 0));
                }
            },
            onLeave: function(retval) {
                var duration = new Date().getTime() - this.startTime;
                log("触发渲染耗时: " + duration + " ms");
                log("==== triggerRenderUpdate 调用完成 ====\n");
                
                // 记录到调用序列
                renderCallSequence.push({
                    function: "triggerRenderUpdate",
                    enginePtr: this.enginePtr ? this.enginePtr.toString() : "null",
                    flags: this.flags ? this.flags.toInt32() : 0,
                    timestamp: new Date().getTime()
                });
            }
        });
    }
    
    // 钩住更新地图视图函数
    function hookUpdateMapView() {
        return hookFunctionSafely("updateMapView", addresses.updateMapView, {
            onEnter: function(args) {
                this.startTime = new Date().getTime();
                this.enginePtr = args[0];
                this.viewPtr = args[1];
                
                log("\n==== updateMapView 被调用 ====");
                log("引擎指针: " + (this.enginePtr ? this.enginePtr : "null"));
                log("视图指针: " + (this.viewPtr ? this.viewPtr : "null"));
                
                // 分析视图状态
                if (this.viewPtr && !this.viewPtr.isNull()) {
                    var viewState = analyzeMapViewState(this.viewPtr);
                    log("视图状态分析:");
                    log("  - 中心坐标: (" + viewState.centerX + ", " + viewState.centerY + ")");
                    log("  - 缩放级别: " + viewState.zoomLevel);
                    log("  - 视口尺寸: " + viewState.viewportWidth + "x" + viewState.viewportHeight);
                    log("  - 需要更新: " + viewState.needsUpdate);
                    log("  - 正在动画: " + viewState.isAnimating);
                }
                
                if (config.stackTraceEnabled) {
                    var stacktrace = getStackTrace.call(this);
                    log("调用堆栈:");
                    for (var i = 0; i < stacktrace.length && i < 5; i++) {
                        log("  " + i + ": " + stacktrace[i]);
                    }
                }
            },
            onLeave: function(retval) {
                var duration = new Date().getTime() - this.startTime;
                log("更新地图视图耗时: " + duration + " ms");
                log("返回值: " + retval);
                log("==== updateMapView 调用完成 ====\n");
                
                // 记录到调用序列
                renderCallSequence.push({
                    function: "updateMapView",
                    enginePtr: this.enginePtr ? this.enginePtr.toString() : "null",
                    viewPtr: this.viewPtr ? this.viewPtr.toString() : "null",
                    timestamp: new Date().getTime()
                });
            }
        });
    }
    
    // 钩住计算可见瓦片函数
    function hookCalculateVisibleTiles() {
        return hookFunctionSafely("calculateVisibleTiles", addresses.calculateVisibleTiles, {
            onEnter: function(args) {
                this.startTime = new Date().getTime();
                this.enginePtr = args[0];
                this.viewPtr = args[1];
                this.tileArrayPtr = args[2];
                
                log("\n==== calculateVisibleTiles 被调用 ====");
                log("引擎指针: " + (this.enginePtr ? this.enginePtr : "null"));
                log("视图指针: " + (this.viewPtr ? this.viewPtr : "null"));
                log("瓦片数组指针: " + (this.tileArrayPtr ? this.tileArrayPtr : "null"));
                
                if (config.stackTraceEnabled) {
                    var stacktrace = getStackTrace.call(this);
                    log("调用堆栈:");
                    for (var i = 0; i < stacktrace.length && i < 5; i++) {
                        log("  " + i + ": " + stacktrace[i]);
                    }
                }
            },
            onLeave: function(retval) {
                var duration = new Date().getTime() - this.startTime;
                var tileCount = retval.toInt32();
                log("计算到 " + tileCount + " 个可见瓦片");
                log("计算可见瓦片耗时: " + duration + " ms");
                log("==== calculateVisibleTiles 调用完成 ====\n");
                
                // 记录到调用序列
                renderCallSequence.push({
                    function: "calculateVisibleTiles",
                    tileCount: tileCount,
                    timestamp: new Date().getTime()
                });
            }
        });
    }
    
    // 钩住加载地图瓦片函数
    function hookLoadMapTiles() {
        return hookFunctionSafely("loadMapTiles", addresses.loadMapTiles, {
            onEnter: function(args) {
                this.startTime = new Date().getTime();
                this.enginePtr = args[0];
                this.tilesPtr = args[1];
                this.tileCount = args[2].toInt32();
                
                log("\n==== loadMapTiles 被调用 ====");
                log("引擎指针: " + (this.enginePtr ? this.enginePtr : "null"));
                log("瓦片数组指针: " + (this.tilesPtr ? this.tilesPtr : "null"));
                log("瓦片数量: " + this.tileCount);
                
                // 分析瓦片信息
                if (this.tilesPtr && !this.tilesPtr.isNull() && this.tileCount > 0) {
                    log("瓦片加载请求分析:");
                    try {
                        for (var i = 0; i < Math.min(this.tileCount, 5); i++) {
                            var tilePtr = safeReadPointer(this.tilesPtr.add(i * Process.pointerSize));
                            if (tilePtr && !tilePtr.isNull()) {
                                var tileInfo = analyzeTileData(tilePtr);
                                log("  - 瓦片 " + i + ": 坐标(" + tileInfo.x + "," + tileInfo.y + 
                                    ") 级别" + tileInfo.zoom + " 优先级" + tileInfo.priority);
                            }
                        }
                        
                        if (this.tileCount > 5) {
                            log("  - ... (还有 " + (this.tileCount - 5) + " 个瓦片)");
                        }
                    } catch (e) {
                        log("  解析瓦片数组失败: " + e.message);
                    }
                }
                
                if (config.stackTraceEnabled) {
                    var stacktrace = getStackTrace.call(this);
                    log("调用堆栈:");
                    for (var i = 0; i < stacktrace.length && i < 5; i++) {
                        log("  " + i + ": " + stacktrace[i]);
                    }
                }
            },
            onLeave: function(retval) {
                var duration = new Date().getTime() - this.startTime;
                log("加载瓦片耗时: " + duration + " ms");
                log("成功加载数量: " + retval.toInt32());
                log("==== loadMapTiles 调用完成 ====\n");
                
                // 记录到调用序列
                renderCallSequence.push({
                    function: "loadMapTiles",
                    tileCount: this.tileCount,
                    loadedCount: retval.toInt32(),
                    timestamp: new Date().getTime()
                });
            }
        });
    }
    
    // 钩住准备渲染函数
    function hookPrepareRender() {
        return hookFunctionSafely("prepareRender", addresses.prepareRender, {
            onEnter: function(args) {
                this.startTime = new Date().getTime();
                this.enginePtr = args[0];
                this.renderCtxPtr = args[1];
                
                log("\n==== prepareRender 被调用 ====");
                log("引擎指针: " + (this.enginePtr ? this.enginePtr : "null"));
                log("渲染上下文指针: " + (this.renderCtxPtr ? this.renderCtxPtr : "null"));
                
                if (config.stackTraceEnabled) {
                    var stacktrace = getStackTrace.call(this);
                    log("调用堆栈:");
                    for (var i = 0; i < stacktrace.length && i < 5; i++) {
                        log("  " + i + ": " + stacktrace[i]);
                    }
                }
                
                // 分析渲染上下文
                if (this.renderCtxPtr && !this.renderCtxPtr.isNull()) {
                    try {
                        var frameId = safeReadInt(this.renderCtxPtr.add(4));
                        var renderFlags = safeReadInt(this.renderCtxPtr.add(8));
                        
                        log("渲染上下文分析:");
                        log("  - 帧ID: " + frameId);
                        log("  - 渲染标志: 0x" + renderFlags.toString(16));
                        log("  - 二维渲染: " + ((renderFlags & 0x1) !== 0));
                        log("  - 三维渲染: " + ((renderFlags & 0x2) !== 0));
                    } catch (e) {
                        log("  解析渲染上下文失败: " + e.message);
                    }
                }
            },
            onLeave: function(retval) {
                var duration = new Date().getTime() - this.startTime;
                log("准备渲染耗时: " + duration + " ms");
                log("准备结果: " + retval.toInt32() + " (0表示成功)");
                log("==== prepareRender 调用完成 ====\n");
                
                // 记录到调用序列
                renderCallSequence.push({
                    function: "prepareRender",
                    enginePtr: this.enginePtr ? this.enginePtr.toString() : "null",
                    result: retval.toInt32(),
                    timestamp: new Date().getTime()
                });
                
                // 如果积累了足够的调用序列，分析渲染流程
                if (renderCallSequence.length >= 10) {
                    analyzeRenderFlow();
                }
            }
        });
    }
    
    // 分析渲染流程
    function analyzeRenderFlow() {
        if (renderCallSequence.length === 0) return;
        
        log("\n========== 渲染流程分析 ==========");
        log("分析最近 " + renderCallSequence.length + " 个渲染相关调用");
        
        // 计算各函数调用频率
        var callCounts = {};
        for (var i = 0; i < renderCallSequence.length; i++) {
            var func = renderCallSequence[i].function;
            callCounts[func] = (callCounts[func] || 0) + 1;
        }
        
        log("调用频率:");
        for (var func in callCounts) {
            log("  - " + func + ": " + callCounts[func] + " 次");
        }
        
        // 分析调用顺序模式
        log("典型调用序列:");
        var sequencePattern = [];
        var lastFunc = null;
        
        for (var i = 0; i < Math.min(renderCallSequence.length, 8); i++) {
            var call = renderCallSequence[i];
            if (call.function !== lastFunc) {
                sequencePattern.push(call.function);
                lastFunc = call.function;
            }
        }
        
        log("  " + sequencePattern.join(" -> "));
        
        // 分析性能
        var funcTimes = {};
        for (var i = 0; i < renderCallSequence.length; i++) {
            var call = renderCallSequence[i];
            if (call.duration) {
                if (!funcTimes[call.function]) {
                    funcTimes[call.function] = [];
                }
                funcTimes[call.function].push(call.duration);
            }
        }
        
        log("性能分析:");
        for (var func in funcTimes) {
            var times = funcTimes[func];
            var total = times.reduce(function(sum, time) { return sum + time; }, 0);
            var avg = total / times.length;
            log("  - " + func + ": 平均 " + avg.toFixed(2) + " ms");
        }
        
        // 清空序列，准备下一轮分析
        renderCallSequence = [];
        
        log("==============================\n");
    }
    
    // 主函数
    function main() {
        log("开始分析地图渲染流程...");
        
        if (!initAddresses()) {
            logError("初始化地址失败，脚本终止");
            return;
        }
        
        var success = true;
        
        // 钩住渲染流程的关键函数
        success &= hookTriggerRenderUpdate();
        success &= hookUpdateMapView();
        success &= hookCalculateVisibleTiles();
        success &= hookLoadMapTiles();
        success &= hookPrepareRender();
        
        if (success) {
            log("钩子安装成功，等待渲染事件触发...");
        } else {
            logError("部分钩子安装失败，可能无法获取完整的渲染流程");
        }
        
        // 添加控制台命令
        log("可用命令：");
        log("  analyze - 分析当前收集的渲染流程数据");
        log("  clear - 清除已收集的数据");
        log("  config - 显示当前配置");
    }
    
    // 启动脚本
    main();
})();