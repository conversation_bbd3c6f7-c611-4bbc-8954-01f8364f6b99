/*
 * 最终的DICE-AM数据提取器
 * 结合文件读取Hook + zlib解压Hook，确保捕获真实数据
 */

(function() {
    'use strict';
    
    console.log("[🎯最终DICE提取器] 启动最终的DICE-AM数据提取器...");
    
    var extractorStats = {
        totalDiceFound: 0,
        coordinatesFound: 0,
        chineseTextFound: 0
    };
    
    // === 简化的DICE-AM解析 ===
    function analyzeDiceAMData(data, source) {
        try {
            var view = new Uint8Array(data);
            
            // 验证DICE-AM头部
            if (view.length < 8 || 
                !(view[0] === 0x44 && view[1] === 0x49 && view[2] === 0x43 && 
                  view[3] === 0x45 && view[4] === 0x2D && view[5] === 0x41 && view[6] === 0x4D)) {
                return false;
            }
            
            extractorStats.totalDiceFound++;
            var version = view[7];
            
            console.log("\n🎯=== DICE-AM数据块 #" + extractorStats.totalDiceFound + " ===");
            console.log("📍 来源: " + source);
            console.log("📄 版本: " + version);
            console.log("📏 大小: " + view.length + " 字节");
            
            // 显示前64字节的hex数据
            var hexData = "";
            for (var i = 0; i < Math.min(64, view.length); i++) {
                var hex = view[i].toString(16).toUpperCase();
                if (hex.length === 1) hex = '0' + hex;
                hexData += hex + " ";
                if ((i + 1) % 16 === 0) hexData += "\n";
            }
            console.log("🔢 Hex数据:\n" + hexData);
            
            // 搜索坐标
            var coords = findCoordinatesInDice(view);
            if (coords.length > 0) {
                console.log("🌍 发现坐标: " + coords.length + " 个");
                for (var c = 0; c < Math.min(3, coords.length); c++) {
                    console.log("  坐标" + c + ": " + coords[c]);
                }
                extractorStats.coordinatesFound += coords.length;
            }
            
            // 搜索中文文本
            var texts = findChineseInDice(view);
            if (texts.length > 0) {
                console.log("🈲 发现中文: " + texts.length + " 个");
                for (var t = 0; t < Math.min(3, texts.length); t++) {
                    console.log("  文本" + t + ": " + texts[t]);
                }
                extractorStats.chineseTextFound += texts.length;
            }
            
            console.log("🎯=================================\n");
            return true;
            
        } catch (e) {
            console.log("[❌DICE分析错误] " + e);
            return false;
        }
    }
    
    // === 在DICE中查找坐标 ===
    function findCoordinatesInDice(view) {
        var results = [];
        try {
            var dataView = new DataView(view.buffer, view.byteOffset, view.byteLength);
            
            for (var i = 16; i < view.length - 8; i += 4) { // 从DICE-AM头部后开始
                try {
                    // 尝试float32坐标
                    var x = dataView.getFloat32(i, true);
                    var y = dataView.getFloat32(i + 4, true);
                    
                    if ((x >= 70 && x <= 140 && y >= 15 && y <= 55) ||
                        (x >= 15 && x <= 55 && y >= 70 && y <= 140)) {
                        results.push("(" + x.toFixed(6) + ", " + y.toFixed(6) + ") @" + i);
                        if (results.length >= 5) break;
                    }
                    
                    // 尝试压缩int32坐标
                    var x2 = dataView.getInt32(i, true) / 1000000.0;
                    var y2 = dataView.getInt32(i + 4, true) / 1000000.0;
                    
                    if ((x2 >= 70 && x2 <= 140 && y2 >= 15 && y2 <= 55) ||
                        (x2 >= 15 && x2 <= 55 && y2 >= 70 && y2 <= 140)) {
                        results.push("(" + x2.toFixed(6) + ", " + y2.toFixed(6) + ") 压缩@" + i);
                        if (results.length >= 5) break;
                    }
                    
                } catch (e) {
                    continue;
                }
            }
        } catch (e) {}
        return results;
    }
    
    // === 在DICE中查找中文 ===
    function findChineseInDice(view) {
        var results = [];
        try {
            for (var i = 16; i < view.length - 3; i++) {
                if (view[i] >= 0xE4 && view[i] <= 0xE9 && 
                    view[i+1] >= 0x80 && view[i+1] <= 0xBF &&
                    view[i+2] >= 0x80 && view[i+2] <= 0xBF) {
                    
                    try {
                        var textBytes = view.slice(i, Math.min(i + 30, view.length));
                        var text = new TextDecoder('utf-8', {fatal: false}).decode(textBytes);
                        var chinese = "";
                        
                        for (var j = 0; j < text.length; j++) {
                            var char = text[j];
                            if (char >= '\u4e00' && char <= '\u9fff') {
                                chinese += char;
                            } else if (chinese.length > 0) {
                                break;
                            }
                        }
                        
                        if (chinese.length >= 2) {
                            results.push("'" + chinese + "' @" + i);
                            i += chinese.length * 3;
                            if (results.length >= 5) break;
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }
        } catch (e) {}
        return results;
    }
    
    // === Hook文件读取 ===
    function setupFileHook() {
        console.log("[文件Hook] 设置文件读取Hook...");
        
        try {
            var readPtr = Module.findExportByName("libc.so", "read");
            if (readPtr) {
                Interceptor.attach(readPtr, {
                    onEnter: function(args) {
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                    },
                    onLeave: function(retval) {
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 100) {
                            try {
                                var data = this.buffer.readByteArray(Math.min(bytesRead, 1024));
                                var view = new Uint8Array(data);
                                
                                // 检查是否包含DICE-AM
                                if (view.length >= 7 && 
                                    view[0] === 0x44 && view[1] === 0x49 && view[2] === 0x43 && 
                                    view[3] === 0x45 && view[4] === 0x2D && view[5] === 0x41 && view[6] === 0x4D) {
                                    analyzeDiceAMData(data, "文件读取");
                                }
                            } catch (e) {}
                        }
                    }
                });
                console.log("[✅文件Hook] 已设置");
            }
        } catch (e) {
            console.log("[❌文件Hook错误] " + e);
        }
    }
    
    // === Hook zlib解压 ===
    function setupZlibHook() {
        console.log("[zlib Hook] 设置zlib解压Hook...");
        
        try {
            var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
            if (uncompressPtr) {
                Interceptor.attach(uncompressPtr, {
                    onEnter: function(args) {
                        this.destBuffer = args[0];
                        this.destLenPtr = args[1];
                    },
                    onLeave: function(retval) {
                        if (retval.toInt32() === 0) {
                            try {
                                var len = this.destLenPtr.readU32();
                                if (len > 100 && len < 1024 * 1024) {
                                    var data = this.destBuffer.readByteArray(len);
                                    var view = new Uint8Array(data);
                                    
                                    // 检查是否包含DICE-AM
                                    if (view.length >= 7 && 
                                        view[0] === 0x44 && view[1] === 0x49 && view[2] === 0x43 && 
                                        view[3] === 0x45 && view[4] === 0x2D && view[5] === 0x41 && view[6] === 0x4D) {
                                        analyzeDiceAMData(data, "zlib解压");
                                    }
                                }
                            } catch (e) {}
                        }
                    }
                });
                console.log("[✅zlib Hook] 已设置");
            }
        } catch (e) {
            console.log("[❌zlib Hook错误] " + e);
        }
    }
    
    // === 生成报告 ===
    function generateReport() {
        if (extractorStats.totalDiceFound === 0) {
            console.log("[📊状态] 等待DICE-AM数据... (尝试更大幅度移动地图)");
            return;
        }
        
        console.log("\n📊=== DICE-AM提取统计 ===");
        console.log("DICE-AM块: " + extractorStats.totalDiceFound + " 个");
        console.log("坐标总数: " + extractorStats.coordinatesFound + " 个");
        console.log("中文总数: " + extractorStats.chineseTextFound + " 个");
        console.log("📊=======================\n");
    }
    
    // === 主函数 ===
    function main() {
        console.log("[🚀主程序] 初始化最终DICE-AM提取器...");
        
        setTimeout(function() {
            setupFileHook();
            setupZlibHook();
            
            setInterval(generateReport, 10000);
            
            console.log("[🎯最终DICE提取器] 已启动!");
            console.log("💡 现在移动地图，等待DICE-AM数据...");
            
        }, 3000);
    }
    
    // === 启动 ===
    try {
        Java.perform(function() {
            console.log("[☕Java] 环境就绪");
            main();
        });
    } catch (e) {
        console.log("[❌Java错误] " + e);
        main();
    }
    
})(); 