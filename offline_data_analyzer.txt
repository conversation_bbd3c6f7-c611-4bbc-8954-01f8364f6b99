     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
[启动] 离线数据解析输出器 已启动
[就绪] 请在地图中缩放/平移以触发加载；当首个读取者为 libamapnsq.so 时会输出结构化解析。
[成功] Hook libz.so:uncompress 地址=0x7f80fe067c
[Remote::com.autonavi.minimap]-> 
[解压完成] 尺寸=8192 字节, 预览(32B)=0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[监控] 启用只读监控，等待首个读取者...
[命中] 首个读取者 模块=libc.so, 偏移=0x1c3dc, 符号=memcpy+0x11c
[跳过] 非 libamapnsq.so（例如 GPU/系统内存拷贝），不解析。
TypeError: undefined not callable
    at [anon] (../../../frida-gum/bindings/gumjs/duktape.c:66061)

[解压完成] 尺寸=8192 字节, 预览(32B)=05 00 00 01 bf 0b 0c 00 00 00 0a b4 0b 78 0b 84 0b 90 0b 9c 0b a8 0b b4 0b c0 0b d8 0b 48 0b cc
[监控] 启用只读监控，等待首个读取者...
[命中] 首个读取者 模块=libamapnsq.so, 偏移=0x22938, 符号=weftlattimqugxvvpvso+0x759c
[解析] 数据块大小=8192 字节
[解析] 头部预览(64B Hex): 05 00 00 01 bf 0b 0c 00 00 00 0a b4 0b 78 0b 84 0b 90 0b 9c 0b a8 0b b4 0b c0 0b d8 0b 48 0b cc 0b e4 0b f0 0b fc 0c 08 0c 14 0c 20 0c 2c 0c 38 0c 44 0c 50 0c 5c 0c 68 0c 74 0c 80 0c 8c 0c 98
[类型] 未知类型，输出前64字节作为参考
[未知] 前64字节: 05 00 00 01 bf 0b 0c 00 00 00 0a b4 0b 78 0b 84 0b 90 0b 9c 0b a8 0b b4 0b c0 0b d8 0b 48 0b cc 0b e4 0b f0 0b fc 0c 08 0c 14 0c 20 0c 2c 0c 38 0c 44 0c 50 0c 5c 0c 68 0c 74 0c 80 0c 8c 0c 98

[解压完成] 尺寸=8192 字节, 预览(32B)=0d 00 00 00 01 05 95 00 05 95 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[监控] 启用只读监控，等待首个读取者...
[命中] 首个读取者 模块=libamapnsq.so, 偏移=0x22938, 符号=weftlattimqugxvvpvso+0x759c
[解析] 数据块大小=8192 字节
[解析] 头部预览(64B Hex): 0d 00 00 00 01 05 95 00 05 95 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[类型] 未知类型，输出前64字节作为参考
[未知] 前64字节: 0d 00 00 00 01 05 95 00 05 95 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00

[解压完成] 尺寸=8192 字节, 预览(32B)=00 00 25 20 01 b2 01 b3 01 b4 01 b5 01 b6 01 b7 01 b8 01 b9 01 ba 01 bb 01 bc 01 bd 01 be 01 bf
[监控] 启用只读监控，等待首个读取者...
[命中] 首个读取者 模块=libamapnsq.so, 偏移=0x20208, 符号=weftlattimqugxvvpvso+0x4e6c
[解析] 数据块大小=8192 字节
[解析] 头部预览(64B Hex): 00 00 25 20 01 b2 01 b3 01 b4 01 b5 01 b6 01 b7 01 b8 01 b9 01 ba 01 bb 01 bc 01 bd 01 be 01 bf 01 c0 01 c1 01 c2 01 c3 01 c4 01 c5 01 c6 01 01 02 01 0f 34 35 36 37 38 39 3a 3b 4c 83 01 84 01
[类型] 未知类型，输出前64字节作为参考
[未知] 前64字节: 00 00 25 20 01 b2 01 b3 01 b4 01 b5 01 b6 01 b7 01 b8 01 b9 01 ba 01 bb 01 bc 01 bd 01 be 01 bf 01 c0 01 c1 01 c2 01 c3 01 c4 01 c5 01 c6 01 01 02 01 0f 34 35 36 37 38 39 3a 3b 4c 83 01 84 01

[解压完成] 尺寸=8192 字节, 预览(32B)=0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[监控] 启用只读监控，等待首个读取者...
[命中] 首个读取者 模块=libamapnsq.so, 偏移=0x22938, 符号=weftlattimqugxvvpvso+0x759c
[解析] 数据块大小=8192 字节
[解析] 头部预览(64B Hex): 0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[类型] 未知类型，输出前64字节作为参考
[未知] 前64字节: 0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00

[解压完成] 尺寸=8192 字节, 预览(32B)=00 00 25 20 01 b2 01 b3 01 b4 01 b5 01 b6 01 b7 01 b8 01 b9 01 ba 01 bb 01 bc 01 bd 01 be 01 bf
[监控] 启用只读监控，等待首个读取者...
[命中] 首个读取者 模块=libamapnsq.so, 偏移=0x20208, 符号=weftlattimqugxvvpvso+0x4e6c
[解析] 数据块大小=8192 字节
[解析] 头部预览(64B Hex): 00 00 25 20 01 b2 01 b3 01 b4 01 b5 01 b6 01 b7 01 b8 01 b9 01 ba 01 bb 01 bc 01 bd 01 be 01 bf 01 c0 01 c1 01 c2 01 c3 01 c4 01 c5 01 c6 01 01 02 01 0f 34 35 36 37 38 39 3a 3b 4c 83 01 84 01
[类型] 未知类型，输出前64字节作为参考
[未知] 前64字节: 00 00 25 20 01 b2 01 b3 01 b4 01 b5 01 b6 01 b7 01 b8 01 b9 01 ba 01 bb 01 bc 01 bd 01 be 01 bf 01 c0 01 c1 01 c2 01 c3 01 c4 01 c5 01 c6 01 01 02 01 0f 34 35 36 37 38 39 3a 3b 4c 83 01 84 01

[Remote::com.autonavi.minimap]-> exit

Thank you for using Frida!
