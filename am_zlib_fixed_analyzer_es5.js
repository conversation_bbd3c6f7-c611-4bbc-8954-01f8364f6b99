// AM-zlib修复版分析脚本
// 目标: 修复之前脚本的问题，更准确地分析AM-zlib处理

console.log("[AM-zlib修复版] 开始分析AM-zlib处理...");
console.log("[修复] 解决之前脚本的Hook问题");

// 修复版分析状态
var fixedAnalysis = {
    mmapFiles: {},           // mmap文件映射
    amZlibAccess: [],        // AM-zlib访问记录
    memoryOperations: [],    // 内存操作记录
    decompressAttempts: [],  // 解压尝试记录
    startTime: Date.now()
};

// 安全的字节数组处理
function safeByteArrayToHex(byteArray, maxLen) {
    var hexBytes = [];
    var len = Math.min(maxLen || 16, byteArray.length);
    for (var i = 0; i < len; i++) {
        hexBytes.push(('0' + byteArray[i].toString(16)).slice(-2));
    }
    return hexBytes.join(' ');
}

// 检查是否是AM-zlib数据
function isAMZlibData(data) {
    if (data.length >= 8) {
        // AM-zlib魔数: 41 4d 2d 7a 6c 69 62 00
        return data[0] === 0x41 && data[1] === 0x4d && data[2] === 0x2d && 
               data[3] === 0x7a && data[4] === 0x6c && data[5] === 0x69 && 
               data[6] === 0x62 && data[7] === 0x00;
    }
    return false;
}

// 检查是否是压缩数据特征
function hasCompressionPattern(data) {
    if (data.length >= 12) {
        // 压缩数据特征: 00 01 00 00 ... fe fe
        return data[0] === 0x00 && data[1] === 0x01 && data[2] === 0x00 && data[3] === 0x00 &&
               data[10] === 0xfe && data[11] === 0xfe;
    }
    return false;
}

// 1. Hook mmap - 重点关注大文件映射
console.log("[1] Hook mmap，寻找AM-zlib文件映射...");

try {
    var libc = Process.getModuleByName("libc.so");
    var mmapPtr = libc.getExportByName("mmap");
    
    Interceptor.attach(mmapPtr, {
        onEnter: function(args) {
            this.addr = args[0];
            this.length = args[1].toInt32();
            this.prot = args[2].toInt32();
            this.flags = args[3].toInt32();
            this.fd = args[4].toInt32();
            this.offset = args[5].toInt32();
        },
        onLeave: function(retval) {
            var mappedAddr = retval;
            var length = this.length;
            var fd = this.fd;
            
            // 关注大文件映射（可能是.ans文件）
            if (!mappedAddr.equals(ptr(-1)) && length > 10000000) { // 10MB+
                console.log("[大文件映射] fd=" + fd + ", 大小=" + (length/1024/1024).toFixed(1) + "MB, 地址=" + mappedAddr);
                
                try {
                    var data = mappedAddr.readByteArray(Math.min(64, length));
                    var header = new Uint8Array(data);
                    
                    if (isAMZlibData(header)) {
                        console.log("  [发现AM-zlib文件!] " + mappedAddr);
                        console.log("  头部: " + safeByteArrayToHex(header, 16));
                        
                        // 分析AM-zlib头部
                        var version = header[8];
                        var flags = header[9];
                        var geometryType = header[10];
                        var geometryFlags = header[11];
                        
                        console.log("  版本: " + version + " (0x" + version.toString(16) + ")");
                        console.log("  几何类型: " + geometryType + " (0x" + geometryType.toString(16) + ")");
                        console.log("  几何标志: " + geometryFlags + " (0x" + geometryFlags.toString(16) + ")");
                        
                        // 检查压缩数据部分
                        var compressedData = mappedAddr.add(16).readByteArray(Math.min(32, length - 16));
                        var compHeader = new Uint8Array(compressedData);
                        console.log("  压缩数据头: " + safeByteArrayToHex(compHeader, 16));
                        
                        if (hasCompressionPattern(compHeader)) {
                            console.log("  [确认] 发现压缩数据特征模式!");
                        }
                        
                        fixedAnalysis.mmapFiles[mappedAddr.toString()] = {
                            address: mappedAddr.toString(),
                            fd: fd,
                            size: length,
                            type: "AM-ZLIB",
                            version: version,
                            geometryType: geometryType,
                            geometryFlags: geometryFlags,
                            timestamp: Date.now()
                        };
                    }
                } catch (e) {
                    console.log("  [错误] 读取映射数据失败: " + e.message);
                }
            }
        }
    });
    
    console.log("[✓] mmap Hook设置成功");
} catch (e) {
    console.log("[✗] mmap Hook失败: " + e.message);
}

// 2. Hook内存访问 - 寻找AM-zlib数据的读取
console.log("[2] Hook内存访问，寻找AM-zlib数据读取...");

try {
    var memcpyPtr = libc.getExportByName("memcpy");
    Interceptor.attach(memcpyPtr, {
        onEnter: function(args) {
            this.dest = args[0];
            this.src = args[1];
            this.n = args[2].toInt32();
        },
        onLeave: function(retval) {
            var size = this.n;
            
            // 关注中等大小的内存拷贝（可能是数据块处理）
            if (size >= 1024 && size <= 100000) {
                try {
                    var srcData = this.src.readByteArray(Math.min(32, size));
                    var srcHeader = new Uint8Array(srcData);
                    
                    if (hasCompressionPattern(srcHeader)) {
                        console.log("[压缩数据拷贝] 大小: " + size + " 字节");
                        console.log("  源地址: " + this.src);
                        console.log("  目标地址: " + this.dest);
                        console.log("  数据头: " + safeByteArrayToHex(srcHeader, 16));
                        
                        fixedAnalysis.memoryOperations.push({
                            type: "compression_data_copy",
                            src: this.src.toString(),
                            dest: this.dest.toString(),
                            size: size,
                            header: safeByteArrayToHex(srcHeader, 16),
                            timestamp: Date.now()
                        });
                    }
                } catch (e) {
                    // 忽略读取错误
                }
            }
        }
    });
    
    console.log("[✓] 内存拷贝Hook设置成功");
} catch (e) {
    console.log("[✗] 内存拷贝Hook失败: " + e.message);
}

// 3. Hook自定义解压函数 - 寻找可能的AM-zlib解压
console.log("[3] 寻找自定义解压函数...");

// 搜索可能的解压函数模式
function searchDecompressionFunctions() {
    try {
        var modules = Process.enumerateModules();
        for (var i = 0; i < modules.length; i++) {
            var module = modules[i];
            if (module.name.indexOf("libgaode") !== -1 || 
                module.name.indexOf("libamap") !== -1 ||
                module.name.indexOf("base.odex") !== -1) {
                
                console.log("[搜索解压函数] " + module.name);
                
                try {
                    // 搜索可能的函数调用模式
                    var functions = Module.enumerateExports(module.name);
                    for (var j = 0; j < functions.length; j++) {
                        var func = functions[j];
                        if (func.name && (
                            func.name.indexOf("decompress") !== -1 ||
                            func.name.indexOf("decode") !== -1 ||
                            func.name.indexOf("inflate") !== -1 ||
                            func.name.indexOf("unpack") !== -1
                        )) {
                            console.log("  [可能的解压函数] " + func.name + " @ " + func.address);
                            
                            // Hook这个函数
                            try {
                                Interceptor.attach(func.address, {
                                    onEnter: function(args) {
                                        console.log("  [调用] " + func.name);
                                        this.funcName = func.name;
                                        
                                        // 尝试读取参数
                                        try {
                                            if (args[0] && args[1]) {
                                                var inputData = args[0].readByteArray(Math.min(32, 1024));
                                                if (inputData) {
                                                    var header = new Uint8Array(inputData);
                                                    console.log("    输入数据: " + safeByteArrayToHex(header, 16));
                                                    
                                                    if (hasCompressionPattern(header)) {
                                                        console.log("    [发现] 压缩数据特征!");
                                                    }
                                                }
                                            }
                                        } catch (e) {
                                            // 忽略参数读取错误
                                        }
                                    },
                                    onLeave: function(retval) {
                                        console.log("  [返回] " + this.funcName + " -> " + retval);
                                    }
                                });
                            } catch (e) {
                                // 忽略Hook错误
                            }
                        }
                    }
                } catch (e) {
                    // 忽略模块分析错误
                }
            }
        }
    } catch (e) {
        console.log("[错误] 搜索解压函数失败: " + e.message);
    }
}

// 延迟搜索
setTimeout(searchDecompressionFunctions, 3000);

// 4. 定期输出修复版分析报告
setInterval(function() {
    var runtime = Math.floor((Date.now() - fixedAnalysis.startTime) / 1000);
    
    console.log("\n[修复版分析报告] ==========================================");
    console.log("运行时间: " + runtime + "s");
    console.log("");
    
    console.log("mmap文件映射:");
    var mmapCount = Object.keys(fixedAnalysis.mmapFiles).length;
    console.log("  发现的AM-zlib映射: " + mmapCount + " 个");
    
    for (var addr in fixedAnalysis.mmapFiles) {
        var file = fixedAnalysis.mmapFiles[addr];
        console.log("  地址=" + addr + ": " + (file.size/1024/1024).toFixed(1) + "MB");
        console.log("    版本=" + file.version + ", 几何类型=0x" + file.geometryType.toString(16));
    }
    
    console.log("");
    console.log("内存操作统计:");
    console.log("  压缩数据拷贝: " + fixedAnalysis.memoryOperations.length + " 次");
    
    for (var i = 0; i < Math.min(3, fixedAnalysis.memoryOperations.length); i++) {
        var op = fixedAnalysis.memoryOperations[i];
        console.log("  操作" + (i+1) + ": " + op.size + " 字节");
        console.log("    数据: " + op.header);
    }
    
    console.log("");
    console.log("解压尝试:");
    console.log("  解压调用: " + fixedAnalysis.decompressAttempts.length + " 次");
    
    console.log("===============================================\n");
}, 25000);

console.log("[修复版分析] 脚本已启动...");
console.log("[目标] 更准确地分析AM-zlib处理");
console.log("[提示] 请打开地图并移动以触发数据处理");
