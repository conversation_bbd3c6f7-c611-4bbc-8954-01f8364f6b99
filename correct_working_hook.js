// 高德地图解压后数据Hook脚本 - 严格按照成功脚本模式
// 兼容 Frida 12.9.7，使用 ES5 语法
// 基于 ans_frida_test45.js 成功模式

(function() {
    'use strict';

    // ==================== 全局配置 ====================
    var config = {
        debug: false,
        logStackTrace: false,
        maxDataCapture: 5,
        delayInitialization: 3000,
        targetModules: ["libamapnsq.so", "libamapr.so", "libz.so"],
        enableZlibHook: true,
        enableFileHook: true
    };

    // ==================== 全局变量 ====================
    var gDataCaptured = 0;
    var gModuleMap = {};
    var gHookedFunctions = {};
    var gExceptionCount = {};

    // ==================== 工具函数 ====================
    function log(message) {
        console.log("[+] " + message);
    }

    function logError(message) {
        console.log("[-] " + message);
    }

    function logDebug(message) {
        if (config.debug) {
            console.log("[DEBUG] " + message);
        }
    }

    // 格式化地址
    function formatAddress(address) {
        if (!address) return "0x0";
        try {
            return "0x" + address.toString(16);
        } catch (e) {
            return "0x???";
        }
    }

    // 安全地获取模块信息
    function safeGetModuleByAddress(address) {
        try {
            if (!address || address.isNull()) return null;
            return Process.findModuleByAddress(address);
        } catch (e) {
            return null;
        }
    }

    // 格式化地址为模块+偏移格式
    function formatAddressWithModule(address) {
        if (!address || address.isNull()) return "0x0";
        
        try {
            var module = safeGetModuleByAddress(address);
            if (module) {
                var offset = address.sub(module.base);
                return module.name + "!" + formatAddress(offset);
            }
        } catch (e) {
            logDebug("格式化地址失败: " + e);
        }
        return formatAddress(address);
    }

    // 安全读取内存
    function safeReadByteArray(address, size) {
        try {
            if (!address || address.isNull() || size <= 0) return null;
            return address.readByteArray(Math.min(size, 8192));
        } catch (e) {
            logDebug("读取内存失败: " + e.message);
            return null;
        }
    }

    // 检查是否为有效经纬度
    function isValidCoordinate(x, y) {
        return (70 <= x && x <= 140 && 15 <= y && y <= 60) || 
               (70 <= y && y <= 140 && 15 <= x && x <= 60);
    }

    // ==================== 模块枚举 ====================
    function enumerateTargetModules() {
        log("开始枚举目标模块...");
        
        try {
            var modules = Process.enumerateModules();
            var foundModules = 0;
            
            for (var i = 0; i < modules.length; i++) {
                var module = modules[i];
                
                // 检查是否为目标模块
                for (var j = 0; j < config.targetModules.length; j++) {
                    if (module.name === config.targetModules[j]) {
                        gModuleMap[module.name] = {
                            base: module.base,
                            size: module.size,
                            path: module.path
                        };
                        
                        log("找到目标模块: " + module.name + " @ " + formatAddress(module.base));
                        foundModules++;
                        break;
                    }
                }
            }
            
            log("共找到 " + foundModules + " 个目标模块");
            return foundModules > 0;
        } catch (e) {
            logError("枚举模块失败: " + e);
            return false;
        }
    }

    // ==================== 数据分析函数 ====================
    function analyzeDecompressedData(data, index, context) {
        if (!data || gDataCaptured >= config.maxDataCapture) return;
        
        try {
            var bytes = new Uint8Array(data);
            
            log("\n========== 解压后数据分析 #" + index + " ==========");
            log("数据大小: " + bytes.length + " 字节");
            log("上下文: " + (context || "未知"));
            
            // 显示头部信息
            var headerHex = "";
            var headerText = "";
            for (var i = 0; i < Math.min(32, bytes.length); i++) {
                headerHex += bytes[i].toString(16).padStart(2, '0') + " ";
                if (bytes[i] >= 32 && bytes[i] < 127) {
                    headerText += String.fromCharCode(bytes[i]);
                } else {
                    headerText += ".";
                }
            }
            
            log("数据头部 (hex): " + headerHex);
            log("数据头部 (text): " + headerText);
            
            // 数据类型分析
            if (headerText.indexOf("DICE-AM") === 0) {
                log("🎯 数据类型: DICE-AM矢量地图数据");
                analyzeDICEAMData(bytes);
            } else if (hasChineseContent(bytes)) {
                log("🎯 数据类型: 包含中文文本");
                analyzeChineseText(bytes);
            } else if (headerText.indexOf("{") >= 0) {
                log("🎯 数据类型: JSON配置数据");
                analyzeJSONData(bytes);
            } else {
                log("🎯 数据类型: 二进制数据");
                analyzeBinaryData(bytes);
            }
            
            log("========== 数据分析完成 ==========\n");
            gDataCaptured++;
            
        } catch (e) {
            logError("数据分析失败: " + e.message);
        }
    }

    function analyzeDICEAMData(bytes) {
        try {
            if (bytes.length < 16) return;
            
            var magic = String.fromCharCode.apply(null, bytes.slice(0, 7));
            var version = bytes[7];
            var view = new DataView(bytes.buffer);
            var dataLen = view.getUint32(8, true);
            var pointCount = view.getUint32(12, true);
            
            log("DICE-AM详细信息:");
            log("  魔数: " + magic);
            log("  版本: " + version + " (验证: " + (version ^ 0xAB) + ")");
            log("  数据长度: " + dataLen);
            log("  坐标点数: " + pointCount);
            
            // 解析前几个坐标点
            var coordCount = 0;
            for (var i = 0; i < Math.min(pointCount, 3) && coordCount < 3; i++) {
                var offset = 16 + i * 12;
                if (offset + 8 <= bytes.length) {
                    try {
                        var x = view.getFloat32(offset, true);
                        var y = view.getFloat32(offset + 4, true);
                        
                        if (isValidCoordinate(x, y)) {
                            log("  🌍 坐标" + i + ": (" + x.toFixed(6) + ", " + y.toFixed(6) + ") ← 真实经纬度!");
                            coordCount++;
                        } else {
                            log("  📐 坐标" + i + ": (" + x.toFixed(6) + ", " + y.toFixed(6) + ") (投影坐标)");
                        }
                    } catch (e) {
                        break;
                    }
                }
            }
            
            if (coordCount > 0) {
                log("  ✅ 发现 " + coordCount + " 个有效经纬度坐标");
            }
            
        } catch (e) {
            log("  ❌ DICE-AM分析失败: " + e.message);
        }
    }

    function hasChineseContent(bytes) {
        try {
            var text = new TextDecoder('utf-8').decode(bytes);
            for (var i = 0; i < Math.min(text.length, 100); i++) {
                if (text.charCodeAt(i) >= 0x4e00 && text.charCodeAt(i) <= 0x9fff) {
                    return true;
                }
            }
            return false;
        } catch (e) {
            return false;
        }
    }

    function analyzeChineseText(bytes) {
        try {
            var text = new TextDecoder('utf-8').decode(bytes);
            var placenames = [];
            var currentName = "";
            
            for (var i = 0; i < text.length; i++) {
                var char = text[i];
                if (char >= '\u4e00' && char <= '\u9fff') {
                    currentName += char;
                } else {
                    if (currentName.length >= 2) {
                        placenames.push(currentName);
                    }
                    currentName = "";
                }
            }
            
            if (currentName.length >= 2) {
                placenames.push(currentName);
            }
            
            if (placenames.length > 0) {
                log("中文文本分析:");
                log("  地名数量: " + placenames.length + " 个");
                log("  前5个地名: " + placenames.slice(0, 5).join(", "));
                
                var roads = placenames.filter(function(name) {
                    return name.indexOf("路") >= 0 || name.indexOf("街") >= 0 || name.indexOf("道") >= 0;
                });
                var areas = placenames.filter(function(name) {
                    return name.indexOf("市") >= 0 || name.indexOf("区") >= 0 || name.indexOf("村") >= 0;
                });
                
                log("  道路名: " + roads.length + " 个");
                log("  行政区: " + areas.length + " 个");
                
                if (roads.length > 0) {
                    log("  道路示例: " + roads.slice(0, 3).join(", "));
                }
            }
            
        } catch (e) {
            log("  ❌ 中文分析失败: " + e.message);
        }
    }

    function analyzeJSONData(bytes) {
        try {
            var text = new TextDecoder('utf-8').decode(bytes);
            var jsonStart = text.indexOf('{');
            var jsonEnd = text.lastIndexOf('}');
            
            if (jsonStart >= 0 && jsonEnd > jsonStart) {
                var jsonText = text.substring(jsonStart, jsonEnd + 1);
                log("JSON数据分析:");
                log("  内容预览: " + jsonText.substring(0, 100) + "...");
                
                if (jsonText.indexOf("style") >= 0) {
                    log("  包含: 样式配置");
                }
                if (jsonText.indexOf("color") >= 0) {
                    log("  包含: 颜色设置");
                }
                if (jsonText.indexOf("zoom") >= 0) {
                    log("  包含: 缩放配置");
                }
            }
            
        } catch (e) {
            log("  ❌ JSON分析失败: " + e.message);
        }
    }

    function analyzeBinaryData(bytes) {
        try {
            var view = new DataView(bytes.buffer);
            var coordCount = 0;
            
            // 搜索可能的浮点坐标
            for (var i = 0; i < bytes.length - 8 && coordCount < 3; i += 4) {
                try {
                    var x = view.getFloat32(i, true);
                    var y = view.getFloat32(i + 4, true);
                    
                    if (isValidCoordinate(x, y)) {
                        log("  🌍 发现坐标: (" + x.toFixed(6) + ", " + y.toFixed(6) + ") at 偏移 " + i);
                        coordCount++;
                    }
                } catch (e) {
                    continue;
                }
            }
            
            if (coordCount === 0) {
                log("二进制数据分析:");
                var header = view.getUint32(0, true);
                log("  头部整数: 0x" + header.toString(16));
            }
            
        } catch (e) {
            log("  ❌ 二进制分析失败: " + e.message);
        }
    }

    // ==================== Hook函数 ====================
    function hookZlibDecompression() {
        if (!config.enableZlibHook) return;
        
        log("设置zlib解压Hook...");
        
        try {
            var zlibModule = gModuleMap["libz.so"];
            if (!zlibModule) {
                logError("libz.so模块未找到");
                return;
            }
            
            var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
            if (!uncompressPtr) {
                logError("uncompress函数未找到");
                return;
            }
            
            if (gHookedFunctions[uncompressPtr]) {
                logDebug("uncompress已经被Hook");
                return;
            }
            
            log("找到uncompress函数: " + formatAddress(uncompressPtr));
            
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    this.destBuffer = args[0];
                    this.destLenPtr = args[1];
                    this.sourceLen = args[3].toInt32();
                    
                    if (config.debug) {
                        log("[解压] 开始解压: " + this.sourceLen + " 字节");
                    }
                },
                onLeave: function(retval) {
                    if (retval.toInt32() === 0 && gDataCaptured < config.maxDataCapture) {
                        try {
                            var decompressedLen = this.destLenPtr.readU32();
                            
                            if (decompressedLen > 0 && decompressedLen < 100000) {
                                log("\n🎯 [zlib解压成功] 压缩前: " + this.sourceLen + " → 解压后: " + decompressedLen + " 字节");
                                
                                var data = safeReadByteArray(this.destBuffer, Math.min(decompressedLen, 8192));
                                if (data) {
                                    analyzeDecompressedData(data, gDataCaptured, "zlib解压");
                                }
                            }
                            
                        } catch (e) {
                            logDebug("读取解压数据失败: " + e.message);
                        }
                    }
                }
            });
            
            gHookedFunctions[uncompressPtr] = true;
            log("✅ zlib解压Hook设置成功");
            
        } catch (e) {
            logError("设置zlib Hook失败: " + e);
        }
    }

    function hookFileOperations() {
        if (!config.enableFileHook) return;
        
        log("设置文件操作Hook...");
        
        try {
            var readPtr = Module.findExportByName("libc.so", "read");
            if (!readPtr) {
                logError("read函数未找到");
                return;
            }
            
            if (gHookedFunctions[readPtr]) {
                logDebug("read已经被Hook");
                return;
            }
            
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    this.buffer = args[1];
                    this.size = args[2].toInt32();
                    this.isTarget = (this.size > 1000 && this.size < 50000);
                },
                onLeave: function(retval) {
                    if (this.isTarget && gDataCaptured < config.maxDataCapture) {
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 0) {
                            try {
                                var data = safeReadByteArray(this.buffer, Math.min(bytesRead, 1024));
                                if (data) {
                                    var bytes = new Uint8Array(data);
                                    
                                    // 检查是否包含目标数据
                                    var hasZlib = (bytes[0] === 0x78 && bytes[1] === 0x9c);
                                    var hasDICE = String.fromCharCode.apply(null, bytes.slice(0, 7)) === "DICE-AM";
                                    var hasChineseText = hasChineseContent(bytes);
                                    
                                    if (hasZlib || hasDICE || hasChineseText) {
                                        log("\n📂 [文件读取] 发现目标数据: " + bytesRead + " 字节");
                                        if (hasZlib) log("  -> 包含zlib压缩数据");
                                        if (hasDICE) log("  -> 包含DICE-AM数据");
                                        if (hasChineseText) log("  -> 包含中文文本");
                                        
                                        analyzeDecompressedData(data, gDataCaptured, "文件读取");
                                    }
                                }
                            } catch (e) {
                                logDebug("文件数据分析失败: " + e.message);
                            }
                        }
                    }
                }
            });
            
            gHookedFunctions[readPtr] = true;
            log("✅ 文件操作Hook设置成功");
            
        } catch (e) {
            logError("设置文件Hook失败: " + e);
        }
    }

    // ==================== 异常处理 ====================
    function setupExceptionHandler() {
        Process.setExceptionHandler(function(exception) {
            var exceptionKey = exception.type + ":" + formatAddress(exception.address);
            
            if (!gExceptionCount[exceptionKey]) {
                gExceptionCount[exceptionKey] = 0;
            }
            
            gExceptionCount[exceptionKey]++;
            
            // 只输出前2次相同异常
            if (gExceptionCount[exceptionKey] <= 2) {
                logError("异常(" + gExceptionCount[exceptionKey] + "/2): " + 
                        exception.type + " @ " + formatAddress(exception.address));
            }
            
            return true;
        });
    }

    // ==================== 主程序 ====================
    function main() {
        log("高德地图解压后数据Hook脚本");
        log("适用于 Frida 12.9.7，使用 ES5 语法");
        log("基于成功脚本模式重构");
        
        // 设置异常处理
        setupExceptionHandler();
        
        try {
            // 1. 枚举目标模块
            if (!enumerateTargetModules()) {
                logError("未找到目标模块，脚本终止");
                return;
            }
            
            // 2. 延迟初始化Hook
            setTimeout(function() {
                try {
                    log("开始设置Hook...");
                    
                    // 设置zlib Hook
                    hookZlibDecompression();
                    
                    // 设置文件Hook
                    hookFileOperations();
                    
                    log("所有Hook设置完成");
                    log("✅ 脚本就绪，开始监控解压数据...");
                    log("💡 请在地图中移动以触发数据解压和分析");
                    
                } catch (e) {
                    logError("设置Hook失败: " + e);
                }
            }, config.delayInitialization);
            
        } catch (e) {
            logError("脚本初始化失败: " + e);
        }
        
        log("脚本设置完成，等待初始化...");
    }
    
    // 启动脚本
    main();
})(); 