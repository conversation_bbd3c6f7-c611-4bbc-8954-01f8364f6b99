// 高德地图GPS模拟器积分破解脚本 v3
// 功能：修改多种可能的积分值，确保功能可用
Java.perform(function() {
    console.log("\n[+] 积分破解脚本已启动...");
    console.log("[+] 修复版：同时处理界面显示积分(0)和内部积分值(-100)");

    // 定位用户模型类
    try {
        var GpsUserModel = Java.use("tgo.ngo.mockgps.model.app.GpsUserModel");
        console.log("[+] 已找到用户模型类: GpsUserModel");
        
        // 1. 钩住所有可能与积分相关的get方法，记录调用并尝试修改返回值
        var methods = GpsUserModel.class.getDeclaredMethods();
        
        methods.forEach(function(method) {
            var name = method.getName();
            if (name.startsWith("get")) {
                try {
                    GpsUserModel[name].implementation = function() {
                        var original = this[name]();
                        console.log("[*] 调用方法: " + name + "(), 返回值: " + original);
                        
                        // 对于数值类型的返回值，可能是积分，修改为大数值
                        if (typeof original === "number" && original <= 0) {
                            console.log("[!] 已修改数值: " + original + " -> 99999");
                            return 99999;
                        }
                        
                        // 对于字符串类型，如果包含"积分不足"或类似提示，替换为空
                        if (typeof original === "string" && 
                           (original.includes("积分") || original.includes("不足") || 
                            original.includes("小程序"))) {
                            console.log("[!] 已清空提示信息: " + original);
                            return "";
                        }
                        
                        return original;
                    };
                    console.log("[+] 已钩住方法: " + name);
                } catch(e) {
                    // 忽略无法钩住的方法
                }
            }
        });
        
        // 2. 显式钩住已知的关键方法
        // 修改积分值
        GpsUserModel.getCount.implementation = function() {
            var original = this.getCount();
            console.log("[*] 拦截到积分查询getCount，原始积分: " + original);
            console.log("[!] 已修改积分值: " + original + " -> 99999");
            return 99999;
        };
        
        // 修改积分不足提示
        GpsUserModel.getNotCanUseMag.implementation = function() {
            var original = this.getNotCanUseMag();
            console.log("[*] 拦截到积分不足提示: " + original);
            console.log("[!] 已清空积分不足提示");
            return "";
        };
        
        // 尝试修改可用状态（如果存在）
        if (GpsUserModel.getCanUse) {
            GpsUserModel.getCanUse.implementation = function() {
                console.log("[*] 拦截到积分可用状态检查");
                console.log("[!] 强制返回可用状态: true");
                return true;
            };
        }
        
    } catch(e) {
        console.log("[!] 钩住GpsUserModel时出错: " + e);
    }
    
    // 3. 尝试拦截任何包含积分检查的对话框
    try {
        var DialogInterface = Java.use("android.content.DialogInterface");
        var DialogButton = DialogInterface.BUTTON_POSITIVE.value;
        
        // 通用方法：当任何对话框出现时，自动点击确定按钮
        DialogInterface.onClick.overload('android.content.DialogInterface', 'int').implementation = function(dialog, which) {
            console.log("[*] 拦截到对话框点击事件, 按钮: " + which);
            
            // 如果是确定按钮，尝试处理
            if (which === DialogButton) {
                console.log("[!] 自动点击确定按钮，尝试绕过提示");
            }
            
            // 继续原有逻辑
            return this.onClick(dialog, which);
        };
    } catch(e) {
        console.log("[!] 钩住对话框失败: " + e);
    }
    
    // 4. 尝试直接修改界面上显示的积分文本
    try {
        // 寻找可能显示积分的TextView
        var TextView = Java.use("android.widget.TextView");
        TextView.setText.overload('java.lang.CharSequence').implementation = function(text) {
            var originalText = text ? text.toString() : "";
            
            // 如果文本内容包含"积分"或纯数字（可能是积分显示）
            if (originalText.includes("积分") || /^\d+$/.test(originalText)) {
                console.log("[*] 拦截到可能的积分显示文本: " + originalText);
                // 修改为大数值
                text = "99999";
                console.log("[!] 已修改显示文本为: " + text);
            }
            
            return this.setText(text);
        };
    } catch(e) {
        console.log("[!] 钩住TextView失败: " + e);
    }
    
    // 5. 尝试钩住MainActivity中与积分检查和启动相关的方法
    try {
        var MainActivity = Java.use("tgo.ngo.mockgps.ui.MainActivity");
        
        // startMock方法很可能包含积分检查
        MainActivity.startMock.implementation = function() {
            console.log("[*] 拦截到启动模拟位置方法");
            console.log("[+] 正在绕过启动前的积分检查...");
            
            // 调用原方法，由于我们已修改积分值，应该能正常启动
            return this.startMock();
        };
        
    } catch(e) {
        console.log("[!] 钩住MainActivity失败: " + e);
    }

    console.log("[*] 积分破解脚本加载完成，请重新操作应用");
    console.log("[*] 如有问题请截图日志进一步分析");
});
