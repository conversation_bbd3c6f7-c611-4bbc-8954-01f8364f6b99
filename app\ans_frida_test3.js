/*
APP 运行正常
Spawning `com.autonavi.minimap`...
[+] 高德地图ANS文件分析脚本 (轻量级)
[+] ANS文件分析脚本设置完成
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [+] 启动ANS文件分析模式
[类] 找到: com.autonavi.minimap.offline.nativesupport.AmapCompat
[类] 找到: com.autonavi.ae.gmap.AMapController
[类] 找到: com.autonavi.jni.ajx3.bl.AjxBLFactoryController
  [方法] init4WarmStart
  [方法] nativeUninit4WarmDestory
  [方法] uninit4WarmDestory
[+] Java层分析完成
*/
(function() {
  console.log("[+] 高德地图ANS文件分析脚本 (轻量级)");
  
  // 全局变量
  var ansFileFDs = {};  // 存储文件描述符到路径的映射
  var ansFileInfo = {}; // 存储ANS文件信息
  
  // 1. 基础反调试 - 只处理strstr
  var strstr_ptr = Module.findExportByName("libc.so", "strstr");
  if (strstr_ptr) {
    Interceptor.attach(strstr_ptr, {
      onEnter: function(args) {
        try {
          var str = args[1].readUtf8String();
          if (str && (str.indexOf("frida") !== -1 || str.indexOf("gum") !== -1)) {
            this.shouldFake = true;
          }
        } catch (e) {}
      },
      onLeave: function(retval) {
        if (this.shouldFake) {
          retval.replace(0);
        }
      }
    });
  }
  
  // 2. 拦截可能导致崩溃的系统调用
  var exit_ptr = Module.findExportByName("libc.so", "exit");
  if (exit_ptr) {
    Interceptor.replace(exit_ptr, new NativeCallback(function() {
      return 0;
    }, 'void', ['int']));
  }

  // 3. 延迟启动分析模式 (10秒后启动，确保应用完全初始化)
  setTimeout(function() {
    console.log("[+] 启动ANS文件分析模式");
    
    // 3.1 监控文件打开操作
    var open_ptr = Module.findExportByName("libc.so", "open");
    if (open_ptr) {
      Interceptor.attach(open_ptr, {
        onEnter: function(args) {
          try {
            var path = args[0].readUtf8String();
            if (path && (path.indexOf("m1.ans") !== -1 || path.indexOf("m3.ans") !== -1)) {
              this.isAnsFile = true;
              this.path = path;
              console.log("[ANS文件] 打开: " + path);
            }
          } catch (e) {}
        },
        onLeave: function(retval) {
          if (this.isAnsFile && retval.toInt32() > 0) {
            var fd = retval.toInt32();
            console.log("[ANS文件] 文件描述符: " + fd);
            ansFileFDs[fd] = this.path;
          }
        }
      });
    }
    
    // 3.2 监控mmap调用 - 轻量级实现
    var mmap_ptr = Module.findExportByName("libc.so", "mmap");
    if (mmap_ptr) {
      Interceptor.attach(mmap_ptr, {
        onEnter: function(args) {
          this.fd = args[4].toInt32();
          if (ansFileFDs[this.fd]) {
            this.isAnsFile = true;
            this.path = ansFileFDs[this.fd];
            this.size = args[1].toInt32();
          }
        },
        onLeave: function(retval) {
          if (!this.isAnsFile) return;
          
          var fileName = this.path.split("/").pop();
          console.log("[ANS映射] " + fileName);
          console.log("  地址: " + retval);
          console.log("  大小: " + this.size + " 字节");
          
          // 存储映射信息
          ansFileInfo[fileName] = {
            path: this.path,
            addr: retval,
            size: this.size,
            fd: this.fd
          };
          
          // 轻量级分析文件头 (只读取前16字节)
          try {
            console.log("[文件头] " + fileName + ":");
            console.log(hexdump(retval, {length: Math.min(16, this.size)}));
          } catch(e) {}
        }
      });
    }
    
    // 3.3 Java层分析
    Java.perform(function() {
      try {
        // 基本反调试
        var Debug = Java.use("android.os.Debug");
        Debug.isDebuggerConnected.implementation = function() {
          return false;
        };
        
        // 尝试找出ANS文件解析相关类
        try {
          var targetClasses = [
            "com.autonavi.minimap.offline.nativesupport.AmapCompat",
            "com.autonavi.ae.gmap.AMapController",
            "com.autonavi.jni.ajx3.bl.AjxBLFactoryController"
          ];
          
          for (var i = 0; i < targetClasses.length; i++) {
            try {
              var clazz = Java.use(targetClasses[i]);
              console.log("[类] 找到: " + targetClasses[i]);
              
              // 只列出可能与ANS相关的方法名，不进行Hook
              var methods = clazz.class.getDeclaredMethods();
              for (var j = 0; j < methods.length; j++) {
                var methodName = methods[j].getName();
                if (methodName.indexOf("ans") !== -1 || 
                    methodName.indexOf("parse") !== -1 || 
                    methodName.indexOf("load") !== -1 ||
                    methodName.indexOf("init") !== -1) {
                  console.log("  [方法] " + methodName);
                }
              }
            } catch(e) {}
          }
        } catch(e) {}
        
        console.log("[+] Java层分析完成");
      } catch(e) {}
    });
    
    // 3.4 监控ANS文件读取操作 (轻量级实现)
    var read_ptr = Module.findExportByName("libc.so", "read");
    if (read_ptr) {
      Interceptor.attach(read_ptr, {
        onEnter: function(args) {
          this.fd = args[0].toInt32();
          if (ansFileFDs[this.fd]) {
            this.isAnsFile = true;
            this.size = args[2].toInt32();
            this.buffer = args[1];
          }
        },
        onLeave: function(retval) {
          if (!this.isAnsFile) return;
          
          var bytesRead = retval.toInt32();
          if (bytesRead <= 0) return;
          
          var fileName = ansFileFDs[this.fd].split("/").pop();
          console.log("[ANS读取] " + fileName + " 读取 " + bytesRead + " 字节");
          
          // 只分析小块数据，避免性能问题
          if (bytesRead <= 16) {
            try {
              console.log(hexdump(this.buffer, {length: bytesRead}));
            } catch(e) {}
          }
        }
      });
    }
  }, 10000); // 延迟10秒，确保应用完全启动
  
  console.log("[+] ANS文件分析脚本设置完成");
})(); 