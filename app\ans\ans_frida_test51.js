// 高德地图 nativeAddMapGestureMsg 下游流程追踪脚本 (V51 - 稳定版)
// 适用于 Frida 12.9.7，使用 ES5 语法
// 专注于稳定地追踪核心下游函数调用链

(function() {
    'use-strict';

    // 全局配置
    var config = {
        // 关键函数偏移量 (相对于 libamapr.so 基地址)
        functionOffsets: {
            nativeAddMapGestureMsg: 0x6ee70c,
            getMapEngineInstance: 0x6FB98C,
            validateEngine: 0x6F3430,
            processGestureMessage: 0x6FB530,
            triggerRenderUpdate: 0x6FBC78,
            updateMapView: 0x6FB9E0,
            finalizeProcessing: 0x6FB550
        }
    };

    // 全局变量
    var gCallDepth = 0; // 用于格式化输出

    // 工具函数 - 日志输出
    function log(message) {
        console.log(message); // 移除[+]前缀，使日志更干净
    }

    function logError(message) {
        console.log("[-] " + message);
    }

    // 工具函数 - 格式化地址
    function formatAddress(address) {
        if (!address || address.isNull()) return "0x0";
        try {
            return "0x" + address.toString(16);
        } catch (e) {
            return "0x???";
        }
    }
    
    // 工具函数 - 格式化地址为模块+偏移格式
    function formatAddressWithModule(address) {
        if (!address || address.isNull()) return "0x0";
        try {
            var module = Process.findModuleByAddress(address);
            if (module) {
                var offset = address.sub(module.base);
                return module.name + "!" + formatAddress(offset);
            }
        }
        catch (e) {}
        return formatAddress(address);
    }

    // 工具函数 - 获取缩进
    function getIndent(depth) {
        var indent = "";
        for (var i = 0; i < depth; i++) {
            indent += "  ";
        }
        return indent;
    }

    // 通用函数钩子
    function hookFunction(address, name) {
        try {
            if (!address || address.isNull()) {
                logError("钩住函数 " + name + " 失败: 无效地址");
                return;
            }
            
            Interceptor.attach(address, {
                onEnter: function(args) {
                    this.startTime = new Date().getTime();
                    gCallDepth++;
                    var indent = getIndent(gCallDepth);
                    log(indent + "-> " + name + " ( " + formatAddressWithModule(address) + " )" );
                },
                onLeave: function(retval) {
                    var execTime = new Date().getTime() - this.startTime;
                    var indent = getIndent(gCallDepth);
                    log(indent + "<- " + name + " 返回: " + formatAddress(retval) + " (耗时: " + execTime + "ms)");
                    gCallDepth--;
                }
            });
            console.log("[+] 成功钩住函数: " + name);
        } catch (e) {
            logError("钩住函数 " + name + " 失败: " + e);
        }
    }

    // 钩住 Java 层的 nativeAddMapGestureMsg 方法
    function hookJavaGestureMethod() {
        log("准备监控 Java 层 nativeAddMapGestureMsg 方法...");
        Java.perform(function() {
            try {
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                        gCallDepth = 0; // 重置调用深度
                        log("\n==================== 新手势事件 (类型: " + type + ") ====================");
                        var result = this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                        log("==================== 手势事件处理结束 ====================\n");
                        return result;
                    };
                    log("[+] 成功钩住 Java 层 nativeAddMapGestureMsg 方法");
                } else {
                    logError("未找到 nativeAddMapGestureMsg 方法");
                }
            } catch (e) {
                logError("钩住 Java 层方法失败: " + e);
            }
        });
    }

    // 钩住 Native 函数
    function hookNativeFunctions() {
        log("准备钩住 Native 层函数...");
        var libamapr = Process.findModuleByName("libamapr.so");
        if (!libamapr) {
            logError("未找到 libamapr.so 模块，无法钩住Native函数");
            return;
        }
        log("[+] 找到 libamapr.so 模块 @ " + libamapr.base);

        // 钩住主函数和下游函数
        var names = Object.keys(config.functionOffsets);
        for (var i = 0; i < names.length; i++) {
            var name = names[i];
            var offset = config.functionOffsets[name];
            var address = libamapr.base.add(offset);
            hookFunction(address, name);
        }
    }

    // 主函数
    function main() {
        log("高德地图手势下游流程追踪脚本 (V51) 启动");
        
        // 延迟执行，确保应用完全初始化
        setTimeout(function() {
            try {
                // 1. 先钩住Java层，作为流程起点
                hookJavaGestureMethod();
                
                // 2. 再钩住Native层函数
                hookNativeFunctions();
                
                log("[+] 脚本设置完成，等待手势事件...");
            } catch (e) {
                logError("脚本初始化失败: " + e);
            }
        }, 4000); // 增加延迟以提高稳定性
    }
    
    // 启动脚本
    main();
})();