/**
 * 高德地图渲染前数据监控脚本
 * 基于 full_execution_flow_analysis.md 的IDA Pro分析结果
 * 目标：监控 libamapnsq.so 中解析出的渲染数据
 * Frida 12.9.7 兼容版本 (ES5语法)
 */
(function() {
    console.log("[GaoDe Render Data Monitor] 脚本启动...");

    // === 配置区域 ===
    var CONFIG = {
        ENABLE_ANTI_DEBUG: true,
        ENABLE_DATA_PARSING: true,
        ENABLE_RENDER_MONITORING: true,
        MAX_DATA_DUMP_SIZE: 1024,
        VERBOSE_LOGGING: false
    };

    // === 数据结构定义 ===
    var parsedDataBuffer = {};
    var renderDataQueue = [];

    // === 反调试机制 ===
    if (CONFIG.ENABLE_ANTI_DEBUG) {
        try {
            var exit_ptr = Module.findExportByName("libc.so", "exit");
            if (exit_ptr) {
                Interceptor.replace(exit_ptr, new NativeCallback(function(code) {
                    console.log("[AntiDebug] 拦截 exit() 调用，代码: " + code);
                    return 0;
                }, 'void', ['int']));
            }

            setTimeout(function() {
                Java.perform(function() {
                    try {
                        var Debug = Java.use("android.os.Debug");
                        Debug.isDebuggerConnected.implementation = function() {
                            return false;
                        };
                        console.log("[AntiDebug] Java 反调试已启用");
                    } catch (e) {
                        console.log("[AntiDebug] Java 反调试失败: " + e);
                    }
                });
            }, 1000);
        } catch (e) {
            console.log("[AntiDebug] 初始化失败: " + e);
        }
    }

    // === 工具函数 ===
    function formatHex(data, maxLen) {
        if (typeof maxLen === 'undefined') maxLen = 64;
        try {
            return hexdump(data, {length: Math.min(maxLen, 1024), ansi: false});
        } catch (e) {
            return "无法格式化数据: " + e;
        }
    }

    function analyzeDataHeader(buffer, size) {
        if (size < 16) return null;
        
        try {
            var header = buffer.readUtf8String(16);
            var analysis = {
                size: size,
                header: header,
                isDICE_AM: header.indexOf("DICE-AM") !== -1,
                isANS: header.indexOf("ANS") !== -1,
                timestamp: Date.now()
            };

            if (size >= 9) {
                var versionByte = buffer.add(8).readU8();
                analysis.version = versionByte ^ 0xAB;
            }

            return analysis;
        } catch (e) {
            return {error: e.toString(), size: size};
        }
    }

    // === 监控 libamapr.so 中的关键函数 ===
    function hookAmaprFunctions() {
        var libamapr = Process.findModuleByName("libamapr.so");
        if (!libamapr) {
            console.log("[Hook] libamapr.so 未找到");
            return;
        }

        console.log("[Hook] libamapr.so 基址: " + libamapr.base);

        try {
            var nativeAddMapGestureMsg = libamapr.base.add(0x6ee70c);
            Interceptor.attach(nativeAddMapGestureMsg, {
                onEnter: function(args) {
                    this.a1 = args[0];
                    this.a2 = args[1]; 
                    this.a3 = args[2].toInt32();
                    this.a4 = args[3];
                    
                    console.log("[GestureMsg] nativeAddMapGestureMsg 调用");
                    console.log("   - engineId: " + this.a3);
                    console.log("   - nativePtr: " + this.a4);
                },
                onLeave: function(retval) {
                    console.log("[GestureMsg] nativeAddMapGestureMsg 完成");
                }
            });

            var processGestureMessage = libamapr.base.add(0x6FB530);
            var callCount = 0;
            Interceptor.attach(processGestureMessage, {
                onEnter: function(args) {
                    callCount++;
                    this.callIndex = callCount;
                    console.log("[ProcessGesture] 第" + this.callIndex + "次调用 processGestureMessage");
                    console.log("   - 参数1: " + args[0]);
                    console.log("   - 参数2: " + args[1]);
                },
                onLeave: function(retval) {
                    console.log("[ProcessGesture] 第" + this.callIndex + "次调用完成，返回值: " + retval);
                }
            });

            var triggerRenderUpdate = libamapr.base.add(0x6FBC78);
            Interceptor.attach(triggerRenderUpdate, {
                onEnter: function(args) {
                    console.log("[Render] triggerRenderUpdate 被调用");
                    console.log("   - 参数: " + args[0]);
                },
                onLeave: function(retval) {
                    console.log("[Render] 渲染更新触发完成");
                }
            });

            console.log("[Hook] libamapr.so 关键函数已挂钩");

        } catch (e) {
            console.log("[Hook] libamapr.so 挂钩失败: " + e);
        }
    }

    // === 监控 libamapnsq.so 中的数据解析函数 ===
    function hookAmapnsqFunctions() {
        var libamapnsq = Process.findModuleByName("libamapnsq.so");
        if (!libamapnsq) {
            console.log("[Hook] libamapnsq.so 未找到");
            return;
        }

        console.log("[Hook] libamapnsq.so 基址: " + libamapnsq.base);

        try {
            var sub_C654 = libamapnsq.base.add(0xC654);
            Interceptor.attach(sub_C654, {
                onEnter: function(args) {
                    console.log("[Decompress] sub_C654 解压协调函数调用");
                    this.inputBuffer = args[0];
                    this.inputSize = args[1] ? args[1].toInt32() : 0;
                    console.log("   - 输入缓冲区: " + this.inputBuffer);
                    console.log("   - 输入大小: " + this.inputSize);
                },
                onLeave: function(retval) {
                    console.log("[Decompress] 解压完成，返回值: " + retval);
                }
            });

            var sub_5C394 = libamapnsq.base.add(0x5C394);
            Interceptor.attach(sub_5C394, {
                onEnter: function(args) {
                    console.log("[ParseDispatcher] sub_5C394 解析调度器调用");
                    this.dataBuffer = args[0];
                    this.dataSize = args[1] ? args[1].toInt32() : 0;
                    
                    console.log("   - 数据缓冲区: " + this.dataBuffer);
                    console.log("   - 数据大小: " + this.dataSize);
                    
                    if (this.dataBuffer && this.dataSize > 0) {
                        var analysis = analyzeDataHeader(this.dataBuffer, this.dataSize);
                        if (analysis) {
                            console.log("[ParseDispatcher] 数据分析: " + JSON.stringify(analysis, null, 2));
                            
                            if (analysis.isDICE_AM) {
                                console.log("[ParseDispatcher] 检测到 DICE-AM 数据块！");
                                console.log("   - 版本: " + analysis.version);
                                if (CONFIG.VERBOSE_LOGGING) {
                                    console.log(formatHex(this.dataBuffer, 128));
                                }
                            }
                        }
                    }
                },
                onLeave: function(retval) {
                    console.log("[ParseDispatcher] 解析调度完成，返回值: " + retval);
                }
            });

            var sub_10F88 = libamapnsq.base.add(0x10F88);
            Interceptor.attach(sub_10F88, {
                onEnter: function(args) {
                    console.log("[DataParser] sub_10F88 数据解析器调用");
                    console.log("   - args[0]: " + args[0]);
                    console.log("   - args[1]: " + args[1]); 
                    console.log("   - args[2]: " + args[2]);
                    
                    // 基于观察，重新分析参数结构
                    this.foundData = false;
                    
                    // 检查每个参数是否包含有效数据
                    for (var i = 0; i < 3; i++) {
                        try {
                            if (args[i] && !args[i].isNull()) {
                                // 尝试读取数据头部进行分析
                                var testData = args[i].readByteArray(32);
                                var analysis = analyzeDataHeader(args[i], 32);
                                
                                if (analysis && (analysis.isDICE_AM || analysis.isANS)) {
                                    console.log("[DataParser] 在 args[" + i + "] 发现地图数据!");
                                    console.log("   - 类型: " + (analysis.isDICE_AM ? "DICE-AM" : "ANS"));
                                    console.log("   - 头部: " + analysis.header);
                                    
                                    this.foundData = true;
                                    this.dataBuffer = args[i];
                                    this.dataIndex = i;
                                    
                                    // 尝试确定数据大小
                                    if (i === 0 && args[1] && !args[1].isNull()) {
                                        try {
                                            this.dataSize = args[1].toInt32();
                                            if (this.dataSize > 0 && this.dataSize < 100000) {
                                                console.log("   - 推测大小: " + this.dataSize + " 字节");
                                            } else {
                                                this.dataSize = 1024;  // 默认大小
                                            }
                                        } catch (e) {
                                            this.dataSize = 1024;
                                        }
                                    } else {
                                        this.dataSize = 1024;  // 默认大小
                                    }
                                    break;
                                }
                            }
                        } catch (e) {
                            // 继续检查下一个参数
                        }
                    }
                    
                    if (!this.foundData) {
                        console.log("[DataParser] 未在参数中发现明显的地图数据");
                    }
                },
                onLeave: function(retval) {
                    var returnCode = retval.toInt32();
                    console.log("[DataParser] 解析完成，返回码: " + returnCode);
                    
                    var statusMsg = "";
                    switch(returnCode) {
                        case 0: statusMsg = "成功"; break;
                        case 8: statusMsg = "错误类型8"; break;
                        case 11: statusMsg = "错误类型11"; break;
                        case 26: statusMsg = "错误类型26"; break;
                        default: statusMsg = "未知状态 (" + returnCode + ")";
                    }
                    console.log("[DataParser] 状态: " + statusMsg);

                    if (returnCode === 0 && this.foundData && this.dataBuffer) {
                        console.log("[DataParser] 成功解析数据，详细分析:");
                        console.log("   - 数据来源: args[" + this.dataIndex + "]");
                        console.log("   - 数据大小: " + this.dataSize + " 字节");
                        console.log(formatHex(this.dataBuffer, 128));
                        
                        renderDataQueue.push({
                            timestamp: Date.now(),
                            buffer: this.dataBuffer,
                            size: this.dataSize,
                            status: "parsed_successfully",
                            returnCode: returnCode,
                            source: "args[" + this.dataIndex + "]"
                        });
                        console.log("[RenderQueue] 数据已加入渲染队列，队列长度: " + renderDataQueue.length);
                    }
                }
            });

            console.log("[Hook] libamapnsq.so 关键函数已挂钩");

        } catch (e) {
            console.log("[Hook] libamapnsq.so 挂钩失败: " + e);
        }
    }

    // === 监控 zlib 解压 ===
    function hookZlibFunctions() {
        try {
            var uncompress = Module.findExportByName("libz.so", "uncompress");
            if (uncompress) {
                Interceptor.attach(uncompress, {
                    onEnter: function(args) {
                        this.dest = args[0];
                        this.destLen = args[1];
                        this.source = args[2];
                        this.sourceLen = args[3].toInt32();
                        
                        console.log("[Zlib] uncompress 调用，源大小: " + this.sourceLen);
                    },
                    onLeave: function(retval) {
                        var result = retval.toInt32();
                        if (result === 0) {
                            var decompressedSize = this.destLen.readU32();
                            console.log("[Zlib] 解压成功，解压后大小: " + decompressedSize);
                            
                            // *** 关键修改：分析解压后的数据 ***
                            if (decompressedSize > 16 && decompressedSize < 100000) {
                                try {
                                    // 检查解压后数据是否包含DICE-AM或其他地图数据标识
                                    var headerData = this.dest.readByteArray(Math.min(64, decompressedSize));
                                    var headerHex = hexdump(headerData, {length: 64, ansi: false});
                                    
                                    console.log("[Zlib] 解压数据头部分析:");
                                    console.log(headerHex);
                                    
                                    // 检查是否包含DICE-AM魔数
                                    var headerBytes = new Uint8Array(headerData);
                                    var diceAmPattern = [0x44, 0x49, 0x43, 0x45, 0x2D, 0x41, 0x4D]; // "DICE-AM"
                                    
                                    for (var i = 0; i <= headerBytes.length - diceAmPattern.length; i++) {
                                        var match = true;
                                        for (var j = 0; j < diceAmPattern.length; j++) {
                                            if (headerBytes[i + j] !== diceAmPattern[j]) {
                                                match = false;
                                                break;
                                            }
                                        }
                                        if (match) {
                                            console.log("*** [Zlib] 发现DICE-AM数据块！偏移: " + i + " ***");
                                            
                                            // 解析DICE-AM结构
                                            var diceAmPtr = this.dest.add(i);
                                            console.log("[DICE-AM] 详细结构分析:");
                                            
                                            try {
                                                var magic = diceAmPtr.readCString();
                                                if (magic.length < 7) {
                                                    magic = "";
                                                    for (var k = 0; k < 8; k++) {
                                                        var byte = diceAmPtr.add(k).readU8();
                                                        if (byte === 0) break;
                                                        magic += String.fromCharCode(byte);
                                                    }
                                                }
                                                var version = diceAmPtr.add(8).readU8();
                                                var flags = diceAmPtr.add(9).readU8();
                                                var unknown1 = diceAmPtr.add(10).readU16();
                                                var dataSize = diceAmPtr.add(12).readU32();
                                                
                                                console.log("   - 魔数: " + magic);
                                                console.log("   - 版本: " + version + " (原始: 0x" + version.toString(16) + ")");
                                                console.log("   - 标志: 0x" + flags.toString(16));
                                                console.log("   - 未知字段: 0x" + unknown1.toString(16));
                                                console.log("   - 数据大小: " + dataSize + " (0x" + dataSize.toString(16) + ")");
                                                
                                                // 根据IDA分析，检查版本字段 (versionByte ^ 0xAB)
                                                var realVersion = version ^ 0xAB;
                                                console.log("   - 解密版本: " + realVersion + " (version ^ 0xAB)");
                                                
                                                // 保存到渲染队列
                                                renderDataQueue.push({
                                                    timestamp: Date.now(),
                                                    buffer: diceAmPtr,
                                                    size: Math.min(dataSize || 1024, decompressedSize - i),
                                                    status: "dice_am_found",
                                                    returnCode: 0,
                                                    source: "zlib_decompress",
                                                    offset: i,
                                                    magic: magic,
                                                    version: version,
                                                    realVersion: realVersion,
                                                    flags: flags,
                                                    dataSize: dataSize
                                                });
                                                
                                                console.log("[RenderQueue] DICE-AM数据已加入队列，队列长度: " + renderDataQueue.length);
                                                
                                                // 输出更多数据用于分析
                                                var analyzeSize = Math.min(256, decompressedSize - i);
                                                var fullData = diceAmPtr.readByteArray(analyzeSize);
                                                console.log("[DICE-AM] 完整数据块 (" + analyzeSize + " 字节):");
                                                console.log(hexdump(fullData, {length: analyzeSize}));
                                                
                                            } catch (e) {
                                                console.log("[DICE-AM] 结构解析失败: " + e);
                                            }
                                            break;
                                        }
                                    }
                                    
                                    // 还检查其他可能的地图数据格式
                                    var headerStr = "";
                                    try {
                                        headerStr = this.dest.readUtf8String(32);
                                        if (headerStr.indexOf("ANS") !== -1 || 
                                            headerStr.indexOf("MAP") !== -1 ||
                                            headerStr.indexOf("TILE") !== -1) {
                                            console.log("[Zlib] 发现其他地图数据格式: " + headerStr.substring(0, 16));
                                        }
                                    } catch (e) {
                                        // 不是UTF8字符串，可能是二进制数据
                                    }
                                    
                                } catch (e) {
                                    console.log("[Zlib] 解压数据分析失败: " + e);
                                }
                            }
                        } else {
                            console.log("[Zlib] 解压失败，错误码: " + result);
                        }
                    }
                });
                console.log("[Hook] zlib 解压函数已挂钩");
            }
        } catch (e) {
            console.log("[Hook] zlib 挂钩失败: " + e);
        }
    }

    // === 文件I/O监控 ===
    function hookFileOperations() {
        try {
            var read_ptr = Module.findExportByName("libc.so", "read");
            if (read_ptr) {
                var ansFileDescriptors = {};
                
                var open_ptr = Module.findExportByName("libc.so", "open");
                if (open_ptr) {
                    Interceptor.attach(open_ptr, {
                        onEnter: function(args) {
                            var path = args[0].readUtf8String();
                            if (path && path.indexOf(".ans") !== -1) {
                                this.ansPath = path;
                            }
                        },
                        onLeave: function(retval) {
                            if (this.ansPath) {
                                var fd = retval.toInt32();
                                if (fd > 0) {
                                    ansFileDescriptors[fd] = this.ansPath;
                                    console.log("[FileIO] 打开ANS文件: " + this.ansPath + " (fd:" + fd + ")");
                                }
                            }
                        }
                    });
                }

                Interceptor.attach(read_ptr, {
                    onEnter: function(args) {
                        this.fd = args[0].toInt32();
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                        
                        if (ansFileDescriptors[this.fd]) {
                            this.isAnsFile = true;
                            this.ansPath = ansFileDescriptors[this.fd];
                        }
                    },
                    onLeave: function(retval) {
                        if (!this.isAnsFile) return;
                        
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 0) {
                           // console.log("[FileIO] 读取ANS数据: " + this.ansPath + ", 大小: " + bytesRead + " 字节");
                            
                            // *** 特别关注m1.ans文件 ***
                            if (this.ansPath.indexOf("m1.ans") !== -1 && bytesRead > 32) {
                                console.log("*** [FileIO] m1.ans 文件内容分析 ***");
                                
                                try {
                                    // 分析m1.ans文件头部
                                    var headerData = this.buffer.readByteArray(Math.min(128, bytesRead));
                                    console.log("[m1.ans] 文件头部数据:");
                                    console.log(hexdump(headerData, {length: Math.min(128, bytesRead)}));
                                    
                                    // 搜索DICE-AM标识
                                    var headerBytes = new Uint8Array(headerData);
                                    var diceAmPattern = [0x44, 0x49, 0x43, 0x45, 0x2D, 0x41, 0x4D]; // "DICE-AM"
                                    
                                    for (var i = 0; i <= headerBytes.length - diceAmPattern.length; i++) {
                                        var match = true;
                                        for (var j = 0; j < diceAmPattern.length; j++) {
                                            if (headerBytes[i + j] !== diceAmPattern[j]) {
                                                match = false;
                                                break;
                                            }
                                        }
                                        if (match) {
                                            console.log("*** [m1.ans] 在文件中发现DICE-AM！偏移: " + i + " ***");
                                            
                                            var diceAmPtr = this.buffer.add(i);
                                            try {
                                                var magic = diceAmPtr.readUtf8String(8);
                                                var version = diceAmPtr.add(8).readU8();
                                                var flags = diceAmPtr.add(9).readU8();
                                                console.log("   - 魔数: " + magic);
                                                console.log("   - 版本: " + version);
                                                console.log("   - 标志: 0x" + flags.toString(16));
                                            } catch (e) {
                                                console.log("   - DICE-AM解析失败: " + e);
                                            }
                                            break;
                                        }
                                    }
                                    
                                    // 检查其他可能的文件头格式
                                    try {
                                        var firstU32 = this.buffer.readU32();
                                        var secondU32 = this.buffer.add(4).readU32();
                                        
                                        console.log("[m1.ans] 文件头部信息:");
                                        console.log("   - 前4字节: 0x" + firstU32.toString(16));
                                        console.log("   - 5-8字节: 0x" + secondU32.toString(16));
                                        
                                        // 检查是否为压缩数据的标识
                                        if (headerBytes[0] === 0x78 && (headerBytes[1] === 0x9C || headerBytes[1] === 0xDA)) {
                                            console.log("   - 检测到zlib压缩数据标识");
                                        }
                                        
                                    } catch (e) {
                                        console.log("[m1.ans] 头部分析失败: " + e);
                                    }
                                    
                                } catch (e) {
                                    console.log("[m1.ans] 内容分析失败: " + e);
                                }
                            }
                            
                            var analysis = analyzeDataHeader(this.buffer, bytesRead);
                            if (analysis && CONFIG.VERBOSE_LOGGING) {
                                console.log("[FileIO] 文件数据分析: " + JSON.stringify(analysis, null, 2));
                            }
                        }
                    }
                });
                console.log("[Hook] 文件I/O监控已启用");
            }
        } catch (e) {
            console.log("[Hook] 文件I/O监控失败: " + e);
        }
    }

    // === 总结函数 ===
    function printRenderDataSummary() {
        console.log("=== 渲染数据捕获总结 ===");
        console.log("队列中的数据块数量: " + renderDataQueue.length);
        
        if (renderDataQueue.length > 0) {
            var diceAmCount = 0;
            var latestData = null;
            
            for (var i = 0; i < renderDataQueue.length; i++) {
                var data = renderDataQueue[i];
                if (data.status === "dice_am_found") {
                    diceAmCount++;
                    latestData = data;
                }
            }
            
            console.log("发现的DICE-AM数据块: " + diceAmCount);
            
            if (latestData) {
                console.log("最新DICE-AM数据详情:");
                console.log("  - 时间戳: " + new Date(latestData.timestamp).toLocaleTimeString());
                console.log("  - 来源: " + latestData.source);
                console.log("  - 偏移: " + latestData.offset);
                console.log("  - 大小: " + latestData.size + " 字节");
                console.log("  - 魔数: " + (latestData.magic || "未解析"));
                console.log("  - 版本: " + (latestData.version || "未知") + " -> 解密版本: " + (latestData.realVersion || "未知"));
                console.log("  - 标志: 0x" + ((latestData.flags || 0).toString(16)));
                console.log("  - 数据大小字段: " + (latestData.dataSize || "未知"));
            }
        }
        console.log("========================");
    }

    // 定期输出总结
    function setupRenderDataSummary() {
        var summaryTimer = setInterval(function() {
            if (renderDataQueue.length > 0) {
                printRenderDataSummary();
            }
        }, 10000); // 每10秒输出一次总结
        
        console.log("[Main] 渲染数据总结功能已启用 (每10秒更新)");
    }

    // === 主函数 ===
    function main() {
        console.log("[Main] 开始设置所有监控...");

        setTimeout(function() {
            if (CONFIG.ENABLE_DATA_PARSING) {
                hookAmaprFunctions();
                hookAmapnsqFunctions();
                hookZlibFunctions();
                hookFileOperations();
            }

            if (CONFIG.ENABLE_RENDER_MONITORING) {
                setupRenderDataSummary();
            }

            console.log("[Main] 所有监控设置完成！");
            console.log("现在可以在地图上进行手势操作来触发数据解析...");
        }, 2000);
    }

    main();

    console.log("[GaoDe Render Data Monitor] 脚本加载完成，等待应用活动...");
})(); 