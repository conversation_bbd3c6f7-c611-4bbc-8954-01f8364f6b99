     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[Memory Content Searcher] 启动内存内容深度搜索器...
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> 
[Remote::com.autonavi.minimap]-> Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=00000215A1503510)

Thread 0x00005228 (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x000077ec (most recent call first):
<no Python frame>
