// 验证脚本 - ES5兼容版本 (Frida 12.9.7)
// 验证高德地图离线数据处理流程

console.log("[验证脚本] 开始验证高德地图离线数据处理流程...");
console.log("[版本信息] ES5兼容版本，适用于Frida 12.9.7");

// 工具函数 - ES5兼容的字节数组转十六进制
function bytesToHex(byteArray, maxLength) {
    var hexBytes = [];
    var length = maxLength || byteArray.length;
    for (var i = 0; i < Math.min(length, byteArray.length); i++) {
        var hex = byteArray[i].toString(16);
        if (hex.length === 1) {
            hex = '0' + hex;
        }
        hexBytes.push(hex);
    }
    return hexBytes.join(' ');
}

// 工具函数 - 安全的内存读取
function safeReadByteArray(ptr, size) {
    try {
        if (ptr && !ptr.isNull() && size > 0) {
            return ptr.readByteArray(size);
        }
    } catch (e) {
        console.log("[警告] 内存读取失败: " + e.message);
    }
    return null;
}

// 1. 验证关键库的加载
var libamapnsq = null;
var libz = null;
var libc = null;

try {
    libamapnsq = Process.getModuleByName("libamapnsq.so");
    console.log("[✓] libamapnsq.so 已加载，基址: " + libamapnsq.base);
} catch (e) {
    console.log("[✗] libamapnsq.so 未找到");
}

try {
    libz = Process.getModuleByName("libz.so");
    console.log("[✓] libz.so 已加载，基址: " + libz.base);
} catch (e) {
    console.log("[✗] libz.so 未找到");
}

try {
    libc = Process.getModuleByName("libc.so");
    console.log("[✓] libc.so 已加载，基址: " + libc.base);
} catch (e) {
    console.log("[✗] libc.so 未找到");
}

// 2. 统计计数器
var stats = {
    fileReads: 0,
    zlibDecompressions: 0,
    sqliteBindings: 0,
    aesDecryptions: 0,
    dataDispatches: 0,
    startTime: Date.now()
};

// 3. 验证关键函数地址
if (libamapnsq) {
    // 验证girf_sqlite3_bind_blob函数 (IDA Pro确认地址: 0x15000)
    var bindBlobAddr = libamapnsq.base.add(0x15000);
    console.log("[验证] girf_sqlite3_bind_blob 地址: " + bindBlobAddr);
    
    // 验证数据处理函数 (IDA Pro确认地址: 0x5c060)
    var dataProcessAddr = libamapnsq.base.add(0x5c060);
    console.log("[验证] 数据处理函数 地址: " + dataProcessAddr);
    
    // Hook girf_sqlite3_bind_blob 验证数据流
    try {
        Interceptor.attach(bindBlobAddr, {
            onEnter: function(args) {
                stats.sqliteBindings++;
                console.log("[Hook] girf_sqlite3_bind_blob 被调用 (#" + stats.sqliteBindings + ")");
                console.log("  参数1 (stmt): " + args[0]);
                console.log("  参数2 (index): " + args[1]);
                console.log("  参数3 (data): " + args[2]);
                console.log("  参数4 (size): " + args[3]);
                
                if (args[2] && args[3].toInt32() > 0) {
                    var size = Math.min(args[3].toInt32(), 64);
                    var data = safeReadByteArray(args[2], size);
                    if (data) {
                        console.log("  数据前64字节: " + hexdump(data, {length: size}));
                        
                        // 检查数据类型
                        var header = safeReadByteArray(args[2], 8);
                        if (header) {
                            var view = new Uint8Array(header);
                            
                            if (view[0] === 0x44 && view[1] === 0x49 && view[2] === 0x43 && view[3] === 0x45) {
                                console.log("  [数据类型] DICE-AM 矢量数据");
                            } else if (view[0] === 0x7B) {
                                console.log("  [数据类型] JSON 配置数据");
                            } else if (view[0] === 0x00 && view[1] === 0x00 && view[2] === 0x25) {
                                console.log("  [数据类型] 0x25xx 文本/属性数据");
                            } else {
                                console.log("  [数据类型] 未知格式: " + bytesToHex(view, 4));
                            }
                        }
                    }
                }
            },
            onLeave: function(retval) {
                console.log("[Hook] girf_sqlite3_bind_blob 返回: " + retval);
            }
        });
        console.log("[✓] girf_sqlite3_bind_blob Hook 设置成功");
    } catch (e) {
        console.log("[✗] girf_sqlite3_bind_blob Hook 失败: " + e.message);
    }
}

// 4. 验证zlib解压流程
if (libz) {
    var uncompressPtr = libz.getExportByName("uncompress");
    if (uncompressPtr) {
        console.log("[验证] uncompress 函数地址: " + uncompressPtr);
        
        try {
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    this.dest = args[0];
                    this.destLen = args[1];
                    this.source = args[2];
                    this.sourceLen = args[3];
                    
                    stats.zlibDecompressions++;
                    console.log("[Hook] zlib uncompress 被调用 (#" + stats.zlibDecompressions + ")");
                    console.log("  源数据大小: " + this.sourceLen.readU32());
                    console.log("  目标缓冲区大小: " + this.destLen.readU32());
                },
                onLeave: function(retval) {
                    if (retval.toInt32() === 0) {
                        var decompressedSize = this.destLen.readU32();
                        console.log("[Hook] zlib 解压成功，解压后大小: " + decompressedSize);
                        
                        if (decompressedSize > 0) {
                            var previewSize = Math.min(decompressedSize, 32);
                            var data = safeReadByteArray(this.dest, previewSize);
                            if (data) {
                                console.log("  解压数据预览: " + hexdump(data, {length: previewSize}));
                            }
                        }
                    } else {
                        console.log("[Hook] zlib 解压失败，错误码: " + retval.toInt32());
                    }
                }
            });
            console.log("[✓] zlib uncompress Hook 设置成功");
        } catch (e) {
            console.log("[✗] zlib uncompress Hook 失败: " + e.message);
        }
    }
}

// 5. 验证文件I/O操作
if (libc) {
    var readPtr = libc.getExportByName("read");
    if (readPtr) {
        try {
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    this.fd = args[0].toInt32();
                    this.buf = args[1];
                    this.count = args[2].toInt32();
                },
                onLeave: function(retval) {
                    var bytesRead = retval.toInt32();
                    if (bytesRead > 0 && this.count >= 8) {
                        var header = safeReadByteArray(this.buf, Math.min(8, bytesRead));
                        if (header) {
                            var view = new Uint8Array(header);
                            
                            // 检查是否是.ans文件数据
                            if (view[0] === 0x08 || (view[0] === 0x78 && view[1] === 0x9C)) {
                                stats.fileReads++;
                                console.log("[Hook] 检测到可能的.ans文件读取 (#" + stats.fileReads + ")");
                                console.log("  文件描述符: " + this.fd);
                                console.log("  读取字节数: " + bytesRead);
                                console.log("  数据头部: " + bytesToHex(view));
                            }
                        }
                    }
                }
            });
            console.log("[✓] libc read Hook 设置成功");
        } catch (e) {
            console.log("[✗] libc read Hook 失败: " + e.message);
        }
    }
}

// 6. 验证AES加密函数 (基于IDA Pro分析的sub_B55C)
if (libamapnsq) {
    var aesDecryptAddr = libamapnsq.base.add(0xB55C);
    console.log("[验证] AES解密函数地址: " + aesDecryptAddr);

    try {
        Interceptor.attach(aesDecryptAddr, {
            onEnter: function(args) {
                stats.aesDecryptions++;
                console.log("[Hook] AES解密函数被调用 (#" + stats.aesDecryptions + ")");
                console.log("  参数数量: " + arguments.length);
                for (var i = 0; i < Math.min(4, arguments.length); i++) {
                    console.log("  参数" + i + ": " + args[i]);
                }
            },
            onLeave: function(retval) {
                console.log("[Hook] AES解密函数返回: " + retval);
            }
        });
        console.log("[✓] AES解密函数 Hook 设置成功");
    } catch (e) {
        console.log("[警告] AES解密函数Hook失败: " + e.message);
    }
}

// 7. 验证数据分发函数 (基于IDA Pro分析的sub_5C060)
if (libamapnsq) {
    try {
        Interceptor.attach(dataProcessAddr, {
            onEnter: function(args) {
                stats.dataDispatches++;
                console.log("[Hook] 数据分发函数被调用 (#" + stats.dataDispatches + ")");
                console.log("  参数1: " + args[0]);
                console.log("  参数2: " + args[1]);
                console.log("  参数3: " + args[2]);
                console.log("  参数4: " + args[3]);
            },
            onLeave: function(retval) {
                console.log("[Hook] 数据分发函数返回: " + retval);
            }
        });
        console.log("[✓] 数据分发函数 Hook 设置成功");
    } catch (e) {
        console.log("[警告] 数据分发函数Hook失败: " + e.message);
    }
}

// 8. 定期输出统计信息
setInterval(function() {
    var runtime = Math.floor((Date.now() - stats.startTime) / 1000);
    console.log("[统计] 运行时间: " + runtime + "s, " +
                "文件读取: " + stats.fileReads + 
                ", zlib解压: " + stats.zlibDecompressions + 
                ", SQLite绑定: " + stats.sqliteBindings + 
                ", AES解密: " + stats.aesDecryptions + 
                ", 数据分发: " + stats.dataDispatches);
}, 10000);

console.log("[验证脚本] 验证脚本已启动，等待地图操作...");
console.log("[提示] 请在地图上进行移动操作以触发数据加载流程");
console.log("[IDA Pro验证] 关键函数地址已通过IDA Pro静态分析验证");
console.log("  - girf_sqlite3_bind_blob: 0x15000");
console.log("  - 数据处理函数: 0x5c060");
console.log("  - AES解密函数: 0xb55c");
console.log("[ES5兼容] 脚本已优化为ES5语法，兼容Frida 12.9.7");
