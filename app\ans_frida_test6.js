/*
程序卡死
 */
(function() {
    console.log("--- [轻量级ANS文件分析] 脚本启动 ---");
    
    // 全局变量
    var ansFileFDs = {};  // 文件描述符到路径的映射
    var targetFunction = "weftlattimqugxvvpvso"; // 目标函数名
    
    // 1. 防止应用崩溃 - 简单版本
    var exit_ptr = Module.findExportByName("libc.so", "exit");
    if (exit_ptr) {
      Interceptor.replace(exit_ptr, new NativeCallback(function() {
        return 0;
      }, 'void', ['int']));
      console.log("[+] 拦截exit()调用");
    }
    
    // 2. 监控文件打开操作 - 简化版
    var open_ptr = Module.findExportByName("libc.so", "open");
    if (open_ptr) {
      Interceptor.attach(open_ptr, {
        onEnter: function(args) {
          try {
            var path = args[0].readUtf8String();
            if (path && path.endsWith(".ans")) {
              this.isAnsFile = true;
              this.path = path;
              this.fileName = path.split("/").pop();
              
              // 只详细记录m1.ans和m3.ans
              if (path.indexOf("m1.ans") !== -1 || path.indexOf("m3.ans") !== -1) {
                console.log("[重要] 打开目标ANS文件: " + path);
                this.isTargetAns = true;
              } else {
                // 简化输出，只显示文件名
                console.log("[ANS] 打开: " + this.fileName);
              }
            }
          } catch (e) {}
        },
        onLeave: function(retval) {
          if (this.isAnsFile && retval.toInt32() > 0) {
            var fd = retval.toInt32();
            ansFileFDs[fd] = {
              path: this.path,
              fileName: this.fileName,
              isTarget: this.isTargetAns
            };
            
            if (this.isTargetAns) {
              console.log("[重要] 文件描述符: " + fd);
              
              // 获取简化调用栈
              try {
                var stack = Thread.backtrace(this.context, Backtracer.ACCURATE)
                  .map(DebugSymbol.fromAddress);
                console.log("[调用栈] 打开ANS文件:");
                for (var i = 0; i < Math.min(stack.length, 3); i++) {
                  var symbol = stack[i];
                  if (symbol) {
                    console.log("  " + i + ": " + symbol.toString());
                    
                    // 检查是否包含目标函数
                    if (symbol.toString().indexOf(targetFunction) !== -1) {
                      console.log("[确认] 由目标函数" + targetFunction + "打开");
                    }
                  }
                }
              } catch(e) {}
            }
          }
        }
      });
      console.log("[+] 监控open()调用");
    }
    
    // 3. 监控read操作 - 简化版，只监控目标文件
    var read_ptr = Module.findExportByName("libc.so", "read");
    if (read_ptr) {
      Interceptor.attach(read_ptr, {
        onEnter: function(args) {
          this.fd = args[0].toInt32();
          if (ansFileFDs[this.fd] && ansFileFDs[this.fd].isTarget) {
            this.isTargetAns = true;
            this.fileInfo = ansFileFDs[this.fd];
            this.buffer = args[1];
            this.size = args[2].toInt32();
          }
        },
        onLeave: function(retval) {
          if (!this.isTargetAns) return;
          
          var bytesRead = retval.toInt32();
          if (bytesRead <= 0) return;
          
          // 只记录小块读取，避免过多输出
          if (bytesRead <= 32) {
            console.log("[读取] " + this.fileInfo.fileName + " -> " + bytesRead + " 字节");
            
            try {
              // 检查是否是ANS文件头 (00 00 fe fe)
              var data = new Uint8Array(Memory.readByteArray(this.buffer, Math.min(bytesRead, 16)));
              if (data[0] === 0 && data[1] === 0 && data[2] === 0xfe && data[3] === 0xfe) {
                console.log("[文件头] 检测到ANS文件头特征!");
                console.log(hexdump(this.buffer, {length: Math.min(bytesRead, 32)}));
              }
            } catch(e) {}
          } else {
            // 对于大块读取，只简单记录大小
            console.log("[读取] " + this.fileInfo.fileName + " -> " + bytesRead + " 字节");
          }
        }
      });
      console.log("[+] 监控read()调用");
    }
    
    // 4. 监控lseek操作 - 简化版，只监控关键偏移
    var lseek_ptr = Module.findExportByName("libc.so", "lseek");
    if (lseek_ptr) {
      Interceptor.attach(lseek_ptr, {
        onEnter: function(args) {
          this.fd = args[0].toInt32();
          if (ansFileFDs[this.fd] && ansFileFDs[this.fd].isTarget) {
            this.isTargetAns = true;
            this.fileInfo = ansFileFDs[this.fd];
            this.offset = args[1].toInt32();
            this.whence = args[2].toInt32();
            
            // 只记录特殊偏移
            if (this.offset === 24 || this.offset === 0 || this.offset === 8192) {
              var whenceStr = this.whence === 0 ? "SET" : (this.whence === 1 ? "CUR" : "END");
              console.log("[定位] " + this.fileInfo.fileName + " -> 偏移: " + this.offset + ", 模式: " + whenceStr);
            }
          }
        }
      });
      console.log("[+] 监控lseek()调用");
    }
    
    // 5. 延迟执行Java层分析，避免启动时卡顿
    setTimeout(function() {
      Java.perform(function() {
        try {
          console.log("[+] Java层分析开始");
          
          // 基本反调试
          var Debug = Java.use("android.os.Debug");
          Debug.isDebuggerConnected.implementation = function() {
            return false;
          };
          
          // 只检查AjxBLFactoryController类的关键方法
          try {
            var AjxBLFactoryController = Java.use("com.autonavi.jni.ajx3.bl.AjxBLFactoryController");
            console.log("[类] 找到AjxBLFactoryController");
            
            // 监控init4WarmStart方法
            if (AjxBLFactoryController.init4WarmStart) {
              AjxBLFactoryController.init4WarmStart.implementation = function() {
                console.log("[调用] init4WarmStart(" + Array.prototype.slice.call(arguments).join(", ") + ")");
                var result = this.init4WarmStart.apply(this, arguments);
                console.log("[返回] init4WarmStart = " + result);
                return result;
              };
              console.log("[+] 监控init4WarmStart成功");
            }
          } catch(e) {
            console.log("[错误] AjxBLFactoryController分析失败: " + e);
          }
          
          console.log("[+] Java层分析完成");
        } catch(e) {
          console.log("[错误] Java层分析异常: " + e);
        }
      });
    }, 5000); // 延迟5秒执行
    
    console.log("--- [轻量级ANS文件分析] 脚本设置完成 ---");
  })(); 