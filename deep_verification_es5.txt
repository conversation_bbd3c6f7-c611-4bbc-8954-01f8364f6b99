     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[深度验证] 开始严格验证高德地图数据的真实性...
[质疑] 重新审视之前的853次'关键发现'
[1] 建立文件描述符映射...
[\u2713] 文件打开Hook设置成功
[\u2713] 文件读取Hook设置成功
[3] 验证zlib解压数据...
[\u2713] zlib解压Hook设置成功
[深度验证] 严格验证脚本已启动...
[目标] 质疑并严格验证之前的发现
[提示] 请移动地图以触发数据加载，我们将进行严格的真实性验证
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [文件映射] fd=7 -> /dev/binder
[文件映射] fd=11 -> /dev/ashmem
[文件映射] fd=11 -> /dev/ashmem
[文件映射] fd=20 -> /system/framework/QPerformance.jar
[文件映射] fd=20 -> /system/lib64/libqti_performance.so
[文件映射] fd=20 -> /dev/ashmem
[文件映射] fd=20 -> /data/app/com.autonavi.minimap-1/base.apk
[文件映射] fd=21 -> /dev/ashmem
[文件映射] fd=21 -> /data/app/com.autonavi.minimap-1/base.apk
[文件映射] fd=21 -> /data/app/com.autonavi.minimap-1/base.apk
[文件映射] fd=21 -> /data/app/com.autonavi.minimap-1/base.apk
[文件映射] fd=21 -> /data/app/com.autonavi.minimap-1/base.apk
[文件映射] fd=21 -> /data/app/com.autonavi.minimap-1/base.apk
[文件映射] fd=21 -> /data/app/com.autonavi.minimap-1/base.apk
[文件映射] fd=21 -> /data/app/com.autonavi.minimap-1/base.apk
[文件映射] fd=21 -> /dev/ashmem
[文件映射] fd=21 -> /dev/ashmem
[文件映射] fd=21 -> /data/user/0/com.autonavi.minimap/shared_prefs/appLanguage.xml
[文件映射] fd=21 -> /data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/launchTime
[文件映射] fd=21 -> /data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/crashCounter
[文件映射] fd=21 -> /data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/crashCounter
[文件映射] fd=21 -> /dev/ashmem
[文件映射] fd=21 -> /dev/ashmem
[文件映射] fd=21 -> /dev/ashmem
[文件映射] fd=21 -> /dev/ashmem
[文件映射] fd=21 -> /data/user/0/com.autonavi.minimap/shared_prefs/AfpSplashEvents.xml
[文件映射] fd=21 -> /sys/devices/system/cpu
[文件映射] fd=21 -> /dev/ashmem
[文件映射] fd=21 -> /dev/ashmem
[文件映射] fd=21 -> /dev/ashmem
[文件映射] fd=21 -> /sys/devices/system/cpu
[文件映射] fd=21 -> /data/app/com.autonavi.minimap-1/lib/arm64/libmmkv.so
[文件映射] fd=22 -> /dev/ashmem
[文件映射] fd=21 -> /data/user/0/com.autonavi.minimap/files/boot/bootbiz/d3ec0a15fe6c9fc3b985bf0ecff5d529
[文件映射] fd=22 -> /data/user/0/com.autonavi.minimap/files/boot/bootbiz/d3ec0a15fe6c9fc3b985bf0ecff5d529.crc
[文件映射] fd=23 -> /data/app/com.autonavi.minimap-1/lib/arm64/libserverkey.so
[文件映射] fd=23 -> /dev/ashmem
[文件映射] fd=24 -> /dev/ashmem
[文件映射] fd=24 -> /dev/ashmem
[文件映射] fd=24 -> /dev/ashmem
[文件映射] fd=24 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapcrash.so
[文件映射] fd=30 -> /dev/ashmem
[文件映射] fd=30 -> /data/user/0/com.autonavi.minimap/shared_prefs/SharedPreferences.xml
[文件映射] fd=30 -> /dev/ashmem
[文件映射] fd=30 -> /sys/devices/system/cpu
[文件映射] fd=30 -> /sys/devices/system/cpu
[文件映射] fd=30 -> /dev/ashmem
[文件映射] fd=37 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapnsq.so
[文件映射] fd=37 -> /data/user/0/com.autonavi.minimap/files/boot/speedup/5d3830e454f6b0d0c3b0106103275177
[文件映射] fd=40 -> /data/user/0/com.autonavi.minimap/files/boot/speedup/5d3830e454f6b0d0c3b0106103275177.crc
[文件映射] fd=41 -> /dev/ashmem
[文件映射] fd=41 -> /dev/ashmem
[文件映射] fd=41 -> /dev/ashmem
[文件映射] fd=41 -> /dev/ashmem
[文件映射] fd=41 -> /dev/ashmem
[文件映射] fd=38 -> /data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_value.xml
[文件映射] fd=42 -> /data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_all_value.xml
[文件映射] fd=43 -> /data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_white_list_key_value.xml
[文件映射] fd=44 -> /dev/ashmem
[文件映射] fd=44 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/journal
[文件映射] fd=41 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/journal
[文件映射] fd=44 -> /data/user/0/com.autonavi.minimap/files/ajx_web_resources/journal
[文件映射] fd=44 -> /data/user/0/com.autonavi.minimap/files/ajx_web_resources/journal
[文件映射] fd=45 -> /data/user/0/com.autonavi.minimap/files/ajx_web3_resources/journal
[文件映射] fd=45 -> /data/user/0/com.autonavi.minimap/files/ajx_web3_resources/journal
[文件映射] fd=46 -> /sys/devices/system/cpu
[文件映射] fd=46 -> /dev/ashmem
[文件映射] fd=46 -> /data/app/com.autonavi.minimap-1/lib/arm64/libc++_shared.so
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/shared_prefs/online_monitor.xml
[文件映射] fd=46 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapmain.so
[文件映射] fd=46 -> /dev/ashmem
[文件映射] fd=47 -> /dev/ashmem
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/shared_prefs/intellectBusMode.xml
[文件映射] fd=46 -> /dev/ashmem
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/shared_prefs/invitation_code_name_space.xml
[文件映射] fd=46 -> /dev/ashmem
[文件映射] fd=43 -> /data/user/0/com.autonavi.minimap/shared_prefs/VDiskUtil.xml
[文件映射] fd=43 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapstore.so
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/boot/speedup/616fc58e2ddf29fca98c00cbe7593007
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/boot/speedup/616fc58e2ddf29fca98c00cbe7593007.crc
[文件映射] fd=43 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamaplog.so
[文件映射] fd=43 -> /dev/ashmem
[文件映射] fd=43 -> /dev/ashmem
[文件映射] fd=43 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/20kc8q3fz51z2o64rdozxpi8u0
[文件映射] fd=48 -> /dev/ashmem
[文件映射] fd=43 -> /dev/ashmem
[文件映射] fd=43 -> /data/user/0/com.autonavi.minimap/shared_prefs/AuiCache.xml
[文件映射] fd=48 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/4yuoyy4o1z64ct9ogsjuceze80
[文件映射] fd=43 -> /dev/ashmem
[文件映射] fd=43 -> /data/user/0/com.autonavi.minimap/files/databases/db_stat.dat
[文件映射] fd=49 -> /dev/ashmem
[文件映射] fd=49 -> /dev/ashmem
[文件映射] fd=48 -> /data/user/0/com.autonavi.minimap/shared_prefs/sp_encryt_sharepreference.xml
[文件映射] fd=50 -> /data/user/0/com.autonavi.minimap/shared_prefs/GenID.xml
[文件映射] fd=49 -> /dev/ashmem
[文件映射] fd=48 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/38gb5m4zru035vlmxsw6stbho0
[文件映射] fd=49 -> /dev/ashmem
[文件映射] fd=49 -> /dev/ashmem
[文件映射] fd=50 -> /dev/ashmem
[文件映射] fd=49 -> /data/user/0/com.autonavi.minimap/shared_prefs/user_route_method_info.xml
[文件映射] fd=48 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/56ehfo4anq09txlwawho8j95d0
[文件映射] fd=38 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/75tx8qjyzwdmcna6efn5mkf1v0
[文件映射] fd=38 -> /dev/ashmem
[文件映射] fd=38 -> /dev/ashmem
[文件映射] fd=38 -> /data/user/0/com.autonavi.minimap/shared_prefs/network_traffic_stats.xml
[文件映射] fd=48 -> /data/user/0/com.autonavi.minimap/files/logs/alc/tra.cfg
[文件映射] fd=38 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/4nvo0sp7h0g3t33v5aot5pmr10
[文件映射] fd=48 -> /dev/ashmem
[文件映射] fd=48 -> /dev/ashmem
[文件映射] fd=49 -> /data/user/0/com.autonavi.minimap/shared_prefs/sp_network_ab_test.xml
[文件映射] fd=48 -> /data/user/0/com.autonavi.minimap/files/logs/alc/tra.cfg
[文件映射] fd=38 -> /data/user/0/com.autonavi.minimap/files/logs/alc/e
[文件映射] fd=38 -> /data/user/0/com.autonavi.minimap/files/logs/alc/e/ANDH151900_20250814154933_16
[文件映射] fd=42 -> /data/user/0/com.autonavi.minimap/files/logs/alc/w
[文件映射] fd=42 -> /data/user/0/com.autonavi.minimap/files/logs/alc/w/ANDH151900_20250814085904_0
[文件映射] fd=48 -> /storage/emulated/0/Android/data/com.autonavi.minimap/cache/anr_traces/.anr_status.txt
[文件映射] fd=48 -> /storage/emulated/0/Android/data/com.autonavi.minimap/cache/anr_traces/.anr_status.txt
[文件映射] fd=48 -> /dev/ashmem
[文件映射] fd=48 -> /data/user/0/com.autonavi.minimap/shared_prefs/ACPrefUtils.xml
[文件映射] fd=48 -> /dev/ashmem
[文件映射] fd=50 -> /dev/ashmem
[文件映射] fd=52 -> /storage/emulated/0/Android/data/com.autonavi.minimap/cache/anr_traces/.anr_status.txt
[文件映射] fd=52 -> /dev/ashmem
[文件映射] fd=52 -> /data/user/0/com.autonavi.minimap/shared_prefs/sp_network_config.xml
[文件映射] fd=53 -> /dev/ashmem
[文件映射] fd=52 -> /dev/ashmem
[文件映射] fd=52 -> /data/user/0/com.autonavi.minimap/files/logs/alc/paas.network/storage
[文件映射] fd=53 -> /data/user/0/com.autonavi.minimap/shared_prefs/main_page.xml
[文件映射] fd=52 -> /data/user/0/com.autonavi.minimap/files/logs/alc/paas.network/storage/ANDH151900_20250814155007_39
[文件映射] fd=53 -> /data/user/0/com.autonavi.minimap/shared_prefs/main_page.xml
[文件映射] fd=53 -> /data/user/0/com.autonavi.minimap/files/boot/cf611119a2491fb9519f0ba78b552850
[文件映射] fd=53 -> /dev/ashmem
[文件映射] fd=55 -> /dev/ashmem
[文件映射] fd=55 -> /dev/ashmem
[文件映射] fd=55 -> /data/user/0/com.autonavi.minimap/shared_prefs/basemap.xml
[文件映射] fd=55 -> /dev/ashmem
[文件映射] fd=55 -> /data/user/0/com.autonavi.minimap/shared_prefs/amaphome.xml
[文件映射] fd=56 -> /dev/ashmem
[文件映射] fd=55 -> /data/user/0/com.autonavi.minimap/files/logs/alc/i/storage
[文件映射] fd=55 -> /data/user/0/com.autonavi.minimap/files/logs/alc/i/storage/ANDH151900_20250814143606_3
[文件映射] fd=56 -> /dev/ashmem
[文件映射] fd=57 -> /dev/ashmem
[文件映射] fd=56 -> /data/app/com.autonavi.minimap-1/lib/arm64/libajxbiz2.so
[文件映射] fd=57 -> /dev/ashmem
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/shared_prefs/amap_perf_schedule.xml
[文件映射] fd=56 -> /data/user/0/com.autonavi.minimap/files/.7934039a7252be16/9983c160aa044115
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.biz2/storage
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.biz2/storage/ANDH151900_20250814155008_61
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/cklist.data
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/local_bundles_map.bin
[文件映射] fd=59 -> /dev/ashmem
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/abandon.bin
[文件映射] fd=59 -> /dev/ashmem
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/shared_prefs/Alvin2.xml
[文件映射] fd=57 -> /storage/emulated/0/.UTSystemConfig/Global/Alvin2.xml
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/files/bizdata/langpack/tables.dat
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/files/bizdata/langpack/recycle.timestamp
[文件映射] fd=59 -> /dev/ashmem
[文件映射] fd=57 -> /dev/ashmem
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/files/.7934039a7252be16/a325712a39bd320a
[文件映射] fd=56 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapr.so
[文件映射] fd=59 -> /dev/ashmem
[文件映射] fd=56 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/35E7BA1884044E408DDFB36A39CDDA60/849BF7418DA74422A13120E3E4F3DEF7
[文件映射] fd=56 -> /dev/ashmem
[文件映射] fd=59 -> /data/user/0/com.autonavi.minimap/files/.7934039a7252be16/d48d3759078396c6
[文件映射] fd=57 -> /dev/ashmem
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/shared_prefs/qtsession.xml
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/files/.qmt/cache2/ANDROID_I_K
[文件映射] fd=57 -> /dev/ashmem
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/shared_prefs/arch.xml
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/shared_prefs/arch.xml
[文件映射] fd=59 -> /dev/ashmem
[文件映射] fd=56 -> /data/user/0/com.autonavi.minimap/files/.qmt/cache2/wsidcache
[文件映射] fd=56 -> /data/user/0/com.autonavi.minimap/files/.qmt/qfs
[文件映射] fd=57 -> /dev/ashmem
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/files/.qmt/config/config
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/cache/QCahe
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/cache/QCahe/1923307013
[文件映射] fd=59 -> /dev/ashmem
[文件映射] fd=57 -> /data/user/0/com.autonavi.minimap/files/.qmt/cache2/wsidcache
[文件映射] fd=57 -> /sys/devices/system/cpu
[文件映射] fd=56 -> /data/user/0/com.autonavi.minimap/shared_prefs/QT.xml
[文件映射] fd=56 -> /dev/ashmem
[文件映射] fd=59 -> /data/user/0/com.autonavi.minimap/cache/QCahe/1923307013
[文件映射] fd=60 -> /dev/ashmem
[文件映射] fd=59 -> /data/user/0/com.autonavi.minimap/shared_prefs/base_path.xml
[文件映射] fd=60 -> /dev/ashmem
[文件映射] fd=60 -> /dev/ashmem
[文件映射] fd=59 -> /dev/ashmem
[文件映射] fd=59 -> /data/user/0/com.autonavi.minimap/shared_prefs/first_install.xml
[文件映射] fd=59 -> /dev/ashmem
[文件映射] fd=59 -> /data/user/0/com.autonavi.minimap/shared_prefs/com.amap.bundle.location.locator.module.LocationStorage.xml
[文件映射] fd=59 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_smart_temp/
[文件映射] fd=59 -> /data/user/0/com.autonavi.minimap/cache/QCahe/1923307013
[文件映射] fd=60 -> /data/user/0/com.autonavi.minimap/files/cache/offlinemap_lite_v3/ackor_offline_compile.db
[文件映射] fd=61 -> /dev/urandom
[可疑数据] TEXT (置信度: 40%)
  警告: 后续数据不像文本
[真实地图数据] TEXT (置信度: 60%)
  文件: /dev/ashmem
  大小: 1024 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /dev/ashmem
  大小: 1024 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=59 -> /dev/ashmem
[文件映射] fd=59 -> /data/user/0/com.autonavi.minimap/files/.qmt/qfs
[文件映射] fd=59 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/geo_fence_global_v2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=59 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/geo_fence_global_v2.ans
[文件映射] fd=60 -> /data/user/0/com.autonavi.minimap/files/.qmt/qfs/qfs.js-20250814
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=60 -> /data/user/0/com.autonavi.minimap/files/cache/offlinemap_lite_v3/offlineLiteConfig.db
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=61 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[文件映射] fd=60 -> /data/user/0/com.autonavi.minimap/files/cache/offlinemap_lite_v3/offlineLiteConfig.db
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=62 -> /data/user/0/com.autonavi.minimap/files/mock/perf/3eb18c8045a67a1ab0e7a4e04603700e
[文件映射] fd=63 -> /data/user/0/com.autonavi.minimap/files/mock/perf/3eb18c8045a67a1ab0e7a4e04603700e.crc
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/urandom
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=61 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/urandom
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] TEXT (置信度: 60%)
  文件: /dev/urandom
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=60 -> /data/user/0/com.autonavi.minimap/files/.qmt/qfs/qfs.js-20250814
[文件映射] fd=61 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/urandom
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=60 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] TEXT (置信度: 60%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=60 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[文件映射] fd=61 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/bundle_list.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/urandom
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=60 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[文件映射] fd=61 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/bundle_list.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/urandom
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] TEXT (置信度: 60%)
  文件: /dev/urandom
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=64 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
[文件映射] fd=60 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=60 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[文件映射] fd=64 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=65 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
[真实地图数据] TEXT (置信度: 60%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=60 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[文件映射] fd=65 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=60 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=66 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/earth_bundle_15.12.0.49.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/earth_bundle_15.12.0.49.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=60 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/earth_bundle_15.12.0.49.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] TEXT (置信度: 60%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=66 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1
[文件映射] fd=66 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapdsl.so
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755075669390535.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755075809288095.dat
[文件映射] fd=68 -> /data/user/0/com.autonavi.minimap/files/boot/dtoken
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755076226096715.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755076625380662.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755076666935659.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755077601103007.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755077806586419.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755133147366311.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755133489629645.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755134332015412.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755135096734586.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755135648875989.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755136192709044.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755136333217876.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755136404226077.dat
[文件映射] fd=67 -> /dev/ashmem
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755136735415817.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755136896341345.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755137016114454.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755137076177658.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755137158231364.dat
[文件映射] fd=67 -> /dev/ashmem
[文件映射] fd=67 -> /data/user/0/com.autonavi.minimap/shared_prefs/ut_setting.xml
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755137618214918.dat
[文件映射] fd=68 -> /data/user/0/com.autonavi.minimap/shared_prefs/sp_loc_bundle_switch.xml
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755138417431823.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755149864146156.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755151148126388.dat
[文件映射] fd=67 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/res/global.db
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755151191517166.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755151291109086.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755152479636824.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755152570758989.dat
[文件映射] fd=68 -> /dev/ashmem
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755152627143244.dat
[文件映射] fd=66 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755153124855440.dat
[文件映射] fd=69 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755153223274609.dat
[文件映射] fd=69 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755153309647772.dat
[文件映射] fd=67 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/res/global.db
[文件映射] fd=67 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755153368352159.dat
[文件映射] fd=70 -> /dev/ashmem
[文件映射] fd=69 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/res/global.db
[文件映射] fd=67 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755154011738346.dat
[文件映射] fd=67 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755154123582642.dat
[文件映射] fd=67 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755154208666740.dat
[文件映射] fd=67 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755157093301278.dat
[文件映射] fd=67 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755157805062138.dat
[文件映射] fd=67 -> /dev/ashmem
[文件映射] fd=67 -> /dev/ashmem
[文件映射] fd=67 -> /dev/ashmem
[文件映射] fd=67 -> /dev/ashmem
[文件映射] fd=67 -> /dev/ashmem
[文件映射] fd=67 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_b73f4b7f6aeeefb926b2a7efec64421d
[文件映射] fd=71 -> /dev/ashmem
[文件映射] fd=71 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_b73f4b7f6aeeefb926b2a7efec64421d
[文件映射] fd=67 -> /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.biz/storage
[文件映射] fd=67 -> /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.biz/storage/ANDH151900_20250814155139_49
[文件映射] fd=72 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapsync.so
[文件映射] fd=73 -> /dev/ashmem
[文件映射] fd=73 -> /dev/ashmem
[文件映射] fd=72 -> /dev/ashmem
[文件映射] fd=73 -> /dev/ashmem
[文件映射] fd=72 -> /dev/ashmem
[文件映射] fd=72 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapaccount.so
[文件映射] fd=72 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapsync.so
[文件映射] fd=73 -> /data/user/0/com.autonavi.minimap/files/girf_sync.db
[文件映射] fd=74 -> /data/user/0/com.autonavi.minimap/shared_prefs/accs_network.xml
[文件映射] fd=74 -> /dev/ashmem
[文件映射] fd=72 -> /data/user/0/com.autonavi.minimap/files/account/userinfo_config
[文件映射] fd=72 -> /dev/ashmem
[文件映射] fd=74 -> /dev/ashmem
[文件映射] fd=72 -> /dev/ashmem
[文件映射] fd=72 -> /dev/ashmem
[文件映射] fd=72 -> /data/user/0/com.autonavi.minimap/shared_prefs/device_info_cloud_sp.xml
[文件映射] fd=72 -> /sys/devices/system/cpu
[文件映射] fd=72 -> /dev/ashmem
[文件映射] fd=72 -> /dev/ashmem
[文件映射] fd=75 -> /dev/ashmem
[文件映射] fd=75 -> /data/user/0/com.autonavi.minimap/files/checkNoSpace.txt
[文件映射] fd=75 -> /data/user/0/com.autonavi.minimap/files/checkNoSpace.txt
[文件映射] fd=75 -> /dev/ashmem
[文件映射] fd=75 -> /dev/ashmem
[文件映射] fd=75 -> /data/user/0/com.autonavi.minimap/shared_prefs/sp_appearance_config.xml
[文件映射] fd=75 -> /dev/ashmem
[文件映射] fd=75 -> /data/user/0/com.autonavi.minimap/shared_prefs/network_config.xml
[文件映射] fd=75 -> /dev/ashmem
[文件映射] fd=75 -> /dev/ashmem
[文件映射] fd=75 -> /dev/ashmem
[文件映射] fd=75 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=76 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=77 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=78 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=79 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=80 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=81 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=82 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=83 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=84 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=87 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=88 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=89 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=90 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=91 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=92 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=93 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=94 -> /data/user/0/com.autonavi.minimap/files/logs/alc/paas.network
[文件映射] fd=94 -> /data/user/0/com.autonavi.minimap/files/logs/alc/paas.network/ANDH151900_20250814155023_65
[文件映射] fd=95 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=96 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=97 -> /data/user/0/com.autonavi.minimap/files/.retain_fd_tmp.txt
[文件映射] fd=98 -> /dev/ashmem
[文件映射] fd=98 -> /data/user/0/com.autonavi.minimap/files/logs/alc/infoservice.trace
[文件映射] fd=98 -> /data/user/0/com.autonavi.minimap/files/logs/alc/infoservice.trace/ANDH151900_20250814085905_0
[文件映射] fd=99 -> /dev/ashmem
[文件映射] fd=99 -> /sys/devices/system/cpu
[文件映射] fd=53 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_4d9b34b5cf5fce4aa911161f2376b73f
[文件映射] fd=54 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_4d9b34b5cf5fce4aa911161f2376b73f
[文件映射] fd=53 -> /dev/ashmem
[文件映射] fd=53 -> /dev/ashmem
[文件映射] fd=53 -> /dev/ashmem
[文件映射] fd=53 -> /dev/ashmem
[文件映射] fd=53 -> /dev/ashmem
[文件映射] fd=101 -> /proc/self/task/23798/stat
[文件映射] fd=101 -> /proc/self/task/23801/stat
[文件映射] fd=101 -> /proc/self/task/23808/stat
[文件映射] fd=101 -> /proc/self/task/23809/stat
[文件映射] fd=101 -> /proc/self/task/23811/stat
[文件映射] fd=101 -> /proc/self/task/23810/stat
[文件映射] fd=101 -> /proc/self/task/23818/stat
[文件映射] fd=101 -> /proc/self/task/23819/stat
[文件映射] fd=101 -> /proc/self/task/23823/stat
[文件映射] fd=101 -> /proc/self/task/23824/stat
[文件映射] fd=101 -> /proc/self/task/23825/stat
[文件映射] fd=101 -> /proc/self/task/23827/stat
[文件映射] fd=101 -> /proc/self/task/23829/stat
[文件映射] fd=101 -> /proc/self/task/23830/stat
[文件映射] fd=101 -> /proc/self/task/23848/stat
[文件映射] fd=101 -> /proc/self/task/23860/stat
[文件映射] fd=101 -> /proc/self/task/23863/stat
[文件映射] fd=101 -> /proc/self/task/23864/stat
[文件映射] fd=101 -> /proc/self/task/23868/stat
[文件映射] fd=101 -> /proc/self/task/23844/stat
[文件映射] fd=101 -> /proc/self/task/23882/stat
[文件映射] fd=101 -> /proc/self/task/23893/stat
[文件映射] fd=101 -> /proc/self/task/23847/stat
[文件映射] fd=101 -> /proc/self/task/23899/stat
[文件映射] fd=101 -> /proc/self/task/23902/stat
[文件映射] fd=101 -> /proc/self/task/23900/stat
[文件映射] fd=101 -> /proc/self/task/23905/stat
[文件映射] fd=101 -> /proc/self/task/23908/stat
[文件映射] fd=101 -> /proc/self/task/23916/stat
[文件映射] fd=101 -> /proc/self/task/23948/stat
[文件映射] fd=101 -> /proc/self/task/23949/stat
[文件映射] fd=101 -> /proc/self/task/23950/stat
[文件映射] fd=101 -> /proc/self/task/23951/stat
[文件映射] fd=101 -> /proc/self/task/23953/stat
[文件映射] fd=101 -> /proc/self/task/23954/stat
[文件映射] fd=101 -> /proc/self/task/23955/stat
[文件映射] fd=101 -> /proc/self/task/23956/stat
[文件映射] fd=101 -> /proc/self/task/23957/stat
[文件映射] fd=101 -> /proc/self/task/23964/stat
[文件映射] fd=101 -> /proc/self/task/23969/stat
[文件映射] fd=101 -> /proc/self/task/23970/stat
[文件映射] fd=101 -> /proc/self/task/23980/stat
[文件映射] fd=101 -> /proc/self/task/23859/stat
[文件映射] fd=101 -> /proc/self/task/23987/stat
[文件映射] fd=101 -> /proc/self/task/23988/stat
[文件映射] fd=101 -> /proc/self/task/23843/stat
[文件映射] fd=101 -> /proc/self/task/23989/stat
[文件映射] fd=101 -> /dev/ashmem
[文件映射] fd=103 -> /dev/ashmem
[文件映射] fd=103 -> /data/user/0/com.autonavi.minimap/shared_prefs/ACIntensiveTask_MAIN.xml
[文件映射] fd=103 -> /dev/ashmem
[文件映射] fd=106 -> /dev/ashmem
[文件映射] fd=106 -> /dev/ashmem
[文件映射] fd=106 -> /data/user/0/com.autonavi.minimap/shared_prefs/VOtaSpUtil.xml
[文件映射] fd=106 -> /dev/ashmem
[文件映射] fd=106 -> /dev/ashmem
[文件映射] fd=106 -> /dev/ashmem
[文件映射] fd=106 -> /dev/ashmem
[文件映射] fd=110 -> /dev/kgsl-3d0
[文件映射] fd=111 -> /dev/ion
[文件映射] fd=112 -> /sys/devices/system/cpu
[文件映射] fd=113 -> /dev/ashmem
[文件映射] fd=112 -> /data/user/0/com.autonavi.minimap/shared_prefs/SCENE_RECOMMEND.xml
[文件映射] fd=112 -> /dev/ashmem
[文件映射] fd=112 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=112 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=112 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/vmap/1/amapstream_1755158272810821.dat
[文件映射] fd=113 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font
[文件映射] fd=113 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755075789453296.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755075809676620.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755076226470795.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755076625756636.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755076667080394.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755077601380184.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755077651503079.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755077651611621.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755077806791140.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755133147534378.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755133489821168.dat
[文件映射] fd=114 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a0/m1.ans
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755133555259195.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755133555365397.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755134332165467.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135096943304.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135649091052.dat
[文件映射] fd=113 -> /dev/ashmem
[文件映射] fd=115 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
[文件映射] fd=115 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755136193081534.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755136333526825.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755136404375763.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755136735496787.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755136896437919.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755137016211668.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755137076286625.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755137158302434.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755137618338137.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755138417564285.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755149864328498.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=115 -> /data/app/com.autonavi.minimap-1/lib/arm64/liblinkProxy-1.0.0.so
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755151148310851.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755151191750056.dat
[文件映射] fd=115 -> /dev/ashmem
[可疑数据] TEXT (置信度: 40%)
  警告: 后续数据不像文本
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755151252032208.dat
[文件映射] fd=115 -> /dev/ashmem
[文件映射] fd=115 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamaploc.so
[文件映射] fd=115 -> /dev/ashmem
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755151252222921.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755151291261648.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755152479859158.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755152570884115.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755152627372405.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755153125083711.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755153223464169.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755153309892084.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755153368548532.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755154011956372.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755154123866397.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755154209005539.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755157093525105.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755157805472171.dat
[文件映射] fd=115 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755075789053822.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755075813333373.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755076227004423.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755076626153794.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755076668127705.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755077604026316.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755077651108706.dat
[文件映射] fd=115 -> /system/app/WebViewGoogle/WebViewGoogle.apk
[文件映射] fd=116 -> /dev/ashmem
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755077806651446.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755133149581741.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755133490681966.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755133554982031.dat
[文件映射] fd=115 -> /system/app/WebViewGoogle/WebViewGoogle.apk
[文件映射] fd=115 -> /data/misc/shared_relro/libwebviewchromium64.relro
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755134333098921.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755135098727054.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755135650546332.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755135780076729.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755136192932505.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755136333313473.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755136404315937.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755136735474076.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755136896392740.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755137016146105.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755137076209070.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755137158270391.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755137618996501.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755138418374918.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755149864264859.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755151130871401.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755151148385180.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755151191733418.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755151251598065.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755151291251147.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755152479748632.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755152570853736.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755152627246364.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755153114189950.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755153124950126.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755153223383683.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755153309891063.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755153368451225.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755153994249400.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755154011950809.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755154123700182.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755154208839194.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755154636389642.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755157093512908.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755157715722733.dat
[文件映射] fd=119 -> /proc/self/maps
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755157805467899.dat
[文件映射] fd=113 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755157899308147.dat
[文件映射] fd=113 -> /proc/self/maps
[文件映射] fd=119 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/lane/0/amapstream_1755158273245436.dat
[文件映射] fd=113 -> /proc/self/maps
[文件映射] fd=113 -> /proc/self/stat
[文件映射] fd=120 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755158273247011.dat
[文件映射] fd=113 -> /proc/self/maps
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=122 -> /dev/ion
[文件映射] fd=117 -> /data/app/com.autonavi.minimap-1/lib/arm64/libajx.so
[文件映射] fd=116 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=116 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=116 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=116 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=117 -> /system/app/WebViewGoogle/WebViewGoogle.apk
[文件映射] fd=115 -> /system/app/WebViewGoogle/WebViewGoogle.apk
[文件映射] fd=115 -> /dev/ashmem
[文件映射] fd=116 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=116 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[文件映射] fd=123 -> /dev/ashmem
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=115 -> /dev/ashmem
[文件映射] fd=115 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[文件映射] fd=116 -> /data/user/0/com.autonavi.minimap/code_cache/com.android.opengl.shaders_cache
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=115 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=123 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=115 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=115 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
[文件映射] fd=124 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapbadge.so
[文件映射] fd=123 -> /dev/ashmem
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=123 -> /system/app/WebViewGoogle/WebViewGoogle.apk
[文件映射] fd=115 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=116 -> /system/lib64/libwebviewchromium_plat_support.so
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=116 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamaphorus.so
[文件映射] fd=116 -> /dev/ashmem
[文件映射] fd=124 -> /data/user/0/com.autonavi.minimap/shared_prefs/WebViewChromiumPrefs.xml
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=116 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[可疑数据] TEXT (置信度: 40%)
  警告: 后续数据不像文本
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=116 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=116 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=116 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=131 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=116 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=129 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamaptbt.so
[文件映射] fd=116 -> /data/user/0/com.autonavi.minimap/shared_prefs/com.autonavi.minimap_preferences.xml
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=116 -> /dev/ashmem
[文件映射] fd=116 -> /dev/ashmem
[文件映射] fd=116 -> /data/user/0/com.autonavi.minimap/files/awcn_strategy
[文件映射] fd=116 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamaptts.so
[文件映射] fd=129 -> /data/user/0/com.autonavi.minimap/files/awcn_strategy/StrategyConfig
[文件映射] fd=129 -> /dev/ashmem
[文件映射] fd=129 -> /data/user/0/com.autonavi.minimap/files/awcn_strategy
[文件映射] fd=116 -> /data/user/0/com.autonavi.minimap/files/awcn_strategy/WIFI$100017_320100_4
[文件映射] fd=129 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/avatar/34a474bb2d50baea3f32892db55c1a81
[文件映射] fd=129 -> /dev/ashmem
[文件映射] fd=116 -> /dev/ashmem
[文件映射] fd=116 -> /data/user/0/com.autonavi.minimap/shared_prefs/MapTextSizeSet.xml
[文件映射] fd=129 -> /dev/ashmem
[文件映射] fd=129 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapvcs.so
[文件映射] fd=116 -> /dev/ashmem
[文件映射] fd=116 -> /dev/ashmem
[文件映射] fd=116 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=116 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=129 -> /dev/ashmem
[文件映射] fd=129 -> /dev/ashmem
[文件映射] fd=129 -> /data/user/0/com.autonavi.minimap/files/cloud_plugin_info_1202.txt
[文件映射] fd=129 -> /dev/ashmem
[文件映射] fd=129 -> /dev/ashmem
[文件映射] fd=137 -> /dev/ashmem
[文件映射] fd=137 -> /dev/ashmem
[文件映射] fd=137 -> /dev/ashmem
[文件映射] fd=138 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
[文件映射] fd=139 -> /dev/ashmem
[文件映射] fd=137 -> /data/app/com.autonavi.minimap-1/lib/arm64/libMNN.so
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=138 -> /data/app/com.autonavi.minimap-1/lib/arm64/libMNN_Express.so
[文件映射] fd=141 -> /data/app/com.autonavi.minimap-1/lib/arm64/libamapml.so
[文件映射] fd=140 -> /data/user/0/com.autonavi.minimap/shared_prefs/sp_switch_cloud_main.xml
[文件映射] fd=138 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=140 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=141 -> /dev/ashmem
[文件映射] fd=142 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755075743133713.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/shared_prefs/sp_switch_cloud_main.xml
[文件映射] fd=142 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755075789160381.dat
[文件映射] fd=141 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755075789402222.dat
[文件映射] fd=141 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755075811018601.dat
[文件映射] fd=141 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755075815976877.dat
[文件映射] fd=142 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.nal
[文件映射] fd=141 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755075935935570.dat
[文件映射] fd=141 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755076055939635.dat
[文件映射] fd=143 -> /dev/urandom
[文件映射] fd=141 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755076229283189.dat
[文件映射] fd=141 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755076234601765.dat
[文件映射] fd=141 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755076354395909.dat
[文件映射] fd=141 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755076474397847.dat
[文件映射] fd=143 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data
[文件映射] fd=141 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755076629300032.dat
[文件映射] fd=141 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755076668331592.dat
[文件映射] fd=140 -> /dev/ashmem
[文件映射] fd=143 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755076673474555.dat
[文件映射] fd=144 -> /dev/ashmem
[文件映射] fd=144 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755076793419860.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755076913420516.dat
[文件映射] fd=140 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755077033426876.dat
[文件映射] fd=140 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755077153431343.dat
[文件映射] fd=140 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755077273435478.dat
[文件映射] fd=140 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755077393438840.dat
[文件映射] fd=146 -> /sys/devices/system/cpu
[文件映射] fd=146 -> /dev/ashmem
[文件映射] fd=140 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755077513454036.dat
[文件映射] fd=140 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755077602605370.dat
[文件映射] fd=146 -> /dev/ashmem
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755077607526134.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755077651558687.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755077727514450.dat
[文件映射] fd=138 -> /dev/ashmem
[文件映射] fd=146 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755077808699061.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133149076616.dat
[文件映射] fd=148 -> /dev/ashmem
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133153844968.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133491223952.dat
[文件映射] fd=140 -> /data/user/0/com.autonavi.minimap/databases/com.autonavi.minimap_uptunnel.db
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133496141861.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133554986996.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133555298441.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133616116254.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133736126113.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133856123075.dat
[文件映射] fd=148 -> /dev/ashmem
[文件映射] fd=149 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133976130602.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755134096128340.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755134216134189.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/shared_prefs/sp_picktimes_cloud.xml
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755134333521965.dat
[文件映射] fd=148 -> /data/user/0/com.autonavi.minimap/shared_prefs/com.autonavi.minimap_tunnel.xml
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755134338690670.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755134458558894.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755135098366030.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755135103065676.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755135650517414.dat
[文件映射] fd=149 -> /dev/ashmem
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/databases/com.autonavi.minimap_uptunnel.db-journal
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755135655495892.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755135775475805.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755135780226216.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755135895454034.dat
[文件映射] fd=145 -> /dev/ashmem
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755136015461855.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755136195816483.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755136200534098.dat
[文件映射] fd=145 -> /dev/ashmem
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755136337370478.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755136406765952.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755136738254646.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755136898148303.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755137017894983.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755137077822201.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/databases/com.autonavi.minimap_uptunnel.db-journal
[文件映射] fd=149 -> /data/user/0/com.autonavi.minimap/databases/com.autonavi.minimap_uptunnel.db-journal
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/shared_prefs/OptRecordLogWrapper_sp.xml
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755137160031855.dat
[文件映射] fd=151 -> /data/user/0/com.autonavi.minimap/databases/com.autonavi.minimap_uptunnel.db-journal
[文件映射] fd=151 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/unlocal_bundles_map.bin
[文件映射] fd=149 -> /data/user/0/com.autonavi.minimap/files/amapml
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_tunnel_pf
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_fmm_common
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/sa_deep_model
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_fmm_yaw_fusion
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/deviceml/amapml.db
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_pos_fusion
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755137619472816.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_pos_fusion_gru
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_pos_lane_model
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755137624664724.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_confidence
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_de10ad2ea98bf87a253ee23d8b1e93da
[文件映射] fd=149 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.biz2/storage/ANDH151900_20250814155754_62
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133976130602.dat
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=149 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755133976130602.dat
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755137744668971.dat
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755137864677273.dat
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755137984678580.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_confidence/1.0.0.2/meta1.json
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/cellage_com.autonavi.minimap
[文件映射] fd=151 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755138070687519.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_confidence/1.0.0.2/vdr_confidence.mnn
[文件映射] fd=151 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755138104685207.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_confidence/1.0.0.2/meta1.json
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755138224681180.dat
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755138344685610.dat
[文件映射] fd=151 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_confidence/1.0.0.2/vdr_confidence.mnn
[文件映射] fd=151 -> /dev/ashmem
[文件映射] fd=151 -> /sys/devices/system/cpu/cpufreq
[文件映射] fd=138 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755138418898188.dat
[文件映射] fd=152 -> /dev/ashmem
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/wifiage_com.autonavi.minimap
[文件映射] fd=58 -> /dev/ashmem
[文件映射] fd=58 -> /dev/ashmem
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755138423783492.dat
[文件映射] fd=138 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755138543764101.dat
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755138663770381.dat
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755138783773437.dat
[文件映射] fd=58 -> /dev/ashmem
[文件映射] fd=138 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/databases/aMap.db
[文件映射] fd=151 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755138903773768.dat
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_03ac7834c73e5e8d84d6c725656f80e7
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=151 -> /data/user/0/com.autonavi.minimap/shared_prefs/sp_common.xml
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755139023782833.dat
[文件映射] fd=155 -> /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755139044421943.dat
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755139143781001.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755139263783736.dat
[文件映射] fd=151 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755149866885340.dat
[文件映射] fd=151 -> /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755149870908646.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755149990854798.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755150110864606.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755150230864716.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755150350871283.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755150470874993.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755150590879025.dat
[文件映射] fd=151 -> /data/user/0/com.autonavi.minimap/databases/aMap.db
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/crash/phase
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755150710890508.dat
[文件映射] fd=155 -> /data/user/0/com.autonavi.minimap/files/fences/gnss
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755150830885155.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/crash/combine/main_1755157802094_crash.txt
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/crash/phase/main_1755157802094/core_log_native.txt
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_tunnel_pf/1.0.0.2/meta1.json
[文件映射] fd=155 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755150950884819.dat
[可疑数据] TEXT (置信度: 40%)
  警告: 后续数据不像文本
[文件映射] fd=155 -> /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
[文件映射] fd=155 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_tunnel_pf/1.0.0.2/vdr_tunnel_pf.mnn
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/crash/phase/main_1755157802094/device_runtime_info.txt.decode
[文件映射] fd=156 -> /dev/ashmem
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755151070891941.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755151131135220.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755151138446361.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755151150805650.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755151195425392.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755151199022292.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755151294021860.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/crash/phase/main_1755157802094/logcat_native.txt
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/crash/phase/main_1755157802094/kv_log_native.txt
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/crash/phase/main_1755157802094/aae_log_native.txt
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/shared_prefs/com.autonavi.minimap.xml
[文件映射] fd=157 -> /dev/ashmem
[文件映射] fd=158 -> /data/user/0/com.autonavi.minimap/files/crash/phase/main_1755157802094/append_native.txt
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755152482492216.dat
[文件映射] fd=160 -> /data/user/0/com.autonavi.minimap/files/crash/phase/main_1755157802094/nrt_log_native.txt
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/crash/combine/main_1755157802094_crash.txt
[文件映射] fd=156 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[文件映射] fd=157 -> /dev/ashmem
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755152486627404.dat
[文件映射] fd=157 -> /dev/ashmem
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755152572514027.dat
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755152628909549.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_tunnel_pf/1.0.0.2/meta1.json
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=156 -> /dev/ashmem
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.eng/storage
[文件映射] fd=157 -> /dev/ashmem
[文件映射] fd=156 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[文件映射] fd=154 -> /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.eng/storage/ANDH151900_20250814153833_13
[文件映射] fd=161 -> /dev/ashmem
[文件映射] fd=160 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755152633912197.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_tunnel_pf/1.0.0.2/meta1.json
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=157 -> /dev/ashmem
[文件映射] fd=157 -> /dev/ashmem
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153118354207.dat
[文件映射] fd=160 -> /data/user/0/com.autonavi.minimap/databases/location_cache_new.db
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/databases/location_cache_new.db-journal
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/databases/location_cache_new.db-journal
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/databases/location_cache_new.db-journal
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/databases/location_cache_new.db-journal
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/hisloc_com.autonavi.minimap
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153128138204.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153132491783.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153225940566.dat
[文件映射] fd=161 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=161 -> /dev/ashmem
[文件映射] fd=161 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/shared_prefs/map_home_message_tab.xml
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153230764948.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153312984642.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153371126516.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153375950210.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=161 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=161 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153495925047.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153615934670.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153735929703.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153855931764.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153975936222.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755153994437690.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154014349817.dat
[文件映射] fd=161 -> /dev/ashmem
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154019053023.dat
[文件映射] fd=162 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=145 -> /dev/ashmem
[文件映射] fd=157 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[文件映射] fd=145 -> /dev/ashmem
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154126809048.dat
[文件映射] fd=156 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154131120607.dat
[文件映射] fd=156 -> /dev/ashmem
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154212666894.dat
[文件映射] fd=156 -> /dev/ashmem
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154216145622.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154336130406.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154456123710.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154576126822.dat
[文件映射] fd=156 -> /dev/ashmem
[文件映射] fd=156 -> /dev/ashmem
[文件映射] fd=156 -> /dev/ashmem
[文件映射] fd=156 -> /dev/ashmem
[文件映射] fd=156 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_tunnel_pf/1.0.0.2/meta1.json
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=156 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154636467055.dat
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154696135069.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_tunnel_pf/1.0.0.2/meta1.json
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_tunnel_pf/1.0.0.2/meta1.json
[文件映射] fd=155 -> /data/user/0/com.autonavi.minimap/shared_prefs/sync_time_file.xml
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154816131738.dat
[文件映射] fd=156 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755154936138113.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_tunnel_pf/1.0.0.2/meta1.json
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=155 -> /dev/ashmem
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_tunnel_pf/1.0.0.2/vdr_tunnel_pf.mnn
[文件映射] fd=156 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
[文件映射] fd=157 -> /dev/ashmem
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_vdr_tunnel_pf/1.0.0.2/meta1.json
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=58 -> /dev/ashmem
[文件映射] fd=155 -> /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755155056146681.dat
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755155176143135.dat
[文件映射] fd=157 -> /dev/ashmem
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
[文件映射] fd=161 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/crash/combine/main_1755157802094_crash.txt
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755155296142521.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=58 -> /dev/ashmem
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/crash/combine/main_1755157802094_crash.txt
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755155416145664.dat
[文件映射] fd=162 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/autonavi_error_log.txt
[文件映射] fd=163 -> /dev/ashmem
[文件映射] fd=164 -> /dev/ashmem
[文件映射] fd=163 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755155536149237.dat
[文件映射] fd=58 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/map_renderer_string.ans
[文件映射] fd=163 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755155656151797.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=163 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755155776164111.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.biz2/storage/ANDH151900_20250814155008_61
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755155896155896.dat
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755156016156345.dat
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755156136160428.dat
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755156256161753.dat
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755156376163695.dat
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755156496171696.dat
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755156616173312.dat
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755157096928651.dat
[文件映射] fd=164 -> /data/user/0/com.autonavi.minimap/files/girf_sync.db-journal
[文件映射] fd=163 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=58 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755157100304833.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=161 -> /dev/ashmem
[文件映射] fd=58 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/map_renderer_string.ans
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=161 -> /dev/ashmem
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/crash/combine/main_1755157802094_crash.txt
[文件映射] fd=161 -> /dev/ashmem
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.biz2/storage/ANDH151900_20250814155008_61
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.native/storage
[文件映射] fd=161 -> /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.native/storage/ANDH151900_20250814155755_9
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755157220235735.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755157778944158.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755157814985625.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/0/amapstream_1755157815636198.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755071952099650.dat
[文件映射] fd=164 -> /data/user/0/com.autonavi.minimap/shared_prefs/MessageBox.xml
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755071979990124.dat
[文件映射] fd=162 -> /dev/ashmem
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072004815912.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072011955844.dat
[可疑数据] TEXT (置信度: 40%)
  警告: 后续数据不像文本
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072036160101.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072058027522.dat
[文件映射] fd=165 -> /dev/ashmem
[文件映射] fd=165 -> /dev/ashmem
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072071904786.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.biz2/storage/ANDH151900_20250814155008_61
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072094024537.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072119084390.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072131908883.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072153797031.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072175187906.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072191909456.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072214113344.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072235688695.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072251908367.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072272821184.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072294368239.dat
[文件映射] fd=167 -> /dev/ashmem
[文件映射] fd=169 -> /data/app/com.autonavi.minimap-1/lib/arm64/libapssdk.so
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=168 -> /dev/ashmem
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072311909723.dat
[文件映射] fd=166 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072334325996.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072348482916.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072349188783.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072371945189.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072877435104.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072882779396.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072884395942.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072943356722.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072943752428.dat
[可疑数据] TEXT (置信度: 40%)
  警告: 后续数据不像文本
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072944423091.dat
[文件映射] fd=166 -> /dev/ashmem
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072983960525.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755072986718175.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755073090024356.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755073286683873.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755073534922460.dat
[文件映射] fd=157 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/autonavi_error_log.txt
[文件映射] fd=162 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755073536597998.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755073537805333.dat
[文件映射] fd=165 -> /dev/ashmem
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755073552111517.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755073837656697.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_ff65db7a68862e5e9dbc636f0e111e8f
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755073866253493.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755073868199373.dat
[文件映射] fd=166 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_ff65db7a68862e5e9dbc636f0e111e8f
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755074141712155.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755074146892348.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755074266813438.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755074386816882.dat
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755074506840504.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755074626831223.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075018126543.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=162 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075023577179.dat
[文件映射] fd=168 -> /dev/ashmem
[文件映射] fd=162 -> /dev/ashmem
[文件映射] fd=162 -> /dev/ashmem
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075140196060.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075145412509.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075427350538.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075432675304.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075434820955.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075469261141.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075549386442.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075670700976.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075672476187.dat
[文件映射] fd=165 -> /data/user/0/com.autonavi.minimap/app_webview/Cookies
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075734322208.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075734812009.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=157 -> /dev/ashmem
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/crash/phase/main_1755157802094
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075743121687.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075789145121.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075789394226.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075811018106.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075815971439.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755075935934203.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755076055937490.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755076229280746.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755076234492943.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755076354392662.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755076629271002.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755076634488980.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/crash/phase
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/crash/phase
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755076668279727.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755076673460158.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755076793418258.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755076913418451.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755077033424092.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755077153429442.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755077273433617.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755077602602103.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755077607512927.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755077651227060.dat
[文件映射] fd=169 -> /data/user/0/com.autonavi.minimap/files/crash/upload
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=167 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755077651547238.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755077808697535.dat
[文件映射] fd=157 -> /dev/ashmem
[文件映射] fd=157 -> /system/fonts/Roboto-Regular.ttf
[文件映射] fd=168 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_idx_33652181813.ans
[文件映射] fd=170 -> /dev/ashmem
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=145 -> /dev/ashmem
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755077813648371.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_828735ee9151047f414e1c6df57b464d
[文件映射] fd=168 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_95c044c585843e05ea8f9f69a13f0775
[文件映射] fd=169 -> /data/app/com.autonavi.minimap-1/lib/arm64/libsgmain.so
[文件映射] fd=169 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_idx_33652181813.ans
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/girf_sync.db-journal
[文件映射] fd=172 -> /data/user/0/com.autonavi.minimap/shared_prefs/dumpcrash_main.xml
[文件映射] fd=174 -> /data/user/0/com.autonavi.minimap/app_SGLib
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755133149076264.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755133153823359.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755133491223600.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755133496130890.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755133554985842.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755133555295897.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755133616113447.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755133736124479.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755133856121248.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755133976128483.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755134096127021.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755134216132647.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755134333516901.dat
[文件映射] fd=175 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=170 -> /data/user/0/com.autonavi.minimap/files/crash/combine
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755134338686048.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755134458557172.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755134491189472.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135098362609.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135103026828.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135132335960.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135165220194.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135197506858.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/app/com.autonavi.minimap-1/lib/arm64/libapssdk.so
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=172 -> /data/user/0/com.autonavi.minimap/files/crash/upload/main_202508141557566_upload.zip
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/files/crash/combine/main_1755157802094_crash.txt
[文件映射] fd=170 -> /data/user/0/com.autonavi.minimap/app_SGLib/app_1752999612/main/main_315417896.pkgInfo
[文件映射] fd=173 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135222996601.dat
[文件映射] fd=170 -> /data/user/0/com.autonavi.minimap/app_SGLib/app_1752999612/main/libsgmain_315417896000.dex.flock
[文件映射] fd=174 -> /data/user/0/com.autonavi.minimap/app_SGLib/app_1752999612/main/libsgmain_315417896000.zip
[文件映射] fd=174 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_95c044c585843e05ea8f9f69a13f0775
[文件映射] fd=176 -> /data/user/0/com.autonavi.minimap/files
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135258184927.dat
[文件映射] fd=176 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_fmm_common/*******/meta1.json
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135283128651.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135343029179.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135463067774.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135583107766.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/app/com.autonavi.minimap-1/lib/arm64/libapssdk.so
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135650515833.dat
[文件映射] fd=177 -> /dev/ashmem
[文件映射] fd=175 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_fmm_common/*******/fusion_map_matching_1829.mnn
[文件映射] fd=173 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[文件映射] fd=167 -> /dev/ashmem
[文件映射] fd=178 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135655482349.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135775452162.dat
[文件映射] fd=168 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_d83a2f6f62d15840367eab6a9158ff8b
[文件映射] fd=170 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_d83a2f6f62d15840367eab6a9158ff8b
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755135780224393.dat
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755136015459879.dat
[文件映射] fd=167 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /dev/ashmem
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files
[文件映射] fd=179 -> /data/app/com.autonavi.minimap-1/lib/arm64/libsgmainso-6.6.240404.so
[文件映射] fd=178 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755136195815918.dat
[文件映射] fd=178 -> /dev/ashmem
[文件映射] fd=168 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_b53d30bde47a4c6347758ae5f12a5b12
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755136200446262.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755136337354901.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755136342401867.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755136406678926.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755136411965853.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=167 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_b53d30bde47a4c6347758ae5f12a5b12
[文件映射] fd=180 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/files/girf_sync.db-journal
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/crash/upload
[文件映射] fd=157 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755136738251650.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755136897997610.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755137017825607.dat
[文件映射] fd=172 -> /data/user/0/com.autonavi.minimap/files/sg_oc.lock
[文件映射] fd=172 -> /data/user/0/com.autonavi.minimap/files/ab914f43b8296c2c.lock
[文件映射] fd=178 -> /data/user/0/com.autonavi.minimap/files/0a231bd8575dcf72.txt
[文件映射] fd=178 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/28009282652775325187
[文件映射] fd=178 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/28009282652775325187
[文件映射] fd=178 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/28009282652775325187/33@21
[文件映射] fd=178 -> /data/app/com.autonavi.minimap-1/lib/arm64/libsgmainso.ucb.so
[文件映射] fd=178 -> /data/app/com.autonavi.minimap-1/lib/arm64/libsgsecuritybodyso.ucb.so
[文件映射] fd=157 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755137077776509.dat
[文件映射] fd=178 -> /data/app/com.autonavi.minimap-1/lib/arm64/libsgmiddletierso.ucb.so
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755137159937180.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755137619468862.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755137624663340.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755137744667372.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755137864673378.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /data/user/0/com.autonavi.minimap/databases/aMap.db-journal
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=178 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/28009282652775325187/33@21
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755137984677165.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755138070685711.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755138104683917.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755138224679996.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755138344683411.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755138418894759.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755138423761386.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755138543762096.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755138663767417.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755138903771875.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755139023781403.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755139044416989.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755139143779863.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755149866828935.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755149870906604.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755149990853032.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755150110859853.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755150230862524.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_a91bf6f07de1dab838c9ecf847720d62
[文件映射] fd=168 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_a91bf6f07de1dab838c9ecf847720d62
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/files
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755150350868726.dat
[文件映射] fd=180 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755150470873198.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=173 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755150590877335.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755150710887563.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755150830883960.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_e8d7ebb90d8e496f52a8d139b3ed5c17
[文件映射] fd=181 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_e8d7ebb90d8e496f52a8d139b3ed5c17
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755150950883181.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/28009282652775325187
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/28009282652775325187
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/28009282652775325187
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/28009282652775325187
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/28009282652775325187
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/28009282652775325187
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/28009282652775325187/33@21
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830725715
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830725715
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_cb24723fc2b0777fd6203374b9f9eecf
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755151070890272.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755151131132358.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755151150796482.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755151155516219.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755151195424993.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755151199003708.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830725715
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830725715
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755151251711823.dat
[文件映射] fd=173 -> /proc/self/task/23798/stat
[文件映射] fd=178 -> /dev/ashmem
[文件映射] fd=173 -> /proc/self/task/23801/stat
[文件映射] fd=173 -> /proc/self/task/23808/stat
[文件映射] fd=173 -> /proc/self/task/23809/stat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755151252100717.dat
[文件映射] fd=178 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_cb24723fc2b0777fd6203374b9f9eecf
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830725715
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755151294021415.dat
[文件映射] fd=173 -> /proc/self/task/23811/stat
[文件映射] fd=173 -> /proc/self/task/23810/stat
[文件映射] fd=173 -> /proc/self/task/23818/stat
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830725715
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830725715/12@9
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830925207
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830925207
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830925207
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755151298124967.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152482491781.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152486625653.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152572510882.dat
[可疑数据] TEXT (置信度: 40%)
  警告: 后续数据不像文本
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152577784912.dat
[文件映射] fd=173 -> /proc/self/task/23819/stat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830925207
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152628909088.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152633908391.dat
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830925207
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830925207
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830925207/1@1
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282612545529285
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282612545529285
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282612545529285
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282612545529285
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282612545529285
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282612545529285
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282612545529285/120@60
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=173 -> /proc/self/task/23823/stat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152664585535.dat
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_4b3ba95527615bacd80e337cbb8c3380
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152700750102.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152732937316.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152753888309.dat
[文件映射] fd=171 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_4b3ba95527615bacd80e337cbb8c3380
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/27756254392545529285
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_349f52781aafa7e0aaf47a5a068663c0
[文件映射] fd=179 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_349f52781aafa7e0aaf47a5a068663c0
[文件映射] fd=173 -> /proc/self/task/23824/stat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/27756254392545529285
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/27756254392545529285
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152787289027.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/27756254392545529285
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152822532894.dat
[文件映射] fd=173 -> /proc/self/task/23825/stat
[文件映射] fd=173 -> /proc/self/task/23827/stat
[文件映射] fd=173 -> /proc/self/task/23829/stat
[文件映射] fd=173 -> /proc/self/task/23830/stat
[文件映射] fd=173 -> /proc/self/task/23848/stat
[文件映射] fd=173 -> /proc/self/task/23860/stat
[文件映射] fd=173 -> /proc/self/task/23863/stat
[文件映射] fd=173 -> /proc/self/task/23864/stat
[文件映射] fd=173 -> /proc/self/task/23868/stat
[文件映射] fd=173 -> /proc/self/task/23844/stat
[文件映射] fd=173 -> /proc/self/task/23882/stat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/27756254392545529285
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/27756254392545529285
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/27756254392545529285/151@86
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/083042929525189
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152857812013.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152873896172.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152908214989.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152943479935.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/083042929525189
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/083042929525189
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/083042929525189
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/083042929525189
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/083042929525189
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/083042929525189/39@17
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/3006226982269812571525955
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152978735559.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755152993894434.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153029094585.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153064356242.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153099624047.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153113905795.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153114390490.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153128137646.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153132466693.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153225929079.dat
[文件映射] fd=182 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/monitor_log/com.autonavi.minimap/1755158277261/1
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153230757865.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/3006226982269812571525955
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153312982413.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153317000321.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153371125974.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153375937284.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153495921991.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153615931320.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153735926314.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153855930205.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153975934556.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755153994435244.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154014338626.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154019015159.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154126805058.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154131060780.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154212657019.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154216136521.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154336126629.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154456121917.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154576124619.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154636465705.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154696133769.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154816130005.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755154936136738.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755155056142569.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755155176141537.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755155296141278.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755155416144119.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755155536147064.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/3006226982269812571525955
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/3006226982269812571525955
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/3006226982269812571525955
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/3006226982269812571525955
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/3006226982269812571525955/39@26
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/30323
[文件映射] fd=157 -> /proc/self/task/23893/stat
[文件映射] fd=157 -> /proc/self/task/23847/stat
[文件映射] fd=157 -> /proc/self/task/23899/stat
[文件映射] fd=157 -> /proc/self/task/23900/stat
[文件映射] fd=157 -> /proc/self/task/23908/stat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755155656150257.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755155776161044.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755155896154355.dat
[文件映射] fd=157 -> /proc/self/task/23916/stat
[文件映射] fd=157 -> /proc/self/task/23948/stat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/30323
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755156136159124.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755156256160478.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755156376162444.dat
[文件映射] fd=173 -> /proc/self/task/23949/stat
[文件映射] fd=173 -> /proc/self/task/23950/stat
[文件映射] fd=173 -> /proc/self/task/23951/stat
[文件映射] fd=173 -> /proc/self/task/23953/stat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755156496167633.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755156616171737.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/30323
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/30323
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/30323
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/30323
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/30323/23@25
[文件映射] fd=173 -> /proc/self/task/23954/stat
[文件映射] fd=173 -> /proc/self/task/23956/stat
[文件映射] fd=173 -> /proc/self/task/23957/stat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157096917408.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157100264222.dat
[文件映射] fd=173 -> /proc/self/task/23964/stat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282611259212593
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157126332213.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157161525653.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157197717026.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157220233716.dat
[文件映射] fd=173 -> /proc/self/task/23969/stat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157254053572.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157289272156.dat
[文件映射] fd=173 -> /proc/self/task/23970/stat
[文件映射] fd=173 -> /proc/self/task/23980/stat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157324496990.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157340235234.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157374830322.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157410036461.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157445270811.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157460236687.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157494571193.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157529790392.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157566013180.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157580242388.dat
[文件映射] fd=173 -> /proc/self/task/23859/stat
[文件映射] fd=173 -> /proc/self/task/23987/stat
[文件映射] fd=173 -> /proc/self/task/23988/stat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282611259212593
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282611259212593
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282611259212593
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282611259212593
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282611259212593
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282611259212593/52@24
[文件映射] fd=173 -> /proc/self/task/23843/stat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157616312035.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157651537032.dat
[文件映射] fd=173 -> /proc/self/task/23989/stat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/Statistics.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157686739161.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157700236439.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157715792066.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157814967269.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/1/amapstream_1755157815612724.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755075743120139.dat
[文件映射] fd=173 -> /proc/self/task/24008/stat
[文件映射] fd=173 -> /proc/self/task/24010/stat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830725715/12@9
[文件映射] fd=157 -> /proc/self/task/24011/stat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755075789108361.dat
[文件映射] fd=173 -> /proc/self/task/24013/stat
[文件映射] fd=173 -> /proc/self/task/24014/stat
[文件映射] fd=182 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
[文件映射] fd=173 -> /proc/self/task/24015/stat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755075811220433.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_fmm_common/*******/meta1.json
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_aad3fd10dd21174c80ebaca7bf576077
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755076229734767.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755076629569908.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755076668579740.dat
[文件映射] fd=157 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755077602686444.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/monitor_log/com.autonavi.minimap/1755158277261/1
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=157 -> /proc/23798/smaps
[文件映射] fd=173 -> /proc/self/task/24016/stat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/0830725715/12@9
[文件映射] fd=175 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755077607509309.dat
[文件映射] fd=180 -> /proc/self/task/24029/stat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755077651202106.dat
[文件映射] fd=180 -> /proc/self/task/24030/stat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/JX0WDG83P1ZN.txt
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755077808940547.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755133149147817.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755133491275405.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755133496121992.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755133554984626.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755134333780715.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755134338676954.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755134491184123.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755135098378288.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755135103013151.dat
[真实地图数据] DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: 魔数匹配DICE, 完整魔数DICE-AM\0匹配, 版本号170合理, 几何类型合理: 0x89
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755135222995174.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755135283123820.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755135650665312.dat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_fmm_yaw_fusion/1.0.0.4/meta1.json
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755135655470300.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_fmm_yaw_fusion/1.0.0.4/yaw_fusion_v5.mnn
[文件映射] fd=180 -> /proc/self/task/24048/stat
[文件映射] fd=183 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755135780219804.dat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/JX0WDG83P1ZN.txt5cf6
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755135918338595.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755136195873939.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755136337677529.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755136407198457.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755136738607146.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755136898255612.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755137018112587.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/sg_uoc.lock
[文件映射] fd=183 -> /proc/self/task/24051/stat
[文件映射] fd=184 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755137077958444.dat
[文件映射] fd=184 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755137160256964.dat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/rt_storage.lock
[文件映射] fd=185 -> /data/user/0/com.autonavi.minimap/app_SGLib/.cd78eccde0
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/rt_storage.lock
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_fmm_yaw_fusion/1.0.0.4/meta1.json
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_23771a443250b9826f4a5eb9fc2d16c7
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755137619789628.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755137624659038.dat
[文件映射] fd=182 -> /proc/self/task/24061/stat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755138418979831.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755138423759554.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755149866886296.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755149870883224.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755151131109493.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755151138419197.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755151150891547.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755151195814373.dat
[文件映射] fd=183 -> /data/user/0/com.autonavi.minimap/app_SGLib/.c49493b420
[文件映射] fd=182 -> /proc/self/task/24062/stat
[文件映射] fd=182 -> /proc/self/task/23915/stat
[文件映射] fd=182 -> /proc/self/task/24063/stat
[文件映射] fd=182 -> /proc/self/task/24068/stat
[文件映射] fd=182 -> /proc/self/task/24070/stat
[文件映射] fd=182 -> /proc/self/task/24071/stat
[文件映射] fd=180 -> /proc/self/task/24076/stat
[文件映射] fd=180 -> /proc/self/task/24079/stat
[文件映射] fd=180 -> /proc/self/task/24080/stat
[文件映射] fd=180 -> /proc/self/task/24081/stat
[文件映射] fd=180 -> /proc/self/task/24074/stat
[文件映射] fd=180 -> /proc/self/task/24082/stat
[文件映射] fd=180 -> /proc/self/task/24083/stat
[文件映射] fd=180 -> /proc/self/task/24085/stat
[文件映射] fd=180 -> /proc/self/task/24086/stat
[文件映射] fd=180 -> /proc/self/task/24088/stat
[文件映射] fd=180 -> /proc/self/task/24089/stat
[文件映射] fd=180 -> /proc/self/task/24094/stat
[文件映射] fd=180 -> /proc/self/task/24096/stat
[文件映射] fd=180 -> /proc/self/task/24095/stat
[文件映射] fd=180 -> /proc/self/task/24098/stat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/rt_storage.lock
[文件映射] fd=183 -> /data/app/com.autonavi.minimap-1/base.apk
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755151198957849.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/app_SGLib/.cd78eccde0
[文件映射] fd=180 -> /proc/self/task/24100/stat
[文件映射] fd=184 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755151251710645.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755151294071204.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755152482644521.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755152573133918.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755152629114473.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755152633906087.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755152753886502.dat
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755152873893889.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755152993892781.dat
[文件映射] fd=173 -> /proc/self/task/24105/stat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/amapml/sa_deep_model/1.0.1.1/meta1.json
[文件映射] fd=173 -> /data/user/0/com.autonavi.minimap/files/amapml/sa_deep_model/1.0.1.1/sa_model_20231031165625.mnn
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755153113889467.dat
[文件映射] fd=182 -> /proc/self/task/24104/stat
[文件映射] fd=182 -> /proc/self/task/24103/stat
[文件映射] fd=182 -> /proc/self/task/24115/stat
[文件映射] fd=182 -> /proc/self/task/24118/stat
[文件映射] fd=182 -> /proc/self/task/24125/stat
[文件映射] fd=182 -> /proc/self/task/24127/stat
[文件映射] fd=182 -> /proc/self/task/24128/stat
[文件映射] fd=182 -> /proc/self/task/24132/stat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755153114388040.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755153118311698.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755153128249168.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755153226172798.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755153313294048.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755153371216637.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755153375921498.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755153994414487.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755154002793906.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755154014460277.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755154126998257.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755154212816026.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755154216132487.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755154636462679.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755154696132407.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755156648302318.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755157097217674.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755157100236773.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755157220231541.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755157340233310.dat
[文件映射] fd=182 -> /proc/self/task/24136/stat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755157460235313.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755157580239453.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755157700234717.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755157715788570.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755157815469211.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_60/2/amapstream_1755157815571369.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755075789158458.dat
[文件映射] fd=182 -> /proc/self/task/24139/stat
[文件映射] fd=182 -> /proc/self/task/23896/stat
[文件映射] fd=182 -> /proc/self/task/24065/stat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=182 -> /proc/self/task/24142/stat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755075811387688.dat
[文件映射] fd=182 -> /proc/self/task/24039/stat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=182 -> /proc/self/task/24145/stat
[文件映射] fd=182 -> /proc/self/task/24146/stat
[文件映射] fd=182 -> /proc/self/task/24151/stat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755075815941459.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755076229877935.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755076234389557.dat
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755076629661889.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=182 -> /dev/ashmem
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755076634458067.dat
[文件映射] fd=182 -> /dev/ashmem
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/sp.lock
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755076668676700.dat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755077602774147.dat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755077607512786.dat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755077809054005.dat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755133149144622.dat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755133491417325.dat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755133496126456.dat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755133555259408.dat
[文件映射] fd=182 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755134333814101.dat
[文件映射] fd=180 -> /dev/ashmem
[文件映射] fd=184 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=180 -> /dev/ashmem
[文件映射] fd=189 -> /data/user/0/com.autonavi.minimap/files/logs/alc/ajx3.biz2/storage/ANDH151900_20250814155758_63
[文件映射] fd=186 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/avatar/34a474bb2d50baea3f32892db55c1a81
[文件映射] fd=188 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755134338672884.dat
[文件映射] fd=180 -> /data/app/com.autonavi.minimap-1/lib/arm64/libsgmainso-6.6.240404.so
[文件映射] fd=190 -> /data/user/0/com.autonavi.minimap/files/SGMANAGER_DATA2
[文件映射] fd=150 -> /dev/ashmem
[文件映射] fd=191 -> /dev/ashmem
[文件映射] fd=192 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/monitor_log/com.autonavi.minimap
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/monitor_log/com.autonavi.minimap/1755157808808
[文件映射] fd=150 -> /dev/ashmem
[文件映射] fd=150 -> /dev/ashmem
[文件映射] fd=150 -> /dev/ashmem
[文件映射] fd=180 -> /data/user/0/com.autonavi.minimap/files/boot/speedup/616fc58e2ddf29fca98c00cbe7593007.crc
[文件映射] fd=185 -> /data/user/0/com.autonavi.minimap/files/boot/speedup/616fc58e2ddf29fca98c00cbe7593007
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755135650692525.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755135655464681.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/app/com.autonavi.minimap-1/lib/arm64/libsgmiddletier.so
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=47 -> /dev/ashmem
[文件映射] fd=191 -> /data/user/0/com.autonavi.minimap/files/rt_storage.lock
[文件映射] fd=192 -> /data/user/0/com.autonavi.minimap/app_SGLib/.cd78eccde0
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/shared_prefs/enhancedMode.xml
[文件映射] fd=47 -> /dev/ashmem
[文件映射] fd=190 -> /data/user/0/com.autonavi.minimap/files/sp.lock
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755135780205131.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/rt_storage.lock
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/app_SGLib/.cd78eccde0
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/app_SGLib/app_1752999612/main/middletier_315417896.pkgInfo
[文件映射] fd=191 -> /data/user/0/com.autonavi.minimap/files/SGMANAGER_DATA2
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/shared_prefs/SP_NAME_layer_list.xml
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=190 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/monitor_log/com.autonavi.minimap/1755157808808/sceneevent.64
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755136195751136.dat
[文件映射] fd=191 -> /data/user/0/com.autonavi.minimap/files/rt_storage.lock
[文件映射] fd=192 -> /data/user/0/com.autonavi.minimap/app_SGLib/.cd78eccde0
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/rt_storage.lock
[文件映射] fd=192 -> /data/user/0/com.autonavi.minimap/app_SGLib/.cd78eccde0
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755136200428707.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755136337914148.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755136342382996.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755136407300107.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755136411937919.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755136739222636.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755136898302593.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755137018196708.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/app/com.autonavi.minimap-1/lib/arm64/libsgsecuritybody.so
[文件映射] fd=47 -> /dev/ashmem
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/app_SGLib/app_1752999612/main/securitybody_315417896.pkgInfo
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755137078009663.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755137160292161.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755137619901307.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/shared_prefs/SP_NAME_layer_checked.xml
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755137624660044.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755138419016729.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755138423760317.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/girf_sync.db-journal
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/app_SGLib/app_1752999612/main/libsgsecuritybody_315417896000.dex.flock
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/app_SGLib/app_1752999612/main/libsgsecuritybody_315417896000.zip
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755139044425489.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755149866643013.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755149870872682.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755151151150815.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755151155485170.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/app/com.autonavi.minimap-1/lib/arm64/libsgsecuritybodyso-6.6.240404.so
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755151194678805.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755151198954582.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/sgFile.lock
[文件映射] fd=191 -> /data/user/0/com.autonavi.minimap/app_SGLib/SG_INNER_DATA_AVMP
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755151252071703.dat
[文件映射] fd=150 -> /dev/ashmem
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755151293895452.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755152482237095.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755152573345758.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755153128189494.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755153132414001.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=47 -> /dev/ashmem
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755153226335420.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755153312665922.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755153316981700.dat
[文件映射] fd=46 -> /data/app/com.autonavi.minimap-1/lib/arm64/libsgsecuritybodyso-6.6.240404.so
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755153371365940.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755153375926034.dat
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755153994410527.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282612545529285/120@60
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755154014461487.dat
[文件映射] fd=186 -> /data/app/com.autonavi.minimap-1/lib/arm64/libzstd.so
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755154019005255.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/app_SGLib/.utask_64/08303282612545529285/120@60
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755154126543397.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=150 -> /dev/ashmem
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755154211731576.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/sa_deep_model/1.0.1.1/meta1.json
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db/0_6cc2bb481ba8d2da411d63b4d0b2cf7d
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755154216134943.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=150 -> /dev/ashmem
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755154696134611.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755157095919457.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755157779009745.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755157808202866.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755157812282707.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755157900184957.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=47 -> /dev/ashmem
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/0/amapstream_1755158276438632.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755072878545813.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755072882786891.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755072884396437.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755072943341821.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755072943753165.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755072944432881.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755072984759595.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_pos_lane_model/1.0.0.8/meta1.json
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_pos_lane_model/1.0.0.8/lane_model.mnn
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755072986692312.dat
[文件映射] fd=173 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/monitor_log/com.autonavi.minimap/1755157808808/1
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/alc/paas.normandy
[文件映射] fd=47 -> /data/user/0/com.autonavi.minimap/files/logs/alc/paas.normandy/ANDH151900_20250814085910_0
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073003412873.dat
[文件映射] fd=194 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/monitor_log/com.autonavi.minimap/1755157808808/sceneevent.99999
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073034767345.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073065257173.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073089779663.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073090026698.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073286683742.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073535772469.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073537671676.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073552055225.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073837657675.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073866908331.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073868176145.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073891053510.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073922250899.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073957492180.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755073993691003.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074025935015.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074062137299.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074097346468.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074132536909.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074142113349.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074146898783.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074173268169.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074209536951.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074241790140.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074266814842.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074302234446.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074337472922.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074372739134.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074386825705.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074422106651.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074457392838.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074489611788.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074506841221.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=193 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074542237477.dat
[文件映射] fd=193 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074574499543.dat
[文件映射] fd=193 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074609805459.dat
[文件映射] fd=193 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074626834472.dat
[文件映射] fd=193 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074659243844.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=193 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074694559989.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074729957420.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074746830762.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755074780401865.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075018730313.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075023490391.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/deviceml/amapml.db
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075045621838.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075078155621.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075113580203.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075140892987.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075145348819.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075169757017.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075205103092.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=195 -> /data/app/com.autonavi.minimap-1/base.apk
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075238418389.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075265361196.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=197 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075298108107.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075427842724.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075432664331.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075434820084.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075469083392.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075549363407.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075670965315.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075672511612.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075694843315.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075734203616.dat
[文件映射] fd=186 -> /dev/ashmem
[文件映射] fd=190 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/monitor_log/com.autonavi.minimap/1755157808808/1
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075734293305.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075734521614.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075743124504.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075789168553.dat
[文件映射] fd=193 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/monitor_log/com.autonavi.minimap/1755157808808
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /storage/emulated/0/Android/data/com.autonavi.minimap/files/monitor_log/com.autonavi.minimap
[文件映射] fd=46 -> /proc/self/stat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075789395553.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=194 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db
[文件映射] fd=194 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db
[文件映射] fd=194 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/download
[文件映射] fd=194 -> /data/user/0/com.autonavi.minimap/files/ajx-biz/db
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075811385057.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075815942850.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075837416916.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075873735279.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075910123528.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075935936818.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755075971696863.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076007994967.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076043256285.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076055938221.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076091659466.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076127903078.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076160259198.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076175942761.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076208689477.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076229866265.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076234395154.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076256538390.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076291898370.dat
[文件映射] fd=46 -> /proc/self/stat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=193 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=193 -> /dev/ashmem
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076328192310.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076354401227.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076389704026.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076425010967.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076458310941.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076474398355.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076509835321.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076542156028.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076577478158.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076594396555.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076629660479.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076634459315.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/shared_prefs/FREQUENCY_SP_MERGE_DATA_FLAG_NAMESPACE.xml
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_pos_lane_model/1.0.0.8/meta1.json
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076668650683.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076673423506.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076697081541.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076733528348.dat
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076768946386.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=145 -> /data/user/0/com.autonavi.minimap/files/amapml/amap_bundle_loc_pos_lane_model/1.0.0.8/lane_model.mnn
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076793420028.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076829544173.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076864915695.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076898217018.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076913421198.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076949665051.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755076981961432.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077017253714.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077033428508.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077066701810.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077102028385.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077137322772.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077153434295.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077187826925.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077223178297.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077258534748.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077273435745.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077309016173.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077344418845.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077379894619.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077393440717.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077429358257.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077464709716.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077500009427.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077513452394.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077549532871.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077584834130.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077602773188.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077607514295.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077634001608.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077651205495.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077809050810.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755077813586092.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755133149143844.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755133153822129.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755133177385459.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755133213594316.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755133248798371.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755133491385269.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755133496128376.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755133519519015.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755133554984252.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755133555271858.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755133616120112.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755134333793530.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755134338674547.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755134364946060.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755134397223653.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755134432475930.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755134458559189.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755134491181768.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755135650692131.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755135655466488.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755135682028460.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/shared_prefs/sp_switch_cloud_main.xml
  大小: 4096 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755135714359057.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755135749708870.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755135775452550.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755135780210727.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755135946796434.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755135981984698.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136014193336.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136015470241.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136049442057.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136084669703.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136116910505.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136135459669.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136170214460.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136195750689.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136200433558.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136212878871.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136249344791.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136284622135.dat
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/files/biz_data/audio/record
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=46 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136319885047.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136320407844.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136337910782.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136342388228.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136407294561.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136411939216.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136739221729.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755136898301963.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137018196406.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137077993417.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=150 -> /dev/ashmem
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137160291837.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137619850883.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137624662162.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137659913697.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137696121617.dat
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137728360935.dat
[文件映射] fd=150 -> /sys/devices/system/cpu
[文件映射] fd=186 -> /dev/ashmem
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137744668568.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /dev/ashmem
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/files/fastweb/fastweb_package_info.json
[文件映射] fd=150 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=150 -> /dev/ashmem
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137780659335.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137812866330.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137849093791.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=195 -> /dev/ashmem
[文件映射] fd=196 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137864677048.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137897414710.dat
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137932637287.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137967860461.dat
[文件映射] fd=195 -> /dev/ashmem
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755137984681688.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=186 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138018186398.dat
[文件映射] fd=186 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138053433938.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138070681868.dat
[文件映射] fd=199 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=199 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=195 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=201 -> /dev/ashmem
[文件映射] fd=201 -> /data/user/0/com.autonavi.minimap/files/logs/alc/paas.deviceml
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=199 -> /dev/ashmem
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138104684038.dat
[文件映射] fd=200 -> /data/user/0/com.autonavi.minimap/files/logs/alc/paas.deviceml/ANDH151900_20250814155017_1
[文件映射] fd=201 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138344687160.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138419016122.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=199 -> /dev/ashmem
[文件映射] fd=201 -> /data/user/0/com.autonavi.minimap/shared_prefs/AliSR.xml
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138423761312.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138447430624.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138483719641.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138518960023.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138543763609.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138579354475.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138615535532.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138647745085.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138663774120.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138696046107.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=199 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=199 -> /data/user/0/com.autonavi.minimap/shared_prefs/pha_manifest.xml
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138731259620.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138766464734.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=202 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138783778236.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138816734330.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138851921472.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138887126936.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138903773230.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138937413496.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755138972623546.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755139007816129.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755139023784525.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755139044430083.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755149866642465.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755149870874150.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755149905959471.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=199 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755149942174258.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755149977389614.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755149990855308.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150026705527.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150062919802.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/trackPost
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150098167798.dat
[文件映射] fd=196 -> /dev/ashmem
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150110864069.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150146517349.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150182727726.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150214998064.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150230867392.dat
[文件映射] fd=199 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=202 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150263379488.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=205 -> /dev/ashmem
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150298697282.dat
[文件映射] fd=202 -> /data/user/0/com.autonavi.minimap/databases/deviceML.db-journal
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150335027563.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150350869932.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150384427345.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150420747710.dat
[文件映射] fd=196 -> /data/user/0/com.autonavi.minimap/files/pha/pha_manifest
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150456092584.dat
[文件映射] fd=195 -> /data/user/0/com.autonavi.minimap/files/logs/amapstream/pos_61/1/amapstream_1755150470874921.dat
[真实地图数据] TEXT (置信度: 60%)
  文件: /data/user/0/com.autonavi.minimap/files/logs/amapstream/maps/10/amapstream_1755135779260317.dat
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[文件映射] fd=204 -> /dev/ashmem
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  原因: TEXT魔数匹配, 包含类似文本的字节
[真实地图数据] TEXT (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
Server terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=000001F9CB1476F0)

Thread 0x00007738 (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x0000a3b0 (most recent call first):
<no Python frame>
