/*
 * 完整的.ans文件解析分析脚本
 * 基于IDA Pro静态分析 + Frida动态跟踪
 * 目标：完整理解.ans文件的解析方法和文件结构
 */

console.log("🎯 启动完整的.ans文件解析分析");
console.log("基于IDA Pro静态分析 + Frida动态跟踪");

// 模块基地址
var libamapnsq = null;
var libamapr = null;

// 解析状态跟踪
var parseState = {
    fileOperations: [],
    decompressOperations: [],
    dataBlocks: [],
    parseSequence: []
};

// 文件操作Hook - 跟踪.ans文件读取
function hookFileOperations() {
    console.log("📁 设置文件操作Hook");
    
    // Hook open系统调用
    var openPtr = Module.findExportByName("libc.so", "open");
    if (openPtr) {
        Interceptor.attach(openPtr, {
            onEnter: function(args) {
                var filename = args[0].readCString();
                this.filename = filename;
                this.isAnsFile = filename && filename.indexOf('.ans') >= 0;
            },
            onLeave: function(retval) {
                if (this.isAnsFile && retval.toInt32() >= 0) {
                    console.log("📂 打开.ans文件:", this.filename);
                    parseState.fileOperations.push({
                        type: 'open',
                        filename: this.filename,
                        fd: retval.toInt32(),
                        timestamp: Date.now()
                    });
                }
            }
        });
    }
    
    // Hook read系统调用
    var readPtr = Module.findExportByName("libc.so", "read");
    if (readPtr) {
        Interceptor.attach(readPtr, {
            onEnter: function(args) {
                this.fd = args[0].toInt32();
                this.buffer = args[1];
                this.size = args[2].toInt32();
            },
            onLeave: function(retval) {
                var bytesRead = retval.toInt32();
                if (bytesRead > 0 && this.size > 100) {
                    // 检查是否可能是.ans文件数据
                    try {
                        var preview = this.buffer.readByteArray(Math.min(32, bytesRead));
                        var bytes = new Uint8Array(preview);
                        var headerHex = Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' ');
                        
                        // 检查文件头
                        if (headerHex.indexOf('41 4d 2d 7a 6c 69 62') >= 0) { // "AM-zlib"
                            console.log("📥 读取AM-zlib文件头:", headerHex);
                            parseState.fileOperations.push({
                                type: 'read_header',
                                fd: this.fd,
                                size: bytesRead,
                                header: headerHex,
                                timestamp: Date.now()
                            });
                        } else if (headerHex.indexOf('78 9c') >= 0) { // zlib压缩数据
                            console.log("📥 读取zlib压缩数据:", this.size, "字节");
                            parseState.fileOperations.push({
                                type: 'read_compressed',
                                fd: this.fd,
                                size: bytesRead,
                                header: headerHex.substring(0, 24),
                                timestamp: Date.now()
                            });
                        }
                    } catch (e) {
                        // 忽略读取错误
                    }
                }
            }
        });
    }
}

// zlib解压Hook - 跟踪数据解压过程
function hookDecompression() {
    console.log("🗜️ 设置zlib解压Hook");
    
    // Hook zlib的uncompress函数
    var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
    if (uncompressPtr) {
        Interceptor.attach(uncompressPtr, {
            onEnter: function(args) {
                this.dest = args[0];
                this.destLen = args[1];
                this.source = args[2];
                this.sourceLen = args[3].toInt32();
                
                // 读取压缩数据头部
                try {
                    var sourcePreview = this.source.readByteArray(Math.min(16, this.sourceLen));
                    var bytes = new Uint8Array(sourcePreview);
                    this.sourceHeader = Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' ');
                } catch (e) {
                    this.sourceHeader = "无法读取";
                }
            },
            onLeave: function(retval) {
                if (retval.toInt32() === 0) { // Z_OK
                    var destLen = this.destLen.readU32();
                    console.log("✅ zlib解压成功:", this.sourceLen, "→", destLen, "字节");
                    console.log("   压缩数据头:", this.sourceHeader);
                    
                    parseState.decompressOperations.push({
                        compressedSize: this.sourceLen,
                        decompressedSize: destLen,
                        sourceHeader: this.sourceHeader,
                        timestamp: Date.now()
                    });
                    
                    // 尝试读取解压后的数据头部
                    try {
                        var destPreview = this.dest.readByteArray(Math.min(32, destLen));
                        var destBytes = new Uint8Array(destPreview);
                        var destHeader = Array.from(destBytes).map(b => b.toString(16).padStart(2, '0')).join(' ');
                        console.log("   解压数据头:", destHeader);
                        
                        // 检查是否包含可读文本
                        var text = "";
                        for (var i = 0; i < destBytes.length; i++) {
                            if (destBytes[i] >= 32 && destBytes[i] < 127) {
                                text += String.fromCharCode(destBytes[i]);
                            } else if (destBytes[i] === 0) {
                                text += ".";
                            }
                        }
                        if (text.length > 4) {
                            console.log("   可读文本:", text);
                        }
                    } catch (e) {
                        console.log("   无法读取解压数据");
                    }
                }
            }
        });
    }
}

// Hook libamapnsq.so中的关键解析函数
function hookMapDataParsing() {
    console.log("🗺️ 设置地图数据解析Hook");
    
    // 等待libamapnsq.so加载
    var intervalId = setInterval(function() {
        libamapnsq = Process.findModuleByName("libamapnsq.so");
        if (libamapnsq) {
            clearInterval(intervalId);
            console.log("📍 找到libamapnsq.so，基地址:", libamapnsq.base);
            
            // Hook之前分析的关键函数
            hookKeyParseFunctions();
        }
    }, 100);
}

function hookKeyParseFunctions() {
    // Hook sub_5C394 - 数据分发函数 (IDA地址需要转换为运行时地址)
    try {
        var sub5C394Addr = libamapnsq.base.add(0x5C060); // IDA中的实际偏移
        console.log("🎯 Hook sub_5C394 (数据分发) at:", sub5C394Addr);
        
        Interceptor.attach(sub5C394Addr, {
            onEnter: function(args) {
                console.log("📨 [sub_5C394] 数据分发函数调用");
                console.log("   参数1:", args[0]);
                console.log("   参数2:", args[1]);
                console.log("   参数3:", args[2]);
                console.log("   参数4:", args[3]);
                
                parseState.parseSequence.push({
                    function: 'sub_5C394',
                    type: 'data_dispatch',
                    args: [args[0].toString(), args[1].toString(), args[2].toString(), args[3].toString()],
                    timestamp: Date.now()
                });
            },
            onLeave: function(retval) {
                console.log("📤 [sub_5C394] 返回值:", retval);
            }
        });
    } catch (e) {
        console.log("❌ 无法Hook sub_5C394:", e.message);
    }
    
    // Hook sub_10F88 - 数据处理函数
    try {
        var sub10F88Addr = libamapnsq.base.add(0x10EFC); // IDA中的实际偏移
        console.log("🎯 Hook sub_10F88 (数据处理) at:", sub10F88Addr);
        
        Interceptor.attach(sub10F88Addr, {
            onEnter: function(args) {
                console.log("🔧 [sub_10F88] 数据处理函数调用");
                
                parseState.parseSequence.push({
                    function: 'sub_10F88',
                    type: 'data_processing',
                    timestamp: Date.now()
                });
            },
            onLeave: function(retval) {
                console.log("🔧 [sub_10F88] 返回值:", retval);
            }
        });
    } catch (e) {
        console.log("❌ 无法Hook sub_10F88:", e.message);
    }
    
    // Hook girf_sqlite3_bind_blob - SQLite数据绑定
    try {
        var bindBlobAddr = libamapnsq.base.add(0x15000); // 实际函数地址
        console.log("🎯 Hook girf_sqlite3_bind_blob at:", bindBlobAddr);
        
        Interceptor.attach(bindBlobAddr, {
            onEnter: function(args) {
                this.stmt = args[0];
                this.index = args[1].toInt32();
                this.data = args[2];
                this.size = args[3].toInt32();
                
                console.log("💾 [bind_blob] SQLite数据绑定");
                console.log("   索引:", this.index);
                console.log("   数据大小:", this.size);
                
                if (this.size > 0 && this.size < 10000) {
                    try {
                        var preview = this.data.readByteArray(Math.min(32, this.size));
                        var bytes = new Uint8Array(preview);
                        var hex = Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' ');
                        console.log("   数据预览:", hex);
                    } catch (e) {
                        console.log("   无法读取数据预览");
                    }
                }
                
                parseState.parseSequence.push({
                    function: 'girf_sqlite3_bind_blob',
                    type: 'sqlite_bind',
                    index: this.index,
                    size: this.size,
                    timestamp: Date.now()
                });
            }
        });
    } catch (e) {
        console.log("❌ 无法Hook girf_sqlite3_bind_blob:", e.message);
    }
}

// 生成完整的解析报告
function generateCompleteReport() {
    console.log("\n" + "=".repeat(80));
    console.log("📊 完整的.ans文件解析分析报告");
    console.log("=".repeat(80));
    
    console.log("\n📁 文件操作统计:");
    console.log("   总操作数:", parseState.fileOperations.length);
    var openOps = parseState.fileOperations.filter(op => op.type === 'open');
    var readOps = parseState.fileOperations.filter(op => op.type.startsWith('read'));
    console.log("   打开操作:", openOps.length);
    console.log("   读取操作:", readOps.length);
    
    console.log("\n🗜️ 解压操作统计:");
    console.log("   解压操作数:", parseState.decompressOperations.length);
    if (parseState.decompressOperations.length > 0) {
        var totalCompressed = parseState.decompressOperations.reduce((sum, op) => sum + op.compressedSize, 0);
        var totalDecompressed = parseState.decompressOperations.reduce((sum, op) => sum + op.decompressedSize, 0);
        console.log("   总压缩数据:", totalCompressed, "字节");
        console.log("   总解压数据:", totalDecompressed, "字节");
        console.log("   压缩比:", (totalCompressed / totalDecompressed * 100).toFixed(1) + "%");
    }
    
    console.log("\n🔧 解析序列统计:");
    console.log("   总解析调用:", parseState.parseSequence.length);
    var dispatchCalls = parseState.parseSequence.filter(call => call.type === 'data_dispatch');
    var processingCalls = parseState.parseSequence.filter(call => call.type === 'data_processing');
    var sqliteCalls = parseState.parseSequence.filter(call => call.type === 'sqlite_bind');
    console.log("   数据分发调用:", dispatchCalls.length);
    console.log("   数据处理调用:", processingCalls.length);
    console.log("   SQLite绑定调用:", sqliteCalls.length);
    
    console.log("\n📋 关键发现:");
    if (openOps.length > 0) {
        console.log("   ✅ 成功跟踪到.ans文件访问");
        openOps.forEach(op => {
            console.log("      文件:", op.filename);
        });
    }
    
    if (parseState.decompressOperations.length > 0) {
        console.log("   ✅ 成功跟踪到数据解压过程");
    }
    
    if (parseState.parseSequence.length > 0) {
        console.log("   ✅ 成功跟踪到解析函数调用");
    }
    
    console.log("\n🎯 完整的解析流程理解:");
    console.log("1. 文件读取 → 2. 数据解压 → 3. 数据分发 → 4. SQLite存储");
    
    return parseState;
}

// 主执行流程
function main() {
    console.log("🚀 开始完整的.ans文件解析分析");
    
    // 设置所有Hook
    hookFileOperations();
    hookDecompression();
    hookMapDataParsing();
    
    // 定期生成报告
    setInterval(function() {
        if (parseState.fileOperations.length > 0 || 
            parseState.decompressOperations.length > 0 || 
            parseState.parseSequence.length > 0) {
            generateCompleteReport();
        }
    }, 10000); // 每10秒生成一次报告
    
    console.log("✅ 所有Hook已设置，等待.ans文件操作...");
    console.log("💡 提示：在地图App中移动地图以触发数据加载");
}

// 启动分析
main(); 