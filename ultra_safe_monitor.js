// 超安全的被动监控脚本 - 绝对不读取内存数据
// 严格按照成功脚本 ans_frida_test45.js 模式
// 兼容 Frida 12.9.7，使用 ES5 语法

(function() {
    'use strict';

    // ==================== 全局配置 ====================
    var config = {
        debug: false,
        maxLogCount: 50,
        logInterval: 2000,  // 每2秒记录一次统计
        enableFileMonitor: true,
        enableZlibMonitor: true
    };

    // ==================== 全局变量 ====================
    var gCallCounts = {
        read: 0,
        uncompress: 0,
        total: 0
    };
    var gLastReportTime = 0;
    var gModuleMap = {};

    // ==================== 工具函数 ====================
    function log(message) {
        console.log("[+] " + message);
    }

    function logError(message) {
        console.log("[-] " + message);
    }

    function formatAddress(address) {
        if (!address) return "0x0";
        try {
            return "0x" + address.toString(16);
        } catch (e) {
            return "0x???";
        }
    }

    // ==================== 被动监控函数 ====================
    function setupPassiveFileMonitor() {
        if (!config.enableFileMonitor) return;
        
        log("设置被动文件监控...");
        
        try {
            var readPtr = Module.findExportByName("libc.so", "read");
            if (!readPtr) {
                logError("read函数未找到");
                return;
            }
            
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    this.size = args[2].toInt32();
                },
                onLeave: function(retval) {
                    var bytesRead = retval.toInt32();
                    
                    // 只统计，不读取任何数据
                    if (bytesRead > 1000 && bytesRead < 100000) {
                        gCallCounts.read++;
                        gCallCounts.total++;
                        
                        // 定期报告统计信息
                        var now = new Date().getTime();
                        if (now - gLastReportTime > config.logInterval) {
                            log("📂 文件读取统计: " + gCallCounts.read + " 次大文件读取");
                            gLastReportTime = now;
                        }
                    }
                }
            });
            
            log("✅ 被动文件监控设置成功");
            
        } catch (e) {
            logError("设置文件监控失败: " + e);
        }
    }

    function setupPassiveZlibMonitor() {
        if (!config.enableZlibMonitor) return;
        
        log("设置被动zlib监控...");
        
        try {
            var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
            if (!uncompressPtr) {
                logError("uncompress函数未找到");
                return;
            }
            
            log("找到uncompress函数: " + formatAddress(uncompressPtr));
            
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    this.sourceLen = args[3].toInt32();
                },
                onLeave: function(retval) {
                    // 只统计成功的解压调用，不读取任何数据
                    if (retval.toInt32() === 0) {
                        gCallCounts.uncompress++;
                        gCallCounts.total++;
                        
                        log("🎯 [zlib解压] 解压操作成功，源数据: " + this.sourceLen + " 字节");
                        log("💡 这里就是解压后数据的位置，但为了安全不读取内存");
                        
                        // 统计报告
                        if (gCallCounts.uncompress % 5 === 0) {
                            log("📊 解压统计: 已完成 " + gCallCounts.uncompress + " 次解压操作");
                        }
                    }
                }
            });
            
            log("✅ 被动zlib监控设置成功");
            
        } catch (e) {
            logError("设置zlib监控失败: " + e);
        }
    }

    function setupStatisticsReporter() {
        // 定期报告统计信息
        setInterval(function() {
            if (gCallCounts.total > 0) {
                log("\n========== 数据流监控统计 ==========");
                log("文件读取次数: " + gCallCounts.read);
                log("zlib解压次数: " + gCallCounts.uncompress);
                log("总调用次数: " + gCallCounts.total);
                log("=====================================\n");
            }
        }, 10000);  // 每10秒报告一次
    }

    // ==================== 模块枚举 ====================
    function enumerateTargetModules() {
        log("枚举关键模块...");
        
        try {
            var modules = Process.enumerateModules();
            var targetModules = ["libz.so", "libamapnsq.so", "libamapr.so"];
            var foundCount = 0;
            
            for (var i = 0; i < modules.length; i++) {
                var module = modules[i];
                
                for (var j = 0; j < targetModules.length; j++) {
                    if (module.name === targetModules[j]) {
                        gModuleMap[module.name] = {
                            base: module.base,
                            size: module.size
                        };
                        
                        log("找到模块: " + module.name + " @ " + formatAddress(module.base));
                        foundCount++;
                        break;
                    }
                }
            }
            
            log("共找到 " + foundCount + " 个关键模块");
            return foundCount > 0;
            
        } catch (e) {
            logError("枚举模块失败: " + e);
            return false;
        }
    }

    // ==================== 异常处理 ====================
    function setupExceptionHandler() {
        Process.setExceptionHandler(function(exception) {
            logError("捕获异常: " + exception.type + " @ " + formatAddress(exception.address));
            return true;
        });
    }

    // ==================== 主程序 ====================
    function main() {
        log("高德地图超安全被动监控脚本");
        log("适用于 Frida 12.9.7，使用 ES5 语法");
        log("模式: 完全被动监控，不读取任何内存数据");
        
        // 设置异常处理
        setupExceptionHandler();
        
        try {
            // 1. 枚举模块
            if (!enumerateTargetModules()) {
                logError("未找到关键模块");
                return;
            }
            
            // 2. 延迟设置监控
            setTimeout(function() {
                try {
                    log("开始设置被动监控...");
                    
                    // 设置被动监控
                    setupPassiveFileMonitor();
                    setupPassiveZlibMonitor();
                    setupStatisticsReporter();
                    
                    log("✅ 被动监控设置完成");
                    log("💡 请在地图中移动，观察数据流统计");
                    log("💡 脚本只统计调用次数，不读取任何敏感数据");
                    
                } catch (e) {
                    logError("设置监控失败: " + e);
                }
            }, 3000);
            
        } catch (e) {
            logError("脚本初始化失败: " + e);
        }
        
        log("脚本设置完成，等待初始化...");
    }
    
    // 启动脚本
    main();
})(); 