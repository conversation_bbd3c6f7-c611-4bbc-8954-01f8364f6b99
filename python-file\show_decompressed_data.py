#!/usr/bin/env python3
"""
专门展示解压后数据的实际内容
直接回答：解压后数据是什么样的
"""

import os
import zlib

def show_real_decompressed_data():
    """展示真实的解压后数据内容"""
    print("🎯 解压后数据实际内容展示")
    print("=" * 80)
    
    # 找到ans文件
    ans_files = ['file/m1.ans', 'file/m3.ans']
    target_file = None
    
    for f in ans_files:
        if os.path.exists(f):
            target_file = f
            break
    
    if not target_file:
        print("❌ 未找到.ans文件")
        return
    
    print(f"📁 分析文件: {target_file}")
    
    with open(target_file, 'rb') as f:
        raw_data = f.read()
    
    # 查找zlib数据并解压
    zlib_pos = raw_data.find(b'\x78\x9c')
    if zlib_pos == -1:
        print("❌ 未找到zlib压缩数据")
        return
    
    print(f"✅ 找到zlib数据位置: {zlib_pos}")
    
    # 尝试解压多个数据块
    block_count = 0
    offset = zlib_pos
    
    while offset < len(raw_data) - 100 and block_count < 5:
        zlib_pos = raw_data.find(b'\x78\x9c', offset)
        if zlib_pos == -1:
            break
            
        # 尝试不同大小的数据块
        for test_size in [8192, 16384, 32768]:
            if zlib_pos + test_size <= len(raw_data):
                try:
                    compressed_chunk = raw_data[zlib_pos:zlib_pos + test_size]
                    decompressed = zlib.decompress(compressed_chunk)
                    
                    print("\n" + "=" * 80)
                    print(f"📊 解压后数据块 #{block_count} - 这就是你要的解压后数据！")
                    print("=" * 80)
                    
                    # 显示基本信息
                    print(f"压缩前大小: {len(compressed_chunk)} 字节")
                    print(f"解压后大小: {len(decompressed)} 字节")
                    print(f"压缩比: {len(compressed_chunk)/len(decompressed)*100:.1f}%")
                    
                    # 显示实际数据内容
                    show_actual_content(decompressed, block_count)
                    
                    offset = zlib_pos + test_size
                    block_count += 1
                    break
                except:
                    continue
        else:
            offset = zlib_pos + 1

def show_actual_content(data, block_id):
    """显示解压后数据的实际内容"""
    print(f"\n📋 数据块 #{block_id} 实际内容:")
    
    # 1. 头部分析
    header_bytes = data[:32]
    header_text = ""
    for b in header_bytes:
        if 32 <= b < 127:
            header_text += chr(b)
        else:
            header_text += "."
    
    print(f"数据头部: {header_text}")
    
    # 2. 十六进制显示
    print(f"\n十六进制数据 (前64字节):")
    hex_lines = []
    for i in range(0, min(64, len(data)), 16):
        chunk = data[i:i+16]
        hex_part = ' '.join(f'{b:02x}' for b in chunk)
        ascii_part = ''.join(chr(b) if 32 <= b < 127 else '.' for b in chunk)
        hex_lines.append(f"{i:04x}: {hex_part:<48} |{ascii_part}|")
    
    for line in hex_lines:
        print(line)
    
    # 3. 数据类型识别
    print(f"\n🔍 数据类型分析:")
    
    if data.startswith(b'DICE-AM'):
        print("✅ DICE-AM矢量地图数据")
        print("💡 这是解压后的地理矢量坐标数据")
        show_dice_am_details(data)
        
    elif b'{' in data[:50] or b'res_list' in data:
        print("✅ JSON配置数据")
        print("💡 这是解压后的地图配置文件")
        show_json_details(data)
        
    elif has_chinese_in_data(data):
        print("✅ 中文文本数据")
        print("💡 这是解压后的中文地名标注")
        show_chinese_details(data)
        
    else:
        print("✅ 其他二进制数据")
        print("💡 这是解压后的其他格式数据")
        
    print("\n" + "-" * 60)
    print("💡 重要说明: 这就是APP从.ans文件解压出来的原始数据")
    print("💡 下一步APP会用sub_5C394和sub_10F88函数进行结构化处理")
    print("💡 结构化后才能传给GPU进行渲染")

def show_dice_am_details(data):
    """显示DICE-AM数据的详细内容"""
    print(f"\n🎯 DICE-AM详细结构:")
    try:
        import struct
        
        # 解析头部
        magic = data[:7].decode('ascii')
        version = data[7]
        data_len = struct.unpack('<I', data[8:12])[0]
        point_count = struct.unpack('<I', data[12:16])[0]
        
        print(f"   魔数: {magic}")
        print(f"   版本: {version}")
        print(f"   数据长度: {data_len}")
        print(f"   坐标点数: {point_count}")
        
        # 显示前几个坐标点
        if len(data) >= 24:
            print(f"   前3个坐标点:")
            offset = 16
            for i in range(min(3, point_count)):
                if offset + 8 <= len(data):
                    x = struct.unpack('<f', data[offset:offset+4])[0]
                    y = struct.unpack('<f', data[offset+4:offset+8])[0]
                    print(f"      点{i}: ({x:.6f}, {y:.6f})")
                    offset += 12  # 假设每个点12字节
                    
        print(f"   💡 这些是真实的经纬度坐标，APP会转换为屏幕像素")
        
    except Exception as e:
        print(f"   ❌ 解析失败: {e}")

def show_json_details(data):
    """显示JSON数据的详细内容"""
    print(f"\n🎯 JSON详细内容:")
    try:
        # 提取可读文本
        text = ""
        for b in data:
            if 32 <= b < 127:
                text += chr(b)
            elif b == 0:
                break
                
        # 查找JSON开始和结束
        json_start = text.find('{')
        json_end = text.rfind('}')
        
        if json_start >= 0 and json_end > json_start:
            json_text = text[json_start:json_end+1]
            print(f"   JSON内容 (前200字符):")
            print(f"   {json_text[:200]}...")
            
            # 分析配置项
            if 'style' in json_text:
                print(f"   包含: 地图样式配置")
            if 'color' in json_text:
                print(f"   包含: 颜色设置")
            if 'res_list' in json_text:
                print(f"   包含: 资源列表")
                
        print(f"   💡 这些配置控制地图的外观和渲染参数")
        
    except Exception as e:
        print(f"   ❌ 解析失败: {e}")

def show_chinese_details(data):
    """显示中文数据的详细内容"""
    print(f"\n🎯 中文文本详细内容:")
    try:
        # 尝试UTF-8解码
        text = data.decode('utf-8', errors='ignore')
        chinese_chars = []
        
        # 提取中文字符
        for char in text:
            if '\u4e00' <= char <= '\u9fff':
                chinese_chars.append(char)
                
        if chinese_chars:
            # 显示前20个中文字符
            sample_text = ''.join(chinese_chars[:20])
            print(f"   中文内容: {sample_text}")
            print(f"   总字符数: {len(chinese_chars)}")
            
            # 分析可能的内容类型
            if any(word in text for word in ['路', '街', '道', '巷']):
                print(f"   包含: 道路名称")
            if any(word in text for word in ['市', '区', '县', '镇']):
                print(f"   包含: 行政区划")
            if any(word in text for word in ['公园', '广场', '大厦', '商场']):
                print(f"   包含: POI地点")
                
        print(f"   💡 这些文字会显示在地图上作为标注")
        
    except Exception as e:
        print(f"   ❌ 解析失败: {e}")

def has_chinese_in_data(data):
    """检查数据中是否包含中文字符"""
    try:
        text = data.decode('utf-8', errors='ignore')
        for char in text[:200]:  # 只检查前200字符
            if '\u4e00' <= char <= '\u9fff':
                return True
    except:
        pass
    return False

if __name__ == "__main__":
    show_real_decompressed_data() 