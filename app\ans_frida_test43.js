// ANS文件处理全链路跟踪脚本 - 专注于Java层nativeAddMapGestureMsg函数
// 最简化版本，只钩住Java层方法

(function() {
    console.log("[ANS全链路分析-简化版] 启动...");

    // 等待应用完全启动
    setTimeout(function() {
        try {
            setupJavaHooks();
        } catch (e) {
            console.log("[错误] 设置钩子失败: " + e);
        }
    }, 2000);

    function setupJavaHooks() {
        console.log("[+] 设置Java层钩子...");
        
        Java.perform(function() {
            try {
                // 钩住AMapController类的addGestureMapMessage方法
                var AMapController = Java.use("com.autonavi.ae.gmap.AMapController");
                if (AMapController.addGestureMapMessage) {
                    console.log("[+] 找到AMapController.addGestureMapMessage方法");
                    AMapController.addGestureMapMessage.implementation = function() {
                        console.log("[Java] 调用AMapController.addGestureMapMessage");
                        console.log("[Java] 参数: " + JSON.stringify(arguments));
                        
                        // 打印调用栈
                        console.log("[Java] Java调用栈:");
                        console.log(Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()));
                        
                        var result = this.addGestureMapMessage.apply(this, arguments);
                        console.log("[Java] AMapController.addGestureMapMessage返回: " + result);
                        return result;
                    };
                } else {
                    console.log("[!] 未找到AMapController.addGestureMapMessage方法");
                }
                
                // 钩住GLMapEngine类的nativeAddMapGestureMsg方法
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine.nativeAddMapGestureMsg) {
                    console.log("[+] 找到GLMapEngine.nativeAddMapGestureMsg方法");
                    
                    // 这是一个native方法，我们不能直接替换它的实现，但可以在调用前后添加日志
                    var nativeAddMapGestureMsg = GLMapEngine.nativeAddMapGestureMsg;
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function() {
                        console.log("[JNI] 调用GLMapEngine.nativeAddMapGestureMsg");
                        console.log("[JNI] 参数: " + JSON.stringify(arguments));
                        
                        // 打印调用栈
                        console.log("[JNI] Java调用栈:");
                        console.log(Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()));
                        
                        // 尝试找出Native函数地址
                        try {
                            var nativeMethod = this.class.getDeclaredMethod("nativeAddMapGestureMsg", 
                                Java.use("long").class, 
                                Java.use("int").class,
                                Java.use("float").class,
                                Java.use("float").class,
                                Java.use("float").class,
                                Java.use("float").class);
                                
                            if (nativeMethod) {
                                console.log("[JNI] Native方法信息: " + nativeMethod.toString());
                            }
                        } catch (e) {
                            console.log("[JNI] 获取Native方法信息失败: " + e);
                        }
                        
                        var result = nativeAddMapGestureMsg.apply(this, arguments);
                        console.log("[JNI] GLMapEngine.nativeAddMapGestureMsg返回: " + result);
                        return result;
                    };
                } else {
                    console.log("[!] 未找到GLMapEngine.nativeAddMapGestureMsg方法");
                }
                
                // 尝试钩住MapCore.nativeRender方法
                try {
                    var MapCore = Java.use("com.autonavi.amap.mapcore.MapCore");
                    if (MapCore.nativeRender) {
                        console.log("[+] 找到MapCore.nativeRender方法");
                        MapCore.nativeRender.implementation = function() {
                            // 只记录少量调用，避免日志过多
                            if (Math.random() < 0.01) {
                                console.log("[Java] 调用MapCore.nativeRender");
                            }
                            var result = this.nativeRender.apply(this, arguments);
                            return result;
                        };
                    }
                } catch (e) {
                    console.log("[!] 钩住MapCore失败: " + e);
                }
                
                // 尝试钩住其他可能的渲染相关方法
                try {
                    var GLMapState = Java.use("com.autonavi.jni.ae.gmap.GLMapState");
                    if (GLMapState.nativeGetMapState) {
                        console.log("[+] 找到GLMapState.nativeGetMapState方法");
                        GLMapState.nativeGetMapState.implementation = function() {
                            // 只记录少量调用，避免日志过多
                            if (Math.random() < 0.01) {
                                console.log("[Java] 调用GLMapState.nativeGetMapState");
                            }
                            var result = this.nativeGetMapState.apply(this, arguments);
                            return result;
                        };
                    }
                } catch (e) {
                    console.log("[!] 钩住GLMapState失败: " + e);
                }
                
                console.log("[+] Java层钩子设置完成");
                
            } catch (e) {
                console.log("[错误] 设置Java钩子失败: " + e);
            }
        });
    }
})(); 