(function() {
  console.log("[ANS文件分析器] 脚本启动...");
  
  // 全局变量
  var ansFiles = {};        
  var ansMmaps = {};        
  var ansOperations = [];   
  var funcHooks = {};       
  var libsLoaded = {};      
  var callStackLimit = 3;   
  
  // 防止应用崩溃
  var exit_ptr = Module.findExportByName("libc.so", "exit");
  if (exit_ptr) {
    Interceptor.replace(exit_ptr, new NativeCallback(function(code) {
      console.log("[ANS文件分析器] 拦截exit()调用: " + code);
      return 0;
    }, 'void', ['int']));
    console.log("[+] 拦截exit()函数");
  }

  // 跟踪库加载
  Interceptor.attach(Module.findExportByName(null, "dlopen"), {
    onEnter: function(args) {
      this.libpath = args[0].readUtf8String();
    },
    onLeave: function(retval) {
      if (this.libpath && this.libpath.indexOf("libamaploc.so") !== -1) {
        console.log("[+] 发现目标库: " + this.libpath);
        libsLoaded[this.libpath] = retval;
        setTimeout(hookAmaplocExports, 500); 
      }
    }
  });

  // 监控文件打开
  var open_ptr = Module.findExportByName("libc.so", "open");
  if (open_ptr) {
    Interceptor.attach(open_ptr, {
      onEnter: function(args) {
        try {
          var path = args[0].readUtf8String();
          if (path && path.indexOf(".ans") !== -1) {
            this.path = path;
            this.filename = path.split("/").pop();
            this.isAns = true;
            
            // 捕获调用栈
            try {
              var stackFrames = Thread.backtrace(this.context, Backtracer.ACCURATE);
              var slicedStack = [];
              for (var i = 0; i < stackFrames.length && i < callStackLimit; i++) {
                slicedStack.push(DebugSymbol.fromAddress(stackFrames[i]));
              }
              this.stack = slicedStack;
            } catch(e) {
              this.stack = [];
            }
          }
        } catch(e) {}
      },
      onLeave: function(retval) {
        if (!this.isAns) return;
        
        var fd = retval.toInt32();
        if (fd > 0) {
          ansFiles[fd] = {
            path: this.path,
            filename: this.filename,
            openTime: new Date().getTime(),
            reads: [],
            maps: []
          };
          
          console.log("[ANS打开] " + this.filename + " (fd: " + fd + ")");
          
          // 打印调用栈
          if (this.stack && this.stack.length > 0) {
            console.log("[调用栈]:");
            for (var i = 0; i < this.stack.length; i++) {
              var sym = this.stack[i];
              var name = sym.name || "0x" + sym.address.toString(16);
              console.log("  " + i + ": " + name);
            }
          }
          
          ansOperations.push({
            type: "open",
            file: this.filename,
            path: this.path,
            fd: fd,
            time: new Date().getTime()
          });
        }
      }
    });
    console.log("[+] 监控文件打开");
  }
  
  // 监控文件读取
  var read_ptr = Module.findExportByName("libc.so", "read");
  if (read_ptr) {
    Interceptor.attach(read_ptr, {
      onEnter: function(args) {
        this.fd = args[0].toInt32();
        this.buffer = args[1];
        this.size = args[2].toInt32();
        
        if (ansFiles[this.fd]) {
          this.isAnsFile = true;
          this.fileInfo = ansFiles[this.fd];
          this.readStart = new Date().getTime();
        }
      },
      onLeave: function(retval) {
        if (!this.isAnsFile) return;
        
        var bytesRead = retval.toInt32();
        if (bytesRead <= 0) return;
        
        var duration = new Date().getTime() - this.readStart;
        
        // 计算偏移量 - 兼容方式
        var totalOffset = 0;
        for (var i = 0; i < this.fileInfo.reads.length; i++) {
          totalOffset += this.fileInfo.reads[i].size;
        }
        
        // 记录读取信息
        this.fileInfo.reads.push({
          offset: totalOffset,
          size: bytesRead,
          time: new Date().getTime(),
          duration: duration
        });
        
        console.log("[ANS读取] " + this.fileInfo.filename + ", 大小: " + bytesRead + " 字节, 耗时: " + duration + "ms");
        
        // 分析头部数据
        if (this.fileInfo.reads.length === 1 && bytesRead >= 8) {
          try {
            var header = new Uint8Array(Memory.readByteArray(this.buffer, Math.min(16, bytesRead)));
            var headerHex = "";
            for (var i = 0; i < header.length; i++) {
              var hex = header[i].toString(16);
              if (hex.length === 1) hex = "0" + hex;
              headerHex += hex + " ";
            }
            console.log("[ANS头部] " + headerHex);
            
            this.fileInfo.header = headerHex;
            this.fileInfo.magicBytes = headerHex.substring(0, 11);
            
            ansOperations.push({
              type: "read_header",
              file: this.fileInfo.filename,
              fd: this.fd,
              header: headerHex,
              time: new Date().getTime()
            });
          } catch(e) {
            console.log("[错误] 解析头部: " + e);
          }
        }
      }
    });
    console.log("[+] 监控文件读取");
  }
  
  // 监控内存映射
  var mmap_ptr = Module.findExportByName("libc.so", "mmap");
  if (mmap_ptr) {
    Interceptor.attach(mmap_ptr, {
      onEnter: function(args) {
        this.addr = args[0];
        this.length = args[1].toInt32();
        this.prot = args[2].toInt32();
        this.flags = args[3].toInt32();
        this.fd = args[4].toInt32();
        this.offset = args[5].toInt32();
        
        if (ansFiles[this.fd]) {
          this.isAnsFile = true;
          this.fileInfo = ansFiles[this.fd];
        }
      },
      onLeave: function(retval) {
        if (!this.isAnsFile) return;
        
        // 记录内存映射信息
        var mapInfo = {
          address: retval,
          size: this.length,
          protection: this.prot,
          offset: this.offset,
          time: new Date().getTime()
        };
        
        this.fileInfo.maps.push(mapInfo);
        ansMmaps[retval.toString()] = {
          file: this.fileInfo.filename,
          path: this.fileInfo.path,
          mapInfo: mapInfo
        };
        
        console.log("[ANS映射] " + this.fileInfo.filename + 
                   " -> 内存: " + retval +
                   ", 大小: " + this.length + 
                   ", 偏移: " + this.offset);
                   
        // 分析内存中的数据结构
        analyzeAnsDataStructure(retval, this.length, this.fileInfo.filename);
        
        ansOperations.push({
          type: "mmap",
          file: this.fileInfo.filename,
          address: retval.toString(),
          size: this.length,
          protection: this.prot,
          time: new Date().getTime()
        });
      }
    });
    console.log("[+] 监控内存映射");
  }
  
  // 内存访问跟踪器
  function setupMemoryAccessMonitor(address, size, filename) {
    try {
      MemoryAccessMonitor.enable(address, 64, {
        onAccess: function(details) {
          console.log("[内存访问] " + filename + " 在 " + details.address + 
                     ", 操作: " + details.operation + 
                     ", PC: " + details.from);
                      
          // 获取访问者
          var symbol = DebugSymbol.fromAddress(details.from);
          var accessorName = symbol.name || "0x" + details.from.toString(16);
           
          ansOperations.push({
            type: "memory_access",
            file: filename,
            address: details.address.toString(),
            operation: details.operation,
            accessor: accessorName,
            time: new Date().getTime()
          });
        }
      });
    } catch(e) {
      // 可能不支持内存访问监控
      console.log("[警告] 内存访问监控不可用: " + e.message);
    }
  }
  
  // 分析ANS数据结构
  function analyzeAnsDataStructure(address, size, filename) {
    try {
      if (size < 16) return;
      
      // 读取前16字节分析文件格式
      var headerData = Memory.readByteArray(address, 16);
      var header = new Uint8Array(headerData);
      
      var magic = "";
      for (var i = 0; i < 4; i++) {
        var hex = header[i].toString(16);
        if (hex.length === 1) hex = "0" + hex;
        magic += hex + " ";
      }
      
      var version = "";
      for (var i = 4; i < 8; i++) {
        var hex = header[i].toString(16);
        if (hex.length === 1) hex = "0" + hex;
        version += hex + " ";
      }
      
      console.log("[ANS结构] " + filename + " 魔数: " + magic + ", 版本: " + version);
      
      // 尝试确定文件类型
      var fileType = "未知";
      if (magic === "00 00 00 00 ") {
        fileType = "空头部ANS";
      } else if (magic === "7f 45 4c 46 ") {
        fileType = "ELF格式ANS";
      } else if (magic === "13 02 a6 e0 ") {
        fileType = "特殊格式ANS";
      }
      
      console.log("[ANS类型] " + filename + ": " + fileType);
      
      // 设置内存访问监控
      setupMemoryAccessMonitor(address, size, filename);
    } catch(e) {
      console.log("[错误] 分析ANS结构: " + e);
    }
  }
  
  // Hook libamaploc.so导出函数
  function hookAmaplocExports() {
    try {
      var amaplocdynamic = Module.findBaseAddress('libamaploc.so');
      if (!amaplocdynamic) {
        console.log("[!] 找不到libamaploc.so");
        return;
      }
      
      console.log("[+] libamaploc.so 基址: " + amaplocdynamic);
      
      var exports = Module.enumerateExports('libamaploc.so');
      console.log("[+] 找到 " + exports.length + " 个导出函数");
      
      // 过滤可能与ANS处理相关的函数 - 兼容方式
      var interestingExports = [];
      for (var i = 0; i < exports.length; i++) {
        var exp = exports[i];
        var name = exp.name.toLowerCase();
        if (name.indexOf("parse") !== -1 || 
            name.indexOf("load") !== -1 || 
            name.indexOf("read") !== -1 || 
            name.indexOf("map") !== -1 || 
            name.indexOf("file") !== -1 || 
            name.indexOf("ans") !== -1 || 
            name.indexOf("data") !== -1) {
          interestingExports.push(exp);
        }
      }
      
      console.log("[+] 找到 " + interestingExports.length + " 个可能相关的导出函数");
      
      // Hook这些函数
      for (var i = 0; i < interestingExports.length; i++) {
        var exp = interestingExports[i];
        try {
          Interceptor.attach(exp.address, {
            onEnter: function(args) {
              this.funcName = exp.name;
              console.log("[调用] " + exp.name);
              
              // 尝试获取参数
              this.args = [];
              for (var i = 0; i < 4; i++) {
                try {
                  var arg = args[i];
                  if (arg.toInt32() === 0) {
                    this.args.push("NULL");
                  } else if (arg.toInt32() < 1000) {
                    this.args.push(arg.toInt32().toString());
                  } else {
                    // 可能是字符串
                    try {
                      var str = arg.readUtf8String();
                      if (str && str.length > 0 && str.length < 100) {
                        this.args.push('"' + str + '"');
                      } else {
                        this.args.push("0x" + arg.toString(16));
                      }
                    } catch(e) {
                      this.args.push("0x" + arg.toString(16));
                    }
                  }
                } catch(e) {
                  this.args.push("?");
                }
              }
              
              // 获取调用栈 - 兼容方式
              try {
                var stackFrames = Thread.backtrace(this.context, Backtracer.ACCURATE);
                var stackInfo = [];
                for (var i = 0; i < stackFrames.length && i < callStackLimit; i++) {
                  var sym = DebugSymbol.fromAddress(stackFrames[i]);
                  stackInfo.push(sym.name || "0x" + sym.address.toString(16));
                }
                this.stack = stackInfo;
              } catch(e) {
                this.stack = [];
              }
              
              // 开始时间
              this.startTime = new Date().getTime();
            },
            onLeave: function(retval) {
              var duration = new Date().getTime() - this.startTime;
              
              console.log("[返回] " + this.funcName + 
                         " -> 0x" + retval.toString(16) + 
                         ", 耗时: " + duration + "ms");
                          
              if (this.args.length > 0) {
                console.log("  参数: " + this.args.join(", "));
              }
              
              if (this.stack && this.stack.length > 0) {
                console.log("  调用栈: ");
                for (var i = 0; i < this.stack.length; i++) {
                  console.log("    " + i + ": " + this.stack[i]);
                }
              }
              
              ansOperations.push({
                type: "native_call",
                function: this.funcName,
                args: this.args,
                result: "0x" + retval.toString(16),
                duration: duration,
                callstack: this.stack,
                time: new Date().getTime()
              });
            }
          });
          funcHooks[exp.name] = true;
          console.log("[+] 已Hook: " + exp.name);
        } catch(e) {
          console.log("[!] Hook " + exp.name + " 失败: " + e);
        }
      }
    } catch(e) {
      console.log("[!] hookAmaplocExports 失败: " + e);
    }
  }
  
  // Java层Hook
  setTimeout(function() {
    Java.perform(function() {
      console.log("[+] Java环境准备就绪");
      
      // 监控AMapController类
      try {
        var AMapController = Java.use("com.autonavi.ae.gmap.AMapController");
        
        // 监控资源加载器设置
        if (AMapController.setAppResourceLoader) {
          AMapController.setAppResourceLoader.implementation = function(loader) {
            console.log("[Java] AMapController.setAppResourceLoader: " + loader);
            
            if (loader) {
              try {
                var className = loader.getClass().getName();
                console.log("[Java] 加载器类: " + className);
              } catch(e) {}
            }
            
            ansOperations.push({
              type: "java_call",
              class: "AMapController",
              method: "setAppResourceLoader",
              arg: loader ? loader.toString() : "null",
              time: new Date().getTime()
            });
            
            return this.setAppResourceLoader(loader);
          };
        }
      } catch(e) {
        console.log("[!] Hook AMapController 失败: " + e);
      }
      
      // 监控InterfaceAppImpl类
      try {
        var InterfaceAppImpl = Java.use("com.amap.jni.app.InterfaceAppImpl");
        
        if (InterfaceAppImpl.getNativeResourceLoader) {
          InterfaceAppImpl.getNativeResourceLoader.implementation = function() {
            console.log("[Java] InterfaceAppImpl.getNativeResourceLoader");
            var result = this.getNativeResourceLoader();
            
            if (result) {
              try {
                var className = result.getClass().getName();
                console.log("[Java] 资源加载器类: " + className);
              } catch(e) {}
            }
            
            ansOperations.push({
              type: "java_call",
              class: "InterfaceAppImpl",
              method: "getNativeResourceLoader",
              result: result ? result.toString() : "null",
              time: new Date().getTime()
            });
            
            return result;
          };
        }
      } catch(e) {
        console.log("[!] Hook InterfaceAppImpl 失败: " + e);
      }
      
      // 监控系统库加载
      try {
        var System = Java.use("java.lang.System");
        System.loadLibrary.implementation = function(libName) {
          console.log("[Java] System.loadLibrary: " + libName);
          
          // 捕获调用栈
          try {
            var exception = Java.use("java.lang.Exception").$new();
            var stackElements = exception.getStackTrace();
            var stack = [];
            
            for (var i = 0; i < 5 && i < stackElements.length; i++) {
              stack.push(stackElements[i].getClassName() + "." + stackElements[i].getMethodName());
            }
            
            console.log("[Java调用栈]:");
            for (var i = 0; i < stack.length; i++) {
              console.log("  " + i + ": " + stack[i]);
            }
            
            if (libName === "amaploc" || libName.indexOf("amap") !== -1) {
              ansOperations.push({
                type: "load_library",
                library: libName,
                callstack: stack,
                time: new Date().getTime()
              });
            }
          } catch(e) {}
          
          return this.loadLibrary(libName);
        };
      } catch(e) {
        console.log("[!] Hook System.loadLibrary 失败: " + e);
      }
    });
  }, 2000);
  
  // 定期打印分析报告
  setTimeout(function() {
    console.log("\n[ANS文件分析报告]");
    
    // ANS文件统计
    var fileCount = 0;
    for (var fd in ansFiles) {
      fileCount++;
    }
    console.log("\n1. 发现的ANS文件: " + fileCount);
    
    for (var fd in ansFiles) {
      var file = ansFiles[fd];
      console.log("  - " + file.filename);
      console.log("    路径: " + file.path);
      console.log("    读取次数: " + file.reads.length);
      console.log("    映射次数: " + file.maps.length);
      if (file.header) {
        console.log("    头部数据: " + file.header);
      }
    }
    
    // 操作时序分析
    console.log("\n2. 操作时序");
    
    var fileOpenings = [];
    var fileReads = [];
    var fileMaps = [];
    for (var i = 0; i < ansOperations.length; i++) {
      var op = ansOperations[i];
      if (op.type === "open") fileOpenings.push(op);
      if (op.type === "read_header") fileReads.push(op);
      if (op.type === "mmap") fileMaps.push(op);
    }
    
    console.log("  文件打开: " + fileOpenings.length);
    console.log("  头部读取: " + fileReads.length);
    console.log("  内存映射: " + fileMaps.length);
    
    // 函数调用统计
    var nativeCalls = [];
    for (var i = 0; i < ansOperations.length; i++) {
      var op = ansOperations[i];
      if (op.type === "native_call") nativeCalls.push(op);
    }
    console.log("\n3. Native函数调用: " + nativeCalls.length);
    
    // 创建函数调用频率统计
    var funcFreq = {};
    for (var i = 0; i < nativeCalls.length; i++) {
      var call = nativeCalls[i];
      if (!funcFreq[call.function]) funcFreq[call.function] = 0;
      funcFreq[call.function]++;
    }
    
    // 排序并显示前10个最常调用的函数
    var sortedFuncs = [];
    for (var func in funcFreq) {
      sortedFuncs.push(func);
    }
    
    sortedFuncs.sort(function(a, b) {
      return funcFreq[b] - funcFreq[a];
    });
    
    var topFuncs = sortedFuncs.slice(0, 10);
    console.log("  最常调用的函数:");
    for (var i = 0; i < topFuncs.length; i++) {
      var func = topFuncs[i];
      console.log("    " + (i+1) + ". " + func + " (" + funcFreq[func] + "次)");
    }
    
    // 分析ANS文件类型
    console.log("\n4. ANS文件类型分析");
    var fileTypes = {};
    
    for (var fd in ansFiles) {
      var file = ansFiles[fd];
      if (file.magicBytes) {
        if (!fileTypes[file.magicBytes]) fileTypes[file.magicBytes] = [];
        fileTypes[file.magicBytes].push(file.filename);
      }
    }
    
    for (var magic in fileTypes) {
      var type = "未知";
      if (magic.indexOf("00 00 00 00") === 0) type = "空头部ANS";
      else if (magic.indexOf("7f 45 4c 46") === 0) type = "ELF格式ANS";
      else if (magic.indexOf("13 02 a6 e0") === 0) type = "特殊格式ANS";
      
      console.log("  类型: " + type + " (魔数: " + magic + ")");
      console.log("    文件: " + fileTypes[magic].join(", "));
    }
    
    console.log("\n[分析完成]");
  }, 30000);
  
  console.log("[ANS文件分析器] 脚本设置完成");
})();
