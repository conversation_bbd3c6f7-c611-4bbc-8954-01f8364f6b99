/**
 * IDA Pro 数据结构分析辅助脚本
 * 用于验证和获取高德地图真实的数据结构信息
 * Frida 12.9.7 兼容版本
 */
(function() {
    console.log("[IDA Structure Analyzer] IDA Pro数据结构分析器启动...");

    var moduleInfo = {
        libamapr: null,
        libamapnsq: null,
        libz: null
    };

    var dataStructures = {
        diceAmHeaders: [],
        parsedBlocks: [],
        functionCalls: []
    };

    // === 获取模块信息 ===
    function getModuleInfo() {
        console.log("[Module] 扫描加载的模块...");
        
        var modules = Process.enumerateModules();
        for (var i = 0; i < modules.length; i++) {
            var mod = modules[i];
            if (mod.name === "libamapr.so") {
                moduleInfo.libamapr = mod;
                console.log("[Module] libamapr.so 找到:");
                console.log("   - 基址: " + mod.base);
                console.log("   - 大小: 0x" + mod.size.toString(16));
                console.log("   - 路径: " + mod.path);
            } else if (mod.name === "libamapnsq.so") {
                moduleInfo.libamapnsq = mod;
                console.log("[Module] libamapnsq.so 找到:");
                console.log("   - 基址: " + mod.base);
                console.log("   - 大小: 0x" + mod.size.toString(16));
                console.log("   - 路径: " + mod.path);
            } else if (mod.name === "libz.so") {
                moduleInfo.libz = mod;
                console.log("[Module] libz.so 找到:");
                console.log("   - 基址: " + mod.base);
            }
        }
    }

    // === 验证IDA分析的函数地址 ===
    function verifyIdaAddresses() {
        console.log("[Verify] 验证IDA Pro分析的函数地址...");
        
        if (!moduleInfo.libamapr || !moduleInfo.libamapnsq) {
            console.log("[Verify] 必要模块未加载");
            return;
        }

        var addresses = [
            {name: "nativeAddMapGestureMsg", offset: 0x6ee70c, size: 0x1c},
            {name: "getMapEngineInstance", offset: 0x6FB98C, size: 0x8},
            {name: "processGestureMessage", offset: 0x6FB530, size: 0x8},
            {name: "triggerRenderUpdate", offset: 0x6FBC78, size: 0x10},
            {name: "updateMapView", offset: 0x6FB9E0, size: 0x8},
            {name: "finalizeProcessing", offset: 0x6FB550, size: 0xc}
        ];

        for (var i = 0; i < addresses.length; i++) {
            var addr = addresses[i];
            try {
                var ptr = moduleInfo.libamapr.base.add(addr.offset);
                var bytes = ptr.readByteArray(Math.min(addr.size, 16));
                console.log("[Verify] " + addr.name + " (0x" + addr.offset.toString(16) + "):");
                console.log("   - 地址: " + ptr);
                console.log("   - 字节: " + hexdump(bytes, {length: addr.size}));
            } catch (e) {
                console.log("[Verify] " + addr.name + " 验证失败: " + e);
            }
        }

        var nsqAddresses = [
            {name: "sub_C654", offset: 0xC654, size: 0x98},
            {name: "sub_5C394", offset: 0x5C394, size: 0x920},
            {name: "sub_10F88", offset: 0x10F88, size: 0x510}
        ];

        for (var j = 0; j < nsqAddresses.length; j++) {
            var addr = nsqAddresses[j];
            try {
                var ptr = moduleInfo.libamapnsq.base.add(addr.offset);
                var bytes = ptr.readByteArray(16);
                console.log("[Verify] " + addr.name + " (0x" + addr.offset.toString(16) + "):");
                console.log("   - 地址: " + ptr);
                console.log("   - 前16字节: " + hexdump(bytes, {length: 16}));
            } catch (e) {
                console.log("[Verify] " + addr.name + " 验证失败: " + e);
            }
        }
    }

    // === 深度分析DICE-AM数据结构 ===
    function analyzeDataStructure(buffer, size, source) {
        console.log("[Structure] 分析数据结构，来源: " + source);
        
        if (size < 16) {
            console.log("[Structure] 数据太小，跳过分析");
            return null;
        }

        var structure = {
            source: source,
            size: size,
            timestamp: Date.now(),
            analysis: {}
        };

        try {
            var headerSize = Math.min(size, 32);
            var header = buffer.readByteArray(headerSize);
            
            console.log("[Structure] 数据头部 (" + headerSize + " 字节):");
            console.log(hexdump(header, {length: headerSize}));

            try {
                var headerStr = buffer.readUtf8String(16);
                structure.analysis.headerString = headerStr;
                console.log("[Structure] 头部字符串: '" + headerStr + "'");
                
                if (headerStr.indexOf("DICE-AM") !== -1) {
                    structure.analysis.isDiceAm = true;
                    console.log("[Structure] 确认为 DICE-AM 数据块");
                    
                    analyzeDiceAmStructure(buffer, size, structure);
                }
            } catch (e) {
                console.log("[Structure] 头部字符串读取失败: " + e);
            }

            console.log("[Structure] 逐字节分析:");
            for (var i = 0; i < Math.min(size, 32); i += 4) {
                try {
                    var u32 = buffer.add(i).readU32();
                    var u16_1 = buffer.add(i).readU16();
                    var u16_2 = buffer.add(i + 2).readU16();
                    var u8_vals = [];
                    for (var j = 0; j < 4 && (i + j) < size; j++) {
                        u8_vals.push(buffer.add(i + j).readU8());
                    }
                    
                    console.log("   [" + i.toString() + "] " +
                        "u32=0x" + u32.toString(16) + " " +
                        "u16=" + u16_1 + "," + u16_2 + " " +
                        "u8=" + u8_vals.join(","));
                } catch (e) {
                    break;
                }
            }

            dataStructures.parsedBlocks.push(structure);
            
        } catch (e) {
            console.log("[Structure] 分析失败: " + e);
        }

        return structure;
    }

    // === 分析DICE-AM特定结构 ===
    function analyzeDiceAmStructure(buffer, size, structure) {
        console.log("[DICE-AM] 深度分析 DICE-AM 结构...");
        
        try {
            if (size >= 9) {
                var versionByte = buffer.add(8).readU8();
                var version = versionByte ^ 0xAB;
                structure.analysis.version = version;
                structure.analysis.versionByte = versionByte;
                console.log("[DICE-AM] 版本信息: byte=0x" + versionByte.toString(16) + ", version=" + version);
            }

            if (size >= 20) {
                var marker16 = buffer.add(16).readU32();
                var count20 = buffer.add(20).readU32();
                structure.analysis.dataMarker = marker16;
                structure.analysis.possibleCount = count20;
                
                console.log("[DICE-AM] 数据标记:");
                console.log("   - 偏移16: 0x" + marker16.toString(16) + " (" + marker16 + ")");
                console.log("   - 偏移20: 0x" + count20.toString(16) + " (" + count20 + ")");
                
                var markerStr = "";
                try {
                    markerStr = buffer.add(16).readUtf8String(4);
                    console.log("   - 标记字符串: '" + markerStr + "'");
                } catch (e) {
                    // 忽略
                }
            }

            if (size >= 40) {
                console.log("[DICE-AM] 搜索坐标数据...");
                for (var offset = 24; offset <= Math.min(size - 8, 64); offset += 4) {
                    try {
                        var float1 = buffer.add(offset).readFloat();
                        var float2 = buffer.add(offset + 4).readFloat();
                        
                        if (float1 >= 70 && float1 <= 140 && float2 >= 10 && float2 <= 60) {
                            console.log("[DICE-AM] 可能的坐标 @" + offset + ": (" + 
                                float1.toFixed(6) + ", " + float2.toFixed(6) + ")");
                        }
                    } catch (e) {
                        break;
                    }
                }
            }

            dataStructures.diceAmHeaders.push({
                timestamp: Date.now(),
                size: size,
                version: structure.analysis.version,
                dataMarker: structure.analysis.dataMarker,
                source: structure.source
            });

        } catch (e) {
            console.log("[DICE-AM] DICE-AM分析失败: " + e);
        }
    }

    // === 设置监控Hook ===
    function setupStructureHooks() {
        console.log("[Hook] 设置结构分析Hook...");

        if (!moduleInfo.libamapnsq) {
            console.log("[Hook] libamapnsq.so 未找到");
            return;
        }

        try {
            var sub_10F88 = moduleInfo.libamapnsq.base.add(0x10F88);
            Interceptor.attach(sub_10F88, {
                onEnter: function(args) {
                    // 基于实际观察，重新分析参数结构
                    console.log("[Hook] sub_10F88 调用，参数分析:");
                    console.log("   - args[0]: " + args[0]);
                    console.log("   - args[1]: " + args[1]);
                    console.log("   - args[2]: " + args[2]);
                    
                    // 尝试多种参数组合来找到真实的数据缓冲区
                    for (var i = 0; i < 3; i++) {
                        try {
                            if (args[i] && !args[i].isNull()) {
                                // 检查是否为有效的内存地址
                                var testRead = args[i].readU32();
                                console.log("   - args[" + i + "] 可读，前4字节: 0x" + testRead.toString(16));
                                
                                // 尝试读取更多数据看是否包含有意义的内容
                                var testData = args[i].readByteArray(64);
                                var header = hexdump(testData, {length: 64});
                                if (header.indexOf("DICE") !== -1 || header.indexOf("ANS") !== -1) {
                                    console.log("   - args[" + i + "] 可能包含地图数据!");
                                    console.log(header);
                                }
                            }
                        } catch (e) {
                            console.log("   - args[" + i + "] 不可读或为空: " + e);
                        }
                    }
                    
                    dataStructures.functionCalls.push({
                        function: "sub_10F88",
                        timestamp: Date.now(),
                        args: [args[0].toString(), args[1].toString(), args[2].toString()]
                    });
                },
                onLeave: function(retval) {
                    console.log("[Hook] sub_10F88 返回: " + retval);
                }
            });

            var sub_5C394 = moduleInfo.libamapnsq.base.add(0x5C394);
            Interceptor.attach(sub_5C394, {
                onEnter: function(args) {
                    console.log("[Hook] sub_5C394 调用，参数分析:");
                    console.log("   - args[0]: " + args[0]);
                    console.log("   - args[1]: " + args[1]);
                    
                    // 检查是否为指向数据的指针
                    try {
                        if (args[0] && !args[0].isNull()) {
                            // 读取指针指向的内容
                            var ptr = args[0].readPointer();
                            console.log("   - args[0] 指向: " + ptr);
                            
                            if (ptr && !ptr.isNull()) {
                                // 尝试读取指针指向的数据
                                var data = ptr.readByteArray(128);
                                var header = hexdump(data, {length: 64});
                                console.log("   - 指针数据预览:");
                                console.log(header);
                                
                                // 检查是否包含地图数据标识
                                try {
                                    var headerStr = ptr.readUtf8String(16);
                                    if (headerStr.indexOf("DICE") !== -1 || headerStr.indexOf("ANS") !== -1) {
                                        console.log("   - 发现地图数据标识: " + headerStr);
                                        analyzeDataStructure(ptr, 128, "sub_5C394_ptr");
                                    }
                                } catch (e) {
                                    console.log("   - 指针数据不是UTF8字符串");
                                }
                            }
                        }
                        
                        // 也检查 args[1] 是否包含有用信息
                        if (args[1] && !args[1].isNull()) {
                            var size = args[1].toInt32();
                            console.log("   - args[1] 作为大小: " + size);
                            
                            // 如果大小合理，尝试用这个大小读取 args[0] 的数据
                            if (size > 0 && size < 10240) {  // 合理的数据大小范围
                                try {
                                    var sizedData = args[0].readByteArray(size);
                                    console.log("   - 按大小读取的数据:");
                                    console.log(hexdump(sizedData, {length: Math.min(size, 128)}));
                                    
                                    analyzeDataStructure(args[0], size, "sub_5C394_sized");
                                } catch (e) {
                                    console.log("   - 按大小读取失败: " + e);
                                }
                            }
                        }
                        
                    } catch (e) {
                        console.log("   - 参数分析失败: " + e);
                    }
                }
            });

            console.log("[Hook] 结构分析Hook设置完成");

        } catch (e) {
            console.log("[Hook] Hook设置失败: " + e);
        }
    }

    // === 定期报告分析结果 ===
    function setupReporting() {
        setInterval(function() {
            console.log("\n======= 数据结构分析报告 =======");
            console.log("DICE-AM数据块数量: " + dataStructures.diceAmHeaders.length);
            console.log("解析的数据块总数: " + dataStructures.parsedBlocks.length);
            console.log("函数调用次数: " + dataStructures.functionCalls.length);

            if (dataStructures.diceAmHeaders.length > 0) {
                console.log("\nDICE-AM统计:");
                var versions = {};
                var markers = {};
                
                for (var i = 0; i < dataStructures.diceAmHeaders.length; i++) {
                    var header = dataStructures.diceAmHeaders[i];
                    versions[header.version] = (versions[header.version] || 0) + 1;
                    markers[header.dataMarker] = (markers[header.dataMarker] || 0) + 1;
                }
                
                console.log("   版本分布: " + JSON.stringify(versions));
                console.log("   数据标记分布: " + JSON.stringify(markers));
            }

            console.log("=====================================\n");
        }, 30000);
    }

    // === 主初始化函数 ===
    function initialize() {
        console.log("[Init] 初始化IDA结构分析器...");

        setTimeout(function() {
            getModuleInfo();
            verifyIdaAddresses();
            setupStructureHooks();
            setupReporting();
            
            console.log("[Init] IDA结构分析器初始化完成！");
            console.log("请在应用中触发地图操作以开始数据收集...");
        }, 2000);
    }

    initialize();

    console.log("[IDA Structure Analyzer] 脚本加载完成");
})(); 