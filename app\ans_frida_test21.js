setTimeout(function() {

    // 防止崩溃
    var libc_exit = Module.findExportByName("libc.so", "exit");
    if (libc_exit) {
      Interceptor.replace(libc_exit, new NativeCallback(function() {
        return 0;
      }, 'void', ['int']));
    }

    //根据导出函数名打印地址
    var helloAddr = Module.findExportByName("libamapnsq.so","weftlattimqugxvvpvso");
    console.log(helloAddr);
    if(helloAddr != null){
        //Interceptor.attach是Frida里的一个拦截器
        Interceptor.attach(helloAddr,{
            //onEnter里可以打印和修改参数
            onEnter: function(args){  //args传入参数
                console.log(args[0]);  //打印第一个参数的值
                //console.log(this.context.x1);  // 打印寄存器内容
                console.log(args[1].toInt32()); //toInt32()转十进制
                console.log(args[2].readCString()); //读取字符串 char类型
                console.log(hexdump(args[2])); //内存dump

            },
            //onLeave里可以打印和修改返回值
            onLeave: function(retval){  //retval返回值
                console.log(retval);
                console.log("retval",retval.toInt32());
            }
        })
    }

    // 在ans_frida_test2.js中添加
    var decompressAddr = Module.findExportByName("libamapnsq.so", "weftlattimqugxvvpvso");
    if (decompressAddr) {
        Interceptor.attach(decompressAddr, {
            onEnter: function(args) {
                // this.outBuf = args[0];
                // this.outSize = args[1].toInt32();
                // this.inBuf = args[2];
                // this.inSize = args[3].toInt32();
                console.log(args[0])
                console.log(args[1])
                console.log(args[2])
                console.log(args[3])
                console.log(args[4])
                //console.log("[ZSTD] 解压数据: 输入大小="+this.inSize+", 预期输出="+this.outSize);

                // 可选：转储前16字节识别文件格式
                console.log("压缩数据头：" + hexdump(args[2], {length: 16}));
            },
            onLeave: function(retval) {
                console.log("[ZSTD] 解压完成: 实际输出="+retval);
            }
        });
    }
    Java.perform(function(){
        
        var Demo = Java.use("com.autonavi.map.activity.NewMapActivity");

        Demo.J.implementation = function(){
            //将参数a和b的值改为123和456。
            console.log("onQsLoadComplete");
            console.log(JSON.stringify(this.y));
        }
        // var off = Java.use("com.amap.bundle.blutils.PathManager");
        // off.containsImportantFiles.implementation = function(a){
        //     //将参数a和b的值改为123和456。
        //
        //     console.log(JSON.stringify(a));
        //     var test = this.containsImportantFiles(a);
        //     console.log(test);
        //     return test;
        // }

        var AMapController = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");

        // // Hook设置数据加载器的方法
        if (AMapController.addGestureMessage) {
            AMapController.addGestureMessage.implementation = function(a,b) {
                console.log("[AMapController] setCloudBundleLoader 被调用");
                console.log("[AMapController] 加载器类型: " + a);
                dumpObject(b)
                return this.addGestureMessage(a,b);
            };
        }
        if (AMapController.nativeAddMapGestureMsg) {
            AMapController.nativeAddMapGestureMsg.implementation = function( i,  j,  i2,  f,  f2,  f3,  i3) {
                console.log("[AMapController] setCloudBundleLoader 被调用");
                console.log("[AMapController] 加载器类型: " + i);
                console.log("[AMapController] 加载器类型: " + j);
                console.log("[AMapController] 加载器类型: " + f);
                console.log("[AMapController] 加载器类型: " + f2);
                console.log("[AMapController] 加载器类型: " + f3);
                console.log("[AMapController] 加载器类型: " + i3);
                //dumpObject(b)
                return this.nativeAddMapGestureMsg(i,  j,  i2,  f,  f2,  f3,  i3);
            };
        }



    })
    function dumpObject(obj) {
        // 获取对象的类
        var clazz = obj.getClass();
        console.log("    [Dumping instance of '" + clazz.getName() + "']");

        // 获取所有声明的字段 (包括 public, protected, private)
        var fields = clazz.getDeclaredFields();

        fields.forEach(function(field) {
            // 对于private等字段，需要设置可访问
            field.setAccessible(true);
            var fieldName = field.getName();
            // 获取字段的值
            var fieldValue = field.get(obj);

            console.log("        " + fieldName + ": " + fieldValue);
        });
        console.log("    [Dump complete]");
    }

}, 5000);