[{"filepath": "F:\\baidu\\decode\\gaodeApp\\app\\file\\autonavi\\data\\navi\\compile_v3\\chn\\a0\\m1.ans", "file_size": 44040192, "version": 170, "geometry_type": 137, "geometry_flags": 141, "compressed_size": 44040176, "decompression_attempts": [], "pattern_analysis": {"entropy": 5.8410945519804764, "most_common_bytes": [[0, 3220], [128, 630], [5, 230], [6, 223], [8, 138]], "common_patterns_2": [["0000", 1967], ["8000", 619], ["0038", 21], ["003d", 21], ["002e", 20]], "common_patterns_4": [["00000000", 114], ["00000037", 15], ["0000002c", 13], ["00000038", 13], ["00000043", 13]], "common_patterns_8": [["0000000000000000", 85], ["0000000000000001", 3], ["0000000000000100", 2]]}}, {"filepath": "F:\\baidu\\decode\\gaodeApp\\app\\file\\autonavi\\data\\navi\\compile_v3\\chn\\a3\\m1.ans", "file_size": 54968320, "version": 170, "geometry_type": 137, "geometry_flags": 141, "compressed_size": 54968304, "decompression_attempts": [], "pattern_analysis": {"entropy": 5.811473617498888, "most_common_bytes": [[0, 3193], [128, 644], [9, 275], [8, 248], [7, 220]], "common_patterns_2": [["0000", 1944], ["8000", 633], ["0002", 27], ["0001", 22], ["0004", 20]], "common_patterns_4": [["00000000", 117], ["80000009", 13], ["80000005", 12], ["8000000c", 12], ["00000004", 11]], "common_patterns_8": [["0000000000000000", 85], ["0000000000000001", 3], ["0000000000000100", 2]]}}, {"filepath": "F:\\baidu\\decode\\gaodeApp\\app\\file\\autonavi\\data\\navi\\compile_v3\\chn\\a3\\m2.ans", "file_size": 133881856, "version": 170, "geometry_type": 137, "geometry_flags": 141, "compressed_size": 133881840, "decompression_attempts": [{"method": "deflate_skip_4", "success": true, "input_size": 133881836, "output_size": 1, "compression_ratio": 7.469273128283064e-09, "output_header": "ea"}], "pattern_analysis": {"entropy": 5.858595271534507, "most_common_bytes": [[0, 3221], [128, 598], [4, 168], [2, 143], [1, 140]], "common_patterns_2": [["0000", 1961], ["8000", 592], ["0028", 26], ["0039", 26], ["001c", 25]], "common_patterns_4": [["00000000", 112], ["00000039", 18], ["0000001d", 17], ["0000002a", 17], ["0000001a", 15]], "common_patterns_8": [["0000000000000000", 82], ["0000000000000001", 3], ["0000000000000100", 3]]}}]