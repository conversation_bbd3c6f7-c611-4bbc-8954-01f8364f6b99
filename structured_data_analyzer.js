/*
 * 结构化数据分析器 - 基于IDA Pro分析的APP数据流解析
 * 目标：分析 解压后数据 → 结构化数据 → GPU渲染指令 的转换过程
 */

console.log("启动结构化数据分析器");

var DataAnalyzer = {
    decompressedBlocks: [],
    structuredData: [],
    totalAnalyzed: 0,
    maxAnalyze: 10
};

// 解析DICE-AM矢量数据
function parseDiceAM(data, offset) {
    try {
        var view = new DataView(data, offset);
        var result = {
            type: "DICE-AM",
            magic: String.fromCharCode.apply(null, new Uint8Array(data.slice(offset, offset + 7))),
            version: view.getUint8(7),
            vectorCount: view.getUint32(12, true),
            vectors: []
        };
        
        console.log(" DICE-AM数据:");
        console.log("   魔数:", result.magic);
        console.log("   版本:", result.version); 
        console.log("   矢量数:", result.vectorCount);
        
        // 解析前几个矢量坐标
        var vectorOffset = 16;
        for (var i = 0; i < Math.min(result.vectorCount, 5); i++) {
            var vector = {
                x: view.getFloat32(vectorOffset, true),
                y: view.getFloat32(vectorOffset + 4, true),
                type: view.getUint8(vectorOffset + 12)
            };
            result.vectors.push(vector);
            console.log("   矢量" + i + ": x=" + vector.x.toFixed(6) + " y=" + vector.y.toFixed(6) + " 类型=" + vector.type);
            vectorOffset += 13;
        }
        
        return result;
    } catch (e) {
        console.log("DICE-AM解析错误:", e.message);
        return null;
    }
}

// 解析JSON配置
function parseJSON(data, offset) {
    try {
        var bytes = new Uint8Array(data.slice(offset));
        var jsonStr = "";
        
        for (var i = 0; i < Math.min(bytes.length, 500); i++) {
            if (bytes[i] >= 32 && bytes[i] < 127) {
                jsonStr += String.fromCharCode(bytes[i]);
            } else if (bytes[i] === 0) break;
        }
        
        console.log(" JSON配置数据:");
        console.log("   内容:", jsonStr.substring(0, 100) + "...");
        
        return { type: "JSON", content: jsonStr };
    } catch (e) {
        console.log(" JSON解析错误:", e.message);
        return null;
    }
}

// 解析中文文本
function parseChineseText(data, offset) {
    try {
        var bytes = new Uint8Array(data.slice(offset));
        var texts = [];
        
        // 简单UTF-8中文检测
        for (var i = 0; i < bytes.length - 2; i++) {
            if (bytes[i] >= 0xE0 && bytes[i + 1] >= 0x80 && bytes[i + 2] >= 0x80) {
                texts.push("中文字符");
                break;
            }
        }
        
        if (texts.length > 0) {
            console.log(" 中文文本数据:");
            console.log("   检测到中文字符");
        }
        
        return { type: "Chinese", texts: texts };
    } catch (e) {
        return null;
    }
}

// 分析解压后的数据块
function analyzeDecompressedData(data, index) {
    console.log("\n 分析解压后数据块", index);
    console.log("数据大小:", data.byteLength, "字节");
    
    var bytes = new Uint8Array(data);
    var header = "";
    for (var i = 0; i < Math.min(16, bytes.length); i++) {
        if (bytes[i] >= 32 && bytes[i] < 127) {
            header += String.fromCharCode(bytes[i]);
        }
    }
    console.log("数据头部:", header);
    
    var structures = [];
    
    // 根据头部识别数据类型
    if (header.indexOf("DICE-AM") >= 0) {
        var result = parseDiceAM(data, 0);
        if (result) structures.push(result);
    } else if (header.indexOf("{") >= 0) {
        var result = parseJSON(data, 0);
        if (result) structures.push(result);
    } else {
        var result = parseChineseText(data, 0);
        if (result) structures.push(result);
    }
    
    return structures;
}

// Hook zlib解压
function hookZlib() {
    console.log(" Hook zlib解压");
    
    var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
    if (uncompressPtr) {
        Interceptor.attach(uncompressPtr, {
            onEnter: function(args) {
                this.dest = args[0];
                this.destLen = args[1];
                this.src = args[2];
                this.srcLen = args[3].toInt32();
            },
            onLeave: function(retval) {
                if (retval.toInt32() === 0 && DataAnalyzer.totalAnalyzed < DataAnalyzer.maxAnalyze) {
                    try {
                        var destLen = this.destLen.readU32();
                        var decompressedData = this.dest.readByteArray(destLen);
                        
                        console.log("\n zlib解压成功");
                        console.log("压缩前:", this.srcLen, "字节");
                        console.log("解压后:", destLen, "字节");
                        
                        var structures = analyzeDecompressedData(decompressedData, DataAnalyzer.totalAnalyzed);
                        
                        DataAnalyzer.decompressedBlocks.push({
                            index: DataAnalyzer.totalAnalyzed,
                            compressedSize: this.srcLen,
                            decompressedSize: destLen,
                            structures: structures
                        });
                        
                        DataAnalyzer.totalAnalyzed++;
                    } catch (e) {
                        console.log(" 解压数据分析失败:", e.message);
                    }
                }
            }
        });
    }
}

// Hook数据分发器
function hookDispatcher() {
    console.log(" Hook数据分发器");
    
    var checkInterval = setInterval(function() {
        var lib = Process.findModuleByName("libamapnsq.so");
        if (lib) {
            clearInterval(checkInterval);
            
            try {
                var addr = lib.base.add(0x5C060);
                console.log(" Hook分发器 at:", addr);
                
                Interceptor.attach(addr, {
                    onEnter: function(args) {
                        this.data = args[0];
                        this.size = args[1].toInt32();
                        this.type = args[2].toInt32();
                    },
                    onLeave: function(retval) {
                        console.log("\n 数据分发:");
                        console.log("   数据大小:", this.size);
                        console.log("   数据类型:", this.type);
                        console.log("   返回值:", retval.toInt32());
                        
                        DataAnalyzer.structuredData.push({
                            size: this.size,
                            type: this.type,
                            returnValue: retval.toInt32()
                        });
                    }
                });
            } catch (e) {
                console.log(" Hook分发器失败:", e.message);
            }
        }
    }, 100);
}

// 生成报告
function generateReport() {
    console.log("\n" + "=".repeat(60));
    console.log(" 结构化数据分析报告");
    console.log("=".repeat(60));
    
    console.log(" 解压后数据:", DataAnalyzer.decompressedBlocks.length, "个");
    DataAnalyzer.decompressedBlocks.forEach(function(block, i) {
        console.log("   块" + i + ":", block.decompressedSize + "字节", 
                   block.structures.map(function(s) { return s.type; }).join(","));
    });
    
    console.log(" 结构化数据:", DataAnalyzer.structuredData.length, "个");
    DataAnalyzer.structuredData.forEach(function(data, i) {
        console.log("   数据" + i + ": 类型" + data.type + " 大小" + data.size + "字节");
    });
    
    console.log("\n 数据流程: 压缩数据 → 解压 → 结构化 → 渲染");
}

// 主函数
function main() {
    console.log(" 启动结构化数据分析器");
    
    hookZlib();
    hookDispatcher();
    
    setInterval(function() {
        if (DataAnalyzer.totalAnalyzed > 0) {
            generateReport();
        }
    }, 15000);
    
    console.log(" Hook设置完成，等待数据...");
}

main(); 