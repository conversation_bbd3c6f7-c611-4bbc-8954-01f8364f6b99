setTimeout(function() {
  console.log("[+] 高德地图手势处理深度分析脚本启动");

  // 辅助函数: 内存dump
  function dumpMemoryAround(address, range) {
    try {
      var start = ptr(address).sub(range/2);
      var data = Memory.readByteArray(start, range);
      console.log(hexdump(data, {offset: 0, length: range, header: true, ansi: false}));
    } catch(e) {
      console.log("[-] 内存读取错误: " + e.message);
    }
  }

  // 辅助函数: 分析结构体
  function analyzeStructure(address, size) {
    try {
      console.log("[分析] 结构体 @ " + address + " (大小: " + size + "):");
      
      // 尝试以不同方式解释内存
      var data = Memory.readByteArray(address, size);
      console.log(hexdump(data, {offset: 0, length: size, header: true}));
      
      // 尝试解析为指针数组
      console.log("  作为指针数组解析:");
      for (var i = 0; i < Math.min(size / Process.pointerSize, 10); i++) {
        try {
          var ptrValue = Memory.readPointer(address.add(i * Process.pointerSize));
          console.log("    [" + i + "]: " + ptrValue);
        } catch(e) {}
      }
      
      // 尝试解析为浮点数
      console.log("  作为浮点数解析:");
      for (var i = 0; i < Math.min(size / 4, 10); i++) {
        try {
          var floatValue = Memory.readFloat(address.add(i * 4));
          if (Math.abs(floatValue) < 1000) {  // 过滤明显不可能的值
            console.log("    [" + i + "]: " + floatValue);
          }
        } catch(e) {}
      }
      
      // 尝试解析为整数
      console.log("  作为整数解析:");
      for (var i = 0; i < Math.min(size / 4, 10); i++) {
        try {
          var intValue = Memory.readS32(address.add(i * 4));
          console.log("    [" + i + "]: " + intValue);
        } catch(e) {}
      }
    } catch(e) {
      console.log("[-] 结构分析失败: " + e);
    }
  }

  // 追踪虚函数
  function hookVirtualFunction(vtablePtr, index, description) {
    if (index < 0 || index > 30) return;  // 安全检查
    
    try {
      var funcPtr = Memory.readPointer(vtablePtr.add(index * Process.pointerSize));
      if (funcPtr.isNull() || funcPtr.toInt32() < 0x1000) return;
      
      Interceptor.attach(funcPtr, {
        onEnter: function(args) {
          this.index = index;
          this.startTime = Date.now();
          this.args = [];
          
          console.log("[调用] " + description + " (vtable函数[" + this.index + "]) @ " + funcPtr);
          
          // 保存参数以备后用
          for (var i = 0; i < 8; i++) {
            this.args[i] = args[i];
          }
          
          // 打印参数
          console.log("  参数1/this: " + args[0]);
          console.log("  参数2: " + args[1]);
          console.log("  参数3: " + args[2]);
          console.log("  参数4: " + args[3]);
          console.log("  参数5: " + args[4]);
          console.log("  参数6: " + args[5]);
          
          // 如果是手势数据指针，尝试分析它
          if (index === 14 || index === 19) {
            console.log("[分析] 手势数据详细信息:");
            
            // 分析参数4 - 可能是手势数据结构
            if (!args[3].isNull()) {
              console.log("  分析参数4 (可能的手势数据结构):");
              analyzeStructure(args[3], 64);
            }
            
            // 在ARM64上，前8个浮点参数在s0-s7寄存器中
            if (Process.arch === "arm64") {
              try {
                var context = this.context;
                console.log("  浮点参数:");
                console.log("    s0: " + context.s0);
                console.log("    s1: " + context.s1);
                console.log("    s2: " + context.s2);
                console.log("    s3: " + context.s3);
              } catch(e) {
                console.log("  无法读取浮点寄存器: " + e.message);
              }
            }
          }
        },
        onLeave: function(retval) {
          var duration = Date.now() - this.startTime;
          console.log("[返回] " + description + " (vtable函数[" + this.index + "]): " + retval + 
                     " (耗时: " + duration + "ms)");
          
          // 检查是否有状态改变
          if (!this.args[0].isNull()) {
            try {
              console.log("  返回后检查对象状态:");
              // 读取对象的前几个字段看是否有变化
              for (var i = 0; i < 3; i++) {
                var offset = i * Process.pointerSize;
                var value = Memory.readPointer(this.args[0].add(offset));
                console.log("    偏移+" + offset + ": " + value);
              }
            } catch(e) {}
          }
        }
      });
      
      console.log("[+] 已Hook " + description + " (vtable函数[" + index + "])");
      return true;
    } catch(e) {
      console.log("[-] Hook " + description + " (vtable函数[" + index + "]) 失败: " + e);
      return false;
    }
  }

  // 监控Java层addGestureMessage方法
  Java.perform(function() {
    try {
      var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
      
      if (GLMapEngine.addGestureMessage) {
        GLMapEngine.addGestureMessage.implementation = function(engineId, gestureMessage) {
          console.log("\n[Java] 调用addGestureMessage: 引擎ID=" + engineId + ", 类型=" + gestureMessage.getType());
          
          // 获取更多手势信息
          if (gestureMessage.getType() == 0) {  // 移动手势
            var moveMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.MoveGestureMapMessage"));
            console.log("  移动参数: X=" + moveMsg.mTouchDeltaX.value + ", Y=" + moveMsg.mTouchDeltaY.value);
          } else if (gestureMessage.getType() == 1) {  // 缩放手势
            var scaleMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.ScaleGestureMapMessage"));
            console.log("  缩放参数: scale=" + scaleMsg.mScaleDelta + ", pivotX=" + scaleMsg.mPivotX + 
                       ", pivotY=" + scaleMsg.mPivotY);
          } else if (gestureMessage.getType() == 2) {  // 旋转手势
            var rotateMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.RotateGestureMapMessage"));
            console.log("  旋转参数: angle=" + rotateMsg.mAngleDelta + ", pivotX=" + rotateMsg.mPivotX + 
                       ", pivotY=" + rotateMsg.mPivotY);
          }
          
          // 获取Native实例指针
          try {
            var field = GLMapEngine.class.getDeclaredField("mNativeMapengineInstance");
            field.setAccessible(true);
            var nativePtr = field.getLong(this);
            console.log("[+] Native实例指针: 0x" + nativePtr.toString(16));
            
            // 只分析一次vtable结构
            var vtablePtr = Memory.readPointer(ptr(nativePtr));
            
            // 只对关键函数14和19进行详细Hook
            if (!global.hasHookedVTable) {
              console.log("[分析] 关键虚函数Hook:");
              hookVirtualFunction(vtablePtr, 14, "可能的地图更新函数");
              hookVirtualFunction(vtablePtr, 19, "可能的手势处理函数");
              global.hasHookedVTable = true;
            }
          } catch(e) {
            console.log("[-] 分析Native实例失败: " + e);
          }
          
          // 调用原始方法
          var result = this.addGestureMessage(engineId, gestureMessage);
          console.log("[Java] addGestureMessage返回: " + result);
          return result;
        };
        
        console.log("[+] 成功Hook Java层addGestureMessage方法");
      }
      
      // 尝试Hook其他相关方法
      if (GLMapEngine.nativeAddMapGestureMsg) {
        GLMapEngine.nativeAddMapGestureMsg.implementation = function(i, j, i2, f, f2, f3, i3) {
          console.log("[JNI直接调用] nativeAddMapGestureMsg:");
          console.log("  engineId: " + i);
          console.log("  nativePtr: " + j);
          console.log("  type: " + i2);
          console.log("  f1/deltaX: " + f);
          console.log("  f2/deltaY: " + f2);
          console.log("  f3/extra: " + f3);
          console.log("  i3/userData: " + i3);
          
          var result = this.nativeAddMapGestureMsg(i, j, i2, f, f2, f3, i3);
          console.log("[JNI直接调用] nativeAddMapGestureMsg返回: " + result);
          return result;
        };
        console.log("[+] 成功Hook直接JNI方法nativeAddMapGestureMsg");
      }
    } catch(e) {
      console.log("[-] Hook Java方法失败: " + e);
    }
  });
  
  // 监控文件操作
  Interceptor.attach(Module.findExportByName("libc.so", "open"), {
    onEnter: function(args) {
      this.path = args[0].readUtf8String();
      if (this.path.indexOf('.ans') !== -1) {
        console.log("[ANS访问] 打开文件: " + this.path);
      }
    }
  });

  // 监控ZSTD解压函数
  Process.enumerateModules().forEach(function(module) {
   if (module.name.indexOf('amap') !== -1) {
      module.enumerateExports().forEach(function(exp) {
        if (exp.name.indexOf('ZSTD') !== -1 || exp.name.indexOf('decompress') !== -1) {
          console.log("[发现解压函数] " + module.name + "!" + exp.name);
          Interceptor.attach(exp.address, {
            onEnter: function(args) {
              console.log("[解压] 调用 " + exp.name + ", 输入大小: " + args[2].toInt32());
            },
            onLeave: function(retval) {
              console.log("[解压] 完成 " + exp.name + ", 返回: " + retval);
            }
          });
        }
      });
    }
  });

  // 追踪函数#14调用后的后续操作
  // 通过添加额外的Interceptor在原有函数结束后
  
  console.log("[+] 增强手势分析脚本已启动，请进行地图操作...");
}, 1000);
