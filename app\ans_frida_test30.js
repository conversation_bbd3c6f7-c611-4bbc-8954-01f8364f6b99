(function() {
    console.log("[ANS分析] 启动精简优化版...");
    
    // 全局变量 - 最小化
    var m1AnsFiles = {}; // 跟踪已打开的m1.ans和m3.ans文件句柄
    var lastGestureTime = 0;
    var foundDecompress = false;

    // 1. 首先设置文件监控 - 这是基础功能
    setTimeout(function() {
        // 监控文件打开
        var openPtr = Module.findExportByName("libc.so", "open");
        if (openPtr) {
            Interceptor.attach(openPtr, {
                onEnter: function(args) {
                    try {
                        this.path = args[0].readUtf8String();
                        if (this.path && (this.path.indexOf("m1.ans") !== -1 || 
                                          this.path.indexOf("m3.ans") !== -1 || 
                                          this.path.indexOf("vector_building.ans") !== -1)) {
                            console.log("[ANS文件] 打开: " + this.path);
                            this.isTargetFile = true;
                        }
                    } catch(e) {}
                },
                onLeave: function(result) {
                    if (this.isTargetFile && result.toInt32() > 0) {
                        var fd = result.toInt32();
                        m1AnsFiles[fd] = this.path;
                        console.log("[ANS文件] 句柄: " + fd);
                    }
                }
            });
        }
        
        // 监控文件读取
        var readPtr = Module.findExportByName("libc.so", "read");
        if (readPtr) {
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    var fd = args[0].toInt32();
                    if (m1AnsFiles[fd]) {
                        this.fd = fd;
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                        this.path = m1AnsFiles[fd];
                    }
                },
                onLeave: function(result) {
                    if (this.fd && result.toInt32() > 0) {
                        var bytesRead = result.toInt32();
                        var fileName = this.path.split("/").pop();
                        
                        // 只记录特殊大小
                        if (bytesRead == 16 || bytesRead == 345 || bytesRead == 2399 || bytesRead >= 8192) {
                            console.log("[ANS读取] " + fileName + ": " + bytesRead + "字节");
                            
                            // 显示小块数据的头部
                            if (bytesRead <= 32) {
                                try {
                                    console.log(hexdump(this.buffer, {length: bytesRead}));
                                } catch(e) {}
                            }
                        }
                    }
                }
            });
        }
        console.log("[ANS文件] 监控已设置");
        
        // 2. 设置解压函数监控 (简化版)
        setTimeout(function() {
            // LZ4函数
            var lz4Functions = [
                "LZ4_decompress_safe", 
                "LZ4_decompress_fast"
            ];
            
            for (var i = 0; i < lz4Functions.length; i++) {
                var addr = Module.findExportByName(null, lz4Functions[i]);
                if (addr) {
                    console.log("[ANS压缩] 找到LZ4函数: " + lz4Functions[i]);
                    hookDecompressFunc(lz4Functions[i], addr);
                }
            }
            
            // 尝试查找和钩住libzstd.so中的函数
            try {
                var zstdModule = null;
                Process.enumerateModules({
                    onMatch: function(module) {
                        if (module.name === "libzstd.so") {
                            zstdModule = module;
                            console.log("[ANS压缩] 找到libzstd.so模块: " + module.base);
                        }
                    },
                    onComplete: function() {}
                });
                
                if (zstdModule) {
                    var zstdFunctions = [
                        "ackor_ZSTD_decompress",
                        "ackor_ZSTD_decompressBlock"
                    ];
                    
                    for (var i = 0; i < zstdFunctions.length; i++) {
                        var addr = Module.findExportByName("libzstd.so", zstdFunctions[i]);
                        if (addr) {
                            console.log("[ANS压缩] 找到ZSTD函数: " + zstdFunctions[i]);
                            hookDecompressFunc(zstdFunctions[i], addr);
                        }
                    }
                }
            } catch(e) {
                console.log("[ANS压缩] 设置ZSTD钩子时出错: " + e);
            }
            
            console.log("[ANS压缩] 解压监控已设置");
            
            // 3. 最后设置手势监控
            setTimeout(function() {
                try {
                    Java.perform(function() {
                        try {
                            var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                            if (GLMapEngine.addGestureMessage) {
                                console.log("[ANS手势] 找到手势处理方法");
                                
                                GLMapEngine.addGestureMessage.implementation = function(engineId, gestureMessage) {
                                    try {
                                        if (gestureMessage) {
                                            var type = gestureMessage.getType();
                                            
                                            // 移动手势
                                            if (type == 0) {
                                                var moveMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.MoveGestureMapMessage"));
                                                var dx = moveMsg.mTouchDeltaX.value;
                                                var dy = moveMsg.mTouchDeltaY.value;
                                                
                                                if (Math.abs(dx) > 20 || Math.abs(dy) > 20) {
                                                    console.log("[ANS手势] 移动: dx=" + dx + ", dy=" + dy);
                                                    lastGestureTime = Date.now();
                                                    foundDecompress = false;
                                                }
                                            }
                                            // 缩放手势
                                            else if (type == 1) {
                                                try {
                                                    var scaleMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.ScaleGestureMapMessage"));
                                                    var factor = scaleMsg.mScaleFactor.value;
                                                    if (Math.abs(factor - 1.0) > 0.1) {
                                                        console.log("[ANS手势] 缩放: factor=" + factor.toFixed(2));
                                                        lastGestureTime = Date.now();
                                                        foundDecompress = false;
                                                    }
                                                } catch(e) {}
                                            }
                                        }
                                    } catch(e) {
                                        console.log("[ANS手势] 处理错误: " + e);
                                    }
                                    return this.addGestureMessage(engineId, gestureMessage);
                                };
                            }
                        } catch(e) {
                            console.log("[ANS手势] 设置失败: " + e);
                        }
                    });
                } catch(e) {
                    console.log("[ANS手势] Java加载失败: " + e);
                }
                console.log("[ANS手势] 监控设置尝试完成");
            }, 1000);
            
        }, 2000);
    }, 1000);
    
    // 通用解压函数钩子 (简化版)
    function hookDecompressFunc(name, address) {
        try {
            Interceptor.attach(address, {
                onEnter: function(args) {
                    this.name = name;
                    this.src = args[0];
                    this.dst = args[1];
                    
                    // 只在手势后5秒内记录
                    if (lastGestureTime > 0 && Date.now() - lastGestureTime < 5000) {
                        this.track = true;
                        foundDecompress = true;
                        console.log("[ANS解压] 调用: " + name);
                    }
                },
                onLeave: function(result) {
                    if (this.track && result.toInt32() > 0) {
                        var size = result.toInt32();
                        console.log("[ANS解压] " + this.name + " 结果: " + size + "字节");
                        
                        try {
                            if (size > 0 && this.dst && !this.dst.isNull()) {
                                console.log(hexdump(this.dst, {length: Math.min(32, size)}));
                            }
                        } catch(e) {}
                    }
                }
            });
        } catch(e) {
            console.log("[ANS压缩] 监控" + name + "失败: " + e);
        }
    }
    
    console.log("[ANS分析] 设置开始，请耐心等待...");
})();