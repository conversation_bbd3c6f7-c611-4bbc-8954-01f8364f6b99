     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
启动结构化数据分析器
 启动结构化数据分析器
 Hook zlib解压
 Hook数据分发器
 Hook设置完成，等待数据...
[Remote::com.autonavi.minimap]->  Hook分发器 at: 0x7f5f9b2060
 Hook分发器失败: unable to intercept function at 0x7f5f9b2060; please file a bug

 zlib解压成功
压缩前: 3121 字节
解压后: 5304 字节

 分析解压后数据块 0
数据大小: 5304 字节
数据头部: P
 中文文本数据:
   检测到中文字符

 zlib解压成功
压缩前: 2149 字节
解压后: 3704 字节

 分析解压后数据块 1
数据大小: 3704 字节
数据头部: P
 中文文本数据:
   检测到中文字符

 zlib解压成功
压缩前: 2232 字节
解压后: 12024 字节

 分析解压后数据块 2
数据大小: 12024 字节
数据头部: P
 中文文本数据:
   检测到中文字符

 zlib解压成功
压缩前: 3349 字节
解压后: 5588 字节

 分析解压后数据块 3
数据大小: 5588 字节
数据头部: P
 中文文本数据:
   检测到中文字符

 zlib解压成功
压缩前: 2262 字节
解压后: 3980 字节

 分析解压后数据块 4
数据大小: 3980 字节
数据头部: P
 中文文本数据:
   检测到中文字符

 zlib解压成功
压缩前: 2091 字节
解压后: 12536 字节

 分析解压后数据块 5
数据大小: 12536 字节
数据头部: P
 中文文本数据:
   检测到中文字符

 zlib解压成功
压缩前: 3505 字节
解压后: 6096 字节

 分析解压后数据块 6
数据大小: 6096 字节
数据头部: P
 中文文本数据:
   检测到中文字符

 zlib解压成功
压缩前: 2618 字节
解压后: 4544 字节

 分析解压后数据块 7
数据大小: 4544 字节
数据头部: P
 中文文本数据:
   检测到中文字符

 zlib解压成功
压缩前: 2568 字节
解压后: 13640 字节

 分析解压后数据块 8
数据大小: 13640 字节
数据头部: P
 中文文本数据:
   检测到中文字符

 zlib解压成功
压缩前: 3505 字节
解压后: 6096 字节

 分析解压后数据块 9
数据大小: 6096 字节
数据头部: P
 中文文本数据:
   检测到中文字符

============================================================
 结构化数据分析报告
============================================================
 解压后数据: 10 个
   块0: 5304字节 Chinese
   块1: 3704字节 Chinese
   块2: 12024字节 Chinese
   块3: 5588字节 Chinese
   块4: 3980字节 Chinese
   块5: 12536字节 Chinese
   块6: 6096字节 Chinese
   块7: 4544字节 Chinese
   块8: 13640字节 Chinese
   块9: 6096字节 Chinese
 结构化数据: 0 个

 数据流程: 压缩数据 → 解压 → 结构化 → 渲染

============================================================
 结构化数据分析报告
============================================================
 解压后数据: 10 个
   块0: 5304字节 Chinese
   块1: 3704字节 Chinese
   块2: 12024字节 Chinese
   块3: 5588字节 Chinese
   块4: 3980字节 Chinese
   块5: 12536字节 Chinese
   块6: 6096字节 Chinese
   块7: 4544字节 Chinese
   块8: 13640字节 Chinese
   块9: 6096字节 Chinese
 结构化数据: 0 个

 数据流程: 压缩数据 → 解压 → 结构化 → 渲染

============================================================
 结构化数据分析报告
============================================================
 解压后数据: 10 个
   块0: 5304字节 Chinese
   块1: 3704字节 Chinese
   块2: 12024字节 Chinese
   块3: 5588字节 Chinese
   块4: 3980字节 Chinese
   块5: 12536字节 Chinese
   块6: 6096字节 Chinese
   块7: 4544字节 Chinese
   块8: 13640字节 Chinese
   块9: 6096字节 Chinese
 结构化数据: 0 个

 数据流程: 压缩数据 → 解压 → 结构化 → 渲染

[Remote::com.autonavi.minimap]-> 
[Remote::com.autonavi.minimap]-> exit

Thank you for using Frida!
