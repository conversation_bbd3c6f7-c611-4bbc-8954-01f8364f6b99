     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
[Improved SQLite Extractor] 启动改进版SQLite数据提取器...
[Main] 初始化改进版SQLite数据提取器...
[Remote::com.autonavi.minimap]-> [Library] libamapnsq.so 基址: 0x7f60794000
[Improved SQLite] 设置改进版SQLite BLOB Hook...
[Improved SQLite] Hook已设置
[Improved SQLite Extractor] 改进版提取器已启动!
配置: 采样率1/5, 大小范围10-10240字节
现在移动地图，观察改进的数据提取...
[SQLite Sample 1] 调用 (第5次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 90, 大小: 17 字节, 有效: true
[SQLite Data] 头部Hex: 6D 75 6C 74 69 70 6C 65 78 5F 63 6F 6E 74 72 6F 
[SQLite Data] 头部字符: 'multiplex_contro'
[INTERESTING DATA] 发现Binary_Data数据！
[Extract Flag] 将提取数据样本
[SQLite Sample 2] 调用 (第10次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 91, 大小: 18 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x7
[SQLite Sample 3] 调用 (第15次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 82, 大小: 9 字节, 有效: true
[SQLite Skip] 数据太小: 9 字节
[SQLite Sample 4] 调用 (第20次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 82, 大小: 9 字节, 有效: true
[SQLite Skip] 数据太小: 9 字节
[SQLite Sample 5] 调用 (第25次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 87, 大小: 14 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x1
[SQLite Sample 6] 调用 (第30次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 82, 大小: 9 字节, 有效: true
[SQLite Skip] 数据太小: 9 字节
[SQLite Sample 7] 调用 (第35次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 45, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 8] 调用 (第40次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 648, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 9] 调用 (第45次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 648, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 10] 调用 (第50次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 5, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 11] 调用 (第55次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 9, 大小: 7 字节, 有效: true
[SQLite Skip] 数据太小: 7 字节
[SQLite Sample 12] 调用 (第60次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 13] 调用 (第65次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 32, 大小: 16 字节, 有效: true
[SQLite Warning] BLOB数据指针为空
[SQLite Sample 14] 调用 (第70次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 4, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x3
[SQLite Sample 15] 调用 (第75次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 16, 大小: 1587537512 字节, 有效: false
[SQLite Skip] 数据大小无效: 1587537512 字节
[SQLite Sample 16] 调用 (第80次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 4, 大小: -6 字节, 有效: false
[SQLite Skip] 数据大小无效: -6 字节
[SQLite Sample 17] 调用 (第85次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 24, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 18] 调用 (第90次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 19] 调用 (第95次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 20] 调用 (第100次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 21] 调用 (第105次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 49, 大小: 1587541056 字节, 有效: false
[SQLite Skip] 数据大小无效: 1587541056 字节
[SQLite Sample 22] 调用 (第110次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 4, 大小: 7 字节, 有效: true
[SQLite Skip] 数据太小: 7 字节
[SQLite Sample 23] 调用 (第115次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 28, 大小: 49 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x1
[SQLite Sample 24] 调用 (第120次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 4, 大小: 4 字节, 有效: true
[SQLite Skip] 数据太小: 4 字节
[SQLite Sample 25] 调用 (第125次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 16, 大小: 1587537160 字节, 有效: false
[SQLite Skip] 数据大小无效: 1587537160 字节
[SQLite Sample 26] 调用 (第130次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 296, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 27] 调用 (第135次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 5, 大小: 4 字节, 有效: true
[SQLite Skip] 数据太小: 4 字节
[SQLite Sample 28] 调用 (第140次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 9, 大小: 4 字节, 有效: true
[SQLite Skip] 数据太小: 4 字节
[SQLite Sample 29] 调用 (第145次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 5, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 30] 调用 (第150次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 296, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 31] 调用 (第155次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 296, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 32] 调用 (第160次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 33] 调用 (第165次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 32, 大小: 16 字节, 有效: true
[SQLite Warning] BLOB数据指针为空
[SQLite Sample 34] 调用 (第170次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 76, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 35] 调用 (第175次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 4, 大小: 8 字节, 有效: true
[SQLite Skip] 数据太小: 8 字节
[SQLite Sample 36] 调用 (第180次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 280, 大小: 1618826156 字节, 有效: false
[SQLite Skip] 数据大小无效: 1618826156 字节
[SQLite Sample 37] 调用 (第185次)
[SQLite Params] 语句: 0x7f57a0f188, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 38] 调用 (第190次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 32, 大小: 32 字节, 有效: true
[SQLite Warning] BLOB数据指针为空
[SQLite Sample 39] 调用 (第195次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 16 字节, 有效: true
[SQLite Warning] BLOB数据指针为空
[SQLite Sample 40] 调用 (第200次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 89, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 41] 调用 (第205次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 4, 大小: 3 字节, 有效: true
[SQLite Skip] 数据太小: 3 字节
[SQLite Sample 42] 调用 (第210次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 24, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 43] 调用 (第215次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 44] 调用 (第220次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 15, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 45] 调用 (第225次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 648, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 46] 调用 (第230次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 120, 大小: 1124235584 字节, 有效: false
[SQLite Skip] 数据大小无效: 1124235584 字节
[SQLite Sample 47] 调用 (第235次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 120, 大小: 1044450056 字节, 有效: false
[SQLite Skip] 数据大小无效: 1044450056 字节
[SQLite Sample 48] 调用 (第240次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 96, 大小: -1 字节, 有效: false
[SQLite Skip] 数据大小无效: -1 字节
[SQLite Sample 49] 调用 (第245次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 16, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 50] 调用 (第250次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 51] 调用 (第255次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 456, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 52] 调用 (第260次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 16, 大小: 1367858664 字节, 有效: false
[SQLite Skip] 数据大小无效: 1367858664 字节
[SQLite Sample 53] 调用 (第265次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 77, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 54] 调用 (第270次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 72, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 55] 调用 (第275次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 4, 大小: 3 字节, 有效: true
[SQLite Skip] 数据太小: 3 字节
[SQLite Sample 56] 调用 (第280次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 24, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 57] 调用 (第285次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 280, 大小: 1618826156 字节, 有效: false
[SQLite Skip] 数据大小无效: 1618826156 字节
[SQLite Sample 58] 调用 (第290次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 59] 调用 (第295次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 141 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x1
[SQLite Sample 60] 调用 (第300次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 296, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 61] 调用 (第305次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 40, 大小: 1367864384 字节, 有效: false
[SQLite Skip] 数据大小无效: 1367864384 字节
[SQLite Sample 62] 调用 (第310次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 480, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 63] 调用 (第315次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 16, 大小: 1367857896 字节, 有效: false
[SQLite Skip] 数据大小无效: 1367857896 字节
[SQLite Sample 64] 调用 (第320次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 76, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 65] 调用 (第325次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 66] 调用 (第330次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 80, 大小: -1 字节, 有效: false
[SQLite Skip] 数据大小无效: -1 字节
[SQLite Sample 67] 调用 (第335次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 280, 大小: 1618826156 字节, 有效: false
[SQLite Skip] 数据大小无效: 1618826156 字节
[SQLite Sample 68] 调用 (第340次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 69] 调用 (第345次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 141 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x1
[SQLite Sample 70] 调用 (第350次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 4, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 71] 调用 (第355次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 76, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 72] 调用 (第360次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 3, 大小: 48 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x2
[SQLite Sample 73] 调用 (第365次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 296, 大小: 1619129879 字节, 有效: false
[SQLite Skip] 数据大小无效: 1619129879 字节
[SQLite Sample 74] 调用 (第370次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 96, 大小: 1172455816 字节, 有效: false
[SQLite Skip] 数据大小无效: 1172455816 字节
[SQLite Sample 75] 调用 (第375次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 16 字节, 有效: true
[SQLite Warning] BLOB数据指针为空
[SQLite Sample 76] 调用 (第380次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 77] 调用 (第385次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 27 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x1
[SQLite Sample 78] 调用 (第390次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 79] 调用 (第395次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 648, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 80] 调用 (第400次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 120, 大小: 1124235584 字节, 有效: false
[SQLite Skip] 数据大小无效: 1124235584 字节
[SQLite Sample 81] 调用 (第405次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 120, 大小: 1044451208 字节, 有效: false
[SQLite Skip] 数据大小无效: 1044451208 字节
[SQLite Sample 82] 调用 (第410次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 96, 大小: -1 字节, 有效: false
[SQLite Skip] 数据大小无效: -1 字节
[SQLite Sample 83] 调用 (第415次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 16, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 84] 调用 (第420次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 85] 调用 (第425次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 456, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 86] 调用 (第430次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 296, 大小: 3 字节, 有效: true
[SQLite Skip] 数据太小: 3 字节
[SQLite Sample 87] 调用 (第435次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 32, 大小: 56 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0xf
[SQLite Sample 88] 调用 (第440次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 4, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x3
[SQLite Sample 89] 调用 (第445次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 72, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 90] 调用 (第450次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 91] 调用 (第455次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 88, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 92] 调用 (第460次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 93] 调用 (第465次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 48, 大小: 39 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x2f
[SQLite Sample 94] 调用 (第470次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 648, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x8
[SQLite Sample 95] 调用 (第475次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 32, 大小: 32 字节, 有效: true
[SQLite Warning] BLOB数据指针为空
[SQLite Sample 96] 调用 (第480次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 16 字节, 有效: true
[SQLite Warning] BLOB数据指针为空
[SQLite Sample 97] 调用 (第485次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 89, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 98] 调用 (第490次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 4, 大小: 3 字节, 有效: true
[SQLite Skip] 数据太小: 3 字节
[SQLite Sample 99] 调用 (第495次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 24, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 100] 调用 (第500次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 101] 调用 (第505次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 15, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 102] 调用 (第510次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 40, 大小: 1365775424 字节, 有效: false
[SQLite Skip] 数据大小无效: 1365775424 字节
[SQLite Sample 103] 调用 (第515次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 480, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 104] 调用 (第520次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 16, 大小: 1365768936 字节, 有效: false
[SQLite Skip] 数据大小无效: 1365768936 字节
[SQLite Sample 105] 调用 (第525次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 76, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 106] 调用 (第530次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 107] 调用 (第535次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 72, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 108] 调用 (第540次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 109] 调用 (第545次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 88, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 110] 调用 (第550次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 111] 调用 (第555次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 48, 大小: 7 字节, 有效: true
[SQLite Skip] 数据太小: 7 字节
[SQLite Sample 112] 调用 (第560次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 76, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 113] 调用 (第565次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 3, 大小: 48 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x2
[SQLite Sample 114] 调用 (第570次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 296, 大小: 1619129879 字节, 有效: false
[SQLite Skip] 数据大小无效: 1619129879 字节
[SQLite Sample 115] 调用 (第575次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 96, 大小: 1172455816 字节, 有效: false
[SQLite Skip] 数据大小无效: 1172455816 字节
[SQLite Sample 116] 调用 (第580次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 16 字节, 有效: true
[SQLite Warning] BLOB数据指针为空
[SQLite Sample 117] 调用 (第585次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 118] 调用 (第590次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 141 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x1
[SQLite Sample 119] 调用 (第595次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 120] 调用 (第600次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 40, 大小: 1367864384 字节, 有效: false
[SQLite Skip] 数据大小无效: 1367864384 字节
[SQLite Sample 121] 调用 (第605次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 4, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 122] 调用 (第610次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 32, 大小: 32 字节, 有效: true
[SQLite Warning] BLOB数据指针为空
[SQLite Sample 123] 调用 (第615次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 76, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 124] 调用 (第620次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 125] 调用 (第625次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 80, 大小: -1 字节, 有效: false
[SQLite Skip] 数据大小无效: -1 字节
[SQLite Sample 126] 调用 (第630次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 280, 大小: 1618826156 字节, 有效: false
[SQLite Skip] 数据大小无效: 1618826156 字节
[SQLite Sample 127] 调用 (第635次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 128] 调用 (第640次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 141 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x1
[SQLite Sample 129] 调用 (第645次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 4, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x3
[SQLite Sample 130] 调用 (第650次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 72, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 131] 调用 (第655次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 132] 调用 (第660次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 88, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 133] 调用 (第665次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 134] 调用 (第670次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 48, 大小: 39 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x2f

=== 改进版SQLite数据提取报告 ===
总调用: 673
采样: 134
有效大小: 108
无效大小: 26
发现数据: 1 次
成功提取: 0 个

最常见的数据大小:
  1 字节: 34 次
  0 字节: 33 次
  32 字节: 7 次
  16 字节: 6 次
  141 字节: 4 次
  3 字节: 4 次
  4 字节: 3 次
  9 字节: 3 次
  7 字节: 3 次
  39 字节: 2 次
=======================================

[SQLite Sample 135] 调用 (第675次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 32, 大小: 57 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0xf
[SQLite Sample 136] 调用 (第680次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 4, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x3
[SQLite Sample 137] 调用 (第685次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 72, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 138] 调用 (第690次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 32, 大小: 57 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0xf
[SQLite Sample 139] 调用 (第695次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 96, 大小: -1 字节, 有效: false
[SQLite Skip] 数据大小无效: -1 字节
[SQLite Sample 140] 调用 (第700次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 16, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 141] 调用 (第705次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 142] 调用 (第710次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 456, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 143] 调用 (第715次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 16, 大小: 1365768888 字节, 有效: false
[SQLite Skip] 数据大小无效: 1365768888 字节
[SQLite Sample 144] 调用 (第720次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 76, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 145] 调用 (第725次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 146] 调用 (第730次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 80, 大小: -1 字节, 有效: false
[SQLite Skip] 数据大小无效: -1 字节
[SQLite Sample 147] 调用 (第735次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 280, 大小: 1618826156 字节, 有效: false
[SQLite Skip] 数据大小无效: 1618826156 字节
[SQLite Sample 148] 调用 (第740次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 149] 调用 (第745次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 141 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x1
[SQLite Sample 150] 调用 (第750次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 4, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x3
[SQLite Sample 151] 调用 (第755次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 72, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 152] 调用 (第760次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 120, 大小: 1044451080 字节, 有效: false
[SQLite Skip] 数据大小无效: 1044451080 字节
[SQLite Sample 153] 调用 (第765次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 96, 大小: -1 字节, 有效: false
[SQLite Skip] 数据大小无效: -1 字节
[SQLite Sample 154] 调用 (第770次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 16, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 155] 调用 (第775次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 156] 调用 (第780次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 3, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 157] 调用 (第785次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 87, 大小: 141 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x1
[SQLite Sample 158] 调用 (第790次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 3, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 159] 调用 (第795次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 296, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 160] 调用 (第800次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 32, 大小: 51 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0xf
[SQLite Sample 161] 调用 (第805次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 4, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x3
[SQLite Sample 162] 调用 (第810次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 72, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 163] 调用 (第815次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 164] 调用 (第820次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 88, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 165] 调用 (第825次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 166] 调用 (第830次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 48, 大小: 39 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x2f
[SQLite Sample 167] 调用 (第835次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 76, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 168] 调用 (第840次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 14, 大小: 48 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0xd
[SQLite Sample 169] 调用 (第845次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 77, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 170] 调用 (第850次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 171] 调用 (第855次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 80, 大小: -1 字节, 有效: false
[SQLite Skip] 数据大小无效: -1 字节
[SQLite Sample 172] 调用 (第860次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 80, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 173] 调用 (第865次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 174] 调用 (第870次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 480, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 175] 调用 (第875次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 648, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x8
[SQLite Sample 176] 调用 (第880次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 72, 大小: 1116748672 字节, 有效: false
[SQLite Skip] 数据大小无效: 1116748672 字节
[SQLite Sample 177] 调用 (第885次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 4, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x3
[SQLite Sample 178] 调用 (第890次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 72, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 179] 调用 (第895次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 180] 调用 (第900次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 88, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 181] 调用 (第905次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 182] 调用 (第910次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 48, 大小: 7 字节, 有效: true
[SQLite Skip] 数据太小: 7 字节
[SQLite Sample 183] 调用 (第915次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 40, 大小: 1367864384 字节, 有效: false
[SQLite Skip] 数据大小无效: 1367864384 字节
[SQLite Sample 184] 调用 (第920次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 480, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 185] 调用 (第925次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 120, 大小: 1177660224 字节, 有效: false
[SQLite Skip] 数据大小无效: 1177660224 字节
[SQLite Sample 186] 调用 (第930次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 80, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 187] 调用 (第935次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 72, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 188] 调用 (第940次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 189] 调用 (第945次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 80, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 190] 调用 (第950次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 191] 调用 (第955次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 6, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 192] 调用 (第960次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 141 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x1
[SQLite Sample 193] 调用 (第965次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 194] 调用 (第970次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 648, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x8
[SQLite Sample 195] 调用 (第975次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 16, 大小: 1367857896 字节, 有效: false
[SQLite Skip] 数据大小无效: 1367857896 字节
[SQLite Sample 196] 调用 (第980次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 76, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 197] 调用 (第985次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 198] 调用 (第990次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 32, 大小: 32 字节, 有效: true
[SQLite Warning] BLOB数据指针为空
[SQLite Sample 199] 调用 (第995次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 24, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 200] 调用 (第1000次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 4, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 201] 调用 (第1005次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 16, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 202] 调用 (第1010次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 203] 调用 (第1015次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 456, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 204] 调用 (第1020次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 16, 大小: 1366813416 字节, 有效: false
[SQLite Skip] 数据大小无效: 1366813416 字节
[SQLite Sample 205] 调用 (第1025次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 76, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 206] 调用 (第1030次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 207] 调用 (第1035次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 80, 大小: -1 字节, 有效: false
[SQLite Skip] 数据大小无效: -1 字节
[SQLite Sample 208] 调用 (第1040次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 280, 大小: 1618826156 字节, 有效: false
[SQLite Skip] 数据大小无效: 1618826156 字节
[SQLite Sample 209] 调用 (第1045次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 210] 调用 (第1050次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 141 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x1
[SQLite Sample 211] 调用 (第1055次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 4, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x3
[SQLite Sample 212] 调用 (第1060次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 72, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 213] 调用 (第1065次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 1008, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 214] 调用 (第1070次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 88, 大小: 0 字节, 有效: true
[SQLite Skip] 数据太小: 0 字节
[SQLite Sample 215] 调用 (第1075次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 32, 大小: 1 字节, 有效: true
[SQLite Skip] 数据太小: 1 字节
[SQLite Sample 216] 调用 (第1080次)
[SQLite Params] 语句: 0x7f3f261f08, 参数: 48, 大小: 7 字节, 有效: true
[SQLite Skip] 数据太小: 7 字节
[SQLite Sample 217] 调用 (第1085次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 648, 大小: 32 字节, 有效: true
[SQLite Data Error] 数据读取失败: Error: access violation accessing 0x8
[SQLite Sample 218] 调用 (第1090次)
[SQLite Params] 语句: 0x7f50b17208, 参数: 32, 大小: 32 字节, 有效: true
Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=000001BE949672B0)

Thread 0x00009650 (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x00004840 (most recent call first):
<no Python frame>
