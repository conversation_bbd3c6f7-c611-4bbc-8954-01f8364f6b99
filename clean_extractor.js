/*
 * 高德地图解压后数据提取器 - 干净版本
 * 基于成功脚本模式：文件读取 + zlib解压
 */

(function() {
    'use strict';
    
    console.log("[🎯解压数据提取器] 启动真实解压后数据提取器...");
    
    var CONFIG = {
        MAX_SAMPLES: 8,            // 最多收集8个样本
        MIN_DATA_SIZE: 100,        // 最小数据大小
        MAX_DATA_SIZE: 1024 * 100, // 最大数据大小
        INIT_DELAY: 3000
    };
    
    var extractorStats = {
        successfulExtractions: 0,
        extractedSamples: []
    };
    
    // === 解压后数据提取（核心功能）===
    function extractDecompressedData(dataPtr, dataSize, sampleName, context) {
        try {
            var extractSize = Math.min(dataSize, 1024);
            var rawData = dataPtr.readByteArray(extractSize);
            var dataView = new Uint8Array(rawData);
            
            var sample = {
                name: sampleName,
                fullSize: dataSize,
                context: context,
                signature: "",
                coordinates: [],
                chineseText: []
            };
            
            // 提取可读签名
            for (var j = 0; j < Math.min(64, dataView.length); j++) {
                if (dataView[j] >= 32 && dataView[j] < 127) {
                    sample.signature += String.fromCharCode(dataView[j]);
                } else if (dataView[j] === 0) {
                    sample.signature += "\\0";
                } else {
                    sample.signature += ".";
                }
            }
            
            // 提取坐标
            sample.coordinates = extractCoordinates(dataView);
            
            // 提取中文文本
            sample.chineseText = extractChineseText(rawData);
            
            // 只在找到有意义数据时输出详细信息
            var hasData = (sample.coordinates.length > 0 || sample.chineseText.length > 0 || 
                          sample.signature.indexOf('.C.U') !== -1 || sample.signature.indexOf('. .f') !== -1);
            
            if (hasData) {
                console.log("🎯=== 找到有效解压后数据！ ===");
                console.log("📍 来源: " + sample.context);
                console.log("📏 大小: " + sample.fullSize + " 字节");
                console.log("📝 签名: '" + sample.signature.substring(0, 40) + "'");
                
                if (sample.coordinates.length > 0) {
                    console.log("🌍 坐标数据: " + sample.coordinates.length + " 个");
                    for (var k = 0; k < Math.min(3, sample.coordinates.length); k++) {
                        var coord = sample.coordinates[k];
                        console.log("  坐标" + k + ": (" + coord.x.toFixed(6) + ", " + coord.y.toFixed(6) + ") ← 真实经纬度！");
                    }
                }
                
                if (sample.chineseText.length > 0) {
                    console.log("🈲 中文文本: " + sample.chineseText.length + " 个");
                    for (var l = 0; l < Math.min(5, sample.chineseText.length); l++) {
                        console.log("  文本" + l + ": " + sample.chineseText[l]);
                    }
                }
                
                console.log("🎯==============================");
            } else {
                console.log("📝 " + sampleName + " (" + context + "): " + sample.signature.substring(0, 20) + " - 无地图数据");
            }
            
            extractorStats.extractedSamples.push(sample);
            extractorStats.successfulExtractions++;
            
            return sample;
            
        } catch (e) {
            console.log("[❌提取错误] " + sampleName + ": " + e);
            return null;
        }
    }
    
    // === 坐标提取 ===
    function extractCoordinates(dataView) {
        var coordinates = [];
        try {
            var view = new DataView(dataView.buffer, dataView.byteOffset, dataView.byteLength);
            
            for (var i = 0; i < dataView.length - 8 && coordinates.length < 5; i += 4) {
                try {
                    var x = view.getFloat32(i, true);
                    var y = view.getFloat32(i + 4, true);
                    
                    if ((70 <= x && x <= 140 && 15 <= y && y <= 60) || 
                        (70 <= y && y <= 140 && 15 <= x && x <= 60)) {
                        coordinates.push({x: x, y: y, offset: i});
                    }
                } catch (e) {
                    continue;
                }
            }
        } catch (e) {}
        return coordinates;
    }
    
    // === 中文文本提取 ===
    function extractChineseText(rawData) {
        var texts = [];
        try {
            var text = new TextDecoder('utf-8').decode(rawData);
            var currentText = "";
            
            for (var i = 0; i < text.length; i++) {
                var char = text[i];
                if (char >= '\u4e00' && char <= '\u9fff') {
                    currentText += char;
                } else {
                    if (currentText.length >= 2) {
                        texts.push(currentText);
                        if (texts.length >= 10) break;
                    }
                    currentText = "";
                }
            }
            
            if (currentText.length >= 2) {
                texts.push(currentText);
            }
        } catch (e) {}
        return texts;
    }
    
    // === Hook文件读取操作 ===
    function setupFileReadHook() {
        console.log("[文件Hook] 设置文件读取Hook...");
        
        try {
            var readPtr = Module.findExportByName("libc.so", "read");
            if (!readPtr) {
                console.log("[❌错误] 无法找到read函数");
                return;
            }
            
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    this.fd = args[0].toInt32();
                    this.buffer = args[1];
                    this.size = args[2].toInt32();
                },
                
                onLeave: function(retval) {
                    var bytesRead = retval.toInt32();
                    if (bytesRead <= 0 || this.size < CONFIG.MIN_DATA_SIZE) return;
                    
                    try {
                        var safeSize = Math.min(bytesRead, 1024);
                        var data = this.buffer.readByteArray(safeSize);
                        var view = new Uint8Array(data);
                        
                        // 构建可读签名
                        var signature = "";
                        for (var i = 0; i < Math.min(32, view.length); i++) {
                            if (view[i] >= 32 && view[i] < 127) {
                                signature += String.fromCharCode(view[i]);
                            } else if (view[i] === 0) {
                                signature += "\\0";
                            } else {
                                signature += ".";
                            }
                        }
                        
                        // 检查是否包含地图数据特征
                        var hasMapData = (
                            signature.indexOf('.C.U') !== -1 ||
                            signature.indexOf('. .f') !== -1 ||
                            signature.indexOf('.i..') !== -1 ||
                            view[0] === 0x08 ||
                            signature.indexOf('DICE') !== -1 ||
                            signature.indexOf('ANS') !== -1
                        );
                        
                        if (hasMapData && extractorStats.successfulExtractions < CONFIG.MAX_SAMPLES) {
                            console.log("[🎯文件数据] 发现地图数据特征: " + signature.substring(0, 20));
                            
                            var sampleName = "file_read_sample_" + extractorStats.successfulExtractions;
                            var sample = extractDecompressedData(this.buffer, bytesRead, sampleName, "文件读取");
                        }
                        
                    } catch (e) {
                        // 忽略读取错误
                    }
                }
            });
            
            console.log("[✅文件Hook] 文件读取Hook已设置");
            
        } catch (e) {
            console.log("[❌文件Hook错误] " + e);
        }
    }
    
    // === Hook zlib解压 ===
    function setupZlibHook() {
        console.log("[zlib Hook] 设置zlib解压Hook...");
        
        try {
            var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
            if (!uncompressPtr) {
                console.log("[❌错误] 无法找到uncompress函数");
                return;
            }
            
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    this.destBuffer = args[0];
                    this.destLenPtr = args[1];
                    this.sourceLen = args[3].toInt32();
                },
                
                onLeave: function(retval) {
                    var result = retval.toInt32();
                    if (result !== 0) return; // Z_OK = 0
                    
                    try {
                        var decompressedLen = this.destLenPtr.readU32();
                        if (decompressedLen <= 0 || decompressedLen > 1024 * 1024) return;
                        
                        if (extractorStats.successfulExtractions < CONFIG.MAX_SAMPLES) {
                            console.log("[🎯zlib解压] 成功解压 " + this.sourceLen + " → " + decompressedLen + " 字节");
                            
                            var sampleName = "zlib_decompressed_" + extractorStats.successfulExtractions;
                            var sample = extractDecompressedData(this.destBuffer, decompressedLen, sampleName, "zlib解压");
                        }
                        
                    } catch (e) {
                        console.log("[❌zlib提取错误] " + e);
                    }
                }
            });
            
            console.log("[✅zlib Hook] zlib解压Hook已设置");
            
        } catch (e) {
            console.log("[❌zlib Hook错误] " + e);
        }
    }
    
    // === 报告 ===
    function generateReport() {
        if (extractorStats.successfulExtractions === 0) return;
        
        console.log("\n🎯=== 真实解压后数据提取报告 ===");
        console.log("成功提取: " + extractorStats.successfulExtractions + " 个样本");
        
        var totalCoords = 0;
        var totalTexts = 0;
        var validSamples = 0;
        
        for (var i = 0; i < extractorStats.extractedSamples.length; i++) {
            var sample = extractorStats.extractedSamples[i];
            totalCoords += sample.coordinates.length;
            totalTexts += sample.chineseText.length;
            if (sample.coordinates.length > 0 || sample.chineseText.length > 0) {
                validSamples++;
            }
        }
        
        console.log("📊 数据统计:");
        console.log("  🌍 总坐标数: " + totalCoords + " 个");
        console.log("  🈲 总中文文本: " + totalTexts + " 个");
        console.log("  ✅ 有效样本: " + validSamples + "/" + extractorStats.successfulExtractions);
        console.log("🎯======================================\n");
    }
    
    // === 主函数 ===
    function main() {
        console.log("[🚀主程序] 初始化真实解压后数据提取器...");
        
        setTimeout(function() {
            try {
                setupFileReadHook();
                setupZlibHook();
                
                setInterval(generateReport, 20000);
                
                console.log("[🎯真实数据提取器] 已启动!");
                console.log("💡 策略: 文件读取 + zlib解压");
                console.log("💡 现在移动地图，观察真实的解压后数据提取!");
                
            } catch (e) {
                console.log("[❌错误] Hook设置失败: " + e);
            }
            
        }, CONFIG.INIT_DELAY);
    }
    
    // === 启动 ===
    try {
        Java.perform(function() {
            console.log("[☕Java] 环境就绪");
            main();
        });
    } catch (e) {
        console.log("[❌Java错误] " + e);
        main();
    }
    
})(); 