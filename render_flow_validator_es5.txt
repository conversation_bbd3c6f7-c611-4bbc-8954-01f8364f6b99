     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[渲染流程验证] 开始验证8阶段离线地图渲染流程...
[\u2713] 阶段1 Hook设置成功
[\u2713] 阶段2 Hook设置成功
[\u2713] 阶段6 Hook设置成功
[渲染流程验证] 验证脚本已启动，等待地图操作...
[提示] 请移动地图以触发完整的8阶段渲染流程验证
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [\u2713] 阶段1验证: 数据获取阶段已触发
[\u2713] 阶段6验证: OpenGL渲染阶段已触发

[渲染流程验证报告] ==========================================
流程完整性: 25.0% (2/8阶段已触发)

各阶段验证状态:
  阶段1-数据获取: \u2713 (触发60次)
  阶段2-数据处理: \u2717 (触发0次)
  阶段3-几何处理: \u2717 (触发0次)
  阶段4-样式应用: \u2717 (触发0次)
  阶段5-文本处理: \u2717 (触发0次)
  阶段6-OpenGL渲染: \u2713 (触发73次)
  阶段7-后处理: \u2717 (触发0次)
  阶段8-显示输出: \u2717 (触发0次)

流程验证结论:
  [结论] 渲染流程分析需要重新评估
===============================================


[渲染流程验证报告] ==========================================
流程完整性: 25.0% (2/8阶段已触发)

各阶段验证状态:
  阶段1-数据获取: \u2713 (触发200次)
  阶段2-数据处理: \u2717 (触发0次)
  阶段3-几何处理: \u2717 (触发0次)
  阶段4-样式应用: \u2717 (触发0次)
  阶段5-文本处理: \u2717 (触发0次)
  阶段6-OpenGL渲染: \u2713 (触发220次)
  阶段7-后处理: \u2717 (触发0次)
  阶段8-显示输出: \u2717 (触发0次)

流程验证结论:
  [结论] 渲染流程分析需要重新评估
===============================================


[Remote::com.autonavi.minimap]-> 
[渲染流程验证报告] ==========================================
流程完整性: 25.0% (2/8阶段已触发)

各阶段验证状态:
  阶段1-数据获取: \u2713 (触发296次)
  阶段2-数据处理: \u2717 (触发0次)
  阶段3-几何处理: \u2717 (触发0次)
  阶段4-样式应用: \u2717 (触发0次)
  阶段5-文本处理: \u2717 (触发0次)
  阶段6-OpenGL渲染: \u2713 (触发279次)
  阶段7-后处理: \u2717 (触发0次)
  阶段8-显示输出: \u2717 (触发0次)

流程验证结论:
  [结论] 渲染流程分析需要重新评估
===============================================

exit

Thank you for using Frida!
