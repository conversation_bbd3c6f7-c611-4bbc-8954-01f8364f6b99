plugins {
    id 'com.android.application'
}

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.2"

    defaultConfig {
        applicationId 'com.autonavi.minimap'
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 151200
        versionName "15.12.0.2055"
        vectorDrawables.useSupportLibrary = true
        useLibrary 'org.apache.http.legacy'

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    lintOptions {
        abortOnError false
    }

    buildFeatures {
        buildConfig = false
    }
}

dependencies {
    // TODO: dependencies
}
