视口更新（Viewport Update）内部流程
核心入口（调度门面）— sub_72E43C(a1, a2, a3)
作用：对外统一入口，读取“更新指令/参数”并分发。
结果：将视口参数指针传递到核心更新链路（a2 指向视口/相机参数结构）。
核心更新序列 — sub_72B5B8(a1, a2)
作用：把新的视口参数写入到渲染引擎的“活动视口”。
主要步骤（按调用次序）:
sub_72B6E0(a1, a2): 计算/校验缓冲大小与布局（推测决定需要的结构体大小或缓冲片数）。
sub_72B7B0(a1, a2): 分配或准备缓冲（看到 operator new(a2 << 6) 的模式）。
sub_72B684(dst, src): 复制新的视口结构（关键：src 即我们 Frida 中 viewportCopy 的 a2，最容易正确解码）。
sub_72B728(a1, a2): 交换活动/待用的视口缓冲（双缓冲或翻转，降低并发写入风险）。
sub_72B78C(void **): 清理旧指针/释放不再使用的结构。
结构访问特征：出现 a1+2、a1+16、a1+80/88、a1+512/520 等偏移，说明 a1 为大结构（渲染上下文/地图引擎对象），包含多路子系统（视口、瓦片选择器、资源队列等）。
可见瓦片计算 — sub_73A820(a1, a2)
作用：依据当前视口（中心、缩放、旋转）与数据层配置，遍历候选元素/瓦片并筛选可见集。
迹象：大量循环与条件判断，访问 a1+24、v3+8/16/24 等，调用 sub_8B95F0、sub_73CDB8 等用于空间裁剪/过滤。
瓦片加载/管线 — sub_748BDC(a1, a2, a3)
作用：对可见集进行调度（内存缓存、网络请求、解码、纹理构建等）。
迹象：调用链很深（sub_75A71C、sub_748EE4、sub_78DA24、sub_755A0C、sub_75AA44 等），访问 a1+80/88/512/520 等区域（推定为加载/解码/上传队列、LRU 缓存等）。
瓦片数据入队/处理 — sub_74E814(a1, a2)
作用：将解析好的数据拷贝入内部结构并提交到后续阶段（见 memcpy 风格与 sub_74DFDC 调用）。
渲染阶段
视口更新后进入渲染帧，OpenGL 调用（glDraw*/glBindTexture/glTexImage2D）出现在后续渲染线程/阶段。可在稳定后再开启 GL 钩子做相关性对齐。
视口结构（sub_72B684 的 a2）字段定位
现阶段在 sub_72B684(dst, src) 入口抓取 src 指针最稳定。脚本采用“float 优先 + 安全读取 + 启发式”解析，避免误把指针/整型按 double 读出导致的“垃圾值”。
典型布局（经验法则，实际机型/版本可能存在差异）：
float[0]: centerX（经度/墨卡托 X）
float[1]: centerY（纬度/墨卡托 Y）
float[2]: zoom（通常 3–20 或更高，依地图类型）
float[3]: rotationRadians（-π~π）
脚本启发式
以 float 序列的前 8–16 个值中，优先取 index 0/1 为中心，index 2/3 为缩放/旋转；并用范围过滤（zoom 1–25，rotation ±6.5，center 在经纬或墨卡托合理范围）打分选最优组合。
输出 heuristics.best，用于快速核对。旋转可换算为角度：deg = rad × 180/π。
你当前运行结果与后续建议
你反馈“应用运行正常”，说明此前崩溃已由“内存越界读取/钩子干扰”修复（我们已加入可读区间裁剪、缩小转储窗口，并默认关闭 GL/瓦片钩子）。
建议校验：
平移地图：centerX/centerY 连续变化、数值在合理范围（经纬在 ±180/±90；若是墨卡托则在 ±几千万）。
缩放：zoom 随双指缩放/双击跳级增减。
旋转：用双指旋转，rotation 以弧度变化（±π），换算角度后直观可读。
若上述都正确，可逐步打开：
打开瓦片钩子：将 config.enableTileHooks = true，验证 calculateVisibleTiles 与 loadTilePipeline 的调用在平移/缩放时显著活跃。
如需对齐渲染帧时序，再打开 config.enableGLHooks = true，观察可见瓦片计算后紧随的 GL 提交。
视口更新与瓦片/渲染的因果关系
sub_72E43C → sub_72B5B8 完成相机/视口状态“落地”（双缓冲保护）。
新的视口状态触发 sub_73A820 重算可见集。
可见集变化驱动 sub_748BDC 的加载/解码/纹理上传。
随后渲染线程在下一帧提交 GL 绘制，实际出现在 libGLESv2/3 的 glDraw* 调用中。
出现异常时的快速定位
视口数值异常：优先看 viewportCopy 的 heuristics.best，并对照 float 序列是否有 pointer-like 大数；必要时缩小 maxViewportFloatsToRead 或改在 updateViewport 入参直接比对。
再次崩溃：保持仅视口钩子开启，逐一恢复瓦片/GL 钩子，隔离具体来源。
你现在可以开启 config.enableTileHooks = true 做瓦片链路验证；如果需要，我可以把“视口摘要行（中心/缩放/旋转，已转角度）”加入日志，便于直接人工观察。若要我加这条摘要输出，我可立即更新脚本。
本次变更的影响
稳定拿到视口（中心/缩放/旋转）且避免崩溃
可按需开启瓦片与 GL 钩子观察完整渲染链路