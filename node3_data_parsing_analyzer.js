/*
 * 节点3: 数据解析流程深度分析器
 * 目标: 分析DICE-AM格式解析逻辑和数据分离机制
 * 方法: IDA Pro静态分析 + Frida动态追踪
 * 作者: 高级Frida程序员 + IDA Pro分析师
 */

console.log("=== 节点3: 数据解析流程深度分析器 ===");
console.log("目标: 深入分析DICE-AM格式解析和几何/文本数据分离");

// 节点3分析状态管理
var node3Analysis = {
    // DICE-AM解析追踪
    diceAmParsing: {
        headerParsing: [],       // DICE-AM头部解析
        geometryParsing: [],     // 几何数据解析
        textParsing: [],         // 文本数据解析
        coordinateParsing: []    // 坐标解析
    },
    
    // 数据分离分析
    dataSeparation: {
        geometryData: [],        // 几何数据记录
        textData: [],            // 文本数据记录
        attributeData: [],       // 属性数据记录
        metadataData: []         // 元数据记录
    },
    
    // 坐标系统分析
    coordinateSystem: {
        transformations: [],     // 坐标转换记录
        projections: [],         // 投影变换记录
        scalingOps: [],          // 缩放操作记录
        offsetOps: []            // 偏移操作记录
    },
    
    // 解析性能分析
    parsingPerformance: {
        parseTime: [],           // 解析时间
        dataSize: [],            // 数据大小
        complexity: []           // 复杂度分析
    },
    
    // 统计信息
    statistics: {
        totalDiceAmBlocks: 0,
        totalGeometryObjects: 0,
        totalTextObjects: 0,
        totalCoordinates: 0,
        startTime: Date.now()
    }
};

// DICE-AM头部结构分析
function analyzeDiceAmHeader(data) {
    if (!data || data.length < 32) return null;
    
    var view = new DataView(data);
    var header = {
        magic: String.fromCharCode.apply(null, new Uint8Array(data.slice(0, 8))),
        version: view.getUint32(8, true),
        blockCount: view.getUint32(12, true),
        geometryOffset: view.getUint32(16, true),
        textOffset: view.getUint32(20, true),
        attributeOffset: view.getUint32(24, true),
        totalSize: view.getUint32(28, true)
    };
    
    return header;
}

// 几何数据类型识别
function identifyGeometryType(data) {
    if (!data || data.length < 4) return "UNKNOWN";
    
    var view = new Uint8Array(data);
    
    // 点数据
    if (view[0] === 0x01) return "POINT";
    // 线数据
    if (view[0] === 0x02) return "LINE";
    // 多边形数据
    if (view[0] === 0x03) return "POLYGON";
    // 复合几何
    if (view[0] === 0x04) return "MULTIGEOMETRY";
    
    return "UNKNOWN";
}

// 文本编码检测
function detectTextEncoding(data) {
    if (!data || data.length < 3) return "UNKNOWN";
    
    var view = new Uint8Array(data);
    
    // UTF-8 BOM
    if (view[0] === 0xEF && view[1] === 0xBB && view[2] === 0xBF) {
        return "UTF-8-BOM";
    }
    
    // UTF-16 LE BOM
    if (view[0] === 0xFF && view[1] === 0xFE) {
        return "UTF-16LE";
    }
    
    // UTF-16 BE BOM
    if (view[0] === 0xFE && view[1] === 0xFF) {
        return "UTF-16BE";
    }
    
    // 检测中文字符（UTF-8）
    for (var i = 0; i < Math.min(data.length - 2, 100); i++) {
        if (view[i] >= 0xE4 && view[i] <= 0xE9 && 
            view[i + 1] >= 0x80 && view[i + 1] <= 0xBF &&
            view[i + 2] >= 0x80 && view[i + 2] <= 0xBF) {
            return "UTF-8-CHINESE";
        }
    }
    
    return "ASCII";
}

// 坐标数据解析
function parseCoordinateData(data, offset, count) {
    if (!data || offset + count * 8 > data.length) return null;
    
    var coordinates = [];
    var view = new DataView(data);
    
    for (var i = 0; i < count; i++) {
        var x = view.getFloat32(offset + i * 8, true);
        var y = view.getFloat32(offset + i * 8 + 4, true);
        coordinates.push({ x: x, y: y });
    }
    
    return coordinates;
}

// 安全的字节数组转十六进制
function safeByteArrayToHex(byteArray, maxLen) {
    if (!byteArray) return "";
    
    var hexBytes = [];
    var len = Math.min(maxLen || 16, byteArray.length);
    
    for (var i = 0; i < len; i++) {
        var byte = byteArray[i];
        if (typeof byte === 'number') {
            hexBytes.push(('0' + byte.toString(16)).slice(-2));
        }
    }
    return hexBytes.join(' ');
}

console.log("初始化节点3分析器...");

// 1. Hook libamapnsq.so 的DICE-AM解析函数
try {
    var libamapnsq = Process.getModuleByName("libamapnsq.so");
    console.log("[✓] libamapnsq.so 模块已找到");
    
    // Hook DICE-AM解析相关函数（基于之前的分析推测）
    var diceAmFunctions = [
        { name: "dice_am_parser", offset: 0x15000 },      // 假设的DICE-AM解析函数
        { name: "geometry_parser", offset: 0x16000 },     // 几何数据解析
        { name: "text_parser", offset: 0x17000 },         // 文本数据解析
        { name: "coordinate_transform", offset: 0x18000 } // 坐标转换
    ];
    
    diceAmFunctions.forEach(function(func) {
        try {
            var funcAddr = libamapnsq.base.add(func.offset);
            Interceptor.attach(funcAddr, {
                onEnter: function(args) {
                    console.log("[" + func.name + "] 函数调用");
                    this.startTime = Date.now();
                    this.funcName = func.name;
                    
                    // 分析输入数据
                    try {
                        if (args[0] && !args[0].isNull()) {
                            var inputData = args[0].readByteArray(64);
                            if (inputData) {
                                var hexData = safeByteArrayToHex(new Uint8Array(inputData), 32);
                                console.log("[" + func.name + "] 输入数据:", hexData);
                                
                                // 特定函数的数据分析
                                if (func.name === "dice_am_parser") {
                                    var header = analyzeDiceAmHeader(inputData);
                                    if (header && header.magic.indexOf("DICE") === 0) {
                                        console.log("[DICE-AM头部] 版本:", header.version, "块数:", header.blockCount);
                                        
                                        node3Analysis.diceAmParsing.headerParsing.push({
                                            timestamp: Date.now(),
                                            header: header,
                                            dataPreview: hexData
                                        });
                                        
                                        node3Analysis.statistics.totalDiceAmBlocks++;
                                    }
                                } else if (func.name === "geometry_parser") {
                                    var geomType = identifyGeometryType(inputData);
                                    console.log("[几何解析] 类型:", geomType);
                                    
                                    node3Analysis.diceAmParsing.geometryParsing.push({
                                        timestamp: Date.now(),
                                        geometryType: geomType,
                                        dataPreview: hexData
                                    });
                                    
                                    node3Analysis.statistics.totalGeometryObjects++;
                                } else if (func.name === "text_parser") {
                                    var encoding = detectTextEncoding(inputData);
                                    console.log("[文本解析] 编码:", encoding);
                                    
                                    node3Analysis.diceAmParsing.textParsing.push({
                                        timestamp: Date.now(),
                                        encoding: encoding,
                                        dataPreview: hexData
                                    });
                                    
                                    node3Analysis.statistics.totalTextObjects++;
                                }
                            }
                        }
                    } catch (e) {
                        console.log("[" + func.name + "] 输入分析错误:", e.message);
                    }
                },
                onLeave: function(retval) {
                    var duration = Date.now() - this.startTime;
                    console.log("[" + this.funcName + "] 返回:", retval, "耗时:", duration, "ms");
                    
                    // 记录性能数据
                    node3Analysis.parsingPerformance.parseTime.push({
                        function: this.funcName,
                        duration: duration,
                        timestamp: Date.now()
                    });
                }
            });
            console.log("[✓] " + func.name + " Hook 已设置");
        } catch (e) {
            console.log("[✗] " + func.name + " Hook 设置失败:", e.message);
        }
    });
    
} catch (e) {
    console.log("[✗] libamapnsq.so Hook 设置失败:", e.message);
}

console.log("节点3分析器初始化完成，等待数据解析操作...");
