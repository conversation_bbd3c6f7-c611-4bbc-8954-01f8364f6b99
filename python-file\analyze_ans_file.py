#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高德地图.ans文件分析器
搜索并提取人类可读的数据
"""

import os
import re
import zlib

def analyze_ans_file(filename):
    print(f"[分析] 开始分析文件: {filename}")
    
    if not os.path.exists(filename):
        print(f"[错误] 文件不存在: {filename}")
        return
    
    file_size = os.path.getsize(filename)
    print(f"[信息] 文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"[分析] 开始搜索人类可读数据...")
    
    # 搜索模式
    patterns = [
        (b'DICE-AM', 'DICE_AM_header'),
        (b'<?xml', 'XML_config'),
        (b'{"res_list"', 'JSON_resource'),
        (b'{"', 'JSON_data'),
        (b'<', 'XML_tag'),
        (b'\xe4\xb8\xad', 'Chinese_UTF8'),
    ]
    
    findings = []
    
    for pattern, description in patterns:
        print(f"\n[搜索] {description}: {pattern}")
        
        # 查找所有匹配
        offset = 0
        count = 0
        while True:
            pos = data.find(pattern, offset)
            if pos == -1:
                break
            
            count += 1
            if count <= 5:  # 只显示前5个匹配
                print(f"  找到 #{count}: 偏移 0x{pos:08X} ({pos})")
                
                # 提取周围数据
                start = max(0, pos - 50)
                end = min(len(data), pos + 500)
                context = data[start:end]
                
                # 转换为可读文本
                readable = ""
                for byte in context:
                    if 32 <= byte < 127:
                        readable += chr(byte)
                    elif byte == 10:
                        readable += '\n'
                    elif byte == 9:
                        readable += '\t'
                    else:
                        readable += '.'
                
                print(f"    上下文预览:")
                print(f"    {readable[:200]}")
                
                # 保存数据块
                extract_name = f"extracted_{description}_{pos:08X}.txt"
                try:
                    with open(extract_name, 'w', encoding='utf-8') as out:
                        out.write(f"# {description} 数据提取\n")
                        out.write(f"# 文件: {filename}\n")
                        out.write(f"# 偏移: 0x{pos:08X} ({pos})\n\n")
                        out.write(readable)
                    
                    print(f"    保存到: {extract_name}")
                    
                    findings.append({
                        'pattern': description,
                        'offset': pos,
                        'file': extract_name,
                        'preview': readable[:100]
                    })
                except Exception as e:
                    print(f"    保存失败: {e}")
            
            offset = pos + 1
        
        print(f"  总计找到: {count} 个匹配")
    
    # 尝试zlib解压
    print(f"\n[解压] 尝试zlib解压...")
    try_decompress(data)
    
    # 生成报告
    generate_report(filename, file_size, findings)
    
    print(f"\n[完成] 分析完成，共找到 {len(findings)} 个数据块")

def try_decompress(data):
    """尝试对数据进行解压"""
    # 查找可能的zlib压缩数据
    zlib_headers = [b'\x78\x9c', b'\x78\x01', b'\x78\xda']
    
    for header in zlib_headers:
        offset = 0
        count = 0
        while True:
            pos = data.find(header, offset)
            if pos == -1:
                break
            
            count += 1
            if count <= 3:  # 只尝试前3个
                print(f"  找到zlib头: 偏移 0x{pos:08X}")
                
                # 尝试解压
                for size in [1024, 4096, 16384]:
                    if pos + size <= len(data):
                        try:
                            compressed = data[pos:pos+size]
                            decompressed = zlib.decompress(compressed)
                            
                            print(f"    解压成功! 原始: {len(compressed)} 字节 -> 解压: {len(decompressed)} 字节")
                            
                            # 保存解压数据
                            decomp_name = f"decompressed_{pos:08X}_{len(decompressed)}.bin"
                            with open(decomp_name, 'wb') as out:
                                out.write(decompressed)
                            
                            # 分析解压后的数据
                            analyze_decompressed(decompressed, decomp_name)
                            break
                            
                        except Exception as e:
                            continue
            
            offset = pos + 1

def analyze_decompressed(data, filename):
    """分析解压后的数据"""
    print(f"    分析解压数据: {filename}")
    
    # 转换为可读文本
    readable = ""
    for byte in data[:1000]:  # 只分析前1000字节
        if 32 <= byte < 127:
            readable += chr(byte)
        elif byte == 10:
            readable += '\n'
        elif byte == 9:
            readable += '\t'
        else:
            readable += '.'
    
    if readable.strip():
        print(f"    解压内容预览:")
        print(f"    {readable[:200]}")
        
        # 保存可读内容
        text_name = filename.replace('.bin', '.txt')
        with open(text_name, 'w', encoding='utf-8') as out:
            out.write(f"# 解压后的内容\n")
            out.write(f"# 原文件: {filename}\n\n")
            out.write(readable)
        
        print(f"    可读内容保存到: {text_name}")

def generate_report(filename, file_size, findings):
    """生成分析报告"""
    with open("ans_analysis_report.txt", 'w', encoding='utf-8') as f:
        f.write("# 高德地图.ans文件分析报告\n\n")
        f.write(f"文件: {filename}\n")
        f.write(f"大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)\n")
        f.write(f"发现数据块: {len(findings)} 个\n\n")
        
        for i, finding in enumerate(findings, 1):
            f.write(f"## 数据块 #{i}\n")
            f.write(f"类型: {finding['pattern']}\n")
            f.write(f"偏移: 0x{finding['offset']:08X} ({finding['offset']})\n")
            f.write(f"文件: {finding['file']}\n")
            f.write(f"预览:\n```\n{finding['preview']}\n```\n\n")
    
    print(f"[报告] 详细报告保存到: ans_analysis_report.txt")

if __name__ == "__main__":
    ans_file = "gb_v6.ans"
    analyze_ans_file(ans_file) 