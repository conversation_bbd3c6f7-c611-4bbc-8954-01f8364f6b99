     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
=== 节点1简化测试 ===
[\u2713] libc.so 找到
[\u2713] open() Hook 设置完成
[\u2713] read() Hook 设置完成
节点1简化测试启动完成
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/geo_fence_global_v2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/geo_fence_global_v2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/bundle_list.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/bundle_list.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/earth_bundle_15.12.0.49.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/earth_bundle_15.12.0.49.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/map_renderer_string.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/map_renderer_string.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a0/m1.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_idx_33652181813.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_idx_33652181813.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
统计: 文件操作=4796, ANS文件=71
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a3/m1.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a3/m2.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_smart/smartmap_file_cache_7000001_aos.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_smart/smartmap_file_cache_600017.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_smart/smartmap_file_cache_10000001.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_smart/smartmap_file_cache_10000001.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_smart/smartmap_file_cache_600017.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_smart/smartmap_file_cache_7000001_aos.ans
统计: 文件操作=9200, ANS文件=103
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[ANS文件] 打开: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
统计: 文件操作=12345, ANS文件=105
统计: 文件操作=14908, ANS文件=105

[Remote::com.autonavi.minimap]-> EXIT
ReferenceError: identifier 'EXIT' undefined
[Remote::com.autonavi.minimap]-> 
[Remote::com.autonavi.minimap]-> 
[Remote::com.autonavi.minimap]-> 统计: 文件操作=17434, ANS文件=105
Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=00000238540D7E40)

Thread 0x00003e38 (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x00002718 (most recent call first):
<no Python frame>
