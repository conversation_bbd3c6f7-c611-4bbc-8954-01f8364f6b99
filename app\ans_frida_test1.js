/*
app 运行正常，只能进行捕获文件
Spawning `com.autonavi.minimap`...
[+] 高德地图离线数据分析脚本启动
[+] 反调试绕过措施已应用
[+] 高德地图离线数据分析脚本设置完成
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [MAP DATA] open 路径: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/geo_fence_global_v2.ans
[MAP DATA] open 路径: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/geo_fence_global_v2.ans
[MAP DATA] open 路径: /data/user/0/com.autonavi.minimap/files/cache/offlinemap_lite_v3/ackor_offline_compile.db
[MAP DATA] open 路径: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[MAP DATA] open 路径: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[MAP DATA] open 路径: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[MAP DATA] open 路径: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[MAP DATA] open 路径: /data/user/0/com.autonavi.minimap/files/cache/offlinemap_lite_v3/offlineLiteConfig.db
[MAP DATA] open 路径: /data/user/0/com.autonavi.minimap/files/cache/offlinemap_lite_v3/offlineLiteConfig.db
[MAP DATA] open 路径: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[MAP DATA] open 路径: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
[MAP DATA] open 路径: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans

 */
(function() {
  console.log("[+] 高德地图离线数据分析脚本启动");
  
  // 反检测措施
  function bypassAntiDebug() {
    // 1. 拦截可能的反调试检测函数
    var antiDebugApis = [
      "strstr", "strcmp", "strlen", "readlink", 
      "stat", "kill", "fork", "ptrace"
    ];
    
    for (var i = 0; i < antiDebugApis.length; i++) {
      var api = antiDebugApis[i];
      var apiPtr = Module.findExportByName(null, api);
      if (apiPtr) {
        Interceptor.attach(apiPtr, {
          onEnter: function(args) {
            try {
              if (this.api === "strstr" || this.api === "strcmp") {
                var str = args[1].readUtf8String();
                if (str && (str.indexOf("frida") !== -1 || str.indexOf("gum-js") !== -1)) {
                  this.shouldModify = true;
                }
              } else if (this.api === "readlink" || this.api === "stat") {
                var path = args[0].readUtf8String();
                if (path && path.indexOf("proc") !== -1 && path.indexOf("maps") !== -1) {
                  this.shouldModify = true;
                }
              }
            } catch(e) {}
          }.bind({api: api}),
          onLeave: function(retval) {
            if (this.shouldModify) {
              retval.replace(0);
            }
          }
        });
      }
    }
    
    // 2. 修改进程名称检测
    try {
      var openptr = Module.findExportByName(null, "open");
      if (openptr) {
        Interceptor.attach(openptr, {
          onEnter: function(args) {
            try {
              var path = args[0].readUtf8String();
              if (path && path.indexOf("/proc/") !== -1) {
                this.path = path;
                this.shouldFake = path.indexOf("cmdline") !== -1 || path.indexOf("maps") !== -1;
              }
            } catch(e) {}
          },
          onLeave: function(retval) {
            if (this.shouldFake && retval.toInt32() !== -1) {
              console.log("[+] 拦截进程检测读取: " + this.path);
            }
          }
        });
      }
    } catch(e) {
      console.log("[-] 绕过进程检测失败: " + e);
    }
    
    console.log("[+] 反调试绕过措施已应用");
  }

  bypassAntiDebug();

  // 监控关键的离线数据加载函数
  function monitorOfflineLoading() {
    // 监控文件操作
    var fileOps = ["open", "fopen", "read", "pread", "mmap"];
    for (var i = 0; i < fileOps.length; i++) {
      var op = fileOps[i];
      var ptr = Module.findExportByName("libc.so", op);
      if (ptr) {
        Interceptor.attach(ptr, {
          onEnter: function(args) {
            try {
              if (this.op === "open" || this.op === "fopen") {
                var path = args[0].readUtf8String();
                if (path && (path.indexOf(".db") !== -1 || 
                             path.indexOf(".zip") !== -1 || 
                             path.indexOf(".ans") !== -1 || 
                             path.indexOf("OfflineDb") !== -1 ||
                             path.indexOf("roadDat") !== -1 || 
                             path.indexOf("vmap") !== -1)) {
                  // 添加简单缓存避免重复日志
                  var loggedPaths = {};
                  if (!loggedPaths[path]) {
                      console.log("[MAP DATA] " + this.op + " 路径: " + path);
                      loggedPaths[path] = true;
                  }
                  this.isMapData = true;
                  this.path = path;
                }
              }
            } catch(e) {}
          }.bind({op: op}),
          onLeave: function(retval) {
            if (this.isMapData && retval.toInt32() > 0) {
              console.log("[MAP DATA] " + this.op + " 成功, 返回: " + retval + ", 路径: " + this.path);
            }
          }.bind({op: op})
        });
      }
    }

    // 监控关键Native函数
    var monitorNativeModules = [
      "libamapnsq.so",
      "libgdamapv4sdk.so", 
      "libmapcore.so",
      "libmapsdkv8jni.so"
    ];

    var modules = Process.enumerateModules();
    for (var i = 0; i < modules.length; i++) {
      var module = modules[i];
      var shouldMonitor = false;
      
      for (var j = 0; j < monitorNativeModules.length; j++) {
        if (module.name.indexOf(monitorNativeModules[j]) !== -1) {
          shouldMonitor = true;
          break;
        }
      }
      
      if (shouldMonitor) {
        console.log("[+] 发现关键模块: " + module.name + ", 基址: " + module.base);
        
        // 尝试查找离线数据相关导出函数
        var keywords = ["loadOffline", "Map", "load", "init", "file", "read"];
        var exports = module.enumerateExports();
        
        for (var k = 0; k < exports.length; k++) {
          var exp = exports[k];
          var matchesKeyword = false;
          
          for (var l = 0; l < keywords.length; l++) {
            if (exp.name.toLowerCase().indexOf(keywords[l].toLowerCase()) !== -1) {
              matchesKeyword = true;
              break;
            }
          }
          
          if (matchesKeyword) {
            console.log("[EXPORT] " + module.name + " -> " + exp.name);
            
            // 尝试Hook关键函数
            try {
              Interceptor.attach(exp.address, {
                onEnter: function(args) {
                  console.log("[CALL] " + this.moduleName + "!" + this.funcName);
                  // 记录前几个参数，可能包含文件路径等信息
                  for (var i = 0; i < 3; i++) {
                    try {
                      var str = args[i].readUtf8String();
                      if (str) console.log("  参数" + i + ": " + str);
                    } catch(e) {}
                  }
                }.bind({moduleName: module.name, funcName: exp.name})
              });
            } catch(e) {}
          }
        }
      }
    }
  }

  monitorOfflineLoading();

  // 监控Java层关键类
  function monitorJavaClasses() {
    setTimeout(function() {
      Java.perform(function() {
        console.log("[+] 开始监控Java层离线地图加载...");

        // 关键类列表
        var targetClasses = [
          "com.autonavi.minimap.offline.nativesupport.AmapCompat",
          "com.autonavi.ae.gmap.AMapController",
          "com.autonavi.jni.vmap.dsl.VMapLocalService",
          "com.amap.bundle.mapstorage.MapSharePreference",
          "com.autonavi.ae.MapCloudBundleLoaderUtil",
          "com.amap.bundle.blutils.PathManager"
        ];

        for (var i = 0; i < targetClasses.length; i++) {
          var className = targetClasses[i];
          try {
            var clazz = Java.use(className);
            console.log("[JAVA] 加载类: " + className);
            
            // 获取所有方法
            var methods = clazz.class.getDeclaredMethods();
            for (var j = 0; j < methods.length; j++) {
              var method = methods[j];
              var methodName = method.getName();
              
              // 过滤可能与离线数据相关的方法
              if (methodName.toLowerCase().indexOf("offline") !== -1 ||
                  methodName.toLowerCase().indexOf("load") !== -1 ||
                  methodName.toLowerCase().indexOf("file") !== -1 ||
                  methodName.toLowerCase().indexOf("map") !== -1 ||
                  methodName.toLowerCase().indexOf("path") !== -1 ||
                  methodName.toLowerCase().indexOf("init") !== -1) {
                  
                console.log("[METHOD] " + className + "." + methodName);
                
                // 尝试Hook方法
                try {
                  // 创建Hook函数
                  var hookFunc = 'function() { ' +
                    'console.log("[CALL] ' + className + '.' + methodName + '(" + Array.prototype.slice.call(arguments) + ")"); ' +
                    'var result = this.' + methodName + '.apply(this, arguments); ' +
                    'console.log("[RETURN] ' + className + '.' + methodName + ' ->" + result); ' +
                    'return result; ' +
                  '}';
                  
                  var hookFunction = eval(hookFunc);
                  
                  // 应用Hook
                  var overloads = clazz[methodName].overloads;
                  for (var k = 0; k < overloads.length; k++) {
                    overloads[k].implementation = hookFunction;
                  }
                } catch(e) {
                  console.log("[-] Hook方法失败: " + className + "." + methodName + " - " + e);
                }
              }
            }
          } catch(e) {
            console.log("[-] 加载类失败: " + className + " - " + e);
          }
        }

        // 特别关注NewMapActivity中的离线数据加载
        try {
          var NewMapActivity = Java.use("com.autonavi.map.activity.NewMapActivity");
          
          // J方法处理地图资源和离线数据加载
          if (NewMapActivity.J) {
            NewMapActivity.J.implementation = function() {
              console.log("[+] 调用 NewMapActivity.J() - 地图初始化");
              var result = this.J();
              console.log("[+] NewMapActivity.J() 执行完成");
              return result;
            };
          }
          
          // loadMap相关方法
          if (NewMapActivity.loadMainMap) {
            NewMapActivity.loadMainMap.implementation = function(z) {
              console.log("[+] 调用 loadMainMap(" + z + ")");
              var result = this.loadMainMap(z);
              console.log("[+] loadMainMap 执行完成");
              return result;
            };
          }
        } catch(e) {
          console.log("[-] Hook NewMapActivity失败: " + e);
        }

        // 追踪PathManager初始化 - 重要的路径管理类
        try {
          var PathManager = Java.use("com.amap.bundle.blutils.PathManager");
          PathManager.init.overload('android.content.Context').implementation = function(ctx) {
            console.log("[+] 调用 PathManager.init()");
            var result = this.init(ctx);
            
            // 获取路径信息
            try {
              console.log("[PATH] getOfflineDBDirPath: " + this.getOfflineDBDirPath());
              console.log("[PATH] getOfflineMapStorageDirPath: " + this.getOfflineMapStorageDirPath());
              console.log("[PATH] getBaseDirPath: " + this.getBaseDirPath());
            } catch(e) {}
            
            return result;
          };
        } catch(e) {
          console.log("[-] Hook PathManager失败: " + e);
        }

        // 监控ANS文件相关操作
        try {
          // 查找可能处理ANS文件的类
          Java.enumerateLoadedClasses({
            onMatch: function(className) {
              if (className.toLowerCase().indexOf("ans") !== -1 || 
                  className.toLowerCase().indexOf("offline") !== -1) {
                console.log("[CLASS] 可能与ANS文件相关: " + className);
              }
            },
            onComplete: function() {}
          });
        } catch(e) {
          console.log("[-] 枚举ANS相关类失败: " + e);
        }
      });
    }, 1000);
  }

  monitorJavaClasses();

  // 特别监控mmap和文件操作中的ANS文件
  var mmap_ptr = Module.findExportByName("libc.so", "mmap");
  if (mmap_ptr) {
    Interceptor.attach(mmap_ptr, {
      onEnter: function(args) {
        this.size = args[1].toInt32();
        this.fd = args[4].toInt32();
      },
      onLeave: function(retval) {
        if (this.fd > 0) {
          // 尝试获取文件名
          try {
            var fdPath = "/proc/self/fd/" + this.fd;
            var filePath = new File(fdPath).readlink();
            // 只针对m1.ans文件分析结构
            if (filePath && filePath.indexOf("m1.ans") !== -1) {
                console.log("[ANS分析] 映射: " + filePath);
                console.log(hexdump(retval, {length: 64, header: true}));
            }
          } catch(e) {}
        }
      }
    });
  }
  
  console.log("[+] 高德地图离线数据分析脚本设置完成");
})(); 