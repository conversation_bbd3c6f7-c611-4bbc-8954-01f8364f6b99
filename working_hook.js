/*
 * 高德地图解压后数据Hook脚本
 * 基于successful脚本结构，专门捕获解压后数据
 * 版本: Frida 12.9.7 兼容
 */

console.log("[解压数据Hook] 启动解压后数据捕获脚本...");

var dataFound = 0;
var maxData = 5;
var isReady = false;

// 立即设置Hook - 完全照搬successful脚本
function setupImmediateHooks() {
    console.log("[Immediate] 立即设置Hook...");
    
    try {
        // Hook 1: 文件读取 - 基础数据获取
        var readPtr = Module.findExportByName("libc.so", "read");
        if (readPtr) {
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    this.buffer = args[1];
                    this.size = args[2].toInt32();
                    this.isTarget = (this.size > 500 && this.size < 20000);
                },
                onLeave: function(retval) {
                    if (this.isTarget && dataFound < maxData && isReady) {
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 0) {
                            console.log("[文件读取] 捕获数据: " + bytesRead + " 字节");
                            
                            try {
                                var preview = this.buffer.readByteArray(Math.min(64, bytesRead));
                                var bytes = new Uint8Array(preview);
                                var text = "";
                                
                                for (var i = 0; i < Math.min(32, bytes.length); i++) {
                                    if (bytes[i] >= 32 && bytes[i] < 127) {
                                        text += String.fromCharCode(bytes[i]);
                                    }
                                }
                                
                                console.log("[预览] " + text.substring(0, 20));
                                
                                if (text.indexOf('DICE') >= 0 || 
                                    text.indexOf('<?xml') >= 0 || 
                                    text.indexOf('{"') >= 0) {
                                    console.log(" [原始数据] 发现目标数据格式!");
                                    dataFound++;
                                }
                            } catch (e) {
                                // 静默处理
                            }
                        }
                    }
                }
            });
            console.log(" [Immediate] read() Hook设置成功");
        }

        // Hook 2: zlib解压 - 这是关键的解压后数据Hook!
        setTimeout(function() {
            hookZlibDecompression();
        }, 100);

    } catch (e) {
        console.log(" [Immediate] Hook设置失败: " + e.message);
    }
    
    console.log("[Immediate] 基础Hook设置完成");
}

// 🎯 重点：Hook zlib解压获取解压后数据
function hookZlibDecompression() {
    console.log("[解压Hook] 设置zlib解压监控...");
    
    try {
        // 找到libz.so模块
        var zlibModule = Process.findModuleByName("libz.so");
        if (!zlibModule) {
            console.log(" [解压Hook] libz.so未找到");
            return;
        }
        
        // Hook uncompress函数
        var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
        if (uncompressPtr) {
            console.log(" [解压Hook] 找到uncompress函数: " + uncompressPtr);
            
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    // 保存参数
                    this.destBuffer = args[0];      // 解压后数据缓冲区
                    this.destLenPtr = args[1];      // 解压后数据长度指针
                    this.sourceBuffer = args[2];    // 压缩数据缓冲区
                    this.sourceLen = args[3].toInt32(); // 压缩数据长度
                    
                    console.log("[解压前] 压缩数据: " + this.sourceLen + " 字节");
                },
                onLeave: function(retval) {
                    // 检查解压是否成功 (返回值0表示成功)
                    if (retval.toInt32() === 0 && dataFound < maxData) {
                        try {
                            // 读取解压后的数据长度
                            var decompressedLen = this.destLenPtr.readU32();
                            console.log("\n [解压成功] 解压后数据: " + decompressedLen + " 字节");
                            
                            // 这就是你要的解压后数据！
                            if (decompressedLen > 0 && decompressedLen < 50000) {
                                var decompressedData = this.destBuffer.readByteArray(Math.min(decompressedLen, 2000));
                                
                                console.log(" [解压后数据分析] 开始...");
                                analyzeDecompressedData(decompressedData, dataFound);
                                dataFound++;
                            }
                            
                        } catch (e) {
                            console.log(" [解压Hook] 读取解压数据失败: " + e.message);
                        }
                    }
                }
            });
            
            console.log(" [解压Hook] uncompress Hook设置成功");
        } else {
            console.log(" [解压Hook] 未找到uncompress函数");
        }
        
    } catch (e) {
        console.log(" [解压Hook] 设置失败: " + e.message);
    }
}

// 🎯 分析解压后数据 - 这就是你要看的解压后数据内容！
function analyzeDecompressedData(data, index) {
    console.log("\n" + "=".repeat(60));
    console.log(" 解压后数据分析 #" + index + " - 这就是解压解析的数据！");
    console.log("=".repeat(60));
    
    try {
        var bytes = new Uint8Array(data);
        
        // 1. 显示数据基本信息
        console.log(" 数据大小: " + bytes.length + " 字节");
        
        // 2. 显示头部信息
        var header = "";
        for (var i = 0; i < Math.min(32, bytes.length); i++) {
            if (bytes[i] >= 32 && bytes[i] < 127) {
                header += String.fromCharCode(bytes[i]);
            } else {
                header += ".";
            }
        }
        console.log(" 数据头部: " + header);
        
        // 3. 十六进制显示
        var hexData = "";
        for (var i = 0; i < Math.min(64, bytes.length); i++) {
            hexData += bytes[i].toString(16).padStart(2, '0') + " ";
            if ((i + 1) % 16 === 0) hexData += "\n";
        }
        console.log(" 十六进制数据:\n" + hexData);
        
        // 4. 识别数据类型
        if (header.indexOf("DICE-AM") >= 0) {
            console.log(" 数据类型: DICE-AM矢量地图数据");
            console.log(" 内容: 经纬度坐标、道路轮廓、建筑边界");
            parseDiceAMData(bytes);
            
        } else if (header.indexOf("{") >= 0 || header.indexOf("res_list") >= 0) {
            console.log(" 数据类型: JSON配置数据");
            console.log(" 内容: 地图样式、颜色设置、图标配置");
            parseJSONData(bytes);
            
        } else if (hasChineseText(bytes)) {
            console.log(" 数据类型: 中文文本数据");
            console.log(" 内容: 地名、道路名、POI名称");
            parseChineseText(bytes);
            
        } else {
            console.log(" 数据类型: 其他二进制数据");
            console.log(" 可能包含: 纹理、音频、其他格式数据");
        }
        
        console.log("\n 重要说明:");
        console.log("• 这是APP解压后的原始数据，还需要进一步结构化处理");
        console.log("• 下一步APP会调用sub_5C394和sub_10F88进行结构化");
        console.log("• 结构化后才能给GPU渲染使用");
        console.log("=".repeat(60));
        
    } catch (e) {
        console.log(" 解压后数据分析失败: " + e.message);
    }
}

// 解析DICE-AM数据
function parseDiceAMData(bytes) {
    try {
        console.log("DICE-AM结构解析:");
        if (bytes.length >= 16) {
            var view = new DataView(bytes.buffer);
            var version = bytes[7];
            var dataLen = view.getUint32(8, true);
            var pointCount = view.getUint32(12, true);
            
            console.log("   版本: " + version);
            console.log("   数据长度: " + dataLen);
            console.log("   点数量: " + pointCount);
            
            // 显示第一个坐标点
            if (bytes.length >= 24) {
                var x = view.getFloat32(16, true);
                var y = view.getFloat32(20, true);
                console.log("   第一个坐标: (" + x.toFixed(6) + ", " + y.toFixed(6) + ")");
                console.log("    这些是经纬度坐标，需要转换为屏幕坐标");
            }
        }
    } catch (e) {
        console.log("    DICE-AM解析失败");
    }
}

// 解析JSON数据
function parseJSONData(bytes) {
    try {
        console.log("🔍 JSON数据解析:");
        var text = "";
        for (var i = 0; i < Math.min(500, bytes.length); i++) {
            if (bytes[i] >= 32 && bytes[i] < 127) {
                text += String.fromCharCode(bytes[i]);
            } else if (bytes[i] === 0) {
                break;
            }
        }
        
        console.log("   内容预览: " + text.substring(0, 150) + "...");
        console.log("   这些配置控制地图的外观和渲染参数");
    } catch (e) {
        console.log("   JSON解析失败");
    }
}

// 解析中文文本
function parseChineseText(bytes) {
    try {
        console.log(" 中文文本解析:");
        var text = new TextDecoder('utf-8').decode(bytes);
        var chineseChars = [];
        
        for (var i = 0; i < text.length && chineseChars.length < 20; i++) {
            if (text.charCodeAt(i) >= 0x4e00 && text.charCodeAt(i) <= 0x9fff) {
                chineseChars.push(text[i]);
            }
        }
        
        if (chineseChars.length > 0) {
            console.log("   中文内容: " + chineseChars.join(''));
            console.log("   这些是地图上显示的中文标注");
        }
    } catch (e) {
        console.log("  中文文本解析失败");
    }
}

// 检查是否包含中文
function hasChineseText(bytes) {
    try {
        var text = new TextDecoder('utf-8').decode(bytes);
        for (var i = 0; i < Math.min(text.length, 100); i++) {
            if (text.charCodeAt(i) >= 0x4e00 && text.charCodeAt(i) <= 0x9fff) {
                return true;
            }
        }
    } catch (e) {
        // 忽略解码错误
    }
    return false;
}

// 设置库特定Hook
function setupLibraryHooks() {
    console.log("[Library Hooks] 设置库特定Hook...");
    
    setTimeout(function() {
        try {
            var libamapnsq = Process.findModuleByName("libamapnsq.so");
            if (libamapnsq) {
                console.log("[Library] 找到libamapnsq.so: " + libamapnsq.base);
                
                // Hook SQLite数据绑定
                var sqlitePtr = libamapnsq.base.add(0x15000);
                Interceptor.attach(sqlitePtr, {
                    onEnter: function(args) {
                        this.dataPtr = args[2];
                        this.dataSize = args[3].toInt32();
                    },
                    onLeave: function(retval) {
                        if (this.dataSize > 100 && this.dataSize < 10000 && dataFound < maxData) {
                            console.log("[SQLite] 数据绑定: " + this.dataSize + " 字节");
                        }
                    }
                });
                
                console.log("[Library] SQLite Hook设置成功");
            }
        } catch (e) {
            console.log("[Library] Hook设置失败: " + e.message);
        }
    }, 1000);
}

// 主程序启动
setupImmediateHooks();
setupLibraryHooks();

// 延迟启动数据捕获
setTimeout(function() {
    isReady = true;
    console.log("[解压数据Hook] 脚本就绪，开始监控解压后数据...");
    console.log("请在地图中移动以触发数据解压和分析");
}, 2000);

console.log("[解压数据Hook] 脚本加载完成，等待激活..."); 