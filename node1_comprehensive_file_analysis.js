/*
 * 节点1: 离线文件读取流程深度分析器
 * 目标: 完整分析.ans文件从磁盘到内存的数据流程
 * 方法: IDA Pro静态分析 + Frida动态追踪
 * 作者: 高级Frida程序员 + IDA Pro分析师
 */

console.log("=== 节点1: 离线文件读取流程深度分析器 ===");
console.log("目标: 追踪.ans文件的完整读取、映射和解析流程");

// 分析状态管理
var node1Analysis = {
    // 文件操作追踪
    fileOperations: {
        opens: [],           // open()调用记录
        reads: [],           // read()调用记录  
        mmaps: [],           // mmap()调用记录
        seeks: [],           // lseek()调用记录
        closes: []           // close()调用记录
    },
    
    // .ans文件特定分析
    ansFileAnalysis: {
        detectedFiles: [],   // 检测到的.ans文件
        headerParsing: [],   // 头部解析记录
        indexParsing: [],    // 索引解析记录
        magicNumbers: [],    // 魔数验证记录
        fileDescriptors: {}  // 文件描述符映射
    },
    
    // 内存映射分析
    memoryMapping: {
        mappedRegions: [],   // 映射区域记录
        accessPatterns: [],  // 访问模式记录
        protections: []      // 内存保护设置
    },
    
    // 调用栈分析
    callStackAnalysis: {
        fileReadStacks: [],  // 文件读取调用栈
        headerParseStacks: [], // 头部解析调用栈
        indexParseStacks: [] // 索引解析调用栈
    },
    
    // 统计信息
    statistics: {
        totalFileOps: 0,
        ansFileCount: 0,
        totalBytesRead: 0,
        startTime: Date.now()
    }
};

// 安全的字节数组处理函数
function safeByteArrayToHex(byteArray, maxLen) {
    if (!byteArray || byteArray.length === 0) return "";
    
    var hexBytes = [];
    var len = Math.min(maxLen || 32, byteArray.length);
    
    for (var i = 0; i < len; i++) {
        var byte = byteArray[i];
        if (typeof byte === 'number') {
            hexBytes.push(('0' + byte.toString(16)).slice(-2));
        }
    }
    return hexBytes.join(' ');
}

// 检查是否是.ans文件
function isAnsFile(filename) {
    if (!filename || typeof filename !== 'string') return false;
    return filename.toLowerCase().indexOf('.ans') !== -1;
}

// 检查AM-zlib魔数
function checkAMZlibMagic(data) {
    if (!data || data.length < 8) return false;
    
    // AM-zlib\x00 魔数检查
    return data[0] === 0x41 && data[1] === 0x4d && data[2] === 0x2d && 
           data[3] === 0x7a && data[4] === 0x6c && data[5] === 0x69 && 
           data[6] === 0x62 && data[7] === 0x00;
}

// 安全的调用栈获取
function getSafeCallStack() {
    try {
        var stack = Thread.backtrace(this.context, Backtracer.ACCURATE);
        var stackInfo = [];
        
        for (var i = 0; i < Math.min(8, stack.length); i++) {
            var frame = stack[i];
            var symbol = DebugSymbol.fromAddress(frame);
            
            stackInfo.push({
                address: frame.toString(),
                symbol: symbol.name || "unknown",
                module: symbol.moduleName || "unknown",
                offset: symbol.address ? frame.sub(symbol.address).toString() : "0x0"
            });
        }
        return stackInfo;
    } catch (e) {
        return [{ error: "Failed to get call stack: " + e.message }];
    }
}

// 获取文件路径（从文件描述符）
function getFilePathFromFd(fd) {
    try {
        var path = "/proc/self/fd/" + fd;
        // 这里可以尝试读取符号链接来获取实际文件路径
        return "fd:" + fd;
    } catch (e) {
        return "unknown";
    }
}

console.log("初始化完成，开始Hook关键函数...");

// 1. Hook libc.so 的文件操作函数
try {
    var libc = Process.getModuleByName("libc.so");
    console.log("[✓] libc.so 模块已找到");
    
    // Hook open() - 文件打开
    var openPtr = libc.getExportByName("open");
    if (openPtr) {
        Interceptor.attach(openPtr, {
            onEnter: function(args) {
                this.filename = args[0].readCString();
                this.flags = args[1].toInt32();
                this.mode = args[2] ? args[2].toInt32() : 0;
                
                if (isAnsFile(this.filename)) {
                    console.log("[文件打开] .ans文件:", this.filename);
                    this.isAnsFile = true;
                    this.callStack = getSafeCallStack.call(this);
                }
            },
            onLeave: function(retval) {
                var fd = retval.toInt32();
                
                if (this.isAnsFile && fd > 0) {
                    console.log("[文件打开成功] FD:", fd, "文件:", this.filename);
                    
                    // 记录文件操作
                    node1Analysis.fileOperations.opens.push({
                        timestamp: Date.now(),
                        filename: this.filename,
                        fd: fd,
                        flags: this.flags,
                        mode: this.mode,
                        callStack: this.callStack
                    });
                    
                    // 记录.ans文件
                    node1Analysis.ansFileAnalysis.detectedFiles.push({
                        filename: this.filename,
                        fd: fd,
                        openTime: Date.now()
                    });
                    
                    node1Analysis.ansFileAnalysis.fileDescriptors[fd] = this.filename;
                    node1Analysis.statistics.ansFileCount++;
                }
                
                node1Analysis.statistics.totalFileOps++;
            }
        });
        console.log("[✓] open() Hook 已设置");
    }
    
} catch (e) {
    console.log("[✗] libc.so Hook 设置失败:", e.message);
}

    // Hook read() - 文件读取
    var readPtr = libc.getExportByName("read");
    if (readPtr) {
        Interceptor.attach(readPtr, {
            onEnter: function(args) {
                this.fd = args[0].toInt32();
                this.buf = args[1];
                this.count = args[2].toInt32();

                // 检查是否是.ans文件的读取
                if (node1Analysis.ansFileAnalysis.fileDescriptors[this.fd]) {
                    this.isAnsRead = true;
                    this.filename = node1Analysis.ansFileAnalysis.fileDescriptors[this.fd];
                    console.log("[文件读取] .ans文件:", this.filename, "FD:", this.fd, "请求字节:", this.count);
                }
            },
            onLeave: function(retval) {
                var bytesRead = retval.toInt32();

                if (this.isAnsRead && bytesRead > 0) {
                    console.log("[文件读取成功] 实际读取:", bytesRead, "字节");

                    try {
                        // 读取数据进行分析
                        var data = this.buf.readByteArray(Math.min(64, bytesRead));
                        if (data) {
                            var hexData = safeByteArrayToHex(new Uint8Array(data), 32);
                            console.log("[数据预览]", hexData);

                            // 检查是否是AM-zlib头部
                            if (checkAMZlibMagic(new Uint8Array(data))) {
                                console.log("[魔数检测] 发现AM-zlib头部!");

                                node1Analysis.ansFileAnalysis.magicNumbers.push({
                                    timestamp: Date.now(),
                                    filename: this.filename,
                                    fd: this.fd,
                                    offset: 0, // 假设是文件开头
                                    magicType: "AM-zlib"
                                });
                            }

                            // 记录读取操作
                            node1Analysis.fileOperations.reads.push({
                                timestamp: Date.now(),
                                filename: this.filename,
                                fd: this.fd,
                                bytesRequested: this.count,
                                bytesRead: bytesRead,
                                dataPreview: hexData,
                                callStack: getSafeCallStack.call(this)
                            });
                        }
                    } catch (e) {
                        console.log("[读取分析错误]", e.message);
                    }

                    node1Analysis.statistics.totalBytesRead += bytesRead;
                }
            }
        });
        console.log("[✓] read() Hook 已设置");
    }

    // Hook mmap() - 内存映射
    var mmapPtr = libc.getExportByName("mmap");
    if (mmapPtr) {
        Interceptor.attach(mmapPtr, {
            onEnter: function(args) {
                this.addr = args[0];
                this.length = args[1].toInt32();
                this.prot = args[2].toInt32();
                this.flags = args[3].toInt32();
                this.fd = args[4].toInt32();
                this.offset = args[5].toInt32();

                // 检查是否是.ans文件的映射
                if (node1Analysis.ansFileAnalysis.fileDescriptors[this.fd]) {
                    this.isAnsMap = true;
                    this.filename = node1Analysis.ansFileAnalysis.fileDescriptors[this.fd];
                    console.log("[内存映射] .ans文件:", this.filename, "长度:", this.length, "偏移:", this.offset);
                }
            },
            onLeave: function(retval) {
                if (this.isAnsMap && !retval.equals(ptr(-1))) {
                    console.log("[内存映射成功] 地址:", retval, "长度:", this.length);

                    // 记录内存映射
                    node1Analysis.memoryMapping.mappedRegions.push({
                        timestamp: Date.now(),
                        filename: this.filename,
                        fd: this.fd,
                        address: retval.toString(),
                        length: this.length,
                        protection: this.prot,
                        flags: this.flags,
                        offset: this.offset,
                        callStack: getSafeCallStack.call(this)
                    });

                    // 尝试分析映射的数据
                    try {
                        var mappedData = retval.readByteArray(Math.min(64, this.length));
                        if (mappedData) {
                            var hexData = safeByteArrayToHex(new Uint8Array(mappedData), 32);
                            console.log("[映射数据预览]", hexData);

                            // 检查魔数
                            if (checkAMZlibMagic(new Uint8Array(mappedData))) {
                                console.log("[映射魔数检测] 发现AM-zlib头部!");
                            }
                        }
                    } catch (e) {
                        console.log("[映射数据分析错误]", e.message);
                    }
                }
            }
        });
        console.log("[✓] mmap() Hook 已设置");
    }

    // Hook close() - 文件关闭
    var closePtr = libc.getExportByName("close");
    if (closePtr) {
        Interceptor.attach(closePtr, {
            onEnter: function(args) {
                this.fd = args[0].toInt32();

                if (node1Analysis.ansFileAnalysis.fileDescriptors[this.fd]) {
                    this.isAnsClose = true;
                    this.filename = node1Analysis.ansFileAnalysis.fileDescriptors[this.fd];
                    console.log("[文件关闭] .ans文件:", this.filename, "FD:", this.fd);
                }
            },
            onLeave: function(retval) {
                if (this.isAnsClose) {
                    // 记录关闭操作
                    node1Analysis.fileOperations.closes.push({
                        timestamp: Date.now(),
                        filename: this.filename,
                        fd: this.fd,
                        result: retval.toInt32()
                    });

                    // 清理文件描述符映射
                    delete node1Analysis.ansFileAnalysis.fileDescriptors[this.fd];
                }
            }
        });
        console.log("[✓] close() Hook 已设置");
    }

} catch (e) {
    console.log("[✗] libc.so Hook 设置失败:", e.message);
}

// 2. Hook libamapnsq.so 的特定函数（如果可用）
try {
    var libamapnsq = Process.getModuleByName("libamapnsq.so");
    console.log("[✓] libamapnsq.so 模块已找到");

    // 尝试Hook已知的文件处理函数
    // 基于之前的分析，这些可能是关键函数
    var knownOffsets = [
        { name: "sub_5C394", offset: 0x5C394 },  // 数据分发函数
        { name: "sub_10F88", offset: 0x10F88 }   // zlib解压函数
    ];

    knownOffsets.forEach(function(func) {
        try {
            var funcAddr = libamapnsq.base.add(func.offset);
            Interceptor.attach(funcAddr, {
                onEnter: function(args) {
                    console.log("[" + func.name + "] 函数调用 - 参数:", args[0], args[1], args[2]);
                    this.callStack = getSafeCallStack.call(this);
                },
                onLeave: function(retval) {
                    console.log("[" + func.name + "] 函数返回:", retval);

                    // 记录调用栈
                    if (func.name === "sub_5C394") {
                        node1Analysis.callStackAnalysis.headerParseStacks.push({
                            timestamp: Date.now(),
                            function: func.name,
                            callStack: this.callStack
                        });
                    }
                }
            });
            console.log("[✓] " + func.name + " Hook 已设置");
        } catch (e) {
            console.log("[✗] " + func.name + " Hook 设置失败:", e.message);
        }
    });

} catch (e) {
    console.log("[✗] libamapnsq.so 未找到或Hook失败:", e.message);
}

// 3. 定期报告分析结果
function generateNode1Report() {
    console.log("\n=== 节点1分析报告 ===");
    console.log("运行时间:", (Date.now() - node1Analysis.statistics.startTime) / 1000, "秒");
    console.log("总文件操作:", node1Analysis.statistics.totalFileOps);
    console.log("检测到的.ans文件:", node1Analysis.statistics.ansFileCount);
    console.log("总读取字节数:", node1Analysis.statistics.totalBytesRead);

    console.log("\n--- 文件操作统计 ---");
    console.log("文件打开:", node1Analysis.fileOperations.opens.length);
    console.log("文件读取:", node1Analysis.fileOperations.reads.length);
    console.log("内存映射:", node1Analysis.memoryMapping.mappedRegions.length);
    console.log("文件关闭:", node1Analysis.fileOperations.closes.length);

    console.log("\n--- .ans文件分析 ---");
    console.log("检测到的文件:");
    node1Analysis.ansFileAnalysis.detectedFiles.forEach(function(file, index) {
        console.log("  " + (index + 1) + ". " + file.filename + " (FD: " + file.fd + ")");
    });

    console.log("\n--- 魔数检测 ---");
    console.log("AM-zlib魔数检测:", node1Analysis.ansFileAnalysis.magicNumbers.length, "次");

    if (node1Analysis.memoryMapping.mappedRegions.length > 0) {
        console.log("\n--- 内存映射详情 ---");
        node1Analysis.memoryMapping.mappedRegions.forEach(function(region, index) {
            console.log("  映射" + (index + 1) + ":", region.filename);
            console.log("    地址:", region.address, "长度:", region.length);
            console.log("    保护:", region.protection, "偏移:", region.offset);
        });
    }

    console.log("=== 节点1分析报告结束 ===\n");
}

// 4. 数据导出功能
function exportNode1Data() {
    var exportData = {
        metadata: {
            analysisType: "Node1_FileReading",
            timestamp: new Date().toISOString(),
            duration: Date.now() - node1Analysis.statistics.startTime,
            version: "1.0"
        },
        statistics: node1Analysis.statistics,
        fileOperations: node1Analysis.fileOperations,
        ansFileAnalysis: node1Analysis.ansFileAnalysis,
        memoryMapping: node1Analysis.memoryMapping,
        callStackAnalysis: node1Analysis.callStackAnalysis
    };

    console.log("\n=== 节点1数据导出 ===");
    console.log(JSON.stringify(exportData, null, 2));
    console.log("=== 导出结束 ===\n");

    return exportData;
}

// 设置定期报告
setInterval(generateNode1Report, 30000); // 每30秒报告一次

console.log("节点1分析器初始化完成，等待.ans文件操作...");
console.log("提示: 请在高德地图中进行操作以触发离线数据读取");
console.log("分析器将自动捕获并分析所有.ans文件的读取流程");

// 导出全局函数供外部调用
global.generateNode1Report = generateNode1Report;
global.exportNode1Data = exportNode1Data;
global.node1Analysis = node1Analysis;
