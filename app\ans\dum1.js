// 高德地图GPS模拟器积分破解脚本
// 功能：修改积分值或绕过积分检查
Java.perform(function() {
    console.log("\n[+] 积分破解脚本已启动...");

    // 1. 定位用户模型类，这里很可能包含积分信息
    try {
        var GpsUserModel = Java.use("tgo.ngo.mockgps.model.app.GpsUserModel");
        
        console.log("[+] 已找到用户模型类: GpsUserModel");
        
        // 列出所有可能与积分相关的方法
        var methods = GpsUserModel.class.getDeclaredMethods();
        console.log("[+] 可能与积分相关的方法:");
        
        methods.forEach(function(method) {
            var name = method.getName();
            if (name.includes("Point") || name.includes("point") || 
                name.includes("Score") || name.includes("score") ||
                name.includes("Credit") || name.includes("credit") ||
                name.includes("get") || name.includes("Check") || 
                name.includes("check")) {
                console.log("    - " + name);
                
                // 钩住所有可能返回积分的getter方法
                if (name.startsWith("get")) {
                    try {
                        GpsUserModel[name].implementation = function() {
                            var original = this[name]();
                            console.log("[*] 调用了积分相关方法: " + name + "()，原始返回值: " + original);
                            
                            // 如果返回类型是数字，可能是积分值，修改为大数字
                            if (typeof original === "number") {
                                console.log("[!] 已修改积分值: " + original + " -> 99999");
                                return 99999;
                            }
                            return original;
                        };
                    } catch(e) {
                        // 某些方法可能无法钩住，忽略异常
                    }
                }
            }
        });
    } catch(e) {
        console.log("[!] 无法找到用户模型类: " + e);
    }
    
    // 2. 钩住主界面的startMock方法
    try {
        var MainActivity = Java.use("tgo.ngo.mockgps.ui.MainActivity");
        console.log("[+] 已找到主界面类: MainActivity");
        
        // 钩住启动模拟定位的方法
        if (MainActivity.startMock) {
            MainActivity.startMock.implementation = function() {
                console.log("[*] 拦截到启动模拟请求");
                
                // 直接调用原始方法，绕过可能的积分检查
                console.log("[!] 正在绕过积分检查并启动模拟...");
                this.startMock();
                
                return;
            };
        }
        
        // 钩住可能的积分检查对话框
        // 从方法列表来看，有很多lambda方法处理对话框事件，可能与积分不足提示有关
        var dialogMethods = [
            "startMock$lambda-12", 
            "startMock$lambda-13", 
            "startMock$lambda-14",
            "startMock$lambda-15"
        ];
        
        dialogMethods.forEach(function(method) {
            try {
                if (MainActivity[method]) {
                    MainActivity[method].implementation = function() {
                        console.log("[*] 拦截到对话框处理方法: " + method);
                        console.log("[!] 正在尝试绕过对话框检查...");
                        // 这里我们不调用原始方法，而是尝试直接调用实际执行的方法
                        try {
                            this.access$startMock(this);
                            console.log("[+] 成功调用模拟启动方法");
                        } catch(e) {
                            console.log("[!] 调用启动方法失败: " + e);
                        }
                        return;
                    };
                }
            } catch(e) {
                // 忽略不存在的方法
            }
        });
        
        // 3. 尝试钩住静态方法 access$startMock
        try {
            MainActivity["access$startMock"].implementation = function(thisObj) {
                console.log("[*] 拦截到 access$startMock 方法");
                console.log("[+] 正在执行启动逻辑，绕过积分检查");
                this.access$startMock(thisObj);
                return;
            };
        } catch(e) {
            console.log("[!] 钩住 access$startMock 失败: " + e);
        }
        
    } catch(e) {
        console.log("[!] 无法钩住主界面类: " + e);
    }
    
    // 4. 通用方法，寻找所有包含"积分"或"点数"等关键词的字符串常量
    try {
        Java.choose("java.lang.String", {
            onMatch: function(instance) {
                var value = instance.toString();
                if (value.includes("积分") || value.includes("点数") || 
                    value.includes("不足") || value.includes("兑换")) {
                    console.log("[+] 发现可能的积分相关字符串: " + value);
                }
            },
            onComplete: function() {}
        });
    } catch(e) {
        console.log("[!] 搜索字符串失败: " + e);
    }

    console.log("[*] 积分破解脚本加载完成，请操作应用尝试使用功能");
});
