#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高德地图数据十六进制查看器
显示真实的人类可读数据内容
"""

def hex_viewer(filename, offset=0, length=512):
    """十六进制查看器"""
    print(f"[查看] 文件: {filename}")
    print(f"[查看] 偏移: 0x{offset:08X} ({offset})")
    print(f"[查看] 长度: {length} 字节")
    print("=" * 80)
    
    try:
        with open(filename, 'rb') as f:
            f.seek(offset)
            data = f.read(length)
        
        # 十六进制和ASCII显示
        for i in range(0, len(data), 16):
            # 地址
            addr = offset + i
            hex_part = f"{addr:08X}  "
            
            # 十六进制部分
            hex_bytes = []
            ascii_part = ""
            
            for j in range(16):
                if i + j < len(data):
                    byte = data[i + j]
                    hex_bytes.append(f"{byte:02X}")
                    
                    # ASCII部分
                    if 32 <= byte < 127:
                        ascii_part += chr(byte)
                    else:
                        ascii_part += "."
                else:
                    hex_bytes.append("  ")
                    ascii_part += " "
            
            # 格式化输出
            hex_str = " ".join(hex_bytes[:8]) + "  " + " ".join(hex_bytes[8:])
            print(f"{hex_part}{hex_str}  |{ascii_part}|")
        
        print("=" * 80)
        
        # 尝试提取可读文本
        readable_text = ""
        for byte in data:
            if 32 <= byte < 127:
                readable_text += chr(byte)
            elif byte in [10, 13]:
                readable_text += "\n"
            elif byte == 9:
                readable_text += "\t"
        
        if readable_text.strip():
            print("\n📄 可读文本内容:")
            print("-" * 40)
            print(readable_text[:500])
            print("-" * 40)
        
        # 检查是否包含特定模式
        patterns = {
            b'DICE-AM': '地图矢量数据头',
            b'<?xml': 'XML配置',
            b'{"': 'JSON数据',
            b'\xe4\xb8\xad': '中文字符(中)',
            b'\xe5\x9c\xb0': '中文字符(地)',
            b'\xe5\x9b\xbe': '中文字符(图)',
        }
        
        found_patterns = []
        for pattern, desc in patterns.items():
            if pattern in data:
                pos = data.find(pattern)
                found_patterns.append(f"{desc} (偏移 +{pos})")
        
        if found_patterns:
            print(f"\n🎯 发现的数据模式:")
            for pattern in found_patterns:
                print(f"  - {pattern}")
    
    except Exception as e:
        print(f"[错误] {e}")

def show_dice_am_structure():
    """显示DICE-AM数据结构"""
    print("\n🎯 DICE-AM 数据结构分析")
    print("=" * 50)
    
    # 查看DICE-AM头部
    hex_viewer("gb_v6.ans", 0, 256)
    
    print("\n" + "=" * 50)
    print("🔍 DICE-AM 头部字段解析:")
    print("  0x00-0x06: 'DICE-AM' (文件标识)")
    print("  0x07-0x0A: 版本信息")
    print("  0x0B-0x0E: 数据块计数")
    print("  0x0F-0x12: 索引表偏移")
    print("=" * 50)

def show_json_data():
    """显示JSON数据"""
    print("\n📋 JSON 数据分析")
    print("=" * 50)
    
    # JSON数据在偏移 0x0009F225
    hex_viewer("gb_v6.ans", 0x0009F225, 512)

def show_chinese_text():
    """显示中文文本数据"""
    print("\n🈲 中文文本数据分析")
    print("=" * 50)
    
    # 中文数据在偏移 0x00030C29
    hex_viewer("gb_v6.ans", 0x00030C29, 512)

if __name__ == "__main__":
    print("🎯 高德地图真实数据完整展示")
    print("基于实际提取的.ans文件分析")
    print("=" * 80)
    
    # 显示DICE-AM结构
    show_dice_am_structure()
    
    # 显示JSON数据
    show_json_data()
    
    # 显示中文文本
    show_chinese_text()
    
    print("\n✅ 完整的人类可读数据已展示！")
    print("这些是从高德地图运行时实际使用的真实数据！") 