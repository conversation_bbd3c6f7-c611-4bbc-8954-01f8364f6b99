     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[IDA Structure Analyzer] IDA Pro数据结构分析器启动...
[Init] 初始化IDA结构分析器...
[IDA Structure Analyzer] 脚本加载完成
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [Module] 扫描加载的模块...
[Module] libz.so 找到:
   - 基址: 0x7f8991e000
[Module] libamapnsq.so 找到:
   - 基址: 0x7f6687e000
   - 大小: 0xbd000
   - 路径: /data/app/com.autonavi.minimap-1/lib/arm64/libamapnsq.so
[Module] libamapr.so 找到:
   - 基址: 0x7f5e405000
   - 大小: 0x1e7d000
   - 路径: /data/app/com.autonavi.minimap-1/lib/arm64/libamapr.so
[Verify] 验证IDA Pro分析的函数地址...
[Verify] nativeAddMapGestureMsg (0x6ee70c):
   - 地址: 0x7f5eaf370c
   - 字节:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  c0 03 5f d6 08 a4 40 a9 1f 01 09 eb 42 00 00 54  .._...@.....B..T
[Verify] getMapEngineInstance (0x6fb98c):
   - 地址: 0x7f5eb0098c
   - 字节:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  a2 51 00 90 42 84 13 91                          .Q..B...
[Verify] processGestureMessage (0x6fb530):
   - 地址: 0x7f5eb00530
   - 字节:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  84 dc 2d 91 a5 a8 26 91                          ..-...&.
[Verify] triggerRenderUpdate (0x6fbc78):
   - 地址: 0x7f5eb00c78
   - 字节:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  08 69 4c f9 15 05 40 f9 a8 02 40 f9 e0 03 15 aa  .iL...@...@.....
[Verify] updateMapView (0x6fb9e0):
   - 地址: 0x7f5eb009e0
   - 字节:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  f6 09 00 94 e2 27 40 f9                          .....'@.
[Verify] finalizeProcessing (0x6fb550):
   - 地址: 0x7f5eb00550
   - 字节:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  a1 51 00 90 21 b8 2d 91 49 0b 00 94              .Q..!.-.I...
[Verify] sub_C654 (0xc654):
   - 地址: 0x7f6688a654
   - 前16字节:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ff 03 01 d1 f6 57 01 a9 f4 4f 02 a9 fd 7b 03 a9  .....W...O...{..
[Verify] sub_5C394 (0x5c394):
   - 地址: 0x7f668da394
   - 前16字节:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  e8 03 1e aa b5 8b 00 94 fe 03 08 aa fd 7b 0e a9  .............{..
[Verify] sub_10F88 (0x10f88):
   - 地址: 0x7f6688ef88
   - 前16字节:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  e2 03 1e aa b4 b6 01 94 fe 03 02 aa fd 7b 07 a9  .............{..
[Hook] 设置结构分析Hook...
[Hook] 结构分析Hook设置完成
[Init] IDA结构分析器初始化完成！
请在应用中触发地图操作以开始数据收集...
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f5cab3f48
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x5d844a08
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f5cab3f48
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x5d844a08
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f5cab3f48
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x5d844a08
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f5cab3f48
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x5d844a08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f5cab3f48
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x5d844a08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c078fe8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3208
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f5ba86838
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x5dec8208
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f5ba86838
   - args[1]: 0x0
   - args[2]: 0x4005
   - args[0] 可读，前4字节: 0x5dec8208
   - args[2] 不可读或为空: Error: access violation accessing 0x4005
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f5ba86838
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x5dec8208
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 返回: 0x0
[Hook] sub_5C394 调用，参数分析:
   - args[0]: 0x7f5a785f08
   - args[1]: 0x7f59af65e8
   - args[0] 指向: 0x7f5dec8208
   - 指针数据预览:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  08 d4 a1 87 7f 00 00 00 88 4b 79 5a 7f 00 00 00  .........KyZ....
00000010  88 61 af 59 7f 00 00 00 88 ac ed 5d 7f 00 00 00  .a.Y.......]....
00000020  b8 84 ec 5d 7f 00 00 00 02 00 00 00 50 00 90 00  ...]........P...
00000030  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
   - 指针数据不是UTF8字符串
   - args[1] 作为大小: 1504667112
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f5ba86838
   - args[1]: 0x1
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x5dec8208
   - args[1] 不可读或为空: Error: access violation accessing 0x1
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f5ba86838
   - args[1]: 0x0
   - args[2]: 0x7f5822a088
   - args[0] 可读，前4字节: 0x5dec8208
   - args[2] 可读，前4字节: 0x73206f6e
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7e5b8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x57042e08
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f55f36128
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x5dec7788
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7e5b8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x57042e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f55f36128
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x5dec7788
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f55f36128
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x5dec7788
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7e5b8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x57042e08
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f55f36128
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x5dec7788
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f55f36128
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x5dec7788
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7e5b8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x57042e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7e5b8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x57042e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7e5b8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x57042e08
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7e5b8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x57042e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7e5b8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x57042e08
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7e5b8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x57042e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7e5b8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x57042e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a0e08
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7d208
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x572a1c08
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7d208
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x572a1c08
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7d208
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a1c08
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7d208
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a1c08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7d208
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a1c08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f54230018
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x54334e08
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f54230018
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x54334e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f54230018
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x54334e08
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f54230018
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x54334e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f54230018
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x54334e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7c498
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x572a1c08
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7c498
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x572a1c08
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7c498
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a1c08
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7c498
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a1c08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7c498
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a1c08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7dac8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x572a1188
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7dac8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x572a1188
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7dac8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a1188
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7dac8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a1188
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7dac8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a1188
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4bdb9238
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x5807ec88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4bdb9238
   - args[1]: 0x0
   - args[2]: 0x4005
   - args[0] 可读，前4字节: 0x5807ec88
   - args[2] 不可读或为空: Error: access violation accessing 0x4005
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4bdb9238
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x5807ec88
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4a5ddde8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x4a67ad88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4a5ddde8
   - args[1]: 0x0
   - args[2]: 0x4005
   - args[0] 可读，前4字节: 0x4a67ad88
   - args[2] 不可读或为空: Error: access violation accessing 0x4005
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4a5ddde8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x4a67ad88
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f492b77a8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x4a67b108
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f492b77a8
   - args[1]: 0x0
   - args[2]: 0x4005
   - args[0] 可读，前4字节: 0x4a67b108
   - args[2] 不可读或为空: Error: access violation accessing 0x4005
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f492b77a8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x4a67b108
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c079268
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3588
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885808
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885808
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885808
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885808
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885b88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885b88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885b88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47adf478
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f7c078fe8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x877a3208
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4f54cc48
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x4df6ef88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4f54cc48
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x4df6ef88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4f54cc48
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x4df6ef88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4f54cc48
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x4df6ef88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4f54cc48
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x4df6ef88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47ed9ed8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47ed9ed8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47ed9ed8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47ed9ed8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x47885b88
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4005
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4005
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_5C394 调用，参数分析:
   - args[0]: 0x7f44a51408
   - args[1]: 0x7f44a61408
   - args[0] 指向: 0x7f44a42e08
   - 指针数据预览:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  08 d4 a1 87 7f 00 00 00 c8 3c 20 48 7f 00 00 00  .........< H....
00000010  08 00 a6 44 7f 00 00 00 c8 ce 97 50 7f 00 00 00  ...D.......P....
00000020  b8 30 a4 44 7f 00 00 00 02 00 00 00 50 00 90 00  .0.D........P...
00000030  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
   - 指针数据不是UTF8字符串
   - args[1] 作为大小: 1151734792
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x1
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[1] 不可读或为空: Error: access violation accessing 0x1
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_5C394 调用，参数分析:
   - args[0]: 0x7f44a51408
   - args[1]: 0x7f44a61548
   - args[0] 指向: 0x7f44a42e08
   - 指针数据预览:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  08 d4 a1 87 7f 00 00 00 c8 3c 20 48 7f 00 00 00  .........< H....
00000010  08 00 a6 44 7f 00 00 00 c8 ce 97 50 7f 00 00 00  ...D.......P....
00000020  b8 30 a4 44 7f 00 00 00 02 00 00 00 50 00 90 00  .0.D........P...
00000030  21 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00  !...............
   - 指针数据不是UTF8字符串
   - args[1] 作为大小: 1151735112
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x1
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[1] 不可读或为空: Error: access violation accessing 0x1
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_5C394 调用，参数分析:
   - args[0]: 0x7f44a51408
   - args[1]: 0x7f44a61548
   - args[0] 指向: 0x7f44a42e08
   - 指针数据预览:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  08 d4 a1 87 7f 00 00 00 c8 3c 20 48 7f 00 00 00  .........< H....
00000010  08 00 a6 44 7f 00 00 00 c8 ce 97 50 7f 00 00 00  ...D.......P....
00000020  b8 30 a4 44 7f 00 00 00 02 00 00 00 50 00 90 00  .0.D........P...
00000030  22 04 00 00 00 00 00 00 00 00 00 00 00 00 00 00  "...............
   - 指针数据不是UTF8字符串
   - args[1] 作为大小: 1151735112
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x1
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[1] 不可读或为空: Error: access violation accessing 0x1
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f45aa6aa8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x44a42e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f6513a0c8
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f65150268
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x3f02db88
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f38d4b588
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x56f12188
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f38d4b588
   - args[1]: 0x0
   - args[2]: 0x4005
   - args[0] 可读，前4字节: 0x56f12188
   - args[2] 不可读或为空: Error: access violation accessing 0x4005
[Hook] sub_10F88 返回: 0x0
[Hook] sub_5C394 调用，参数分析:
   - args[0]: 0x7f4d4ac308
   - args[1]: 0x7f3c3cc9e8
   - args[0] 指向: 0x7f56f12188
   - 指针数据预览:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  08 d4 a1 87 7f 00 00 00 88 22 89 3a 7f 00 00 00  .........".:....
00000010  c8 37 5c 4d 7f 00 00 00 88 f6 80 30 7f 00 00 00  .7\M.......0....
00000020  38 24 f1 56 7f 00 00 00 02 00 00 00 50 00 90 00  8$.V........P...
00000030  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
   - 指针数据不是UTF8字符串
   - args[1] 作为大小: 1010616808
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f38d4b588
   - args[1]: 0x1
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x56f12188
   - args[1] 不可读或为空: Error: access violation accessing 0x1
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47ed93e8
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x5807bf08
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f38d4b588
   - args[1]: 0x0
   - args[2]: 0x4005
   - args[0] 可读，前4字节: 0x56f12188
   - args[2] 不可读或为空: Error: access violation accessing 0x4005
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47ed93e8
   - args[1]: 0x0
   - args[2]: 0x4005
   - args[0] 可读，前4字节: 0x5807bf08
   - args[2] 不可读或为空: Error: access violation accessing 0x4005
[Hook] sub_10F88 返回: 0x0
[Hook] sub_5C394 调用，参数分析:
   - args[0]: 0x7f47915a08
   - args[1]: 0x7f3bf6d3a8
   - args[0] 指向: 0x7f5807bf08
   - 指针数据预览:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  08 d4 a1 87 7f 00 00 00 08 80 0e 49 7f 00 00 00  ...........I....
00000010  a8 5a dc 42 7f 00 00 00 d8 6e 79 30 7f 00 00 00  .Z.B.....ny0....
00000020  b8 c1 07 58 7f 00 00 00 02 00 00 00 50 00 90 00  ...X........P...
00000030  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
   - 指针数据不是UTF8字符串
   - args[1] 作为大小: 1006031784
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47ed93e8
   - args[1]: 0x1
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x5807bf08
   - args[1] 不可读或为空: Error: access violation accessing 0x1
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f47ed93e8
   - args[1]: 0x0
   - args[2]: 0x4005
   - args[0] 可读，前4字节: 0x5807bf08
   - args[2] 不可读或为空: Error: access violation accessing 0x4005
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4f548c38
   - args[1]: 0x0
   - args[2]: 0xffffffff
   - args[0] 可读，前4字节: 0x66ecb988
   - args[2] 不可读或为空: Error: access violation accessing 0xffffffff
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4f548c38
   - args[1]: 0x0
   - args[2]: 0x4009
   - args[0] 可读，前4字节: 0x66ecb988
   - args[2] 不可读或为空: Error: access violation accessing 0x4009
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4f548c38
   - args[1]: 0x2
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x66ecb988
   - args[1] 不可读或为空: Error: access violation accessing 0x2
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4f548c38
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x66ecb988
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f4f548c38
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x66ecb988
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0

======= 数据结构分析报告 =======
DICE-AM数据块数量: 0
解析的数据块总数: 0
函数调用次数: 246
=====================================

[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f56f7cfd8
   - args[1]: 0x0
   - args[2]: 0x4001
   - args[0] 可读，前4字节: 0x572a0e08
   - args[2] 不可读或为空: Error: access violation accessing 0x4001
[Hook] sub_10F88 返回: 0x0
[Hook] sub_10F88 调用，参数分析:
   - args[0]: 0x7f440b2a18
   - args[1]: 0x0
   - args[2]: 0x4000
   - args[0] 可读，前4字节: 0x420df488
   - args[2] 不可读或为空: Error: access violation accessing 0x4000
[Hook] sub_10F88 返回: 0x0
