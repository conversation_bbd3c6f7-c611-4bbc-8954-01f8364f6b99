     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
[解压数据Hook] 启动解压后数据捕获脚本...
[Immediate] 立即设置Hook...
 [Immediate] read() Hook设置成功
[Immediate] 基础Hook设置完成
[Library Hooks] 设置库特定Hook...
[解压数据Hook] 脚本加载完成，等待激活...
[Remote::com.autonavi.minimap]-> [解压Hook] 设置zlib解压监控...
 [解压Hook] 找到uncompress函数: 0x7f8037967c
 [解压Hook] uncompress Hook设置成功
[Library] 找到libamapnsq.so: 0x7f5ef03000
[Library] SQLite Hook设置成功
[解压数据Hook] 脚本就绪，开始监控解压后数据...
请在地图中移动以触发数据解压和分析
[文件读取] 捕获数据: 1024 字节
[预览] ize:           4 kBL
[文件读取] 捕获数据: 4096 字节
[预览] q,6(T-"xd,'%!dw/#
[文件读取] 捕获数据: 4096 字节
[预览] 4___tr4onnx::Transpo
[文件读取] 捕获数据: 4096 字节
[预览] s/81Y,Hf)1X-
[文件读取] 捕获数据: 4096 字节
[预览] *&Q0X0-(&0N*T
[文件读取] 捕获数据: 4096 字节
[预览] ./x/m/2.B[-0.
[文件读取] 捕获数据: 4096 字节
[预览] +^Y62:P),,k./
[文件读取] 捕获数据: 4096 字节
[预览] +_0n:"!_V.D,a/j
[文件读取] 捕获数据: 4096 字节
[预览] *0sl,Au..,9,
[文件读取] 捕获数据: 1024 字节
[预览] 00000 00:00 0       
[文件读取] 捕获数据: 4096 字节
[预览] (p,01R0C0/
[文件读取] 捕获数据: 4096 字节
[预览] v'*%.W11-2
[文件读取] 捕获数据: 1024 字节
[预览] ize:               1
[文件读取] 捕获数据: 17711 字节
[预览] PK!8,@CDME
[文件读取] 捕获数据: 4096 字节
[预览] #h2M:,0L3
[文件读取] 捕获数据: 4096 字节
[预览] q0O0f+vH4.:n,
[文件读取] 捕获数据: 1284 字节
[预览] <?xml version='1.0' 
 [原始数据] 发现目标数据格式!
[文件读取] 捕获数据: 4096 字节
[预览] )b%,2,6*1!$)^e,W+
[文件读取] 捕获数据: 4096 字节
[预览] #-F0)$)M$Ri
[文件读取] 捕获数据: 4096 字节
[预览] ')\T"1" )
[文件读取] 捕获数据: 1024 字节
[预览]         128 kBRss:  
[文件读取] 捕获数据: 4096 字节
[预览] gb(p.e%)8$,N%g
[文件读取] 捕获数据: 4096 字节
[预览] a0D!?.]% qA,(
[文件读取] 捕获数据: 1024 字节
[预览]         8 kBShared_C
[文件读取] 捕获数据: 4096 字节
[预览] 0-+$*+(9+.,)
[文件读取] 捕获数据: 1024 字节
[预览] vate_Dirty:         
[文件读取] 捕获数据: 4096 字节
[预览] m,Q*Dm+`*T2(Z
[文件读取] 捕获数据: 4096 字节
[预览] Q!?&y*2J+W#-Sz):,
[文件读取] 捕获数据: 1024 字节
[预览] mous:             0 
[文件读取] 捕获数据: 1024 字节
[预览]           0 kBKernel
[文件读取] 捕获数据: 1015 字节
[预览] <?xml version='1.0' 
 [原始数据] 发现目标数据格式!
[文件读取] 捕获数据: 4096 字节
[预览] -)&0e%+5)-{U4+z*
[文件读取] 捕获数据: 1024 字节
[预览] kBKernelPageSize:   
[文件读取] 捕获数据: 4096 字节
[预览] -,z+<10(!@*pF
[文件读取] 捕获数据: 1024 字节
[预览] w me nr 7f3a973000-7
[文件读取] 捕获数据: 9952 字节
[预览] PK#|IqCDME
[文件读取] 捕获数据: 1024 字节
[预览] 75654               
[文件读取] 捕获数据: 4096 字节
[预览] /&*x(^$5)D+!
[文件读取] 捕获数据: 1024 字节
[预览] 54                  
[文件读取] 捕获数据: 4096 字节
[预览] ,7&(-ho"OO'!
[文件读取] 捕获数据: 1024 字节
[预览]   4 kBLocked:       
[文件读取] 捕获数据: 4096 字节
[预览] I%($(V%Eq)*S
[文件读取] 捕获数据: 1024 字节
[预览] 0:00 0 Size:        
[文件读取] 捕获数据: 4096 字节
[预览] wo*).i).Z-z(
[文件读取] 捕获数据: 4096 字节
[预览] 2@"RI&C,
[文件读取] 捕获数据: 4096 字节
[预览] 0Ss,v[L-.
[文件读取] 捕获数据: 1024 字节
[预览] e:           [anon:l
[文件读取] 捕获数据: 4096 字节
[预览] )X`[.-/))0l
[文件读取] 捕获数据: 1024 字节
[预览] :00 0 Size:         
[文件读取] 捕获数据: 4096 字节
[预览] iK$&3)_,.##.(m1d&
[文件读取] 捕获数据: 4096 字节
[预览] (<=/Pb-.x=&*,
[文件读取] 捕获数据: 4096 字节
[预览] w*,7$($+K(('B'+l,
[文件读取] 捕获数据: 4096 字节
[预览]  K{#_/b"@u`$Y.
[文件读取] 捕获数据: 4096 字节
[预览] *%)"h,0 
[文件读取] 捕获数据: 4096 字节
[预览] r'k!{Uf('!$q)Y((
[文件读取] 捕获数据: 4096 字节
[预览] h'`'.,:()%
[文件读取] 捕获数据: 1024 字节
[预览] ss:                 
[文件读取] 捕获数据: 4096 字节
[预览] -),O@!T,<+!uA+l
[文件读取] 捕获数据: 1024 字节
[预览]       0 kBPrivate_Di
[文件读取] 捕获数据: 4096 字节
[预览] M'$P) u)n'7 =!|,*T#\
[文件读取] 捕获数据: 1024 字节
[预览]   0 kBPrivate_Clean:
[文件读取] 捕获数据: 1024 字节
[预览] 4 kBReferenced:     
[文件读取] 捕获数据: 4096 字节
[预览] k-.%(Q'=">M/,&(+f
[文件读取] 捕获数据: 1024 字节
[预览] rty:          0 kBPr
[文件读取] 捕获数据: 3939 字节
[预览] PK'|IqCDME
[文件读取] 捕获数据: 4096 字节
[预览] 1,Snd7( j =*(9"j*/
[文件读取] 捕获数据: 1024 字节
[预览] ty:         4 kBRefe
[文件读取] 捕获数据: 4096 字节
[预览] p./1'n*h',1+/*Q/
[文件读取] 捕获数据: 1024 字节
[预览] 6 kBReferenced:     
[文件读取] 捕获数据: 4096 字节
[预览] ,t2,V",c&.(!$>(u1>
[文件读取] 捕获数据: 1024 字节
[预览] ced:           12 kB
[文件读取] 捕获数据: 1024 字节
[预览] irty:         0 kBRe
[文件读取] 捕获数据: 4096 字节
[预览] %]%-&%)/*$M.s-
[文件读取] 捕获数据: 4096 字节
[预览]  (#O,,*\^~xw'J
[文件读取] 捕获数据: 1024 字节
[预览]              12 kBPs
[文件读取] 捕获数据: 4096 字节
[预览] #|+,$h)x)a-`+,*
[文件读取] 捕获数据: 1024 字节
[预览]     0 kBPrivate_Clea
[文件读取] 捕获数据: 4096 字节
[预览] A% T,);-//0Hb)$
[文件读取] 捕获数据: 1024 字节
[预览] Referenced:         
[文件读取] 捕获数据: 4096 字节
[预览] C$$8=(8-1J(8[,.0)y
[文件读取] 捕获数据: 1024 字节
[预览]           0 kBPrivat
[文件读取] 捕获数据: 4096 字节
[预览] X,j)2(,^!(t&'+.
[文件读取] 捕获数据: 1024 字节
[预览] nonymous:           
[文件读取] 捕获数据: 4096 字节
[预览] %)/ +$)&>)i#{
[文件读取] 捕获数据: 1024 字节
[预览] enced:         1192 
[文件读取] 捕获数据: 4096 字节
[预览] 40j0Ky,2-~$t.A.+)
[文件读取] 捕获数据: 1024 字节
[预览]         0 kBKernelPa
[文件读取] 捕获数据: 1024 字节
[预览] 4 kBLocked:         
[文件读取] 捕获数据: 4096 字节
[预览] :(,>/(''+/ ! 
[文件读取] 捕获数据: 4096 字节
[预览] P$(v*&N0OT-(*"T6*
[文件读取] 捕获数据: 1024 字节
[预览] r 7f3bd80000-7f3bfc0
[文件读取] 捕获数据: 4096 字节
[预览] ',#&*-
[文件读取] 捕获数据: 1024 字节
[预览] :           4 kBLock
[文件读取] 捕获数据: 4096 字节
[预览] !P3iAtg(3
[文件读取] 捕获数据: 338 字节
[预览] <?xml version='1.0' 
 [原始数据] 发现目标数据格式!
[文件读取] 捕获数据: 12 字节
[预览] 401
[文件读取] 捕获数据: 4096 字节
[预览] '-y*zr!!*p
[文件读取] 捕获数据: 1024 字节
[预览] 00000000 00:00 0 Siz
[文件读取] 捕获数据: 4096 字节
[预览] aO[&U-)-]M.!/}
[文件读取] 捕获数据: 1024 字节
[预览] J1Cu0L6JVJv/fZpCsdv2
[文件读取] 捕获数据: 4096 字节
[预览] BL@H
[文件读取] 捕获数据: 234 字节
[预览] <?xml version='1.0' 
 [原始数据] 发现目标数据格式!
[文件读取] 捕获数据: 4096 字节
[预览] b3.2-E408a2+A.p
[文件读取] 捕获数据: 1024 字节
[预览]            0 kBPss: 
[文件读取] 捕获数据: 1024 字节
[预览] biKw1GsSMGy0A+17E+Id
[文件读取] 捕获数据: 4096 字节
[预览] K`2x4"&
[文件读取] 捕获数据: 1024 字节
[预览]     1792 kBRss:     
[文件读取] 捕获数据: 4096 字节
[预览] n$W![ #@$i$
[文件读取] 捕获数据: 4096 字节
[预览] z( %0d# 
[文件读取] 捕获数据: 4096 字节
[预览] J  $"(& o R
[文件读取] 捕获数据: 4096 字节
[预览] B$ X<%
[文件读取] 捕获数据: 4096 字节
[预览] Y`!!07- U
[文件读取] 捕获数据: 4096 字节
[预览] +$#$$!!>$?"W!{%
[文件读取] 捕获数据: 1024 字节
[预览] BShared_Dirty:      
[文件读取] 捕获数据: 1024 字节
[预览] MBdJRGksqt5geSGgPgcV
[文件读取] 捕获数据: 1024 字节
[预览] +9/NNJqEkHQFpULfBXiX
[文件读取] 捕获数据: 1024 字节
[预览] 0a11GncGDJfLy+j5Ks1y
[文件读取] 捕获数据: 1024 字节
[预览] Ho8Nw45PKN2Qui8l1d1w
[文件读取] 捕获数据: 1024 字节
[预览] zec07kg9Nx3q4J1JzvpG
[文件读取] 捕获数据: 1024 字节
[预览] gBtmH6lfp/J0nwIlzaNR
[文件读取] 捕获数据: 1024 字节
[预览] 6JI5glyTFN0dry6MTGjX
[文件读取] 捕获数据: 37 字节
[预览] crG5XGm9VjVjJC5bWOao
[文件读取] 捕获数据: 1024 字节
[预览] e_Dirty:         8 k
[文件读取] 捕获数据: 1024 字节
[预览]     12 kBReferenced:
[文件读取] 捕获数据: 1024 字节
[预览]    4 kBAnonHugePages
[文件读取] 捕获数据: 1024 字节
[预览] e_Dirty:       660 k
[文件读取] 捕获数据: 1024 字节
[预览]        0 kBSwap:    
[文件读取] 捕获数据: 1024 字节
[预览]  kBMMUPageSize:     
[文件读取] 捕获数据: 4356 字节
[预览] D-MM,1970-01-06 04:2
[文件读取] 捕获数据: 1024 字节
[预览] ize:           4 kBL
[文件读取] 捕获数据: 1024 字节
[预览] 00000 00:00 0       
[文件读取] 捕获数据: 1024 字节
[预览]         0 kBPss:    
[文件读取] 捕获数据: 1024 字节
[预览]       0 kBShared_Dir
[文件读取] 捕获数据: 1024 字节
[预览] kBPrivate_Dirty:    
[文件读取] 捕获数据: 836 字节
[预览] Name:utonavi.minimap
[文件读取] 捕获数据: 4096 字节
[预览] %uD%#Sj@& N
[文件读取] 捕获数据: 1024 字节
[预览] Anonymous:          
[文件读取] 捕获数据: 1024 字节
[预览]            68 kBAnon
[文件读取] 捕获数据: 4096 字节
[预览] #$'#^& $
[文件读取] 捕获数据: 19782 字节
[预览] PK!8UEN)or
[文件读取] 捕获数据: 4096 字节
[预览] o5%Y5%a$
[文件读取] 捕获数据: 1024 字节
[预览]             0 kBAnon
[文件读取] 捕获数据: 748 字节
[预览] Processor: AArch64 P
[文件读取] 捕获数据: 1024 字节
[预览] _Clean:         0 kB
[文件读取] 捕获数据: 4096 字节
[预览] p6RN-d8K3/9b-6l,0
[文件读取] 捕获数据: 4096 字节
[预览] *HE%r!,#
[文件读取] 捕获数据: 4096 字节
[预览] h!)$&P'9*"$)&e*`)%*"
[文件读取] 捕获数据: 4096 字节
[预览] H!#G$$P&"q&"
[文件读取] 捕获数据: 4096 字节
[预览] V  7V#X%#<d
[文件读取] 捕获数据: 4096 字节
[预览] $YN5$ P`!o q
[文件读取] 捕获数据: 4096 字节
[预览] 6 G#FV"WEw!
[文件读取] 捕获数据: 4096 字节
[预览] $8*$$j'N
[文件读取] 捕获数据: 4096 字节
[预览] "VVb#
[文件读取] 捕获数据: 1024 字节
[预览] 12 kBPss:           
[文件读取] 捕获数据: 165 字节
[预览] 00000000000000000000
[文件读取] 捕获数据: 3 字节
[预览] 22
[文件读取] 捕获数据: 4096 字节
[预览] %$F$~67F+&o$"tVw
[文件读取] 捕获数据: 3 字节
[预览] 21
[文件读取] 捕获数据: 1024 字节
[预览] ate_Clean:         0
[文件读取] 捕获数据: 3 字节
[预览] 20
[文件读取] 捕获数据: 4096 字节
[预览] )i &%VJ!%$"
[文件读取] 捕获数据: 3 字节
[预览] 19
[文件读取] 捕获数据: 1024 字节
[预览]           20 kBAnony
[文件读取] 捕获数据: 1024 字节
[预览] BPrivate_Clean:     
[文件读取] 捕获数据: 1024 字节
[预览] s:             0 kBA
[文件读取] 捕获数据: 3 字节
[预览] 18
[文件读取] 捕获数据: 3 字节
[预览] 17
[文件读取] 捕获数据: 1024 字节
[预览]              0 kBKer
[文件读取] 捕获数据: 4096 字节
[预览] '07K"F4at41
[文件读取] 捕获数据: 1024 字节
[预览]      4 kBLocked:    
[文件读取] 捕获数据: 3 字节
[预览] 16
[文件读取] 捕获数据: 4096 字节
[预览] O !kR%sAL$3!
[文件读取] 捕获数据: 1024 字节
[预览] cked:               
[文件读取] 捕获数据: 4096 字节
[预览] 22010R100`1100201d1;
[文件读取] 捕获数据: 10574 字节
[预览] PK!8,@CDME
[文件读取] 捕获数据: 4096 字节
[预览] E\ L$ S'>^!
[文件读取] 捕获数据: 1024 字节
[预览]                     
[文件读取] 捕获数据: 3 字节
[预览] 15
[文件读取] 捕获数据: 4096 字节
[预览] v"#&%=%z(
[文件读取] 捕获数据: 10574 字节
[预览] PK!8,@CDME
[文件读取] 捕获数据: 1024 字节
[预览] rw-p 00000000 00:00 
[文件读取] 捕获数据: 4096 字节
[预览] "#L">z] T"h_
[文件读取] 捕获数据: 1024 字节
[预览] 0 0 Size:           
[文件读取] 捕获数据: 4096 字节
[预览] "%!7 %XD%%> 
[文件读取] 捕获数据: 3 字节
[预览] 14
[文件读取] 捕获数据: 1024 字节
[预览]           [anon:libc
[文件读取] 捕获数据: 3 字节
[预览] 10
[文件读取] 捕获数据: 2 字节
[预览] 9
[文件读取] 捕获数据: 2 字节
[预览] 8
[文件读取] 捕获数据: 2 字节
[预览] 7
[文件读取] 捕获数据: 2 字节
[预览] 6
[文件读取] 捕获数据: 3 字节
[预览] 13
[文件读取] 捕获数据: 3 字节
[预览] 12
[文件读取] 捕获数据: 3 字节
[预览] 11
[文件读取] 捕获数据: 2 字节
[预览] 2
[文件读取] 捕获数据: 3 字节
[预览] 27
[文件读取] 捕获数据: 2 字节
[预览] 5
[文件读取] 捕获数据: 2 字节
[预览] 1
[文件读取] 捕获数据: 2 字节
[预览] 4
[文件读取] 捕获数据: 3 字节
[预览] 28
[文件读取] 捕获数据: 4096 字节
[预览] ?^!b+!r(!#XP%N
[文件读取] 捕获数据: 2 字节
[预览] 3
[文件读取] 捕获数据: 1024 字节
[预览] 0000000 00:00 0 Size
[文件读取] 捕获数据: 4096 字节
[预览] !>$xoZ' %!
[文件读取] 捕获数据: 4096 字节
[预览] ;x}P&$ 9_O&
[文件读取] 捕获数据: 4096 字节
[预览] !U?& 80~?$BX$
[文件读取] 捕获数据: 4096 字节
[预览] >)*55$H94[6}&:
[文件读取] 捕获数据: 4096 字节
[预览] 'N$zc$e$:"#&Y!
[文件读取] 捕获数据: 4096 字节
[预览] s$tF(mlaFTZx
[文件读取] 捕获数据: 17711 字节
[预览] PK!8,@CDME
[文件读取] 捕获数据: 1024 字节
[预览] 0:00 0 Size:        
[文件读取] 捕获数据: 4096 字节
[预览] ^8#{%E:#)l$Z7
[文件读取] 捕获数据: 1024 字节
[预览]                   /d
[文件读取] 捕获数据: 1024 字节
[预览]  4 kBRss:           
[文件读取] 捕获数据: 4096 字节
[预览] 9j %$@$##e @!
[文件读取] 捕获数据: 4096 字节
[预览] R${C}$4$"
[文件读取] 捕获数据: 4096 字节
[预览] 3#&-!1!"o$h 
[文件读取] 捕获数据: 4096 字节
[预览] 'dL0!d&'l:
[文件读取] 捕获数据: 4096 字节
[预览] d"1"hWa"!"
[文件读取] 捕获数据: 4096 字节
[预览] p!!xG$")H'
[文件读取] 捕获数据: 4096 字节
[预览] >  %!!$:_Rq%
[文件读取] 捕获数据: 4096 字节
[预览] 4)8(1@:4Hrpr$08
[文件读取] 捕获数据: 4096 字节
[预览]  5&27%8!
[文件读取] 捕获数据: 4096 字节
[预览] 44H4w4444434/44~4$4{
[文件读取] 捕获数据: 4096 字节
[预览] %"?J)1'_!'c $
[文件读取] 捕获数据: 4096 字节
[预览] Y"e6$#n#U
[文件读取] 捕获数据: 334 字节
[预览] <?xml version='1.0' 
 [原始数据] 发现目标数据格式!
[解压前] 压缩数据: 3121 字节
[解压前] 压缩数据: 2149 字节
[解压前] 压缩数据: 2232 字节
[解压前] 压缩数据: 3349 字节
[解压前] 压缩数据: 2262 字节
[解压前] 压缩数据: 2091 字节
[解压前] 压缩数据: 3505 字节
[解压前] 压缩数据: 2618 字节
[解压前] 压缩数据: 2568 字节
[解压前] 压缩数据: 3505 字节
[解压前] 压缩数据: 2740 字节
[解压前] 压缩数据: 2591 字节
[解压前] 压缩数据: 3505 字节
[解压前] 压缩数据: 2618 字节
[解压前] 压缩数据: 2568 字节

[Remote::com.autonavi.minimap]-> exit
