/*
 * 高德地图数据提取器 - 简化版
 * 专注于数据捕获，避免复杂语法
 * 版本: Frida 12.9.7 兼容
 */

console.log("[Data Extractor] 启动数据提取器...");

var extractCount = 0;
var maxExtracts = 3;
var fileReadCount = 0;

function extractMapData(dataPtr, size, source) {
    if (extractCount >= maxExtracts) return;
    
    try {
        console.log("[Extract] " + source + " - 大小: " + size);
        
        var previewSize = Math.min(64, size);
        var data = dataPtr.readByteArray(previewSize);
        var bytes = new Uint8Array(data);
        
        var hexStr = "";
        var charStr = "";
        
        for (var i = 0; i < Math.min(16, bytes.length); i++) {
            var hex = bytes[i].toString(16);
            if (hex.length === 1) hex = "0" + hex;
            hexStr += hex + " ";
            
            if (bytes[i] >= 32 && bytes[i] < 127) {
                charStr += String.fromCharCode(bytes[i]);
            } else {
                charStr += ".";
            }
        }
        
        console.log("[Data] Hex: " + hexStr);
        console.log("[Data] Char: " + charStr);
        
        // 检查地图数据标识
        if (charStr.indexOf("ANS") >= 0 || charStr.indexOf("DICE") >= 0 || 
            charStr.indexOf(".!9") >= 0 || charStr.indexOf(".C.") >= 0 || bytes[0] === 8) {
            
            extractCount++;
            console.log("[MAP DATA] 发现地图数据! 类型: " + 
                       (charStr.indexOf("ANS") >= 0 ? "ANS" : 
                        charStr.indexOf("DICE") >= 0 ? "DICE" : 
                        bytes[0] === 8 ? "Compressed" : "Vector"));
            
            console.log("[HexDump] 完整数据预览:");
            console.log(hexdump(data, {length: previewSize, ansi: false}));
        }
        
    } catch (e) {
        console.log("[Extract Error] " + e.message);
    }
}

function isSystemFile(charStr) {
    // 过滤系统文件，只关注地图文件
    return charStr.indexOf("VmFlags") >= 0 || 
           charStr.indexOf("kB.") >= 0 ||
           charStr.indexOf("/proc/") >= 0 ||
           charStr.indexOf("/dev/") >= 0 ||
           charStr.indexOf("Referenced") >= 0 ||
           charStr.indexOf("Anonymous") >= 0 ||
           charStr.indexOf("Shared_") >= 0 ||
           charStr.indexOf("Private_") >= 0;
}

function setupDataHooks() {
    setTimeout(function() {
        console.log("[Setup] 开始设置数据Hook...");
        
        try {
            var lib = Module.findBaseAddress("libamapnsq.so");
            if (!lib) {
                console.log("[Error] 未找到libamapnsq.so");
                return;
            }
            
            console.log("[Library] 库基址: " + lib);
            
            // Hook文件读取 - 修复：添加文件过滤
            var readPtr = Module.findExportByName("libc.so", "read");
            if (readPtr) {
                Interceptor.attach(readPtr, {
                    onEnter: function(args) {
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                        // 修改：只关注可能的地图文件大小
                        this.isMapFile = (this.size > 5000 && this.size < 1024*1024);
                    },
                    onLeave: function(retval) {
                        if (!this.isMapFile) return;
                        
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 0 && extractCount < maxExtracts) {
                            fileReadCount++;
                            
                            // 先预览内容判断是否为系统文件
                            try {
                                var previewData = this.buffer.readByteArray(Math.min(32, bytesRead));
                                var previewBytes = new Uint8Array(previewData);
                                var previewStr = "";
                                
                                for (var i = 0; i < Math.min(16, previewBytes.length); i++) {
                                    if (previewBytes[i] >= 32 && previewBytes[i] < 127) {
                                        previewStr += String.fromCharCode(previewBytes[i]);
                                    }
                                }
                                
                                // 过滤系统文件
                                if (!isSystemFile(previewStr)) {
                                    console.log("[File Read #" + fileReadCount + "] 非系统文件，大小: " + bytesRead);
                                    extractMapData(this.buffer, bytesRead, "FileRead");
                                } else if (fileReadCount <= 2) {
                                    console.log("[File Read #" + fileReadCount + "] 跳过系统文件");
                                }
                            } catch (e) {
                                console.log("[File Preview Error] " + e.message);
                            }
                        }
                    }
                });
                console.log("[Hook] 文件读取Hook设置成功");
            }
            
            // 修复SQLite Hook - 尝试不同的地址
            var sqliteHookSuccess = false;
            
            // 尝试修复：使用实际实现地址而不是PLT
            try {
                var sqliteImplAddr = lib.add(0x15000);  // girf_sqlite3_bind_blob实现
                Interceptor.attach(sqliteImplAddr, {
                    onEnter: function(args) {
                        console.log("[SQLite Impl] bind_blob实现调用");
                        
                        try {
                            // 修复：更安全的参数解析
                            var stmt = args[0];
                            var index = args[1];
                            var dataPtr = args[2]; 
                            var sizeArg = args[3];
                            
                            // 安全检查参数
                            if (sizeArg && dataPtr) {
                                try {
                                    var size = sizeArg.toInt32();
                                    
                                    // 修复：更严格的大小检查和指针验证
                                    if (size > 16 && size < 10000 && 
                                        !dataPtr.isNull() && 
                                        extractCount < maxExtracts) {
                                        
                                        // 修复：先测试指针可读性
                                        try {
                                            var testByte = dataPtr.readU8();
                                            console.log("[SQLite Data] 发现有效数据: " + size + " 字节");
                                            extractMapData(dataPtr, size, "SQLite-Impl");
                                        } catch (readError) {
                                            // 指针不可读，跳过
                                            if (size < 200) {
                                                console.log("[SQLite Skip] 数据指针不可读，大小: " + size);
                                            }
                                        }
                                    }
                                } catch (parseError) {
                                    // 参数解析失败，静默跳过
                                }
                            }
                        } catch (e) {
                            // 整体参数处理失败，静默跳过
                        }
                    }
                });
                console.log("[Hook] SQLite实现Hook设置成功");
                sqliteHookSuccess = true;
            } catch (e) {
                console.log("[Hook] SQLite实现Hook失败: " + e.message);
            }
            
            // 如果实现Hook失败，尝试其他地址
            if (!sqliteHookSuccess) {
                try {
                    // 尝试另一个可能的SQLite函数
                    var altSqliteAddr = lib.add(0x13B24);  // sub_13B24
                    Interceptor.attach(altSqliteAddr, {
                        onEnter: function(args) {
                            console.log("[SQLite Alt] sub_13B24调用");
                            
                            try {
                                if (args[1] && !args[1].isNull() && args[2]) {
                                    var size = args[2].toInt32();
                                    if (size > 16 && size < 50000 && extractCount < maxExtracts) {
                                        extractMapData(args[1], size, "SQLite-Alt");
                                    }
                                }
                            } catch (e) {
                                // 忽略参数错误
                            }
                        }
                    });
                    console.log("[Hook] SQLite备用Hook设置成功");
                } catch (e) {
                    console.log("[Hook] SQLite备用Hook也失败: " + e.message);
                }
            }
            
            console.log("[Ready] 所有Hook设置完成，请移动地图到新区域");
            
        } catch (e) {
            console.log("[Setup Error] " + e.message);
        }
    }, 3000);
}

// 状态报告 - 修复：添加文件读取统计
function statusReport() {
    console.log("[Report] 文件读取: " + fileReadCount + " 次");
    console.log("[Report] 地图数据提取: " + extractCount + "/" + maxExtracts);
    if (extractCount >= maxExtracts) {
        console.log("[Complete] 地图数据提取完成");
    } else {
        console.log("[Tip] 请移动地图到新区域触发数据加载");
    }
}

// 启动
setupDataHooks();
setInterval(statusReport, 20000);

console.log("[Data Extractor] 脚本加载完成，3秒后开始Hook设置"); 