// 高德地图解压数据Hook脚本 - 严格按照 ans_frida_test45.js 模式
// 适用于 Frida 12.9.7，使用 ES5 语法
// 专门Hook解压函数获取真实解压后数据

(function() {
    'use strict';

    // ==================== 全局配置 ====================
    var config = {
        debug: false,                  // 调试模式开关
        logStackTrace: false,          // 是否记录调用栈
        maxDataCapture: 5,             // 最大数据捕获次数
        delayInitialization: 3000,     // 延迟初始化时间
        targetSoNames: [               // 目标 so 文件名列表
            "libz.so",                 // zlib解压
            "libamapnsq.so",           // ANS文件解析
            "libamapr.so"              // 高德地图渲染
        ]
    };

    // ==================== 全局变量 ====================
    var gModuleMap = {};               // 模块映射 {模块名: {base, size, path}}
    var gHookedFunctions = {};         // 已钩住的函数 {地址: true}
    var gDataCaptured = 0;             // 已捕获数据计数
    var gExceptionCount = {};          // 异常计数

    // ==================== 工具函数 ====================
    function padString(str, length, padChar) {
        str = String(str);
        padChar = padChar || ' ';
        var padding = '';
        for (var i = str.length; i < length; i++) {
            padding += padChar;
        }
        return str + padding;
    }

    function log(message) {
        console.log("[+] " + message);
    }

    function logError(message) {
        console.log("[-] " + message);
    }

    function logDebug(message) {
        if (config.debug) {
            console.log("[DEBUG] " + message);
        }
    }

    function formatAddress(address) {
        if (!address) return "0x0";
        return "0x" + address.toString(16);
    }

    function getModuleByAddress(address) {
        try {
            return Process.findModuleByAddress(address);
        } catch (e) {
            return null;
        }
    }

    function formatAddressWithModule(address) {
        if (!address || address.isNull()) return "0x0";
        
        try {
            var module = getModuleByAddress(address);
            if (module) {
                var offset = address.sub(module.base);
                return module.name + "!" + formatAddress(offset);
            }
        } catch (e) {
            logDebug("格式化地址失败: " + e);
        }
        return formatAddress(address);
    }

    // ==================== 模块枚举 ====================
    function enumerateLoadedModules() {
        log("枚举已加载的模块...");
        
        try {
            var modules = Process.enumerateModules();
            var targetModuleCount = 0;
            
            modules.forEach(function(module) {
                // 检查是否为目标模块
                config.targetSoNames.forEach(function(targetName) {
                    if (module.name === targetName) {
                        gModuleMap[module.name] = {
                            base: module.base,
                            size: module.size,
                            path: module.path
                        };
                        
                        log("找到目标模块: " + padString(module.name, 20) + 
                           " 基址: " + formatAddress(module.base) + 
                           " 大小: 0x" + module.size.toString(16));
                        targetModuleCount++;
                    }
                });
            });
            
            log("共找到 " + targetModuleCount + " 个目标模块");
            return targetModuleCount > 0;
        } catch (e) {
            logError("枚举模块失败: " + e);
            return false;
        }
    }

    // ==================== 数据分析函数 ====================
    function analyzeDecompressedData(data, dataSize, context) {
        if (!data || gDataCaptured >= config.maxDataCapture) return;
        
        try {
            log("\n========== 解压后数据分析 #" + gDataCaptured + " ==========");
            log("数据来源: " + (context || "未知"));
            log("数据大小: " + dataSize + " 字节");
            
            // 安全读取数据
            var safeSize = Math.min(dataSize, 2048);  // 限制读取大小防止崩溃
            var bytes = data.readByteArray(safeSize);
            
            if (!bytes) {
                log("❌ 无法读取数据");
                return;
            }
            
            var uint8Array = new Uint8Array(bytes);
            
            // 显示头部数据
            var headerHex = "";
            var headerText = "";
            var headerSize = Math.min(32, uint8Array.length);
            
            for (var i = 0; i < headerSize; i++) {
                var byte = uint8Array[i];
                headerHex += (byte < 16 ? "0" : "") + byte.toString(16) + " ";
                headerText += (byte >= 32 && byte < 127) ? String.fromCharCode(byte) : ".";
            }
            
            log("数据头部 (hex): " + headerHex);
            log("数据头部 (text): " + headerText);
            
            // 数据类型识别和分析
            identifyAndAnalyzeDataType(uint8Array, safeSize);
            
            log("========== 数据分析完成 ==========\n");
            gDataCaptured++;
            
        } catch (e) {
            logError("数据分析失败: " + e.message);
        }
    }

    function identifyAndAnalyzeDataType(bytes, size) {
        try {
            // 检查DICE-AM头部
            if (size >= 7) {
                var header = "";
                for (var i = 0; i < 7; i++) {
                    header += String.fromCharCode(bytes[i]);
                }
                
                if (header === "DICE-AM") {
                    log("🎯 数据类型: DICE-AM 矢量地图数据");
                    analyzeDICEAMData(bytes, size);
                    return;
                }
            }
            
            // 检查JSON数据
            var textContent = "";
            try {
                for (var i = 0; i < Math.min(size, 100); i++) {
                    if (bytes[i] >= 32 && bytes[i] < 127) {
                        textContent += String.fromCharCode(bytes[i]);
                    }
                }
                
                if (textContent.indexOf("{") >= 0 && textContent.indexOf("}") >= 0) {
                    log("🎯 数据类型: JSON 配置数据");
                    analyzeJSONData(bytes, size);
                    return;
                }
            } catch (e) {
                // 忽略编码错误
            }
            
            // 检查中文文本
            if (hasChineseContent(bytes, size)) {
                log("🎯 数据类型: 包含中文文本数据");
                analyzeChineseContent(bytes, size);
                return;
            }
            
            // 检查坐标数据
            if (hasCoordinateData(bytes, size)) {
                log("🎯 数据类型: 包含坐标数据");
                analyzeCoordinateData(bytes, size);
                return;
            }
            
            log("🎯 数据类型: 二进制数据");
            
        } catch (e) {
            logError("数据类型识别失败: " + e.message);
        }
    }

    function analyzeDICEAMData(bytes, size) {
        try {
            if (size < 16) return;
            
            var view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);
            var version = bytes[7];
            var dataLen = view.getUint32(8, true);
            var pointCount = view.getUint32(12, true);
            
            log("  魔数验证: DICE-AM");
            log("  版本: " + version + " (验证码: " + (version ^ 0xAB) + ")");
            log("  数据长度: " + dataLen + " 字节");
            log("  坐标点数: " + pointCount + " 个");
            
            // 尝试解析坐标点
            var validCoords = 0;
            for (var i = 0; i < Math.min(pointCount, 5) && validCoords < 3; i++) {
                var offset = 16 + i * 8;
                if (offset + 8 <= size) {
                    try {
                        var x = view.getFloat32(offset, true);
                        var y = view.getFloat32(offset + 4, true);
                        
                        if (isValidCoordinate(x, y)) {
                            log("  🌍 坐标 " + i + ": (" + x.toFixed(6) + ", " + y.toFixed(6) + ") <- 真实经纬度!");
                            validCoords++;
                        } else {
                            log("  📐 坐标 " + i + ": (" + x.toFixed(6) + ", " + y.toFixed(6) + ")");
                        }
                    } catch (e) {
                        break;
                    }
                }
            }
            
            if (validCoords > 0) {
                log("  ✅ 发现 " + validCoords + " 个有效经纬度坐标");
            }
            
        } catch (e) {
            logError("  DICE-AM分析失败: " + e.message);
        }
    }

    function analyzeJSONData(bytes, size) {
        try {
            var text = "";
            for (var i = 0; i < size; i++) {
                if (bytes[i] >= 32 && bytes[i] < 127) {
                    text += String.fromCharCode(bytes[i]);
                }
            }
            
            var jsonStart = text.indexOf('{');
            var jsonEnd = text.lastIndexOf('}');
            
            if (jsonStart >= 0 && jsonEnd > jsonStart) {
                var jsonPreview = text.substring(jsonStart, Math.min(jsonEnd + 1, jsonStart + 200));
                log("  JSON预览: " + jsonPreview + "...");
                
                if (text.indexOf("style") >= 0) log("  -> 包含样式配置");
                if (text.indexOf("color") >= 0) log("  -> 包含颜色设置");
                if (text.indexOf("zoom") >= 0) log("  -> 包含缩放配置");
                if (text.indexOf("res_list") >= 0) log("  -> 包含资源列表");
            }
            
        } catch (e) {
            logError("  JSON分析失败: " + e.message);
        }
    }

    function hasChineseContent(bytes, size) {
        try {
            // 检查UTF-8中文字符模式 (0xE4-0xE9 开头的3字节序列)
            for (var i = 0; i < size - 2; i++) {
                if (bytes[i] >= 0xE4 && bytes[i] <= 0xE9 && 
                    bytes[i + 1] >= 0x80 && bytes[i + 1] <= 0xBF &&
                    bytes[i + 2] >= 0x80 && bytes[i + 2] <= 0xBF) {
                    return true;
                }
            }
            return false;
        } catch (e) {
            return false;
        }
    }

    function analyzeChineseContent(bytes, size) {
        try {
            var chineseCount = 0;
            var placeNames = [];
            
            // 简单的中文字符计数
            for (var i = 0; i < size - 2; i++) {
                if (bytes[i] >= 0xE4 && bytes[i] <= 0xE9 && 
                    bytes[i + 1] >= 0x80 && bytes[i + 1] <= 0xBF &&
                    bytes[i + 2] >= 0x80 && bytes[i + 2] <= 0xBF) {
                    chineseCount++;
                    i += 2;  // 跳过多字节字符的后续字节
                }
            }
            
            log("  检测到中文字符: 约 " + chineseCount + " 个");
            log("  可能包含: 地名、道路名、POI名称等");
            
        } catch (e) {
            logError("  中文内容分析失败: " + e.message);
        }
    }

    function hasCoordinateData(bytes, size) {
        try {
            var view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);
            for (var i = 0; i < size - 8; i += 4) {
                try {
                    var x = view.getFloat32(i, true);
                    var y = view.getFloat32(i + 4, true);
                    if (isValidCoordinate(x, y)) {
                        return true;
                    }
                } catch (e) {
                    continue;
                }
            }
            return false;
        } catch (e) {
            return false;
        }
    }

    function analyzeCoordinateData(bytes, size) {
        try {
            var view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);
            var coordCount = 0;
            
            for (var i = 0; i < size - 8 && coordCount < 5; i += 4) {
                try {
                    var x = view.getFloat32(i, true);
                    var y = view.getFloat32(i + 4, true);
                    
                    if (isValidCoordinate(x, y)) {
                        log("  🌍 坐标: (" + x.toFixed(6) + ", " + y.toFixed(6) + ") at 偏移 " + i);
                        coordCount++;
                    }
                } catch (e) {
                    continue;
                }
            }
            
            if (coordCount > 0) {
                log("  ✅ 发现 " + coordCount + " 个有效坐标点");
            }
            
        } catch (e) {
            logError("  坐标数据分析失败: " + e.message);
        }
    }

    function isValidCoordinate(x, y) {
        // 中国大陆经纬度范围
        return (70 <= x && x <= 140 && 15 <= y && y <= 60) || 
               (70 <= y && y <= 140 && 15 <= x && x <= 60);
    }

    // ==================== Hook函数 ====================
    function hookZlibUncompress() {
        log("设置 zlib uncompress Hook...");
        
        try {
            var zlibModule = gModuleMap["libz.so"];
            if (!zlibModule) {
                logError("libz.so 模块未找到");
                return false;
            }
            
            var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
            if (!uncompressPtr) {
                logError("uncompress 函数未找到");
                return false;
            }
            
            if (gHookedFunctions[uncompressPtr]) {
                logDebug("uncompress 已经被Hook");
                return true;
            }
            
            log("找到 uncompress 函数: " + formatAddress(uncompressPtr));
            
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    this.destBuffer = args[0];
                    this.destLenPtr = args[1];
                    this.sourceLen = args[3].toInt32();
                    
                    logDebug("开始解压: 源大小 " + this.sourceLen + " 字节");
                },
                onLeave: function(retval) {
                    var result = retval.toInt32();
                    
                    if (result === 0 && gDataCaptured < config.maxDataCapture) {
                        try {
                            var decompressedLen = this.destLenPtr.readU32();
                            
                            if (decompressedLen > 0 && decompressedLen < 100000) {
                                log("\n🎯 [zlib解压成功] 压缩前: " + this.sourceLen + 
                                   " → 解压后: " + decompressedLen + " 字节");
                                
                                // 分析解压后的数据
                                analyzeDecompressedData(this.destBuffer, decompressedLen, "zlib解压");
                            }
                        } catch (e) {
                            logError("读取解压数据失败: " + e.message);
                        }
                    }
                }
            });
            
            gHookedFunctions[uncompressPtr] = true;
            log("✅ zlib uncompress Hook 设置成功");
            return true;
            
        } catch (e) {
            logError("设置 zlib Hook 失败: " + e);
            return false;
        }
    }

    // ==================== 异常处理 ====================
    function setupExceptionHandler() {
        Process.setExceptionHandler(function(exception) {
            var exceptionKey = exception.type + ":" + formatAddress(exception.address);
            
            if (!gExceptionCount[exceptionKey]) {
                gExceptionCount[exceptionKey] = 0;
            }
            
            gExceptionCount[exceptionKey]++;
            
            // 只显示前2次相同异常
            if (gExceptionCount[exceptionKey] <= 2) {
                logError("异常(" + gExceptionCount[exceptionKey] + "/2): " + 
                        exception.type + " @ " + formatAddress(exception.address));
                
                if (config.debug && gExceptionCount[exceptionKey] === 1) {
                    logDebug("异常详情: " + JSON.stringify(exception));
                }
            }
            
            // 返回 true 表示已处理异常，脚本将继续运行
            return true;
        });
    }

    // ==================== 主函数 ====================
    function main() {
        log("高德地图解压数据Hook脚本启动");
        log("适用于 Frida 12.9.7，使用 ES5 语法");
        log("基于 ans_frida_test45.js 成功模式");
        
        // 设置异常处理
        setupExceptionHandler();
        
        try {
            // 1. 枚举已加载模块
            if (!enumerateLoadedModules()) {
                logError("未找到目标模块，脚本终止");
                return;
            }
            
            // 延迟执行Hook设置，确保模块完全加载
            setTimeout(function() {
                try {
                    log("开始设置解压数据Hook...");
                    
                    // 设置 zlib 解压 Hook
                    var success = hookZlibUncompress();
                    
                    if (success) {
                        log("✅ 解压数据Hook设置完成");
                        log("💡 请在地图中移动以触发数据解压");
                        log("💡 脚本将捕获并分析真实的解压后数据");
                    } else {
                        logError("Hook设置失败");
                    }
                    
                } catch (e) {
                    logError("设置Hook失败: " + e);
                }
            }, config.delayInitialization);
            
        } catch (e) {
            logError("脚本初始化失败: " + e);
        }
        
        log("脚本设置完成，等待初始化...");
    }
    
    // 启动脚本
    main();
})(); 