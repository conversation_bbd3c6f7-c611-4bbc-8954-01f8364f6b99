     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
[Branch Analyzer] 启动分支逻辑分析器...
[Java] Java环境已准备就绪
[Main] 等待应用初始化完成...
[Remote::com.autonavi.minimap]-> [Main] 开始初始化分支分析器...
[Library] libamapnsq.so 已加载，基址: 0x7f75f3e000
[Branch] libamapnsq.so 基址: 0x7f75f3e000
[Branch] 设置 sub_10F88 主分支监控...
[Branch] sub_10F88 hook 已设置
[Branch] 设置 sub_5C394 主分支监控...
[Branch] sub_5C394 hook 已设置
[Branch Analyzer] 分支逻辑分析器已启动!
现在移动地图以触发分支执行，观察代码分支逻辑...
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 23ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 13ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 2ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms

=== 分支执行路径分析 ===
分支执行统计:
  function_result:
    总执行次数: 130
    成功次数: 130
    失败次数: 0
    最后值: success
  type_detection:
    总执行次数: 24
    成功次数: 0
    失败次数: 24
    最后值: Other

当前条件状态:
  sub_10F88_success: 通过
  is_dice_am: 失败
==========================

[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.!9h.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 6ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '.C.Q.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 1ms
[Branch] sub_5C394 调度器进入
[Dispatch] 缓冲区大小: -1807052984 字节
[TypeCheck] DICE-AM检测: 否
[Branch] sub_5C394 调度完成, 耗时: 0ms
[Branch] sub_10F88 函数进入
[Data] 第一个字节: 0x8
[Header] 数据头部: '...f.'
[Branch] sub_10F88 函数退出, 返回码: 0, 耗时: 0ms

[Remote::com.autonavi.minimap]-> 
[Remote::com.autonavi.minimap]-> exit

Thank you for using Frida!
