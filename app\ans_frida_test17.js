setTimeout(function() {
  console.log("[Java层ANS加载分析] 启动");
  
  // 1. 监控m1.ans文件打开
  var open_ptr = Module.findExportByName("libc.so", "open");
  if (open_ptr) {
    Interceptor.attach(open_ptr, {
      onEnter: function(args) {
        try {
          var path = args[0].readUtf8String();
          if (path && path.indexOf("m1.ans") !== -1) {
            this.path = path;
            console.log("[文件] 打开m1.ans: " + path);
          }
        } catch(e) {}
      },
      onLeave: function(retval) {
        if (this.path) {
          this.fd = retval.toInt32();
          console.log("[文件] m1.ans文件描述符: " + this.fd);
        }
      }
    });
  }
  
  // 2. 简化Java层分析
  setTimeout(function() {
    Java.perform(function() {
      console.log("[Java层] 开始分析");
      
      // 2.1 分析NewMapActivity.J方法
      try {
        var NewMapActivity = Java.use("com.autonavi.map.activity.NewMapActivity");
        NewMapActivity.J.implementation = function() {
          console.log("[调用] NewMapActivity.J()");
          var result = this.J();
          console.log("[完成] NewMapActivity.J()");
          return result;
        };
        console.log("[+] Hook J()成功");
      } catch(e) {
        console.log("[-] Hook J()失败: " + e);
      }
      
      // 2.2 分析AMapController.setAppResourceLoader
      try {
        var AMapController = Java.use("com.autonavi.ae.gmap.AMapController");
        AMapController.setAppResourceLoader.implementation = function(loader) {
          console.log("[调用] AMapController.setAppResourceLoader()");
          
          if (loader) {
            try {
              var loaderClass = loader.getClass().getName();
              console.log("  加载器类型: " + loaderClass);
            } catch(e) {}
          }
          
          var result = this.setAppResourceLoader(loader);
          console.log("[完成] setAppResourceLoader()");
          return result;
        };
        console.log("[+] Hook setAppResourceLoader()成功");
      } catch(e) {
        console.log("[-] Hook setAppResourceLoader()失败: " + e);
      }
      
      // 2.3 分析InterfaceAppImpl.getNativeResourceLoader
      try {
        var InterfaceAppImpl = Java.use("com.amap.jni.app.InterfaceAppImpl");
        InterfaceAppImpl.getNativeResourceLoader.implementation = function() {
          console.log("[调用] getNativeResourceLoader()");
          var loader = this.getNativeResourceLoader();
          console.log("[完成] getNativeResourceLoader()");
          return loader;
        };
        console.log("[+] Hook getNativeResourceLoader()成功");
      } catch(e) {
        console.log("[-] Hook getNativeResourceLoader()失败: " + e);
      }
    });
  }, 1000);
  
}, 500);
