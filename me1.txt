# ANS文件解析项目总结报告

## 1. 项目目标
本项目的核心目标是获取高德地图App中m1.ans和m3.ans（以及可能m2.ans）文件的解析方法逻辑，并能正确解析出其中的数据。

## 2. 核心发现与结论

### 2.1. ANS文件压缩方式
经过Frida动态分析和十六进制数据验证，最终确认m1.ans和m3.ans文件中的核心数据块使用了标准的**zlib压缩算法**。压缩数据块的起始魔术数字为`78 9c`。

### 2.2. 关键函数识别 (libamapnsq.so)

#### 2.2.1. zlib解压包装函数：`sub_C654` (偏移量 `0xC654`)
*   **功能：** 该函数是zlib `uncompress` 函数的直接包装器，负责将压缩数据解压到指定的内存缓冲区。
*   **C语言原型推断：** `int sub_C654(void* context, void* uncompressed_buffer, size_t* uncompressed_size_ptr, void* compressed_buffer, size_t compressed_size);`
*   **验证：** Frida日志和IDA伪代码均证实其直接调用`uncompress`。

#### 2.2.2. 缓冲区管理函数：`sub_C39C` (偏移量 `0xC39C`)
*   **功能：** 该函数负责管理解压所需的内存缓冲区。它会检查现有缓冲区大小，并在需要时分配新的内存空间。
*   **验证：** IDA伪代码证实其在`sub_C654`内部被调用，用于准备解压目标缓冲区。

#### 2.2.3. 高层解析协调函数：`sub_5C394` (偏移量 `0x5C394`)
*   **功能：** 该函数是调用`sub_C654`的间接上层函数，负责协调ANS文件的读取、解压和初步解析流程。
*   **验证：** Frida调用栈明确指出其在`sub_C654`的调用链中。尽管IDA伪代码未能直接显示对`sub_C654`的直接调用（可能由于间接调用或编译器优化），但其复杂逻辑和对解压后数据的处理表明其为核心解析入口。

## 3. 动态分析 (Frida脚本演进)
通过迭代开发Frida脚本，我们逐步增强了对ANS文件解析流程的洞察：

*   **`extract_ans_parsing_logic.js` (V36)：** 初始版本，通过Hook `open`/`read` 和 `uncompress`，关联文件IO与解压操作，定位到`sub_C654`。
*   **`ans_parser_finder.js` (V37)：** 尝试扩展到`m2.ans`，但后续聚焦于`m1.ans`/`m3.ans`。
*   **`ans_frida_test33.js` (V40)：** 增强日志输出，初步识别`DICE-AM`和索引块。
*   **`ans_parser_v41.js`：** 增强头部识别，增加了对`bc bc bc bc`和`ce ca 0b b1`的识别。
*   **`ans_parser_v42.js`：** 进一步增强头部识别，增加了对`05 00 00 XX`和`00 00 25 XX`的识别。
*   **`ans_parser_v43.js`：** 尝试解析`DICE-AM`内部字段，但存在ES5兼容性问题。
*   **`ans_parser_v44.js`：** 修复`Array.from`等ES6特性导致的兼容性问题。
*   **`ans_parser_v45.js`：** 彻底解决ES5兼容性问题（箭头函数、`padStart`等），确保脚本在Frida 12.9.7环境下稳定运行。
*   **`ans_parser_v46.js`：** 增加了对`00 00 03 42`和`00 00 03 43`新块类型的识别。

## 4. 静态分析 (IDA Pro应用)
IDA Pro在整个分析过程中扮演了关键角色：

*   **函数定位：** 结合Frida日志，精确跳转到`libamapnsq.so`中的`sub_C654`、`sub_C39C`和`sub_5C394`等函数。
*   **伪代码分析：** 尝试通过伪代码理解函数逻辑，尽管在处理复杂间接调用和编译器优化时遇到挑战。
*   **数据结构推断：** 通过分析伪代码中对内存地址的访问和操作，结合Frida的hexdump输出，逐步推断数据块的内部结构。
*   **全局变量值获取：** 成功读取了`unk_89B7E`和`unk_89B82`的值，解开了`DICE-AM`块验证逻辑的谜团。

## 5. 已识别数据块结构初步分析

### 5.1. `DICE-AM` 数据块
*   **魔数：** `44 49 43 45 2d 41 4d 00` ("DICE-AM\000")
*   **验证签名：** 偏移量`0x0B`处3字节为`8d cf 8d`。
*   **初步结构推断：**
    ```c
    struct DICE_AM_Block {
        char     magic[8];          // "DICE-AM\000"
        uint8_t  byte_0x08;         // 实际值 0xaa
        uint8_t  byte_0x09;         // 实际值 0x00
        uint8_t  byte_0x0A;         // 实际值 0x89
        uint8_t  validation_bytes[3]; // 实际值 0x8d 0xcf 0x8d
        // ... (未知字段) ...
        uint8_t  bytes_0x1A_0x1B[2]; // 实际值 0xfe 0xfe
        // ... (未知字段) ...
        uint32_t dword_0x2C;        // 偏移量 0x2C，大端序，实际值 0x00000000
        // ... (未知字段) ...
        uint32_t dword_0x38;        // 偏移量 0x38，大端序，实际值 0x00000000
        // ... (后续数据) ...
    } __attribute__((packed));
    ```
*   **解析函数：** `sub_10F88` (偏移量 `0x10F88`) 负责解析此块，包含`strcmp`和`memcmp`验证。

### 5.2. 索引块 (0x0D00)
*   **魔数：** `0d 00 00 00` (0x0000000D)
*   **初步结构推断：**
    ```c
    struct IndexBlock_0x0D00 {
        uint32_t magic;             // 0x0000000D
        uint32_t count_and_size;    // 偏移量 0x04，例如 0x00A21F04 (7940条目，每条162字节？)
        // ... (后续为索引条目列表) ...
    } __attribute__((packed));
    ```

### 5.3. 类型05数据块
*   **魔数：** `05 00 00 00` 或 `05 00 00 01`
*   **子类型：** 偏移量`0x03`处的字节。
*   **初步结构推断：** 包含长度或计数，后续为实际数据。

### 5.4. 类型25数据块
*   **魔数：** `00 00 25 XX`
*   **子类型：** 偏移量`0x03`处的字节。
*   **初步结构推断：** 包含长度或计数，后续为实际数据。

### 5.5. 其他已识别块 (待详细分析)
*   **标准数据块：** 魔数 `bc bc bc bc`
*   **POI/道路数据块：** 魔数 `ce ca 0b b1` (小端序可能为`0xB10BCA00`)
*   **新发现未知块：** 魔数 `00 00 03 42` 和 `00 00 03 43`

## 6. 后续工作/待办事项
1.  **收集更多日志：** 运行`ans_parser_v46.js`（或更高版本），确保获取包含所有已知和新发现块类型的完整日志，特别是`00 00 03 42`和`00 00 03 43`的hexdump。
2.  **详细分析所有数据块结构：**
    *   对于每种数据块，根据日志中的hexdump和IDA伪代码，精确确定所有字段的偏移量、大小和数据类型。
    *   特别关注长度字段、计数、标志和嵌套结构。
    *   尝试理解每个字段的语义。
3.  **编写离线解析器：** 基于对数据块结构的完整理解，使用Python或其他语言编写一个离线解析工具，能够读取`.ans`文件，解压数据，并解析出所有结构化信息。
4.  **逆向工程子函数：** 如果某些数据块的解析逻辑过于复杂，需要进一步逆向工程`sub_10F88`调用的子函数（如`sub_20208`、`sub_260F4`等），以理解它们对数据块的处理方式。
