     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
[SQLite Data Extractor] 启动SQLite BLOB数据提取器...
[Main] 初始化SQLite BLOB数据提取器...
[Remote::com.autonavi.minimap]-> 
[Remote::com.autonavi.minimap]-> [Library] libamapnsq.so 基址: 0x7f60639000
[SQLite] 设置SQLite BLOB Hook for girf_sqlite3_bind_blob...
[SQLite] girf_sqlite3_bind_blob Hook已设置
[SQLite Data Extractor] SQLite数据提取器已启动!
采样率: 1/10, 最大样本: 3
现在移动地图，观察SQLite BLOB数据...
[SQLite Sample 1] girf_sqlite3_bind_blob 调用 (第10次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 91, 数据大小: 18 字节
[SQLite Skip] BLOB大小超出范围: 18 字节
[SQLite Sample 2] girf_sqlite3_bind_blob 调用 (第20次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 82, 数据大小: 9 字节
[SQLite Skip] BLOB大小超出范围: 9 字节
[SQLite Sample 3] girf_sqlite3_bind_blob 调用 (第30次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 82, 数据大小: 9 字节
[SQLite Skip] BLOB大小超出范围: 9 字节
[SQLite Sample 4] girf_sqlite3_bind_blob 调用 (第40次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 648, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 5] girf_sqlite3_bind_blob 调用 (第50次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 5, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 6] girf_sqlite3_bind_blob 调用 (第60次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 7] girf_sqlite3_bind_blob 调用 (第70次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 4, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 8] girf_sqlite3_bind_blob 调用 (第80次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 4, 数据大小: -6 字节
[SQLite Skip] BLOB大小超出范围: -6 字节
[SQLite Sample 9] girf_sqlite3_bind_blob 调用 (第90次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 10] girf_sqlite3_bind_blob 调用 (第100次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 11] girf_sqlite3_bind_blob 调用 (第110次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 4, 数据大小: 7 字节
[SQLite Skip] BLOB大小超出范围: 7 字节
[SQLite Sample 12] girf_sqlite3_bind_blob 调用 (第120次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 4, 数据大小: 4 字节
[SQLite Skip] BLOB大小超出范围: 4 字节
[SQLite Sample 13] girf_sqlite3_bind_blob 调用 (第130次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 296, 数据大小: 2 字节
[SQLite Skip] BLOB大小超出范围: 2 字节
[SQLite Sample 14] girf_sqlite3_bind_blob 调用 (第140次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 9, 数据大小: 4 字节
[SQLite Skip] BLOB大小超出范围: 4 字节
[SQLite Sample 15] girf_sqlite3_bind_blob 调用 (第150次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 296, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 16] girf_sqlite3_bind_blob 调用 (第160次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 17] girf_sqlite3_bind_blob 调用 (第170次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 76, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 18] girf_sqlite3_bind_blob 调用 (第180次)
[SQLite Params] 语句: 0x7f7e28fb88, 参数索引: 280, 数据大小: 1617404844 字节
[SQLite Skip] BLOB大小超出范围: 1617404844 字节

=== SQLite BLOB数据提取报告 ===
总调用次数: 189
采样次数: 18
发现BLOB: 0 次
成功提取: 0 个样本
==================================

[SQLite Sample 19] girf_sqlite3_bind_blob 调用 (第190次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 32, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 20] girf_sqlite3_bind_blob 调用 (第200次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 89, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 21] girf_sqlite3_bind_blob 调用 (第210次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 24, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 22] girf_sqlite3_bind_blob 调用 (第220次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 15, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 23] girf_sqlite3_bind_blob 调用 (第230次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 120, 数据大小: 1364105024 字节
[SQLite Skip] BLOB大小超出范围: 1364105024 字节
[SQLite Sample 24] girf_sqlite3_bind_blob 调用 (第240次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 96, 数据大小: -1 字节
[SQLite Skip] BLOB大小超出范围: -1 字节
[SQLite Sample 25] girf_sqlite3_bind_blob 调用 (第250次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 26] girf_sqlite3_bind_blob 调用 (第260次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 16, 数据大小: 1367862760 字节
[SQLite Skip] BLOB大小超出范围: 1367862760 字节
[SQLite Sample 27] girf_sqlite3_bind_blob 调用 (第270次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 72, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 28] girf_sqlite3_bind_blob 调用 (第280次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 24, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 29] girf_sqlite3_bind_blob 调用 (第290次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 30] girf_sqlite3_bind_blob 调用 (第300次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 296, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 31] girf_sqlite3_bind_blob 调用 (第310次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 480, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 32] girf_sqlite3_bind_blob 调用 (第320次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 76, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 33] girf_sqlite3_bind_blob 调用 (第330次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 80, 数据大小: -1 字节
[SQLite Skip] BLOB大小超出范围: -1 字节
[SQLite Sample 34] girf_sqlite3_bind_blob 调用 (第340次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 35] girf_sqlite3_bind_blob 调用 (第350次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 4, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 36] girf_sqlite3_bind_blob 调用 (第360次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 3, 数据大小: 48 字节
[SQLite Skip] BLOB大小超出范围: 48 字节
[SQLite Sample 37] girf_sqlite3_bind_blob 调用 (第370次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 96, 数据大小: 1088589192 字节
[SQLite Skip] BLOB大小超出范围: 1088589192 字节
[SQLite Sample 38] girf_sqlite3_bind_blob 调用 (第380次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 39] girf_sqlite3_bind_blob 调用 (第390次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 40] girf_sqlite3_bind_blob 调用 (第400次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 120, 数据大小: 1364105024 字节
[SQLite Skip] BLOB大小超出范围: 1364105024 字节
[SQLite Sample 41] girf_sqlite3_bind_blob 调用 (第410次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 96, 数据大小: -1 字节
[SQLite Skip] BLOB大小超出范围: -1 字节
[SQLite Sample 42] girf_sqlite3_bind_blob 调用 (第420次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 43] girf_sqlite3_bind_blob 调用 (第430次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 296, 数据大小: 3 字节
[SQLite Skip] BLOB大小超出范围: 3 字节
[SQLite Sample 44] girf_sqlite3_bind_blob 调用 (第440次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 4, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 45] girf_sqlite3_bind_blob 调用 (第450次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 46] girf_sqlite3_bind_blob 调用 (第460次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 47] girf_sqlite3_bind_blob 调用 (第470次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 648, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 48] girf_sqlite3_bind_blob 调用 (第480次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 16 字节
[SQLite Skip] BLOB大小超出范围: 16 字节
[SQLite Sample 49] girf_sqlite3_bind_blob 调用 (第490次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 4, 数据大小: 4 字节
[SQLite Skip] BLOB大小超出范围: 4 字节
[SQLite Sample 50] girf_sqlite3_bind_blob 调用 (第500次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 51] girf_sqlite3_bind_blob 调用 (第510次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 32, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 52] girf_sqlite3_bind_blob 调用 (第520次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 4, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 53] girf_sqlite3_bind_blob 调用 (第530次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 54] girf_sqlite3_bind_blob 调用 (第540次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 55] girf_sqlite3_bind_blob 调用 (第550次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 76, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 56] girf_sqlite3_bind_blob 调用 (第560次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 296, 数据大小: 1617708567 字节
[SQLite Skip] BLOB大小超出范围: 1617708567 字节
[SQLite Sample 57] girf_sqlite3_bind_blob 调用 (第570次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 16 字节
[SQLite Skip] BLOB大小超出范围: 16 字节
[SQLite Sample 58] girf_sqlite3_bind_blob 调用 (第580次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 141 字节
[SQLite Error] 参数分析失败: Error: access violation accessing 0x1
[SQLite Sample 59] girf_sqlite3_bind_blob 调用 (第590次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 89, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 60] girf_sqlite3_bind_blob 调用 (第600次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 24, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 61] girf_sqlite3_bind_blob 调用 (第610次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 15, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 62] girf_sqlite3_bind_blob 调用 (第620次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 120, 数据大小: 1364094272 字节
[SQLite Skip] BLOB大小超出范围: 1364094272 字节
[SQLite Sample 63] girf_sqlite3_bind_blob 调用 (第630次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 296, 数据大小: 1617708567 字节
[SQLite Skip] BLOB大小超出范围: 1617708567 字节
[SQLite Sample 64] girf_sqlite3_bind_blob 调用 (第640次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 16 字节
[SQLite Skip] BLOB大小超出范围: 16 字节
[SQLite Sample 65] girf_sqlite3_bind_blob 调用 (第650次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 69, 数据大小: 38 字节
[SQLite Skip] BLOB大小超出范围: 38 字节
[SQLite Sample 66] girf_sqlite3_bind_blob 调用 (第660次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 496, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 67] girf_sqlite3_bind_blob 调用 (第670次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 80, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 68] girf_sqlite3_bind_blob 调用 (第680次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 69] girf_sqlite3_bind_blob 调用 (第690次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 648, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 70] girf_sqlite3_bind_blob 调用 (第700次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 71] girf_sqlite3_bind_blob 调用 (第710次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 40, 数据大小: 1368912960 字节
[SQLite Skip] BLOB大小超出范围: 1368912960 字节
[SQLite Sample 72] girf_sqlite3_bind_blob 调用 (第720次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 16 字节
[SQLite Skip] BLOB大小超出范围: 16 字节
[SQLite Sample 73] girf_sqlite3_bind_blob 调用 (第730次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 72, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 74] girf_sqlite3_bind_blob 调用 (第740次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 80, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 75] girf_sqlite3_bind_blob 调用 (第750次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 76] girf_sqlite3_bind_blob 调用 (第760次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 87, 数据大小: 141 字节
[SQLite Error] 参数分析失败: Error: access violation accessing 0x1
[SQLite Sample 77] girf_sqlite3_bind_blob 调用 (第770次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 72, 数据大小: -1990851916 字节
[SQLite Skip] BLOB大小超出范围: -1990851916 字节
[SQLite Sample 78] girf_sqlite3_bind_blob 调用 (第780次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 89, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 79] girf_sqlite3_bind_blob 调用 (第790次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 80, 数据大小: -1 字节
[SQLite Skip] BLOB大小超出范围: -1 字节
[SQLite Sample 80] girf_sqlite3_bind_blob 调用 (第800次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 81] girf_sqlite3_bind_blob 调用 (第810次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 4, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 82] girf_sqlite3_bind_blob 调用 (第820次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 76, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 83] girf_sqlite3_bind_blob 调用 (第830次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 80, 数据大小: -1 字节
[SQLite Skip] BLOB大小超出范围: -1 字节
[SQLite Sample 84] girf_sqlite3_bind_blob 调用 (第840次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 85] girf_sqlite3_bind_blob 调用 (第850次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 16, 数据大小: 1367861944 字节
[SQLite Skip] BLOB大小超出范围: 1367861944 字节
[SQLite Sample 86] girf_sqlite3_bind_blob 调用 (第860次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 32, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 87] girf_sqlite3_bind_blob 调用 (第870次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 96, 数据大小: 1088589192 字节
[SQLite Skip] BLOB大小超出范围: 1088589192 字节
[SQLite Sample 88] girf_sqlite3_bind_blob 调用 (第880次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 89] girf_sqlite3_bind_blob 调用 (第890次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 90] girf_sqlite3_bind_blob 调用 (第900次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 3, 数据大小: 48 字节
[SQLite Skip] BLOB大小超出范围: 48 字节
[SQLite Sample 91] girf_sqlite3_bind_blob 调用 (第910次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 96, 数据大小: 1142362504 字节
[SQLite Skip] BLOB大小超出范围: 1142362504 字节
[SQLite Sample 92] girf_sqlite3_bind_blob 调用 (第920次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 93] girf_sqlite3_bind_blob 调用 (第930次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 16, 数据大小: 1368906472 字节
[SQLite Skip] BLOB大小超出范围: 1368906472 字节
[SQLite Sample 94] girf_sqlite3_bind_blob 调用 (第940次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 120, 数据大小: 1081153032 字节
[SQLite Skip] BLOB大小超出范围: 1081153032 字节
[SQLite Sample 95] girf_sqlite3_bind_blob 调用 (第950次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 80, 数据大小: -1 字节
[SQLite Skip] BLOB大小超出范围: -1 字节
[SQLite Sample 96] girf_sqlite3_bind_blob 调用 (第960次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 97] girf_sqlite3_bind_blob 调用 (第970次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 76, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 98] girf_sqlite3_bind_blob 调用 (第980次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 296, 数据大小: 1617708567 字节
[SQLite Skip] BLOB大小超出范围: 1617708567 字节
[SQLite Sample 99] girf_sqlite3_bind_blob 调用 (第990次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 16 字节
[SQLite Skip] BLOB大小超出范围: 16 字节
[SQLite Sample 100] girf_sqlite3_bind_blob 调用 (第1000次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 141 字节
[SQLite Error] 参数分析失败: Error: access violation accessing 0x1
[SQLite Sample 101] girf_sqlite3_bind_blob 调用 (第1010次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 102] girf_sqlite3_bind_blob 调用 (第1020次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 480, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 103] girf_sqlite3_bind_blob 调用 (第1030次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 32, 数据大小: 53 字节
[SQLite Skip] BLOB大小超出范围: 53 字节
[SQLite Sample 104] girf_sqlite3_bind_blob 调用 (第1040次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 72, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 105] girf_sqlite3_bind_blob 调用 (第1050次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 88, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 106] girf_sqlite3_bind_blob 调用 (第1060次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 48, 数据大小: 36 字节
[SQLite Skip] BLOB大小超出范围: 36 字节
[SQLite Sample 107] girf_sqlite3_bind_blob 调用 (第1070次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 31, 数据大小: 1819047278 字节
[SQLite Skip] BLOB大小超出范围: 1819047278 字节
[SQLite Sample 108] girf_sqlite3_bind_blob 调用 (第1080次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 40, 数据大小: 1367868480 字节
[SQLite Skip] BLOB大小超出范围: 1367868480 字节
[SQLite Sample 109] girf_sqlite3_bind_blob 调用 (第1090次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 16, 数据大小: 1368906472 字节
[SQLite Skip] BLOB大小超出范围: 1368906472 字节
[SQLite Sample 110] girf_sqlite3_bind_blob 调用 (第1100次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 111] girf_sqlite3_bind_blob 调用 (第1110次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 280, 数据大小: 1617404844 字节
[SQLite Skip] BLOB大小超出范围: 1617404844 字节
[SQLite Sample 112] girf_sqlite3_bind_blob 调用 (第1120次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 141 字节
[SQLite Error] 参数分析失败: Error: access violation accessing 0x1
[SQLite Sample 113] girf_sqlite3_bind_blob 调用 (第1130次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 72, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 114] girf_sqlite3_bind_blob 调用 (第1140次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 88, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 115] girf_sqlite3_bind_blob 调用 (第1150次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 48, 数据大小: 37 字节
[SQLite Skip] BLOB大小超出范围: 37 字节
[SQLite Sample 116] girf_sqlite3_bind_blob 调用 (第1160次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 32, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 117] girf_sqlite3_bind_blob 调用 (第1170次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 40, 数据大小: 1367868480 字节
[SQLite Skip] BLOB大小超出范围: 1367868480 字节
[SQLite Sample 118] girf_sqlite3_bind_blob 调用 (第1180次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 119] girf_sqlite3_bind_blob 调用 (第1190次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 120, 数据大小: 1364087104 字节
[SQLite Skip] BLOB大小超出范围: 1364087104 字节
[SQLite Sample 120] girf_sqlite3_bind_blob 调用 (第1200次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 96, 数据大小: -1 字节
[SQLite Skip] BLOB大小超出范围: -1 字节
[SQLite Sample 121] girf_sqlite3_bind_blob 调用 (第1210次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 122] girf_sqlite3_bind_blob 调用 (第1220次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 16, 数据大小: 1366817464 字节
[SQLite Skip] BLOB大小超出范围: 1366817464 字节
[SQLite Sample 123] girf_sqlite3_bind_blob 调用 (第1230次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 296, 数据大小: 1617708567 字节
[SQLite Skip] BLOB大小超出范围: 1617708567 字节
[SQLite Sample 124] girf_sqlite3_bind_blob 调用 (第1240次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 16 字节
[SQLite Skip] BLOB大小超出范围: 16 字节
[SQLite Sample 125] girf_sqlite3_bind_blob 调用 (第1250次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 141 字节
[SQLite Error] 参数分析失败: Error: access violation accessing 0x1
[SQLite Sample 126] girf_sqlite3_bind_blob 调用 (第1260次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 76, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 127] girf_sqlite3_bind_blob 调用 (第1270次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 296, 数据大小: 1617708567 字节
[SQLite Skip] BLOB大小超出范围: 1617708567 字节
[SQLite Sample 128] girf_sqlite3_bind_blob 调用 (第1280次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 16 字节
[SQLite Skip] BLOB大小超出范围: 16 字节
[SQLite Sample 129] girf_sqlite3_bind_blob 调用 (第1290次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 40, 数据大小: 1366824000 字节
[SQLite Skip] BLOB大小超出范围: 1366824000 字节
[SQLite Sample 130] girf_sqlite3_bind_blob 调用 (第1300次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 16 字节
[SQLite Skip] BLOB大小超出范围: 16 字节
[SQLite Sample 131] girf_sqlite3_bind_blob 调用 (第1310次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 4, 数据大小: 12 字节
[SQLite Skip] BLOB大小超出范围: 12 字节
[SQLite Sample 132] girf_sqlite3_bind_blob 调用 (第1320次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 133] girf_sqlite3_bind_blob 调用 (第1330次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 480, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 134] girf_sqlite3_bind_blob 调用 (第1340次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 32, 数据大小: 53 字节
[SQLite Skip] BLOB大小超出范围: 53 字节
[SQLite Sample 135] girf_sqlite3_bind_blob 调用 (第1350次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 72, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 136] girf_sqlite3_bind_blob 调用 (第1360次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 88, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 137] girf_sqlite3_bind_blob 调用 (第1370次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 48, 数据大小: 36 字节
[SQLite Skip] BLOB大小超出范围: 36 字节
[SQLite Sample 138] girf_sqlite3_bind_blob 调用 (第1380次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 32, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 139] girf_sqlite3_bind_blob 调用 (第1390次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 89, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 140] girf_sqlite3_bind_blob 调用 (第1400次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 24, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 141] girf_sqlite3_bind_blob 调用 (第1410次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 15, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 142] girf_sqlite3_bind_blob 调用 (第1420次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 120, 数据大小: 1364090688 字节
[SQLite Skip] BLOB大小超出范围: 1364090688 字节
[SQLite Sample 143] girf_sqlite3_bind_blob 调用 (第1430次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 96, 数据大小: -1 字节
[SQLite Skip] BLOB大小超出范围: -1 字节
[SQLite Sample 144] girf_sqlite3_bind_blob 调用 (第1440次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 145] girf_sqlite3_bind_blob 调用 (第1450次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 296, 数据大小: 3 字节
[SQLite Skip] BLOB大小超出范围: 3 字节
[SQLite Sample 146] girf_sqlite3_bind_blob 调用 (第1460次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 4, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 147] girf_sqlite3_bind_blob 调用 (第1470次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 148] girf_sqlite3_bind_blob 调用 (第1480次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 149] girf_sqlite3_bind_blob 调用 (第1490次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 648, 数据大小: 32 字节
[SQLite Skip] BLOB大小超出范围: 32 字节
[SQLite Sample 150] girf_sqlite3_bind_blob 调用 (第1500次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 16 字节
[SQLite Skip] BLOB大小超出范围: 16 字节
[SQLite Sample 151] girf_sqlite3_bind_blob 调用 (第1510次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 4, 数据大小: 16 字节
[SQLite Skip] BLOB大小超出范围: 16 字节
[SQLite Sample 152] girf_sqlite3_bind_blob 调用 (第1520次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 153] girf_sqlite3_bind_blob 调用 (第1530次)
[SQLite Params] 语句: 0x7f52c97288, 参数索引: 40, 数据大小: 1366824000 字节
[SQLite Skip] BLOB大小超出范围: 1366824000 字节
[SQLite Sample 154] girf_sqlite3_bind_blob 调用 (第1540次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 16, 数据大小: 1366817512 字节
[SQLite Skip] BLOB大小超出范围: 1366817512 字节
[SQLite Sample 155] girf_sqlite3_bind_blob 调用 (第1550次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 1008, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 156] girf_sqlite3_bind_blob 调用 (第1560次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 280, 数据大小: 1617404844 字节
[SQLite Skip] BLOB大小超出范围: 1617404844 字节
[SQLite Sample 157] girf_sqlite3_bind_blob 调用 (第1570次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 141 字节
[SQLite Error] 参数分析失败: Error: access violation accessing 0x1
[SQLite Sample 158] girf_sqlite3_bind_blob 调用 (第1580次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 76, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 159] girf_sqlite3_bind_blob 调用 (第1590次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 296, 数据大小: 1617708567 字节
[SQLite Skip] BLOB大小超出范围: 1617708567 字节
[SQLite Sample 160] girf_sqlite3_bind_blob 调用 (第1600次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 16 字节
[SQLite Skip] BLOB大小超出范围: 16 字节
[SQLite Sample 161] girf_sqlite3_bind_blob 调用 (第1610次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 32, 数据大小: 141 字节
[SQLite Error] 参数分析失败: Error: access violation accessing 0x1
[SQLite Sample 162] girf_sqlite3_bind_blob 调用 (第1620次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 648, 数据大小: 1 字节
[SQLite Skip] BLOB大小超出范围: 1 字节
[SQLite Sample 163] girf_sqlite3_bind_blob 调用 (第1630次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 120, 数据大小: 1081152904 字节
[SQLite Skip] BLOB大小超出范围: 1081152904 字节
[SQLite Sample 164] girf_sqlite3_bind_blob 调用 (第1640次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 16, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节
[SQLite Sample 165] girf_sqlite3_bind_blob 调用 (第1650次)
[SQLite Params] 语句: 0x7f3afb6688, 参数索引: 456, 数据大小: 0 字节
[SQLite Skip] BLOB大小超出范围: 0 字节

=== SQLite BLOB数据提取报告 ===
总调用次数: 1652
采样次数: 165
发现BLOB: 0 次
成功提取: 0 个样本
==================================


[Remote::com.autonavi.minimap]-> 
[Remote::com.autonavi.minimap]-> exit

Thank you for using Frida!
