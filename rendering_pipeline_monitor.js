/*
 * 高德地图渲染管道监控器
 * 专注于追踪数据加载到渲染的完整流程，解决白屏问题
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[Rendering Pipeline Monitor] 启动渲染管道监控器...");
    
    var CONFIG = {
        INIT_DELAY: 3000,
        MONITOR_FILE_IO: true,
        MONITOR_RENDERING: true,
        MONITOR_MEMORY_ALLOC: true,
        LOG_DATA_FLOW: true
    };
    
    var pipelineResults = {
        fileOperations: [],
        dataProcessing: [],
        memoryAllocations: [],
        renderCalls: [],
        totalEvents: 0
    };
    
    // === 文件I/O监控 ===
    function setupFileIOMonitoring() {
        console.log("[File Monitor] 设置文件I/O监控...");
        
        try {
            // Hook open() 系统调用
            var openPtr = Module.getExportByName("libc.so", "open");
            if (openPtr) {
                Interceptor.attach(openPtr, {
                    onEnter: function(args) {
                        this.filename = args[0].readUtf8String();
                    },
                    onLeave: function(retval) {
                        if (this.filename && this.filename.indexOf('.ans') !== -1) {
                            console.log("[File IO] 打开ANS文件: " + this.filename);
                            pipelineResults.fileOperations.push({
                                operation: "open",
                                filename: this.filename,
                                fd: retval.toInt32(),
                                timestamp: Date.now()
                            });
                            pipelineResults.totalEvents++;
                        }
                    }
                });
            }
            
            // Hook read() 系统调用
            var readPtr = Module.getExportByName("libc.so", "read");
            if (readPtr) {
                Interceptor.attach(readPtr, {
                    onEnter: function(args) {
                        this.fd = args[0].toInt32();
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                    },
                    onLeave: function(retval) {
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 0 && this.size > 1000) { // 过滤大文件读取
                            console.log("[File IO] 读取数据: FD=" + this.fd + ", 大小=" + bytesRead + "字节");
                            
                            // 检查是否为地图数据
                            try {
                                var header = this.buffer.readUtf8String(8);
                                if (header && (header.indexOf('ANS') !== -1 || header.indexOf('DICE') !== -1)) {
                                    console.log("[Map Data] 发现地图数据: " + header);
                                    pipelineResults.fileOperations.push({
                                        operation: "read_map_data",
                                        fd: this.fd,
                                        size: bytesRead,
                                        header: header,
                                        timestamp: Date.now()
                                    });
                                }
                            } catch (e) {
                                // 继续监控
                            }
                        }
                    }
                });
            }
            
            console.log("[File Monitor] 文件I/O监控设置完成");
            
        } catch (e) {
            console.log("[Error] 文件监控设置失败: " + e);
        }
    }
    
    // === 内存分配监控 ===
    function setupMemoryAllocationMonitoring() {
        console.log("[Memory Monitor] 设置内存分配监控...");
        
        try {
            // Hook malloc
            var mallocPtr = Module.getExportByName("libc.so", "malloc");
            if (mallocPtr) {
                Interceptor.attach(mallocPtr, {
                    onEnter: function(args) {
                        this.size = args[0].toInt32();
                    },
                    onLeave: function(retval) {
                        if (this.size > 10000) { // 只关注大内存分配
                            console.log("[Memory] 大内存分配: " + this.size + "字节 @ " + retval);
                            pipelineResults.memoryAllocations.push({
                                operation: "malloc",
                                size: this.size,
                                address: retval,
                                timestamp: Date.now()
                            });
                        }
                    }
                });
            }
            
            console.log("[Memory Monitor] 内存监控设置完成");
            
        } catch (e) {
            console.log("[Error] 内存监控设置失败: " + e);
        }
    }
    
    // === 渲染调用监控 ===
    function setupRenderingMonitoring() {
        console.log("[Render Monitor] 设置渲染监控...");
        
        try {
            // Hook OpenGL ES 调用
            var glDrawElementsPtr = Module.findExportByName("libGLESv2.so", "glDrawElements");
            if (glDrawElementsPtr) {
                Interceptor.attach(glDrawElementsPtr, {
                    onEnter: function(args) {
                        this.mode = args[0].toInt32();
                        this.count = args[1].toInt32();
                    },
                    onLeave: function(retval) {
                        if (this.count > 100) { // 过滤小的绘制调用
                            console.log("[Render] glDrawElements: 模式=" + this.mode + ", 顶点数=" + this.count);
                            pipelineResults.renderCalls.push({
                                function: "glDrawElements",
                                mode: this.mode,
                                count: this.count,
                                timestamp: Date.now()
                            });
                        }
                    }
                });
            }
            
            // Hook glBufferData
            var glBufferDataPtr = Module.findExportByName("libGLESv2.so", "glBufferData");
            if (glBufferDataPtr) {
                Interceptor.attach(glBufferDataPtr, {
                    onEnter: function(args) {
                        this.target = args[0].toInt32();
                        this.size = args[1].toInt32();
                        this.data = args[2];
                    },
                    onLeave: function(retval) {
                        if (this.size > 1000) {
                            console.log("[Render] glBufferData: 目标=" + this.target + ", 大小=" + this.size + "字节");
                            
                            // 尝试分析缓冲区数据
                            if (this.data && !this.data.isNull()) {
                                analyzeBufferData(this.data, this.size);
                            }
                        }
                    }
                });
            }
            
            console.log("[Render Monitor] 渲染监控设置完成");
            
        } catch (e) {
            console.log("[Error] 渲染监控设置失败: " + e);
        }
    }
    
    // === 数据处理函数增强监控 ===
    function setupEnhancedDataProcessingMonitoring(libBase) {
        console.log("[Enhanced Monitor] 设置增强数据处理监控...");
        
        try {
            // Hook sub_5C394 (数据调度器) - 更详细的监控
            var sub5C394 = libBase.add(0x5C394);
            Interceptor.attach(sub5C394, {
                onEnter: function(args) {
                    this.startTime = Date.now();
                    console.log("[sub_5C394] 数据调度器调用 - 详细分析");
                    
                    // 详细分析所有参数
                    for (var i = 0; i < 5; i++) {
                        try {
                            if (args[i] && !args[i].isNull()) {
                                console.log("[Param " + i + "] 地址: " + args[i] + ", 值: " + args[i].readU32());
                                
                                // 尝试读取指针指向的数据
                                try {
                                    var ptrData = args[i].readPointer();
                                    if (ptrData && !ptrData.isNull()) {
                                        console.log("[Param " + i + "] 指向: " + ptrData);
                                        var headerBytes = ptrData.readByteArray(16);
                                        console.log("[Param " + i + "] 头部:");
                                        console.log(hexdump(headerBytes, {length: 16, ansi: false}));
                                    }
                                } catch (e) {
                                    // 不是指针或无法访问
                                }
                            }
                        } catch (e) {
                            console.log("[Param " + i + "] 读取失败: " + e);
                        }
                    }
                },
                
                onLeave: function(retval) {
                    var duration = Date.now() - this.startTime;
                    var returnCode = retval.toInt32();
                    console.log("[sub_5C394] 调度器完成, 耗时: " + duration + "ms, 返回码: " + returnCode);
                    
                    pipelineResults.dataProcessing.push({
                        function: "sub_5C394",
                        duration: duration,
                        returnCode: returnCode,
                        timestamp: Date.now()
                    });
                    
                    pipelineResults.totalEvents++;
                }
            });
            
            // Hook sub_10F88 (数据解析器) - 增强版
            var sub10F88 = libBase.add(0x10F88);
            Interceptor.attach(sub10F88, {
                onEnter: function(args) {
                    this.startTime = Date.now();
                    console.log("[sub_10F88] 数据解析器调用 - 增强分析");
                    
                    // 保存输入数据进行分析
                    this.inputData = [];
                    for (var j = 0; j < 3; j++) {
                        if (args[j] && !args[j].isNull()) {
                            try {
                                var data = args[j].readByteArray(32);
                                this.inputData.push(data);
                                console.log("[Input " + j + "] 输入数据:");
                                console.log(hexdump(data, {length: 32, ansi: false}));
                            } catch (e) {
                                this.inputData.push(null);
                            }
                        }
                    }
                },
                
                onLeave: function(retval) {
                    var duration = Date.now() - this.startTime;
                    var returnCode = retval.toInt32();
                    console.log("[sub_10F88] 解析器完成, 耗时: " + duration + "ms, 返回码: " + returnCode);
                    
                    // 如果解析成功，立即进行内存扫描
                    if (returnCode === 0) {
                        console.log("[Parse Success] 解析成功，立即扫描新数据...");
                        setTimeout(function() {
                            scanForNewData();
                        }, 50); // 更短的延迟
                    }
                    
                    pipelineResults.dataProcessing.push({
                        function: "sub_10F88",
                        duration: duration,
                        returnCode: returnCode,
                        inputDataCount: this.inputData.length,
                        timestamp: Date.now()
                    });
                }
            });
            
            console.log("[Enhanced Monitor] 增强监控设置完成");
            
        } catch (e) {
            console.log("[Error] 增强监控设置失败: " + e);
        }
    }
    
    // === 分析缓冲区数据 ===
    function analyzeBufferData(dataPtr, size) {
        try {
            var sampleSize = Math.min(size, 64);
            var data = dataPtr.readByteArray(sampleSize);
            var view = new Float32Array(data);
            
            var hasValidCoords = false;
            for (var i = 0; i < view.length; i++) {
                var val = view[i];
                if (isFinite(val) && val >= -1000 && val <= 1000 && Math.abs(val) > 0.1) {
                    hasValidCoords = true;
                    break;
                }
            }
            
            if (hasValidCoords) {
                console.log("[Buffer Analysis] 发现疑似坐标数据:");
                var coords = [];
                for (var j = 0; j < Math.min(4, view.length); j++) {
                    coords.push(view[j].toFixed(6));
                }
                console.log("[Buffer Coords] " + coords.join(", "));
            }
            
        } catch (e) {
            console.log("[Buffer Error] 缓冲区分析失败: " + e);
        }
    }
    
    // === 扫描新数据 ===
    function scanForNewData() {
        try {
            console.log("[Scan] 快速扫描新生成的数据...");
            
            // 更积极的内存扫描
            var ranges = Process.enumerateRanges('rw-');
            var foundNewData = false;
            
            for (var i = 0; i < Math.min(ranges.length, 10); i++) {
                var range = ranges[i];
                if (range.size < 0x1000 || range.size > 0x1000000) continue;
                
                try {
                    // 扫描最近分配的内存区域
                    var scanSize = Math.min(range.size, 0x4000);
                    for (var offset = 0; offset < scanSize; offset += 64) {
                        try {
                            var addr = range.base.add(offset);
                            var floats = [];
                            
                            for (var j = 0; j < 4; j++) {
                                var val = addr.add(j * 4).readFloat();
                                if (isFinite(val)) {
                                    floats.push(val);
                                }
                            }
                            
                            // 检查是否为新的坐标数据
                            if (floats.length === 4 && isNewCoordinatePattern(floats)) {
                                console.log("[New Data] 发现新坐标数据 @ " + addr + ": " + 
                                           floats.map(function(v) { return v.toFixed(3); }).join(", "));
                                foundNewData = true;
                            }
                            
                        } catch (e) {
                            // 继续扫描
                        }
                    }
                } catch (e) {
                    // 继续下一个范围
                }
                
                if (foundNewData) break;
            }
            
            if (!foundNewData) {
                console.log("[Scan] 未发现新的坐标数据");
            }
            
        } catch (e) {
            console.log("[Scan Error] 数据扫描失败: " + e);
        }
    }
    
    // === 辅助函数 ===
    function isNewCoordinatePattern(floats) {
        // 检查是否为有效的坐标模式
        var validCount = 0;
        for (var i = 0; i < floats.length; i++) {
            var val = floats[i];
            if (Math.abs(val) >= 0.1 && Math.abs(val) <= 10000) {
                validCount++;
            }
        }
        return validCount >= 2;
    }
    
    function waitForLibrary(libraryName, callback) {
        var maxAttempts = 30;
        var attempt = 0;
        
        function checkLibrary() {
            try {
                var lib = Module.findBaseAddress(libraryName);
                if (lib) {
                    console.log("[Library] " + libraryName + " 已加载，基址: " + lib);
                    callback(lib);
                    return;
                }
            } catch (e) {
                // 继续等待
            }
            
            attempt++;
            if (attempt < maxAttempts) {
                setTimeout(checkLibrary, 1000);
            } else {
                console.log("[Error] " + libraryName + " 加载超时");
            }
        }
        
        checkLibrary();
    }
    
    // === 管道状态报告 ===
    function generatePipelineReport() {
        console.log("\n=== 渲染管道监控报告 ===");
        console.log("文件操作: " + pipelineResults.fileOperations.length);
        console.log("数据处理: " + pipelineResults.dataProcessing.length);
        console.log("内存分配: " + pipelineResults.memoryAllocations.length);
        console.log("渲染调用: " + pipelineResults.renderCalls.length);
        console.log("总事件数: " + pipelineResults.totalEvents);
        
        // 分析管道瓶颈
        if (pipelineResults.fileOperations.length === 0) {
            console.log("\n⚠️  没有文件读取活动 - 可能数据已缓存");
        }
        
        if (pipelineResults.dataProcessing.length === 0) {
            console.log("\n⚠️  没有数据处理活动 - 可能手势未触发数据加载");
        }
        
        if (pipelineResults.renderCalls.length === 0) {
            console.log("\n⚠️  没有渲染调用 - 可能渲染管道堵塞");
        } else {
            console.log("\n✅ 渲染管道正常工作");
        }
        
        console.log("==================================\n");
    }
    
    // === 主入口 ===
    function main() {
        console.log("[Main] 等待应用初始化完成...");
        
        setTimeout(function() {
            console.log("[Main] 开始渲染管道监控...");
            
            try {
                // 设置各种监控
                setupFileIOMonitoring();
                setupMemoryAllocationMonitoring();
                setupRenderingMonitoring();
                
                // 等待并Hook库函数
                waitForLibrary("libamapnsq.so", function(libBase) {
                    setupEnhancedDataProcessingMonitoring(libBase);
                    
                    // 定期生成报告
                    setInterval(function() {
                        if (pipelineResults.totalEvents > 0) {
                            generatePipelineReport();
                        }
                    }, 20000);
                });
                
                console.log("[Rendering Pipeline Monitor] 渲染管道监控已启动!");
                console.log("现在移动地图到新区域，观察完整的数据流...");
                
            } catch (e) {
                console.log("[Error] 管道监控初始化失败: " + e);
            }
        }, CONFIG.INIT_DELAY);
    }
    
    // === 启动监控 ===
    try {
        Java.perform(function() {
            console.log("[Java] Java环境已准备就绪");
            main();
        });
    } catch (e) {
        console.log("[Error] Java环境初始化失败: " + e);
        main();
    }
    
})(); 