// 高德地图GPS模拟器积分破解脚本 v2
// 功能：修改积分值，使应用认为用户有足够积分
Java.perform(function() {
    console.log("\n[+] 积分破解脚本已启动...");

    // 定位用户模型类
    try {
        var GpsUserModel = Java.use("tgo.ngo.mockgps.model.app.GpsUserModel");
        console.log("[+] 已找到用户模型类: GpsUserModel");
        
        // 1. 修改积分值 - 针对性修改getCount()方法
        GpsUserModel.getCount.implementation = function() {
            var original = this.getCount();
            console.log("[*] 拦截到积分查询，原始积分: " + original);
            
            // 返回大量积分
            var fakePoints = 99999;
            console.log("[!] 已修改积分值: " + original + " -> " + fakePoints);
            return fakePoints;
        };
        
        // 2. 修改积分不足的提示信息，防止其他检查
        GpsUserModel.getNotCanUseMag.implementation = function() {
            var original = this.getNotCanUseMag();
            console.log("[*] 拦截到积分不足提示信息: " + original);
            
            // 返回空字符串，防止显示积分不足提示
            console.log("[!] 已清空积分不足提示");
            return "";
        };
        
        // 3. 修改积分可用状态判断
        if (GpsUserModel.getCanUse) {
            GpsUserModel.getCanUse.implementation = function() {
                var original = this.getCanUse();
                console.log("[*] 拦截到积分可用状态检查，原始状态: " + original);
                
                // 强制返回true，表示积分可用
                console.log("[!] 已修改积分可用状态为: true");
                return true;
            };
        }

        console.log("[+] 已成功钩住所有积分相关方法");
        
    } catch(e) {
        console.log("[!] 钩住积分相关方法时出错: " + e);
    }
    
    // 4. 尝试钩住MainActivity中可能与积分检查相关的方法
    try {
        var MainActivity = Java.use("tgo.ngo.mockgps.ui.MainActivity");
        
        // startMock方法可能包含积分检查逻辑
        MainActivity.startMock.implementation = function() {
            console.log("[*] 拦截到启动模拟位置方法");
            console.log("[+] 正在绕过启动前的积分检查...");
            
            // 调用原方法，由于我们已修改了积分值，应该能正常启动
            return this.startMock();
        };
        
    } catch(e) {
        console.log("[!] 钩住MainActivity方法时出错: " + e);
    }

    console.log("[*] 积分破解脚本加载完成，请尝试使用模拟定位功能");
    console.log("[*] 如果还提示积分不足，请截图日志反馈给作者进一步分析");
});
