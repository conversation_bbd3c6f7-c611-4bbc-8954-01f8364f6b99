/*
app运行正常，没有hook到方法
Spawning `com.autonavi.minimap`...
[+] 高德ANS文件轻量级分析脚本启动
[+] 轻量级分析脚本设置完成
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [打开文件] /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a78/m1.ans
[文件打开成功] m1.ans: fd=123
[+] 轻量级Java分析
[Hook成功] init4WarmStart
[Hook成功] nativeInit4WarmStart
[+] 开始轻量级分析
[库] 分析 libamapnsq.so
*/

(function() {
  console.log("[+] 高德ANS文件轻量级分析脚本启动");
  
  // 关键参数配置
  var targetFiles = ["m1.ans", "m3.ans"];
  var ansFileInfo = {};
  var isAnalyzing = false; // 防止重复分析
  var hookCount = 0;  // 限制Hook数量
  
  // 优化1: 轻量级mmap监控 - 只关注ANS文件
  var mmap_ptr = Module.findExportByName("libc.so", "mmap");
  if (mmap_ptr) {
    Interceptor.attach(mmap_ptr, {
      onEnter: function(args) {
        this.size = args[1].toInt32();
        this.fd = args[4].toInt32();
      },
      onLeave: function(retval) {
        if (this.fd > 0) {
          try {
            var fdPath = "/proc/self/fd/" + this.fd;
            var filePath = new File(fdPath).readlink();
            
            // 只关注m1.ans和m3.ans文件
            if (filePath) {
              for (var i = 0; i < targetFiles.length; i++) {
                if (filePath.indexOf(targetFiles[i]) !== -1) {
                  console.log("[ANS映射] " + targetFiles[i] + " 被映射: " + filePath);
                  console.log("  大小: " + this.size + " 字节");
                  console.log("  内存地址: " + retval);
                  
                  // 保存文件信息
                  ansFileInfo[targetFiles[i]] = {
                    path: filePath,
                    addr: retval,
                    size: this.size,
                    mapped: true
                  };
                  
                  // 只分析文件头，减少处理量
                  try {
                    console.log("[文件头] " + targetFiles[i] + " 前32字节:");
                    console.log(hexdump(retval, {length: 32}));
                  } catch(e) {}
                  break;
                }
              }
            }
          } catch(e) {}
        }
      }
    });
  }
  
  // 优化2: 轻量级文件打开监控
  var open_ptr = Module.findExportByName("libc.so", "open");
  if (open_ptr) {
    Interceptor.attach(open_ptr, {
      onEnter: function(args) {
        try {
          var path = args[0].readUtf8String();
          for (var i = 0; i < targetFiles.length; i++) {
            if (path && path.indexOf(targetFiles[i]) !== -1) {
              console.log("[打开文件] " + path);
              this.path = path;
              this.targetFile = targetFiles[i];
              break;
            }
          }
        } catch(e) {}
      },
      onLeave: function(retval) {
        if (this.path && retval.toInt32() > 0) {
          console.log("[文件打开成功] " + this.targetFile + ": fd=" + retval.toInt32());
          // 启动延迟分析，给应用足够时间启动
          if (!isAnalyzing) {
            isAnalyzing = true;
            setTimeout(analyzeNativeLibrary, 10000); // 延迟10秒启动分析
          }
        }
      }
    });
  }

  // 优化3: 针对性分析，而不是扫描整个内存
  function analyzeNativeLibrary() {
    console.log("[+] 开始轻量级分析");
    Process.enumerateModules({
      onMatch: function(module) {
        if (module.name.indexOf("libamapnsq.so") !== -1 || 
            module.name.indexOf("libgdamapv4sdk.so") !== -1) {
          console.log("[库] 分析 " + module.name);
          
          // 只查找少量关键导出函数
          var keyFunctions = [];
          module.enumerateExports({
            onMatch: function(exp) {
              if (hookCount >= 5) return; // 限制最多Hook 5个函数
              
              if (exp.name.indexOf("parse") !== -1 || 
                  exp.name.indexOf("loadAns") !== -1 || 
                  exp.name.indexOf("initMap") !== -1) {
                console.log("[函数] " + module.name + "!" + exp.name);
                keyFunctions.push(exp);
                hookCount++;
              }
            },
            onComplete: function() {
              // 只Hook少量函数
              for (var i = 0; i < Math.min(keyFunctions.length, 3); i++) {
                var exp = keyFunctions[i];
                try {
                  Interceptor.attach(exp.address, {
                    onEnter: function(args) {
                      this.fname = exp.name;
                      console.log("[调用] " + module.name + "!" + this.fname);
                    },
                    onLeave: function(retval) {
                      console.log("[返回] " + module.name + "!" + this.fname + " -> " + retval);
                    }
                  });
                } catch(e) {}
              }
            }
          });
        }
      },
      onComplete: function() {}
    });
  }

  // 优化4: 限制Java Hook数量
  setTimeout(function() {
    Java.perform(function() {
      console.log("[+] 轻量级Java分析");
      
      // 只关注核心类
      var targetClass = "com.autonavi.jni.ajx3.bl.AjxBLFactoryController";
      try {
        var clazz = Java.use(targetClass);
        
        // 只Hook最关键的方法
        var methodsToHook = ["init4WarmStart", "nativeInit4WarmStart"];
        
        for (var i = 0; i < methodsToHook.length; i++) {
          var methodName = methodsToHook[i];
          try {
            // 直接内联实现，避免复杂闭包
            var method = clazz[methodName];
            if (method && method.overloads) {
              method.overloads[0].implementation = function() {
                console.log("[调用] " + targetClass + "." + methodName + "()");
                var result = this[methodName].apply(this, arguments);
                console.log("[返回] " + targetClass + "." + methodName + "()");
                return result;
              };
              console.log("[Hook成功] " + methodName);
            }
          } catch(e) {
            console.log("[Hook失败] " + methodName + ": " + e);
          }
        }
      } catch(e) {
        console.log("[类加载失败] " + targetClass);
      }
    });
  }, 5000);
  
  console.log("[+] 轻量级分析脚本设置完成");
})();
