/*
 * 高德地图 离线数据解析输出器（基于内存首读者判定）
 *
 * 目标：当 zlib 解压后的数据块被 libamapnsq.so 首次读取时，直接在控制台输出该数据块的结构化解析信息。
 *
 * 方法：
 *  1) Hook libz.so:uncompress，获取解压后缓冲区指针和大小；
 *  2) 对该内存范围启用只读 MemoryAccessMonitor；
 *  3) 首次读取者若属于 libamapnsq.so，则解析并输出该数据块：
 *     - 头部识别（DICE-AM / bc bc bc bc / ce ca 0b b1 / 其他）
 *     - 若为 bc/ce 类型：解析 version(4..6), record_count(8..12), header_size(12..16)
 *     - 遍历前若干条变长记录：[4字节长度] + [记录数据]，输出每条记录长度与十六进制预览
 *
 * 语法：ES5（兼容 Frida 12.9.7），仅输出中文 ASCII 文本，无特殊表情字符。
 */

console.log("[启动] 离线数据解析输出器 已启动");

var monitoring = false;
var monitorTimer = null;
var curBuf = null;
var curSize = 0;

// ========== 内存读取安全封装 ==========
function getReadableLimit(ptr, desiredLen) {
	try {
		var r = Process.findRangeByAddress(ptr);
		if (!r) return 0;
		var offsetInRange = ptr.sub(r.base).toInt32();
		var remain = r.size - offsetInRange;
		if (remain <= 0) return 0;
		return Math.max(0, Math.min(remain, desiredLen));
	} catch (e) {
		return 0;
	}
}

function safeReadByteArray(ptr, desiredLen) {
	var len = getReadableLimit(ptr, desiredLen);
	if (len <= 0) return null;
	try { return ptr.readByteArray(len); } catch (e) { return null; }
}

function hexPreview(ptr, size, limit) {
	var lim = Math.min(size, limit || 64);
	var arr = safeReadByteArray(ptr, lim);
	if (!arr) return "<预览不可用>";
	try {
		var bytes = new Uint8Array(arr);
		var hex = "";
		for (var i = 0; i < bytes.length; i++) {
			var b = bytes[i].toString(16);
			if (b.length === 1) b = "0" + b;
			hex += b + (i + 1 < bytes.length ? " " : "");
		}
		return hex;
	} catch (e) {
		return "<预览不可用>";
	}
}

function hexPreviewAt(basePtr, totalSize, off, before, after) {
	var start = Math.max(0, off - (before || 16));
	var end = Math.min(totalSize, off + (after || 16));
	var p = basePtr.add(start);
	var arr = safeReadByteArray(p, end - start);
	if (!arr) return "<预览不可用>";
	var bytes = new Uint8Array(arr);
	var hex = "";
	for (var i = 0; i < bytes.length; i++) {
		var b = bytes[i].toString(16);
		if (b.length === 1) b = "0" + b;
		hex += b + (i + 1 < bytes.length ? " " : "");
	}
	return hex;
}

function readU32(ptr, off) {
	return ptr.add(off).readU32();
}
function readU16(ptr, off) {
	return ptr.add(off).readU16();
}
function readU8(ptr, off) {
	return ptr.add(off).readU8();
}

// ========== UTF-8中文子串提取 ==========
function extractUtf8Chinese(ptr, totalSize, scanLimitBytes) {
	var lim = Math.min(totalSize, scanLimitBytes || 4096);
	var arr = safeReadByteArray(ptr, lim);
	if (!arr) {
		console.log("[文本] 读取失败，跳过");
		return;
	}
	var bytes = new Uint8Array(arr);
	var res = [];
	var i = 0;
	while (i < bytes.length) {
		var start = i;
		var s = "";
		var count = 0;
		while (i < bytes.length) {
			var b = bytes[i];
			if ((b & 0xf0) === 0xe0 && i + 2 < bytes.length) {
				var b1 = bytes[i + 1], b2 = bytes[i + 2];
				if ((b1 & 0xc0) === 0x80 && (b2 & 0xc0) === 0x80) {
					s += String.fromCharCode(((b & 0x0f) << 12) | ((b1 & 0x3f) << 6) | (b2 & 0x3f));
					i += 3;
					count++;
					continue;
				}
			}
			break;
		}
		if (count >= 3) {
			res.push({ off: start, text: s.substr(0, 64), end: i });
		}
		i = (count > 0 ? i : i + 1);
	}
	if (res.length > 0) {
		console.log("[文本] 发现中文子串条数=" + res.length + " (展示前3条)");
		for (var t = 0; t < Math.min(3, res.length); t++) {
			console.log("[文本] 偏移=" + res[t].off + ", 内容=" + res[t].text);
		}
	} else {
		console.log("[文本] 未检出连续中文子串");
	}
}

// 0x25xx 专用：中文分组与上下文
function extractChineseGroupsWithContext(basePtr, totalSize, scanLimitBytes) {
	var lim = Math.min(totalSize, scanLimitBytes || totalSize);
	var arr = safeReadByteArray(basePtr, lim);
	if (!arr) {
		console.log("[文本] 读取失败，跳过");
		return;
	}
	var bytes = new Uint8Array(arr);
	var groups = [];
	var i = 0;
	while (i < bytes.length) {
		// 寻找一个中文串
		if ((bytes[i] & 0xf0) === 0xe0 && i + 2 < bytes.length && (bytes[i+1] & 0xc0) === 0x80 && (bytes[i+2] & 0xc0) === 0x80) {
			var start = i;
			var s = "";
			while (i + 2 < bytes.length) {
				var b = bytes[i], b1 = bytes[i+1], b2 = bytes[i+2];
				if ((b & 0xf0) === 0xe0 && (b1 & 0xc0) === 0x80 && (b2 & 0xc0) === 0x80) {
					s += String.fromCharCode(((b & 0x0f) << 12) | ((b1 & 0x3f) << 6) | (b2 & 0x3f));
					i += 3;
				} else {
					break;
				}
			}
			// 合并紧邻中文组（中间穿插<=6字节的非中文，如空字节/少量标点）
			var mergeEnd = i;
			var j = i;
			while (j < bytes.length) {
				var gapStart = j;
				var gap = 0;
				while (gap < 6 && j < bytes.length && !(((bytes[j] & 0xf0) === 0xe0) && j + 2 < bytes.length && (bytes[j+1] & 0xc0) === 0x80 && (bytes[j+2] & 0xc0) === 0x80)) {
					j++; gap++;
				}
				if (j + 2 < bytes.length && (bytes[j] & 0xf0) === 0xe0 && (bytes[j+1] & 0xc0) === 0x80 && (bytes[j+2] & 0xc0) === 0x80) {
					// 新中文组，继续吸收
					while (j + 2 < bytes.length && (bytes[j] & 0xf0) === 0xe0 && (bytes[j+1] & 0xc0) === 0x80 && (bytes[j+2] & 0xc0) === 0x80) {
						s += String.fromCharCode(((bytes[j] & 0x0f) << 12) | ((bytes[j+1] & 0x3f) << 6) | (bytes[j+2] & 0x3f));
						j += 3;
					}
					mergeEnd = j;
					i = j;
				} else {
					break;
				}
			}
			if (s.length >= 3) {
				groups.push({ off: start, end: mergeEnd, text: s.substr(0, 120) });
			}
		} else {
			i++;
		}
	}
	if (groups.length > 0) {
		console.log("[文本] 分组条数=" + groups.length + " (展示前5组，含上下文)");
		for (var g = 0; g < Math.min(5, groups.length); g++) {
			var gr = groups[g];
			var ctx = hexPreviewAt(basePtr, totalSize, gr.off, 24, 24);
			console.log("[文本] 组#" + (g+1) + " 偏移=" + gr.off + " 长度=" + (gr.end - gr.off) + " 内容=" + gr.text);
			console.log("[上下文] 前后(24B)=" + ctx);
		}
	} else {
		console.log("[文本] 未发现中文分组");
	}
}

// ========== 新增：未知类型启发式分析 ==========
function printLeadingDwords(ptr, totalSize, count) {
	var n = Math.min(count, Math.floor(totalSize / 4));
	var out = [];
	for (var i = 0; i < n; i++) {
		try { out.push("0x" + readU32(ptr, i * 4).toString(16)); } catch (e) { break; }
	}
	console.log("[未知] 开头DWORD(" + out.length + ")=" + out.join(", "));
}

function tryParseVarLenFrom(ptr, totalSize, startOff, maxRecords) {
	var off = startOff;
	var records = [];
	for (var i = 0; i < maxRecords; i++) {
		if (off + 4 > totalSize) break;
		var len = 0;
		try { len = ptr.add(off).readU32(); } catch (e) { break; }
		if (len <= 0) break;
		var end = off + 4 + len;
		if (end > totalSize) break;
		records.push({ off: off, len: len });
		off = end;
	}
	return { list: records, endOff: off };
}

function dumpU16(ptr, totalSize, count) {
	var n = Math.min(count, Math.floor(totalSize / 2));
	var arr = [];
	for (var i = 0; i < n; i++) {
		try { arr.push(readU16(ptr, i * 2)); } catch (e) { break; }
	}
	console.log("[u16] 前" + arr.length + "项=" + arr.join(","));
}

function analyzeUnknownFormat(ptr, totalSize) {
	console.log("[类型] 未知类型，输出启发式分析");
	console.log("[未知] 前64字节: " + hexPreview(ptr, totalSize, 64));
	printLeadingDwords(ptr, totalSize, 8);

	// 候选起点：常见头部长度 0, 16, 32, 64, 80, 96, 128
	var candidates = [0, 4, 8, 12, 16, 32, 64, 80, 96, 128];
	var best = null;
	for (var ci = 0; ci < candidates.length; ci++) {
		var s = candidates[ci];
		if (s >= totalSize) continue;
		var parsed = tryParseVarLenFrom(ptr, totalSize, s, 10);
		if (parsed.list.length >= 2) {
			var used = parsed.endOff - s;
			var ratio = used / totalSize;
			if (ratio > 0.1 && ratio <= 1.0) {
				if (!best || parsed.list.length > best.list.length || (parsed.list.length === best.list.length && ratio > best.ratio)) {
					best = { start: s, list: parsed.list, ratio: ratio };
				}
			}
		}
	}
	if (best) {
		console.log("[启发] 疑似变长记录 起点偏移=" + best.start + ", 记录数=" + best.list.length + ", 覆盖比例=" + (best.ratio * 100).toFixed(1) + "%");
		var show = Math.min(best.list.length, 5);
		for (var i = 0; i < show; i++) {
			var r = best.list[i];
			var pv = hexPreview(ptr.add(r.off + 4), r.len, 32);
			console.log("[记录] 起始=" + r.off + ", 长度=" + r.len + ", 预览(32B)=" + pv);
		}
	} else {
		console.log("[启发] 未能稳定识别变长记录结构");
	}
}
// ========== 新增：0x05/0x0d 专用解析 ==========
function parseAscendingU16Table(ptr, totalSize, startOff, maxCount) {
	var table = [];
	var off = startOff;
	var last = -1;
	for (var i = 0; i < maxCount; i++) {
		if (off + 2 > totalSize) break;
		var v = 0;
		try { v = readU16(ptr, off); } catch (e) { break; }
		// 要求非递减且非零（零常作为终止或占位）
		if (v === 0) break;
		if (last >= 0 && v < last) break;
		table.push({ off: off, val: v });
		last = v;
		off += 2;
	}
	return table;
}

function parseType05Block(ptr, totalSize) {
	// 经验：头部 4~12 字节后常见连续上升的 u16 序列（疑似偏移表或采样表）
	console.log("[05] 尝试解析疑似偏移表");
	var candidates = [12, 8, 4, 16, 20, 24, 32];
	var best = null;
	for (var i = 0; i < candidates.length; i++) {
		var s = candidates[i];
		if (s >= totalSize) continue;
		var t = parseAscendingU16Table(ptr, totalSize, s, 512);
		if (t.length >= 8) {
			if (!best || t.length > best.length) best = t;
		}
	}
	if (best) {
		console.log("[05] 偏移表项数=" + best.length + " 起点偏移=" + best[0].off);
		var show = Math.min(16, best.length);
		var vals = [];
		for (var k = 0; k < show; k++) vals.push(best[k].val);
		console.log("[05] 前" + show + "项(u16)=" + vals.join(","));
		// 估算段落区间（相邻差值）
		var segments = [];
		for (var k2 = 1; k2 < best.length; k2++) {
			segments.push(best[k2].val - best[k2 - 1].val);
		}
		if (segments.length > 0) {
			var showSeg = segments.slice(0, Math.min(16, segments.length));
			console.log("[05] 相邻差值(前" + showSeg.length + ")=" + showSeg.join(","));
		}
		// 如果表值看似指向块内偏移，打印几处窗口预览
		var base = 0; // 有些表是相对0或相对某数据体起点，这里先按相对0
		for (var m = 0; m < Math.min(5, best.length); m++) {
			var tgt = base + best[m].val;
			if (tgt < totalSize) {
				console.log("[05] 预览@" + tgt + " (32B)=" + hexPreview(ptr.add(tgt), totalSize - tgt, 32));
			}
		}
	} else {
		console.log("[05] 未发现明显的上升 u16 表");
	}
}

function parseType0dBlock(ptr, totalSize) {
	console.log("[0d] 扩展字段打印");
	dumpU16(ptr, totalSize, 64);
}
// ========== 新增结束 ==========

function parseAndPrintBlock(basePtr, totalSize) {
	if (!basePtr || totalSize <= 0) return;
	console.log("[解析] 数据块大小=" + totalSize + " 字节");
	console.log("[解析] 头部预览(64B Hex): " + hexPreview(basePtr, totalSize, 64));

	if (totalSize < 16) {
		console.log("[解析] 数据过小，跳过。");
		return;
	}

	var b0 = readU8(basePtr, 0), b1 = readU8(basePtr, 1), b2 = readU8(basePtr, 2), b3 = readU8(basePtr, 3);
	var isDICE = (b0 === 0x44 && b1 === 0x49 && b2 === 0x43 && b3 === 0x45);
	var isBC = (b0 === 0xbc && b1 === 0xbc && b2 === 0xbc && b3 === 0xbc);
	var isCE = (b0 === 0xce && b1 === 0xca && b2 === 0x0b && b3 === 0xb1);

	if (isDICE) {
		console.log("[类型] DICE-AM 块（可能为着色器或特定矢量容器，需后续专用解析）");
		console.log("[DICE] 前64字节: " + hexPreview(basePtr, totalSize, 64));
		return;
	}

	// 0x0d 头：打印更多 u16 辅助比对
	if (b0 === 0x0d && b1 === 0x00 && b2 === 0x00 && b3 === 0x00) {
		var f1 = readU16(basePtr, 4), f2 = readU16(basePtr, 6), f3 = readU16(basePtr, 8), f4 = readU16(basePtr, 10);
		console.log("[头部] 0x0d类型字段 u16[4]=" + f1 + "," + f2 + "," + f3 + "," + f4);
		dumpU16(basePtr, totalSize, 16);
		parseType0dBlock(basePtr, totalSize);
	}

	// 0x05 头：打印前32个 u16 快速识别表结构
	if (b0 === 0x05 && b1 === 0x00 && (b2 === 0x00 || b2 === 0x00)) {
		dumpU16(basePtr, totalSize, 32);
		parseType05Block(basePtr, totalSize);
	}

	// 0x25xx：文本块，打印类型ID并提取中文分组与上下文
	if (b0 === 0x00 && b1 === 0x00 && b2 === 0x25) {
		var typeId = readU32(basePtr, 0);
		console.log("[头部] 0x25xx 类型ID=0x" + (typeId >>> 0).toString(16));
		extractChineseGroupsWithContext(basePtr, totalSize, Math.min(totalSize, 12 * 1024));
	}

	if (!(isBC || isCE)) {
		analyzeUnknownFormat(basePtr, totalSize);
		return;
	}

	// 已知 bc/ce 格式头（若出现）
	var versionMajor = readU16(basePtr, 4);
	var versionMinor = readU16(basePtr, 6);
	var recordCount = readU32(basePtr, 8);
	var headerSize = readU32(basePtr, 12);
	console.log("[头部] 魔数=" + (isBC ? "bc bc bc bc" : "ce ca 0b b1"));
	console.log("[头部] 版本=" + versionMajor + "." + versionMinor + ", 记录数=" + recordCount + ", 头部大小=" + headerSize + " 字节");

	if (headerSize < 16 || headerSize > totalSize) {
		console.log("[校验] 头部大小不合理，跳过记录解析。");
		return;
	}

	var bodySize = totalSize - headerSize;
	if (recordCount === 0 || bodySize <= 0) {
		console.log("[校验] 无记录或无数据体，结束。");
		return;
	}

	console.log("[记录] 数据体大小=" + bodySize + " 字节");
	var off = headerSize;
	var maxToShow = Math.min(recordCount, 5);
	for (var i2 = 0; i2 < maxToShow; i2++) {
		if (off + 4 > totalSize) { console.log("[记录] 偏移越界，停止。"); break; }
		var len = basePtr.add(off).readU32();
		var recStart = off + 4;
		var recEnd = recStart + len;
		if (len <= 0 || recEnd > totalSize) { console.log("[记录] 第" + (i2+1) + "条长度异常 len=" + len + "，停止。"); break; }
		var preview = hexPreview(basePtr.add(recStart), len, 32);
		console.log("[记录] 第" + (i2+1) + "条: 长度=" + len + " 字节, 预览(32B)=" + preview);
		off = recEnd;
	}
	console.log("[记录] 解析结束（仅输出前" + maxToShow + "条）。");
}

function hook_uncompress_and_monitor() {
	var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
	if (!uncompressPtr) {
		console.log("[错误] 未在 libz.so 中找到 uncompress。");
		return;
	}
	console.log("[成功] Hook libz.so:uncompress 地址=" + uncompressPtr);

	Interceptor.attach(uncompressPtr, {
		onEnter: function(args) { this.dest = args[0]; this.destLen = args[1]; },
		onLeave: function(retval) {
			if (retval.toInt32() !== 0) return;
			if (monitoring) return;
			var size = 0; try { size = this.destLen.readU32(); } catch (e) { return; }
			if (size <= 0 || size > 64 * 1024 * 1024) return;
			curBuf = this.dest; curSize = size; monitoring = true;
			console.log("\n[解压完成] 尺寸=" + size + " 字节, 预览(32B)=" + hexPreview(curBuf, curSize, 32));
			console.log("[监控] 启用只读监控，等待首个读取者...");

			MemoryAccessMonitor.enable({ base: curBuf, size: curSize }, {
				onAccess: function(details) {
					var sym = DebugSymbol.fromAddress(details.from);
					var mod = sym && sym.moduleName ? sym.moduleName : "<未知模块>";
					var modBase = Module.findBaseAddress(mod);
					var offStr = modBase ? ("0x" + ptr(details.from).sub(modBase).toString(16)) : "<未知偏移>";
					console.log("[命中] 首个读取者 模块=" + mod + ", 偏移=" + offStr + ", 符号=" + (sym && sym.name ? sym.name : "<无符号>") );
					if (mod === "libamapnsq.so") { parseAndPrintBlock(curBuf, curSize); } else { console.log("[跳过] 非 libamapnsq.so（例如 GPU/系统内存拷贝），不解析。"); }
					try { MemoryAccessMonitor.disable(); } catch (_) {}
					monitoring = false; curBuf = null; curSize = 0;
					if (monitorTimer) { try { clearTimeout(monitorTimer); } catch (_) {} monitorTimer = null; }
				}
			});

			monitorTimer = setTimeout(function() {
				if (monitoring) { try { MemoryAccessMonitor.disable(); } catch (_) {} monitoring = false; curBuf = null; curSize = 0; console.log("[监控] 超时未命中，已关闭监控。"); }
			}, 5000);
		}
	});
}

setImmediate(function() { hook_uncompress_and_monitor(); });

console.log("[就绪] 请在地图中缩放/平移以触发加载；当首个读取者为 libamapnsq.so 时会输出结构化解析。"); 