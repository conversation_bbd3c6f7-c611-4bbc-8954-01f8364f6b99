// list_exports_es5.js

// ES5 兼容的字符串右侧填充函数
// @param {string} str 原始字符串
// @param {number} length 目标长度
// @param {string} padChar 用于填充的字符
// @returns {string} 填充后的字符串
function padStringEnd(str, length, padChar) {
    // 如果原始字符串比目标长度还长，直接返回
    if (str.length >= length) {
        return str;
    }
    // 计算需要填充的长度
    var remaining = length - str.length;
    // 构建填充字符串
    var padding = '';
    while (padding.length < remaining) {
        padding += padChar;
    }
    // 返回拼接后的结果
    return str + padding.slice(0, remaining);
}


// 使用 setImmediate 确保 Frida 环境已准备好
setImmediate(function() {
    console.log("[*] Script starting (ES5 compatible)...");

    // **************************************************
    //  在这里修改你要查找的 .so 文件名
    var moduleName = "libzstd.so";
    // **************************************************

    var targetModule = Process.findModuleByName(moduleName);

    if (targetModule) {
        console.log("[+] Found module: " + moduleName + " at address: " + targetModule.base);

        var exports = targetModule.enumerateExports();

        console.log("[*] Enumerating " + exports.length + " exports...");
        console.log("--------------------------------------------------");
        // 使用辅助函数进行格式化对齐
        var headerType = padStringEnd("Type", 8, ' ');
        var headerName = padStringEnd("Name", 30, ' ');
        console.log(headerType + "| " + headerName + "| Address");
        console.log("--------------------------------------------------");

        exports.forEach(function(exp) {
            // 使用辅助函数进行格式化对齐
            var type = padStringEnd(exp.type, 7, ' ');
            var name = padStringEnd(exp.name, 30, ' ');
            console.log(type + " | " + name + "| " + exp.address);
        });

        console.log("--------------------------------------------------");
        console.log("[*] Script finished.");

    } else {
        console.error("[-] Could not find module: " + moduleName);
        console.error("[!] Make sure the library is loaded in the target process.");
    }
});

