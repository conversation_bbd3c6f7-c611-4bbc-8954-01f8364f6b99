(function() {
    console.log("[ANS流程跟踪] 启动...");

    var libamapnsq = null;
    var libamapr = null;
    var libamapmain = null;
    var callChain = [];
    var javaToNativeCalls = [];
    var lastAnsData = null;
    var hookInterval = null;
    var fileOperations = {};
    var callDepth = 0;
    var maxCallsToShow = 200; // 限制调用链显示数量

    // 阶段性执行，降低启动压力
    setTimeout(function() {
        // 持续检查库是否加载
        hookInterval = setInterval(checkAndSetupHooks, 1000);
    }, 1000);

    function checkAndSetupHooks() {
        // 检查库是否已加载
        libamapnsq = Process.findModuleByName("libamapnsq.so");
        libamapr = Process.findModuleByName("libamapr.so");
        libamapmain = Process.findModuleByName("libamapmain.so");
        
        if (!libamapnsq || !libamapr || !libamapmain) {
            console.log("[ANS流程跟踪] 等待库加载...");
            return;
        }
        
        // 所有库都已加载，清除定时器并设置钩子
        clearInterval(hookInterval);
        console.log("[ANS流程跟踪] 已找到关键库:");
        console.log("  libamapnsq.so: " + libamapnsq.base);
        console.log("  libamapr.so: " + libamapr.base);
        console.log("  libamapmain.so: " + libamapmain.base);
        
        // 设置钩子
        setupHooks();
    }

    function setupHooks() {
        try {
            // 1. 钩住Java层的关键方法
            setupJavaHooks();
            
            // 2. 钩住文件操作
            setupFileOperationHooks();
            
            // 3. 钩住ANS解析函数
            setupAnsParserHooks();
            
            // 4. 钩住渲染函数
            setupRenderHooks();
            
            // 5. 钩住JNI注册函数，捕获Java到Native的转换点
            setupJNIHooks();
            
            console.log("[ANS流程跟踪] 钩子设置完成，请操作地图...");
            
            // 定期显示调用链
            setInterval(function() {
                if (callChain.length > 0) {
                    showCallChain();
                    callChain = []; // 清空调用链
                }
                
                if (javaToNativeCalls.length > 0) {
                    showJavaToNativeCalls();
                }
            }, 10000); // 每10秒显示一次
        } catch(e) {
            console.log("[ANS流程跟踪] 设置钩子时出错: " + e);
        }
    }
    
    // 设置Java层钩子
    function setupJavaHooks() {
        try {
            // 钩住可能的手势处理类
            var gestureClasses = [
                "com.amap.api.maps.AMap",
                "com.amap.api.maps.MapView",
                "com.autonavi.ae.gmap.GLMapEngine",
                "com.autonavi.ae.gmap.gesture.GestureDetector"
            ];
            
            for (var i = 0; i < gestureClasses.length; i++) {
                var className = gestureClasses[i];
                
                Java.perform(function() {
                    try {
                        var GestureClass = Java.use(className);
                        var methods = GestureClass.class.getDeclaredMethods();
                        
                        for (var j = 0; j < methods.length; j++) {
                            var method = methods[j];
                            var methodName = method.getName();
                            
                            // 只钩住可能与手势、地图操作相关的方法
                            if (methodName.indexOf("gesture") !== -1 || 
                                methodName.indexOf("touch") !== -1 ||
                                methodName.indexOf("scroll") !== -1 ||
                                methodName.indexOf("zoom") !== -1 ||
                                methodName.indexOf("move") !== -1 ||
                                methodName.indexOf("drag") !== -1 ||
                                methodName.indexOf("render") !== -1 ||
                                methodName.indexOf("draw") !== -1 ||
                                methodName.indexOf("map") !== -1 ||
                                methodName.indexOf("load") !== -1) {
                                
                                try {
                                    GestureClass[methodName].overloads.forEach(function(overload) {
                                        overload.implementation = function() {
                                            var retval;
                                            
                                            // 记录调用
                                            var callInfo = {
                                                type: "Java",
                                                class: className,
                                                method: methodName,
                                                time: new Date().getTime()
                                            };
                                            
                                            addToCallChain(callInfo);
                                            console.log("[Java调用] " + className + "." + methodName);
                                            
                                            // 执行原始方法
                                            retval = this[methodName].apply(this, arguments);
                                            return retval;
                                        };
                                    });
                                } catch(e) {
                                    // 忽略无法钩住的方法
                                }
                            }
                        }
                    } catch(e) {
                        // 忽略不存在的类
                    }
                });
            }
            
            // 特别关注GLMapEngine的native方法
            Java.perform(function() {
                try {
                    var GLMapEngine = Java.use("com.autonavi.ae.gmap.GLMapEngine");
                    var methods = GLMapEngine.class.getDeclaredMethods();
                    
                    for (var j = 0; j < methods.length; j++) {
                        var method = methods[j];
                        var methodName = method.getName();
                        
                        // 检查是否是native方法
                        if (method.getModifiers() & 0x0100) { // 0x0100是Native标志
                            try {
                                GLMapEngine[methodName].overloads.forEach(function(overload) {
                                    overload.implementation = function() {
                                        var callInfo = {
                                            type: "Java-Native",
                                            class: "com.autonavi.ae.gmap.GLMapEngine",
                                            method: methodName,
                                            time: new Date().getTime()
                                        };
                                        
                                        // 记录Java到Native的调用
                                        javaToNativeCalls.push(callInfo);
                                        console.log("[Java→Native] GLMapEngine." + methodName);
                                        
                                        // 执行原始方法
                                        var retval = this[methodName].apply(this, arguments);
                                        return retval;
                                    };
                                });
                            } catch(e) {
                                // 忽略无法钩住的方法
                            }
                        }
                    }
                } catch(e) {
                    console.log("[ANS流程跟踪] 钩住GLMapEngine失败: " + e);
                }
            });
        } catch(e) {
            console.log("[ANS流程跟踪] 设置Java钩子失败: " + e);
        }
    }
    
    // 设置文件操作钩子
    function setupFileOperationHooks() {
        try {
            // 钩住open系统调用
            Interceptor.attach(Module.findExportByName(null, "open"), {
                onEnter: function(args) {
                    var path = args[0].readUtf8String();
                    
                    // 只关注ANS文件
                    if (path && path.indexOf(".ans") !== -1) {
                        this.path = path;
                        this.isAnsFile = true;
                        
                        var callInfo = {
                            type: "SysCall",
                            function: "open",
                            path: path,
                            time: new Date().getTime()
                        };
                        
                        addToCallChain(callInfo);
                        console.log("[文件操作] 打开ANS文件: " + path);
                    }
                },
                onLeave: function(retval) {
                    if (this.isAnsFile) {
                        var fd = retval.toInt32();
                        if (fd > 0) {
                            fileOperations[fd] = {
                                path: this.path,
                                openTime: new Date().getTime(),
                                reads: []
                            };
                            console.log("[文件操作] ANS文件打开成功，文件描述符: " + fd);
                        }
                    }
                }
            });
            
            // 钩住read系统调用
            Interceptor.attach(Module.findExportByName(null, "read"), {
                onEnter: function(args) {
                    var fd = args[0].toInt32();
                    this.fd = fd;
                    this.buffer = args[1];
                    this.size = args[2].toInt32();
                    
                    // 只关注ANS文件
                    if (fileOperations[fd]) {
                        var callInfo = {
                            type: "SysCall",
                            function: "read",
                            fd: fd,
                            size: this.size,
                            time: new Date().getTime()
                        };
                        
                        addToCallChain(callInfo);
                        console.log("[文件操作] 读取ANS文件 fd=" + fd + ", 大小=" + this.size);
                    }
                },
                onLeave: function(retval) {
                    var bytesRead = retval.toInt32();
                    
                    if (fileOperations[this.fd] && bytesRead > 0) {
                        fileOperations[this.fd].reads.push({
                            time: new Date().getTime(),
                            size: bytesRead,
                            buffer: this.buffer
                        });
                        
                        console.log("[文件操作] ANS文件读取完成，读取了 " + bytesRead + " 字节");
                        
                        // 记录最近读取的数据，供后续分析
                        try {
                            var dataBytes = Memory.readByteArray(this.buffer, Math.min(bytesRead, 16));
                            var header = new Uint8Array(dataBytes);
                            var headerHex = "";
                            
                            for (var i = 0; i < header.length; i++) {
                                headerHex += padZero(header[i].toString(16)) + " ";
                            }
                            
                            console.log("[文件操作] 数据头部: " + headerHex + "...");
                        } catch(e) {}
                    }
                }
            });
            
            // 钩住close系统调用
            Interceptor.attach(Module.findExportByName(null, "close"), {
                onEnter: function(args) {
                    var fd = args[0].toInt32();
                    this.fd = fd;
                    
                    // 只关注ANS文件
                    if (fileOperations[fd]) {
                        var callInfo = {
                            type: "SysCall",
                            function: "close",
                            fd: fd,
                            time: new Date().getTime()
                        };
                        
                        addToCallChain(callInfo);
                        console.log("[文件操作] 关闭ANS文件 fd=" + fd);
                    }
                },
                onLeave: function(retval) {
                    if (fileOperations[this.fd]) {
                        var totalRead = 0;
                        for (var i = 0; i < fileOperations[this.fd].reads.length; i++) {
                            totalRead += fileOperations[this.fd].reads[i].size;
                        }
                        
                        console.log("[文件操作] ANS文件 " + fileOperations[this.fd].path + 
                                   " 已关闭，总共读取 " + totalRead + " 字节");
                        
                        // 清理文件操作记录
                        delete fileOperations[this.fd];
                    }
                }
            });
        } catch(e) {
            console.log("[ANS流程跟踪] 设置文件操作钩子失败: " + e);
        }
    }
    
    // 设置ANS解析函数钩子
    function setupAnsParserHooks() {
        try {
            // 1. 钩住主要解析函数 sub_C654
            var parserFuncOffset = 0xC654;
            var parserFuncAddr = libamapnsq.base.add(parserFuncOffset);
            
            console.log("[ANS流程跟踪] 设置解析函数钩子: " + parserFuncAddr);
            
            Interceptor.attach(parserFuncAddr, {
                onEnter: function(args) {
                    this.srcData = args[0];
                    this.destBuffer = args[1];
                    this.sizePtr = args[2];
                    this.controlStruct = args[3];
                    this.size = args[4].toInt32();
                    
                    var callInfo = {
                        type: "Native",
                        module: "libamapnsq.so",
                        function: "sub_C654",
                        args: {
                            srcData: args[0],
                            destBuffer: args[1],
                            size: this.size
                        },
                        time: new Date().getTime()
                    };
                    
                    addToCallChain(callInfo);
                    
                    callDepth++;
                    var indent = getIndent(callDepth);
                    console.log(indent + "[ANS解析] ↓ 调用解析函数 sub_C654");
                    console.log(indent + "  源数据: " + this.srcData);
                    console.log(indent + "  目标缓冲区: " + this.destBuffer);
                    console.log(indent + "  大小: " + this.size + " 字节");
                    
                    // 记录调用栈
                    var backtrace = Thread.backtrace(this.context, Backtracer.ACCURATE);
                    console.log(indent + "  调用栈:");
                    for (var i = 0; i < Math.min(5, backtrace.length); i++) {
                        console.log(indent + "    " + i + ": " + backtrace[i]);
                    }
                },
                onLeave: function(retval) {
                    var indent = getIndent(callDepth);
                    
                    if (retval.toInt32() === 0) {
                        try {
                            if (this.sizePtr) {
                                var size = Memory.readInt(this.sizePtr);
                                lastAnsData = {
                                    buffer: this.destBuffer,
                                    size: size,
                                    timestamp: new Date().getTime()
                                };
                                
                                console.log(indent + "[ANS解析] ↑ 解析成功，解压后大小: " + size);
                                
                                // 分析解压后的数据头部
                                try {
                                    var headerBytes = Memory.readByteArray(this.destBuffer, Math.min(16, size));
                                    var header = new Uint8Array(headerBytes);
                                    var headerHex = "";
                                    
                                    for (var i = 0; i < header.length; i++) {
                                        headerHex += padZero(header[i].toString(16)) + " ";
                                    }
                                    
                                    console.log(indent + "  数据头部: " + headerHex + "...");
                                } catch(e) {
                                    console.log(indent + "  读取头部失败: " + e);
                                }
                            }
                        } catch(e) {
                            console.log(indent + "[ANS解析] ↑ 读取解析数据失败: " + e);
                        }
                    } else {
                        console.log(indent + "[ANS解析] ↑ 解析失败，返回值: " + retval);
                    }
                    
                    callDepth--;
                }
            });
            
            // 2. 钩住预处理函数 sub_C39C
            var preprocessFuncOffset = 0xC39C;
            var preprocessFuncAddr = libamapnsq.base.add(preprocessFuncOffset);
            
            console.log("[ANS流程跟踪] 设置预处理函数钩子: " + preprocessFuncAddr);
            
            Interceptor.attach(preprocessFuncAddr, {
                onEnter: function(args) {
                    var callInfo = {
                        type: "Native",
                        module: "libamapnsq.so",
                        function: "sub_C39C",
                        args: {
                            arg1: args[0],
                            arg2: args[1],
                            arg3: args[2]
                        },
                        time: new Date().getTime()
                    };
                    
                    addToCallChain(callInfo);
                    
                    callDepth++;
                    var indent = getIndent(callDepth);
                    console.log(indent + "[ANS解析] ↓ 调用预处理函数 sub_C39C");
                    console.log(indent + "  参数1: " + args[0]);
                    console.log(indent + "  参数2: " + args[1]);
                    console.log(indent + "  参数3: " + args[2]);
                },
                onLeave: function(retval) {
                    var indent = getIndent(callDepth);
                    console.log(indent + "[ANS解析] ↑ 预处理函数返回: " + retval);
                    callDepth--;
                }
            });
            
            // 3. 钩住zlib的uncompress函数
            var uncompressAddr = Module.findExportByName("libz.so", "uncompress");
            
            if (uncompressAddr) {
                console.log("[ANS流程跟踪] 设置uncompress函数钩子: " + uncompressAddr);
                
                Interceptor.attach(uncompressAddr, {
                    onEnter: function(args) {
                        this.destBuffer = args[0];
                        this.destLenPtr = args[1];
                        this.srcBuffer = args[2];
                        this.srcLen = args[3].toInt32();
                        
                        var callInfo = {
                            type: "Native",
                            module: "libz.so",
                            function: "uncompress",
                            args: {
                                destBuffer: args[0],
                                srcBuffer: args[2],
                                srcLen: this.srcLen
                            },
                            time: new Date().getTime()
                        };
                        
                        addToCallChain(callInfo);
                        
                        callDepth++;
                        var indent = getIndent(callDepth);
                        console.log(indent + "[ANS解压] ↓ 调用uncompress函数");
                        console.log(indent + "  源缓冲区: " + this.srcBuffer);
                        console.log(indent + "  源大小: " + this.srcLen + " 字节");
                        console.log(indent + "  目标缓冲区: " + this.destBuffer);
                        
                        // 检查zlib头部
                        try {
                            var headerBytes = Memory.readByteArray(this.srcBuffer, Math.min(4, this.srcLen));
                            var header = new Uint8Array(headerBytes);
                            var headerHex = "";
                            
                            for (var i = 0; i < header.length; i++) {
                                headerHex += padZero(header[i].toString(16)) + " ";
                            }
                            
                            console.log(indent + "  zlib头部: " + headerHex);
                            
                            // 检查是否是标准zlib头部 (78 9C)
                            if (header[0] === 0x78 && header[1] === 0x9C) {
                                console.log(indent + "  确认为标准zlib压缩数据");
                            }
                        } catch(e) {}
                    },
                    onLeave: function(retval) {
                        var indent = getIndent(callDepth);
                        console.log(indent + "[ANS解压] ↑ uncompress返回: " + retval);
                        
                        // zlib返回值: Z_OK(0), Z_MEM_ERROR(-4), Z_BUF_ERROR(-5), Z_DATA_ERROR(-3)
                        if (retval.toInt32() === 0) {
                            console.log(indent + "  解压成功");
                            
                            try {
                                var destLen = Memory.readUInt(this.destLenPtr);
                                console.log(indent + "  解压后大小: " + destLen + " 字节");
                            } catch(e) {}
                        } else {
                            console.log(indent + "  解压失败，错误码: " + retval);
                        }
                        
                        callDepth--;
                    }
                });
            } else {
                console.log("[ANS流程跟踪] 未找到uncompress函数");
            }
        } catch(e) {
            console.log("[ANS流程跟踪] 设置ANS解析钩子失败: " + e);
        }
    }
    
    // 设置渲染函数钩子
    function setupRenderHooks() {
        try {
            // 1. 钩住主渲染函数 (0x7f5e57e0a8)
            // 注意：这个地址每次运行可能不同，需要动态查找
            
            // 搜索可能的渲染函数特征
            var renderFuncPatterns = [
                "FF 43 01 D1 F3 0B 00 F9 FD 7B 02 A9", // 可能的文本渲染函数
                "F? 0F 1D F8 F? 07 41 F9 F? 03 00 AA", // 可能的主渲染函数
                "FF 83 00 D1 FD 7B 01 A9 FD 43 00 91"  // 通用函数序言
            ];
            
            for (var i = 0; i < renderFuncPatterns.length; i++) {
                var pattern = renderFuncPatterns[i];
                
                try {
                    // 限制搜索范围，提高性能
                    var searchRange = 0x10000; // 64KB
                    var searchStart = libamapr.base;
                    var searchEnd = searchStart.add(searchRange);
                    
                    while (searchStart.compare(libamapr.base.add(libamapr.size)) < 0) {
                        try {
                            Memory.scan(searchStart, Math.min(searchRange, libamapr.base.add(libamapr.size).sub(searchStart)), pattern, {
                                onMatch: function(address, size) {
                                    console.log("[ANS流程跟踪] 找到可能的渲染函数: " + address);
                                    
                                    // 尝试钩住这个函数
                                    try {
                                        Interceptor.attach(address, {
                                            onEnter: function(args) {
                                                var callInfo = {
                                                    type: "Native",
                                                    module: "libamapr.so",
                                                    function: "render_" + address,
                                                    args: {
                                                        arg1: args[0],
                                                        arg2: args[1]
                                                    },
                                                    time: new Date().getTime()
                                                };
                                                
                                                // 只记录与ANS数据相关的渲染调用
                                                if (lastAnsData && (new Date().getTime() - lastAnsData.timestamp < 500)) {
                                                    addToCallChain(callInfo);
                                                    
                                                    callDepth++;
                                                    var indent = getIndent(callDepth);
                                                    console.log(indent + "[ANS渲染] ↓ 调用渲染函数 " + address);
                                                    console.log(indent + "  参数1: " + args[0]);
                                                    console.log(indent + "  参数2: " + args[1]);
                                                    
                                                    // 记录调用栈
                                                    var backtrace = Thread.backtrace(this.context, Backtracer.ACCURATE);
                                                    console.log(indent + "  调用栈:");
                                                    for (var i = 0; i < Math.min(3, backtrace.length); i++) {
                                                        console.log(indent + "    " + i + ": " + backtrace[i]);
                                                    }
                                                }
                                            },
                                            onLeave: function(retval) {
                                                if (lastAnsData && (new Date().getTime() - lastAnsData.timestamp < 500)) {
                                                    var indent = getIndent(callDepth);
                                                    console.log(indent + "[ANS渲染] ↑ 渲染函数返回: " + retval);
                                                    callDepth--;
                                                }
                                            }
                                        });
                                    } catch(e) {
                                        // 忽略无法钩住的地址
                                    }
                                    
                                    return 'continue';
                                },
                                onComplete: function() {}
                            });
                        } catch(e) {
                            console.log("[ANS流程跟踪] 搜索区间错误: " + e);
                        }
                        
                        searchStart = searchEnd;
                        searchEnd = searchStart.add(searchRange);
                    }
                } catch(e) {
                    console.log("[ANS流程跟踪] 搜索渲染函数模式失败: " + e);
                }
            }
            
            // 2. 特别关注已知的文本处理函数 (0x7f5eac3730)
            // 这个函数在日志中多次出现，处理Unicode字符
            try {
                var textFuncPattern = "FF 43 01 D1 F3 0B 00 F9 FD 7B 02 A9 FD 03 00 91";
                
                Memory.scan(libamapr.base, libamapr.size, textFuncPattern, {
                    onMatch: function(address, size) {
                        console.log("[ANS流程跟踪] 找到疑似文本处理函数: " + address);
                        
                        Interceptor.attach(address, {
                            onEnter: function(args) {
                                // 第一个参数通常是Unicode码点
                                var charCode = args[0].toInt32();
                                
                                // 只处理可能是中文的字符
                                if (charCode > 0x4E00 && charCode < 0x9FFF) {
                                    var char = String.fromCharCode(charCode);
                                    
                                    var callInfo = {
                                        type: "Native",
                                        module: "libamapr.so",
                                        function: "textRender",
                                        args: {
                                            charCode: charCode,
                                            char: char
                                        },
                                        time: new Date().getTime()
                                    };
                                    
                                    addToCallChain(callInfo);
                                    
                                    callDepth++;
                                    var indent = getIndent(callDepth);
                                    console.log(indent + "[ANS文本] ↓ 处理文本字符: " + char + " (0x" + charCode.toString(16) + ")");
                                }
                            },
                            onLeave: function(retval) {
                                // 只处理中文字符的返回
                                if (args && args[0] && args[0].toInt32() > 0x4E00 && args[0].toInt32() < 0x9FFF) {
                                    var indent = getIndent(callDepth);
                                    console.log(indent + "[ANS文本] ↑ 文本处理返回: " + retval);
                                    callDepth--;
                                }
                            }
                        });
                        
                        return 'stop'; // 找到一个匹配就停止
                    },
                    onComplete: function() {}
                });
            } catch(e) {
                console.log("[ANS流程跟踪] 搜索文本处理函数失败: " + e);
            }
        } catch(e) {
            console.log("[ANS流程跟踪] 设置渲染钩子失败: " + e);
        }
    }
    
    // 设置JNI钩子，捕获Java到Native的转换点
    function setupJNIHooks() {
        try {
            // 钩住JNI函数注册
            var RegisterNativesAddr = null;
            
            // 查找JNI RegisterNatives函数
            var symbols = Process.findModuleByName("libart.so").enumerateSymbols();
            for (var i = 0; i < symbols.length; i++) {
                var symbol = symbols[i];
                if (symbol.name.indexOf("CheckJNI") >= 0 && symbol.name.indexOf("RegisterNatives") >= 0) {
                    RegisterNativesAddr = symbol.address;
                    break;
                }
            }
            
            if (!RegisterNativesAddr) {
                console.log("[ANS流程跟踪] 未找到JNI RegisterNatives函数");
                return;
            }
            
            console.log("[ANS流程跟踪] 找到JNI RegisterNatives: " + RegisterNativesAddr);
            
            Interceptor.attach(RegisterNativesAddr, {
                onEnter: function(args) {
                    var env = args[0];
                    var jclass = args[1];
                    var methods = args[2];
                    var nMethods = args[3].toInt32();
                    
                    var className = Java.vm.tryGetEnv().getClassName(jclass);
                    
                    // 只关注地图相关类
                    if (className && (
                        className.indexOf("amap") >= 0 || 
                        className.indexOf("autonavi") >= 0 || 
                        className.indexOf("map") >= 0 ||
                        className.indexOf("Map") >= 0)) {
                        
                        console.log("[JNI注册] 类: " + className + ", 方法数: " + nMethods);
                        
                        // 遍历注册的方法
                        for (var i = 0; i < nMethods; i++) {
                            var methodInfo = methods.add(i * Process.pointerSize * 3).readPointer();
                            var methodName = methodInfo.readUtf8String();
                            var methodSig = methodInfo.add(Process.pointerSize).readUtf8String();
                            var methodAddr = methodInfo.add(Process.pointerSize * 2).readPointer();
                            
                            console.log("[JNI注册]   方法: " + methodName + ", 签名: " + methodSig + ", 地址: " + methodAddr);
                            
                            // 记录Java到Native的映射
                            var jniInfo = {
                                className: className,
                                methodName: methodName,
                                methodSig: methodSig,
                                nativeAddr: methodAddr
                            };
                            
                            // 钩住这个Native方法
                            try {
                                Interceptor.attach(methodAddr, {
                                    onEnter: function(args) {
                                        var callInfo = {
                                            type: "JNI",
                                            class: className,
                                            method: methodName,
                                            time: new Date().getTime()
                                        };
                                        
                                        addToCallChain(callInfo);
                                        
                                        callDepth++;
                                        var indent = getIndent(callDepth);
                                        console.log(indent + "[JNI调用] ↓ " + className + "." + methodName);
                                        
                                        // 记录调用栈
                                        var backtrace = Thread.backtrace(this.context, Backtracer.ACCURATE);
                                        console.log(indent + "  调用栈:");
                                        for (var i = 0; i < Math.min(3, backtrace.length); i++) {
                                            console.log(indent + "    " + i + ": " + backtrace[i]);
                                        }
                                    },
                                    onLeave: function(retval) {
                                        var indent = getIndent(callDepth);
                                        console.log(indent + "[JNI调用] ↑ " + className + "." + methodName + " 返回");
                                        callDepth--;
                                    }
                                });
                            } catch(e) {
                                // 忽略无法钩住的方法
                            }
                        }
                    }
                }
            });
        } catch(e) {
            console.log("[ANS流程跟踪] 设置JNI钩子失败: " + e);
        }
    }
    
    // 添加调用到调用链
    function addToCallChain(callInfo) {
        callChain.push(callInfo);
        
        // 限制调用链长度
        if (callChain.length > maxCallsToShow) {
            callChain.shift();
        }
    }
    
    // 显示调用链
    function showCallChain() {
        if (callChain.length === 0) {
            return;
        }
        
        console.log("\n[ANS流程分析] === 调用链分析 ===");
        console.log("共记录 " + callChain.length + " 个调用");
        
        // 按时间排序
        callChain.sort(function(a, b) {
            return a.time - b.time;
        });
        
        // 分析调用链
        var fileOps = 0;
        var parserCalls = 0;
        var renderCalls = 0;
        var javaCalls = 0;
        var jniCalls = 0;
        
        for (var i = 0; i < callChain.length; i++) {
            var call = callChain[i];
            
            if (call.type === "SysCall") {
                fileOps++;
            } else if (call.type === "Native" && call.module === "libamapnsq.so") {
                parserCalls++;
            } else if (call.type === "Native" && call.module === "libamapr.so") {
                renderCalls++;
            } else if (call.type === "Java") {
                javaCalls++;
            } else if (call.type === "JNI" || call.type === "Java-Native") {
                jniCalls++;
            }
        }
        
        console.log("文件操作: " + fileOps + " 次");
        console.log("解析调用: " + parserCalls + " 次");
        console.log("渲染调用: " + renderCalls + " 次");
        console.log("Java调用: " + javaCalls + " 次");
        console.log("JNI调用: " + jniCalls + " 次");
        
        // 显示关键调用顺序
        console.log("\n[ANS流程分析] === 关键调用顺序 ===");
        
        var lastType = "";
        var callCount = 0;
        
        for (var i = 0; i < callChain.length; i++) {
            var call = callChain[i];
            var callDesc = "";
            
            if (call.type === "SysCall") {
                callDesc = call.function + "(" + (call.path || call.fd) + ")";
            } else if (call.type === "Native") {
                callDesc = call.module + ":" + call.function;
            } else if (call.type === "Java") {
                callDesc = call.class + "." + call.method;
            } else if (call.type === "JNI" || call.type === "Java-Native") {
                callDesc = call.class + "." + call.method + " → Native";
            }
            
            // 合并连续相同类型的调用
            if (call.type === lastType) {
                callCount++;
                
                if (i === callChain.length - 1) {
                    console.log("  " + callCount + "x " + lastType);
                }
            } else {
                if (callCount > 1) {
                    console.log("  " + callCount + "x " + lastType);
                }
                
                console.log("  " + call.type + ": " + callDesc);
                lastType = call.type;
                callCount = 1;
            }
        }
        
        console.log("[ANS流程分析] === 分析结束 ===\n");
    }
    
    // 显示Java到Native的调用
    function showJavaToNativeCalls() {
        if (javaToNativeCalls.length === 0) {
            return;
        }
        
        console.log("\n[ANS流程分析] === Java→Native调用分析 ===");
        console.log("共记录 " + javaToNativeCalls.length + " 个Java→Native调用");
        
        // 按时间排序
        javaToNativeCalls.sort(function(a, b) {
            return a.time - b.time;
        });
        
        // 统计调用频率
        var callFreq = {};
        
        for (var i = 0; i < javaToNativeCalls.length; i++) {
            var call = javaToNativeCalls[i];
            var key = call.class + "." + call.method;
            
            if (!callFreq[key]) {
                callFreq[key] = 1;
            } else {
                callFreq[key]++;
            }
        }
        
        // 显示调用频率
        console.log("\n最常见的Java→Native调用:");
        
        var sortedCalls = [];
        for (var key in callFreq) {
            sortedCalls.push({key: key, count: callFreq[key]});
        }
        
        sortedCalls.sort(function(a, b) {
            return b.count - a.count;
        });
        
        for (var i = 0; i < Math.min(10, sortedCalls.length); i++) {
            console.log("  " + sortedCalls[i].key + ": " + sortedCalls[i].count + " 次");
        }
        
        console.log("[ANS流程分析] === 分析结束 ===\n");
        
        // 清空记录
        javaToNativeCalls = [];
    }
    
    // 获取缩进字符串
    function getIndent(depth) {
        var indent = "";
        for (var i = 0; i < depth; i++) {
            indent += "  ";
        }
        return indent;
    }
    
    // 辅助函数：补零
    function padZero(str) {
        return str.length < 2 ? "0" + str : str;
    }
})();