// APK资源分析脚本 - 专门分析高德地图APK内的地图数据
// 基于验证发现：地图数据存储在APK内而非.ans文件

console.log("[APK资源分析] 开始分析高德地图APK内嵌的地图数据...");
console.log("[发现] 地图数据存储在APK内，而非独立的.ans文件");

// 分析状态
var apkAnalysis = {
    assetOperations: [],
    zipOperations: [],
    resourceAccess: [],
    dataBlocks: [],
    startTime: Date.now()
};

// 安全的字节数组处理
function safeByteArrayToHex(byteArray, maxLen) {
    var hexBytes = [];
    var len = Math.min(maxLen || 16, byteArray.length);
    for (var i = 0; i < len; i++) {
        hexBytes.push(('0' + byteArray[i].toString(16)).slice(-2));
    }
    return hexBytes.join(' ');
}

// 数据类型识别
function identifyDataType(header) {
    if (header.length >= 4) {
        // DICE-AM矢量数据
        if (header[0] === 0x44 && header[1] === 0x49 && header[2] === 0x43 && header[3] === 0x45) {
            return "DICE-AM";
        }
        // CONFIG配置数据
        if (header[0] === 0xbc && header[1] === 0xbc && header[2] === 0xbc && header[3] === 0xbc) {
            return "CONFIG";
        }
        // TEXT文本数据
        if (header[0] === 0x0d && header[1] === 0x00 && header[2] === 0x00 && header[3] === 0x00) {
            return "TEXT";
        }
        // zlib压缩数据
        if (header[0] === 0x78 && header[1] === 0x9c) {
            return "ZLIB";
        }
        // AM-zlib容器
        if (header[0] === 0x08) {
            return "AM-ZLIB";
        }
        // ZIP文件头
        if (header[0] === 0x50 && header[1] === 0x4b) {
            return "ZIP";
        }
    }
    return "UNKNOWN";
}

// 1. Hook Android Asset Manager - APK资源访问
console.log("[1] Hook Android Asset Manager...");

try {
    var libandroid = Process.getModuleByName("libandroid.so");
    
    // Hook AAssetManager_open
    var assetOpenPtr = libandroid.getExportByName("AAssetManager_open");
    Interceptor.attach(assetOpenPtr, {
        onEnter: function(args) {
            this.filename = args[1].readCString();
            this.mode = args[2].toInt32();
        },
        onLeave: function(retval) {
            if (!retval.isNull() && this.filename) {
                var assetOp = {
                    operation: "AAssetManager_open",
                    filename: this.filename,
                    mode: this.mode,
                    asset: retval.toString(),
                    timestamp: Date.now()
                };
                apkAnalysis.assetOperations.push(assetOp);
                
                console.log("[Asset打开] " + this.filename + " (asset=" + retval + ")");
                
                // 检查是否是地图相关资源
                if (this.filename.indexOf('map') !== -1 || 
                    this.filename.indexOf('data') !== -1 ||
                    this.filename.indexOf('tile') !== -1) {
                    console.log("  [地图资源] 可能的地图数据文件");
                }
            }
        }
    });
    
    // Hook AAsset_read
    var assetReadPtr = libandroid.getExportByName("AAsset_read");
    Interceptor.attach(assetReadPtr, {
        onEnter: function(args) {
            this.asset = args[0];
            this.buf = args[1];
            this.count = args[2].toInt32();
        },
        onLeave: function(retval) {
            var bytesRead = retval.toInt32();
            if (bytesRead > 0) {
                try {
                    var data = this.buf.readByteArray(Math.min(32, bytesRead));
                    var header = new Uint8Array(data);
                    var dataType = identifyDataType(header);
                    var headerHex = safeByteArrayToHex(header, 16);
                    
                    var readOp = {
                        operation: "AAsset_read",
                        asset: this.asset.toString(),
                        bytesRead: bytesRead,
                        requestedBytes: this.count,
                        dataType: dataType,
                        headerHex: headerHex,
                        timestamp: Date.now()
                    };
                    apkAnalysis.assetOperations.push(readOp);
                    
                    console.log("[Asset读取] " + bytesRead + " 字节");
                    console.log("  数据类型: " + dataType);
                    console.log("  头部数据: " + headerHex);
                    
                    if (dataType === "DICE-AM" || dataType === "CONFIG" || dataType === "TEXT") {
                        console.log("  [关键发现] 地图数据类型: " + dataType);
                        apkAnalysis.dataBlocks.push(readOp);
                    }
                } catch (e) {
                    console.log("  [警告] Asset读取分析失败: " + e.message);
                }
            }
        }
    });
    
    console.log("[✓] Android Asset Manager Hook设置成功");
} catch (e) {
    console.log("[✗] Android Asset Manager Hook失败: " + e.message);
}

// 2. Hook zlib解压 - 分析APK内压缩数据
console.log("[2] Hook zlib解压函数...");

try {
    var libz = Process.getModuleByName("libz.so");
    var inflatePtr = libz.getExportByName("inflate");
    
    Interceptor.attach(inflatePtr, {
        onEnter: function(args) {
            this.strm = args[0];
            this.flush = args[1].toInt32();
        },
        onLeave: function(retval) {
            var result = retval.toInt32();
            if (result === 0) { // Z_OK
                try {
                    // 读取zlib流结构
                    var strm = this.strm;
                    var nextOut = strm.readPointer();
                    var availOut = strm.add(8).readU32();
                    var totalOut = strm.add(20).readU32();
                    
                    if (totalOut > 0 && totalOut < 1000000) {
                        var outputData = nextOut.readByteArray(Math.min(32, totalOut));
                        var header = new Uint8Array(outputData);
                        var dataType = identifyDataType(header);
                        var headerHex = safeByteArrayToHex(header, 16);
                        
                        var inflateOp = {
                            operation: "inflate",
                            result: result,
                            totalOut: totalOut,
                            dataType: dataType,
                            headerHex: headerHex,
                            timestamp: Date.now()
                        };
                        apkAnalysis.zipOperations.push(inflateOp);
                        
                        console.log("[zlib解压] 输出 " + totalOut + " 字节");
                        console.log("  数据类型: " + dataType);
                        console.log("  头部数据: " + headerHex);
                        
                        if (dataType === "DICE-AM" || dataType === "CONFIG" || dataType === "TEXT") {
                            console.log("  [关键发现] 解压出地图数据: " + dataType);
                            apkAnalysis.dataBlocks.push(inflateOp);
                        }
                    }
                } catch (e) {
                    console.log("  [警告] zlib解压分析失败: " + e.message);
                }
            }
        }
    });
    
    console.log("[✓] zlib inflate Hook设置成功");
} catch (e) {
    console.log("[✗] zlib inflate Hook失败: " + e.message);
}

// 3. Hook文件读取 - 监控APK文件访问
console.log("[3] Hook文件读取操作...");

try {
    var libc = Process.getModuleByName("libc.so");
    var readPtr = libc.getExportByName("read");
    
    Interceptor.attach(readPtr, {
        onEnter: function(args) {
            this.fd = args[0].toInt32();
            this.buf = args[1];
            this.count = args[2].toInt32();
        },
        onLeave: function(retval) {
            var bytesRead = retval.toInt32();
            if (bytesRead > 0 && this.count >= 16) {
                try {
                    var data = this.buf.readByteArray(Math.min(32, bytesRead));
                    var header = new Uint8Array(data);
                    var dataType = identifyDataType(header);
                    
                    // 只记录可能的地图数据
                    if (dataType === "DICE-AM" || dataType === "CONFIG" || dataType === "TEXT" || 
                        dataType === "ZLIB" || dataType === "AM-ZLIB") {
                        
                        var headerHex = safeByteArrayToHex(header, 16);
                        
                        var readOp = {
                            operation: "read",
                            fd: this.fd,
                            bytesRead: bytesRead,
                            dataType: dataType,
                            headerHex: headerHex,
                            timestamp: Date.now()
                        };
                        apkAnalysis.resourceAccess.push(readOp);
                        
                        console.log("[文件读取] fd=" + this.fd + " 读取 " + bytesRead + " 字节");
                        console.log("  数据类型: " + dataType);
                        console.log("  头部数据: " + headerHex);
                        
                        if (dataType === "DICE-AM" || dataType === "CONFIG" || dataType === "TEXT") {
                            console.log("  [关键发现] 直接读取地图数据: " + dataType);
                            apkAnalysis.dataBlocks.push(readOp);
                        }
                    }
                } catch (e) {
                    // 忽略分析错误
                }
            }
        }
    });
    
    console.log("[✓] 文件读取Hook设置成功");
} catch (e) {
    console.log("[✗] 文件读取Hook失败: " + e.message);
}

// 4. Hook内存映射 - 分析APK内存映射
console.log("[4] Hook内存映射操作...");

try {
    var mmapPtr = libc.getExportByName("mmap");
    
    Interceptor.attach(mmapPtr, {
        onEnter: function(args) {
            this.length = args[1].toInt32();
            this.fd = args[4].toInt32();
            this.offset = args[5].toInt32();
        },
        onLeave: function(retval) {
            if (!retval.isNull() && this.length > 1024) {
                try {
                    var mappedData = retval.readByteArray(Math.min(64, this.length));
                    var header = new Uint8Array(mappedData);
                    var dataType = identifyDataType(header);
                    
                    // 只记录可能的地图数据映射
                    if (dataType === "DICE-AM" || dataType === "CONFIG" || dataType === "TEXT" || 
                        dataType === "ZLIB" || dataType === "AM-ZLIB" || dataType === "ZIP") {
                        
                        var headerHex = safeByteArrayToHex(header, 16);
                        
                        var mmapOp = {
                            operation: "mmap",
                            addr: retval.toString(),
                            length: this.length,
                            fd: this.fd,
                            offset: this.offset,
                            dataType: dataType,
                            headerHex: headerHex,
                            timestamp: Date.now()
                        };
                        apkAnalysis.resourceAccess.push(mmapOp);
                        
                        console.log("[内存映射] " + retval + " 大小: " + this.length);
                        console.log("  数据类型: " + dataType);
                        console.log("  头部数据: " + headerHex);
                        
                        if (dataType === "DICE-AM" || dataType === "CONFIG" || dataType === "TEXT") {
                            console.log("  [关键发现] 映射地图数据: " + dataType);
                            apkAnalysis.dataBlocks.push(mmapOp);
                        }
                    }
                } catch (e) {
                    // 忽略分析错误
                }
            }
        }
    });
    
    console.log("[✓] 内存映射Hook设置成功");
} catch (e) {
    console.log("[✗] 内存映射Hook失败: " + e.message);
}

// 5. 定期输出分析报告
setInterval(function() {
    var runtime = Math.floor((Date.now() - apkAnalysis.startTime) / 1000);
    
    console.log("\n[APK资源分析报告] ==========================================");
    console.log("运行时间: " + runtime + "s");
    console.log("");
    console.log("Asset操作统计:");
    console.log("  Asset打开: " + apkAnalysis.assetOperations.filter(function(op) {
        return op.operation === "AAssetManager_open";
    }).length);
    console.log("  Asset读取: " + apkAnalysis.assetOperations.filter(function(op) {
        return op.operation === "AAsset_read";
    }).length);
    
    console.log("");
    console.log("数据解压统计:");
    console.log("  zlib解压: " + apkAnalysis.zipOperations.length);
    
    console.log("");
    console.log("资源访问统计:");
    console.log("  文件读取: " + apkAnalysis.resourceAccess.filter(function(op) {
        return op.operation === "read";
    }).length);
    console.log("  内存映射: " + apkAnalysis.resourceAccess.filter(function(op) {
        return op.operation === "mmap";
    }).length);
    
    console.log("");
    console.log("地图数据发现:");
    console.log("  总数据块: " + apkAnalysis.dataBlocks.length);
    
    var dataTypes = {};
    for (var i = 0; i < apkAnalysis.dataBlocks.length; i++) {
        var type = apkAnalysis.dataBlocks[i].dataType;
        dataTypes[type] = (dataTypes[type] || 0) + 1;
    }
    
    for (var type in dataTypes) {
        console.log("  " + type + "数据: " + dataTypes[type] + "次");
    }
    
    console.log("===============================================\n");
}, 25000);

console.log("[APK资源分析] 分析脚本已启动，等待地图操作...");
console.log("[提示] 请移动地图、缩放或切换图层以触发APK资源访问");
