     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
[Data Extractor] 启动数据提取器...
[Data Extractor] 脚本加载完成，3秒后开始Hook设置
[Remote::com.autonavi.minimap]-> [Setup] 开始设置数据Hook...
[Library] 库基址: 0x7f82ab9000
[Hook] 文件读取Hook设置成功
[Hook] SQLite实现Hook设置成功
[Ready] 所有Hook设置完成，请移动地图到新区域
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现有效数据: 17 字节
[Extract] SQLite-Impl - 大小: 17
[Data] Hex: 6d 75 6c 74 69 70 6c 65 78 5f 63 6f 6e 74 72 6f 
[Data] Char: multiplex_contro
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 17
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 17
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现有效数据: 18 字节
[Extract] SQLite-Impl - 大小: 18
[Data] Hex: 73 70 65 6c 6c 66 69 78 31 5f 74 72 61 6e 73 6c 
[Data] Char: spellfix1_transl
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 18
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现有效数据: 19 字节
[Extract] SQLite-Impl - 大小: 19
[Data] Hex: 73 70 65 6c 6c 66 69 78 31 5f 70 68 6f 6e 65 68 
[Data] Char: spellfix1_phoneh
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现有效数据: 20 字节
[Extract] SQLite-Impl - 大小: 20
[Data] Hex: 73 70 65 6c 6c 66 69 78 31 5f 73 63 72 69 70 74 
[Data] Char: spellfix1_script
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 115
[File Read #1] 非系统文件，大小: 8192
[Extract] FileRead - 大小: 8192
[Data] Hex: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00 
[Data] Char: DICE-AM.........
[MAP DATA] 发现地图数据! 类型: DICE
[HexDump] 完整数据预览:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  fe ff 00 00 00 04 00 00 00 00 fe fe 00 00 00 00  ................
00000020  00 00 00 01 00 00 00 04 00 00 00 00 00 00 00 00  ................
00000030  00 00 00 01 00 00 00 00 00 00 00 00 00 00 00 00  ................
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[File Read #2] 非系统文件，大小: 152
[Extract] FileRead - 大小: 152
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[File Read #3] 非系统文件，大小: 8192
[Extract] FileRead - 大小: 8192
[Data] Hex: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00 
[Data] Char: DICE-AM.........
[MAP DATA] 发现地图数据! 类型: DICE
[HexDump] 完整数据预览:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  fe ff 00 00 00 04 00 00 00 00 fe fe 00 00 00 00  ................
00000020  00 00 00 01 00 00 00 04 00 00 00 00 00 00 00 00  ................
00000030  00 00 00 01 00 00 00 00 00 00 00 00 00 00 00 00  ................
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 100
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 32
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 32
[SQLite Impl] bind_blob实现调用
[File Read #4] 非系统文件，大小: 30316
[Extract] FileRead - 大小: 30316
[Data] Hex: 41 66 72 69 63 61 2f 41 62 69 64 6a 61 6e 00 00 
[Data] Char: Africa/Abidjan..
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 32
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 48
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 114
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[File Read #5] 非系统文件，大小: 41464
[Extract] FileRead - 大小: 41464
[Data] Hex: 54 5a 69 66 32 00 00 00 00 00 00 00 00 00 00 00 
[Data] Char: TZif2...........
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现有效数据: 32 字节
[Extract] SQLite-Impl - 大小: 32
[Data] Hex: e0 1f 40 f9 08 40 41 39 48 01 00 35 68 0a 40 f9 
[Data] Char: ..@..@A9H..5h.@.
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 43
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 141
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 100
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 49
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 22
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 22
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 22
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 22
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 32
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 48
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 22
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[File Read #6] 非系统文件，大小: 8192
[Extract] FileRead - 大小: 8192
[Data] Hex: 0a 00 00 00 06 1f d6 00 1f d6 1f dd 1f e4 1f f9 
[Data] Char: ................
[File Read #7] 非系统文件，大小: 8192
[Extract] FileRead - 大小: 8192
[Data] Hex: 0d 15 f0 00 06 13 ab 05 13 ab 16 15 16 0d 16 05 
[Data] Char: ................
[SQLite Impl] bind_blob实现调用
[SQLite Skip] 数据指针不可读，大小: 43
[File Read #8] 非系统文件，大小: 170
[Extract] FileRead - 大小: 170
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #9] 非系统文件，大小: 13265
[Extract] FileRead - 大小: 13265
[Data] Hex: 98 0d 60 5b 66 c0 39 d6 4e 0b 88 85 d3 cc a4 01 
[Data] Char: ..`[f.9.N.......
[File Read #10] 非系统文件，大小: 9683
[Extract] FileRead - 大小: 9683
[Data] Hex: 69 86 78 c0 b2 48 b2 d2 15 84 be 66 49 66 75 32 
[Data] Char: i.x..H.....fIfu2
[File Read #11] 非系统文件，大小: 169
[Extract] FileRead - 大小: 169
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #12] 非系统文件，大小: 6076
[Extract] FileRead - 大小: 6076
[Data] Hex: 3c 5a 1b be 70 1e 44 54 bb 1d fd ad fe b8 52 f4 
[Data] Char: <Z..p.DT......R.
[File Read #13] 非系统文件，大小: 201
[Extract] FileRead - 大小: 201
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #14] 非系统文件，大小: 65
[Extract] FileRead - 大小: 65
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #15] 非系统文件，大小: 2853
[Extract] FileRead - 大小: 2853
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #16] 非系统文件，大小: 334
[Extract] FileRead - 大小: 334
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #17] 非系统文件，大小: 111
[Extract] FileRead - 大小: 111
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #18] 非系统文件，大小: 5185
[Extract] FileRead - 大小: 5185
[Data] Hex: 2f f2 68 9b d3 1b 08 16 b5 ac 9b 95 d6 6b 2f 4e 
[Data] Char: /.h..........k/N
[File Read #19] 非系统文件，大小: 5350
[Extract] FileRead - 大小: 5350
[Data] Hex: f8 76 0c 52 7a f1 1f 81 ab 4e 8d c5 0e 90 b1 95 
[Data] Char: .v.Rz....N......
[File Read #20] 非系统文件，大小: 1567
[Extract] FileRead - 大小: 1567
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #21] 非系统文件，大小: 499
[Extract] FileRead - 大小: 499
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #22] 非系统文件，大小: 141
[Extract] FileRead - 大小: 141
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #23] 非系统文件，大小: 9413
[Extract] FileRead - 大小: 9413
[Data] Hex: 7b 22 72 65 73 5f 6c 69 73 74 22 3a 5b 7b 22 6c 
[Data] Char: {"res_list":[{"l
[File Read #24] 非系统文件，大小: 3938
[Extract] FileRead - 大小: 3938
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #25] 非系统文件，大小: 261
[Extract] FileRead - 大小: 261
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #26] 非系统文件，大小: 267
[Extract] FileRead - 大小: 267
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #27] 非系统文件，大小: 118
[Extract] FileRead - 大小: 118
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #28] 非系统文件，大小: 199
[Extract] FileRead - 大小: 199
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #29] 非系统文件，大小: 65
[Extract] FileRead - 大小: 65
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #30] 非系统文件，大小: 9413
[Extract] FileRead - 大小: 9413
[Data] Hex: 7b 22 72 65 73 5f 6c 69 73 74 22 3a 5b 7b 22 6c 
[Data] Char: {"res_list":[{"l
[File Read #31] 非系统文件，大小: 150
[Extract] FileRead - 大小: 150
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #32] 非系统文件，大小: 358
[Extract] FileRead - 大小: 358
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #33] 非系统文件，大小: 316
[Extract] FileRead - 大小: 316
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #34] 非系统文件，大小: 748
[Extract] FileRead - 大小: 748
[Data] Hex: 50 72 6f 63 65 73 73 6f 72 09 3a 20 41 41 72 63 
[Data] Char: Processor.: AArc
[File Read #35] 非系统文件，大小: 8
[Extract] FileRead - 大小: 8
[Data] Hex: 31 33 36 33 32 30 30 0a 
[Data] Char: 1363200.
[File Read #36] 非系统文件，大小: 954
[Extract] FileRead - 大小: 954
[Data] Hex: 4d 65 6d 54 6f 74 61 6c 3a 20 20 20 20 20 20 20 
[Data] Char: MemTotal:       
[File Read #37] 非系统文件，大小: 8192
[Extract] FileRead - 大小: 8192
[Data] Hex: 0a 23 20 62 65 67 69 6e 20 62 75 69 6c 64 20 70 
[Data] Char: .# begin build p
[File Read #38] 非系统文件，大小: 2151
[Extract] FileRead - 大小: 2151
[Data] Hex: 70 65 72 73 69 73 74 2e 63 65 6c 6c 62 72 6f 61 
[Data] Char: persist.cellbroa
[File Read #40] 非系统文件，大小: 748
[Extract] FileRead - 大小: 748
[Data] Hex: 50 72 6f 63 65 73 73 6f 72 09 3a 20 41 41 72 63 
[Data] Char: Processor.: AArc
[File Read #41] 非系统文件，大小: 116
[Extract] FileRead - 大小: 116
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #42] 非系统文件，大小: 303
[Extract] FileRead - 大小: 303
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #43] 非系统文件，大小: 8192
[Extract] FileRead - 大小: 8192
[Data] Hex: 0a 23 20 62 65 67 69 6e 20 62 75 69 6c 64 20 70 
[Data] Char: .# begin build p
[File Read #44] 非系统文件，大小: 165
[Extract] FileRead - 大小: 165
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #45] 非系统文件，大小: 4
[Extract] FileRead - 大小: 4
[Data] Hex: 30 2d 33 0a 
[Data] Char: 0-3.
[File Read #46] 非系统文件，大小: 1015
[Extract] FileRead - 大小: 1015
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #47] 非系统文件，大小: 174
[Extract] FileRead - 大小: 174
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #48] 非系统文件，大小: 65
[Extract] FileRead - 大小: 65
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #49] 非系统文件，大小: 2151
[Extract] FileRead - 大小: 2151
[Data] Hex: 70 65 72 73 69 73 74 2e 63 65 6c 6c 62 72 6f 61 
[Data] Char: persist.cellbroa
[File Read #50] 非系统文件，大小: 456
[Extract] FileRead - 大小: 456
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #51] 非系统文件，大小: 247
[Extract] FileRead - 大小: 247
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #52] 非系统文件，大小: 258
[Extract] FileRead - 大小: 258
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #53] 非系统文件，大小: 748
[Extract] FileRead - 大小: 748
[Data] Hex: 50 72 6f 63 65 73 73 6f 72 09 3a 20 41 41 72 63 
[Data] Char: Processor.: AArc
[File Read #54] 非系统文件，大小: 165
[Extract] FileRead - 大小: 165
[Data] Hex: 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 
[Data] Char: 0000000000000000
[File Read #55] 非系统文件，大小: 149
[Extract] FileRead - 大小: 149
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #56] 非系统文件，大小: 234
[Extract] FileRead - 大小: 234
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #57] 非系统文件，大小: 65
[Extract] FileRead - 大小: 65
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #58] 非系统文件，大小: 748
[Extract] FileRead - 大小: 748
[Data] Hex: 50 72 6f 63 65 73 73 6f 72 09 3a 20 41 41 72 63 
[Data] Char: Processor.: AArc
[File Read #59] 非系统文件，大小: 165
[Extract] FileRead - 大小: 165
[Data] Hex: 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 
[Data] Char: 0000000000000000
[File Read #60] 非系统文件，大小: 361
[Extract] FileRead - 大小: 361
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #61] 非系统文件，大小: 169
[Extract] FileRead - 大小: 169
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #62] 非系统文件，大小: 31
[Extract] FileRead - 大小: 31
[Data] Hex: 6c 69 62 63 6f 72 65 2e 69 6f 2e 44 69 73 6b 4c 
[Data] Char: libcore.io.DiskL
[File Read #63] 非系统文件，大小: 4049
[Extract] FileRead - 大小: 4049
[Data] Hex: 31 32 63 30 30 30 30 30 2d 31 32 65 30 37 30 30 
[Data] Char: 12c00000-12e0700
[File Read #64] 非系统文件，大小: 4122
[Extract] FileRead - 大小: 4122
[Data] Hex: 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 
[Data] Char:                 
[File Read #65] 非系统文件，大小: 19782
[Extract] FileRead - 大小: 19782
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 91 9d 
[Data] Char: PK..........!8..
[File Read #67] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 50 72 6f 74 6f 5f 41 53 50 4b 01 02 14 00 14 00 
[Data] Char: Proto_ASPK......
[File Read #68] 非系统文件，大小: 1284
[Extract] FileRead - 大小: 1284
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #69] 非系统文件，大小: 10574
[Extract] FileRead - 大小: 10574
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #70] 非系统文件，大小: 4094
[Extract] FileRead - 大小: 4094
[Data] Hex: 2d 33 64 30 0a 37 66 66 65 64 65 30 30 30 2d 37 
[Data] Char: -3d0.7ffede000-7
[File Read #71] 非系统文件，大小: 10574
[Extract] FileRead - 大小: 10574
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #72] 非系统文件，大小: 4105
[Extract] FileRead - 大小: 4105
[Data] Hex: 30 30 30 30 30 20 30 30 3a 30 30 20 30 20 0a 37 
[Data] Char: 00000 00:00 0 .7
[File Read #73] 非系统文件，大小: 17711
[Extract] FileRead - 大小: 17711
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #74] 非系统文件，大小: 4107
[Extract] FileRead - 大小: 4107
[Data] Hex: 5b 61 6e 6f 6e 3a 6c 69 62 63 5f 6d 61 6c 6c 6f 
[Data] Char: [anon:libc_mallo
[File Read #75] 非系统文件，大小: 44467
[Extract] FileRead - 大小: 44467
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 85 7c 04 49 b7 8c 
[Data] Char: PK.........|.I..
[File Read #76] 非系统文件，大小: 31
[Extract] FileRead - 大小: 31
[Data] Hex: 6c 69 62 63 6f 72 65 2e 69 6f 2e 44 69 73 6b 4c 
[Data] Char: libcore.io.DiskL
[File Read #77] 非系统文件，大小: 4133
[Extract] FileRead - 大小: 4133
[Data] Hex: 66 30 30 30 2d 37 66 36 30 30 37 63 30 30 30 20 
[Data] Char: f000-7f6007c000 
[File Read #78] 非系统文件，大小: 9952
[Extract] FileRead - 大小: 9952
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 23 7c 04 49 b7 8c 
[Data] Char: PK........#|.I..
[File Read #79] 非系统文件，大小: 4049
[Extract] FileRead - 大小: 4049
[Data] Hex: 65 39 37 30 30 30 20 2d 2d 2d 70 20 30 30 30 30 
[Data] Char: e97000 ---p 0000
[File Read #80] 非系统文件，大小: 167
[Extract] FileRead - 大小: 167
[Data] Hex: 6c 69 62 63 6f 72 65 2e 69 6f 2e 44 69 73 6b 4c 
[Data] Char: libcore.io.DiskL
[File Read #81] 非系统文件，大小: 4177
[Extract] FileRead - 大小: 4177
[Data] Hex: 30 30 30 30 20 30 30 3a 30 31 20 39 32 37 36 34 
[Data] Char: 0000 00:01 92764
[File Read #82] 非系统文件，大小: 4093
[Extract] FileRead - 大小: 4093
[Data] Hex: 20 30 20 0a 37 66 36 38 64 30 35 30 30 30 2d 37 
[Data] Char:  0 .7f68d05000-7
[File Read #83] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 73 2f 64 72 61 77 61 62 6c 65 2d 78 78 68 64 70 
[Data] Char: s/drawable-xxhdp
[File Read #84] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 29 39 2f 32 96 6f 63 7d 76 48 da 23 97 04 0e 28 
[Data] Char: )9/2.oc}vH.#...(
[File Read #85] 非系统文件，大小: 4046
[Extract] FileRead - 大小: 4046
[Data] Hex: 0a 37 66 36 62 65 66 39 30 30 30 2d 37 66 36 62 
[Data] Char: .7f6bef9000-7f6b
[File Read #86] 非系统文件，大小: 4179
[Extract] FileRead - 大小: 4179
[Data] Hex: 78 70 20 30 30 31 36 64 30 30 30 20 31 30 33 3a 
[Data] Char: xp 0016d000 103:
[File Read #87] 非系统文件，大小: 4145
[Extract] FileRead - 大小: 4145
[Data] Hex: 39 32 37 31 34 35 20 20 20 20 20 20 20 20 20 20 
[Data] Char: 927145          
[File Read #88] 非系统文件，大小: 338
[Extract] FileRead - 大小: 338
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #89] 非系统文件，大小: 4192
[Extract] FileRead - 大小: 4192
[Data] Hex: 34 35 30 32 30 30 30 2d 37 66 37 34 35 30 34 30 
[Data] Char: 4502000-7f745040
[File Read #90] 非系统文件，大小: 4153
[Extract] FileRead - 大小: 4153
[Data] Hex: 20 20 20 20 20 20 2f 64 61 74 61 2f 61 70 70 2f 
[Data] Char:       /data/app/
[File Read #91] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 41 01 09 a0 97 41 01 09 ec 8e 41 01 09 b4 8f 41 
[Data] Char: A....A....A....A
[File Read #92] 非系统文件，大小: 16384
[Extract] FileRead - 大小: 16384
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #93] 非系统文件，大小: 4061
[Extract] FileRead - 大小: 4061
[Data] Hex: 2d 37 66 37 38 66 63 36 30 30 30 20 72 77 2d 70 
[Data] Char: -7f78fc6000 rw-p
[File Read #94] 非系统文件，大小: 16384
[Extract] FileRead - 大小: 16384
[Data] Hex: 33 30 31 30 30 2c 33 35 30 34 30 30 2c 33 35 30 
[Data] Char: 30100,350400,350
[File Read #95] 非系统文件，大小: 16384
[Extract] FileRead - 大小: 16384
[Data] Hex: 74 69 6d 65 73 26 71 75 6f 74 3b 3a 31 2c 26 71 
[Data] Char: times&quot;:1,&q
[File Read #96] 非系统文件，大小: 4137
[Extract] FileRead - 大小: 4137
[Data] Hex: 61 35 62 65 30 30 30 20 72 77 2d 70 20 30 30 30 
[Data] Char: a5be000 rw-p 000
[File Read #97] 非系统文件，大小: 16384
[Extract] FileRead - 大小: 16384
[Data] Hex: 91 a8 e5 b2 81 e7 9a 84 e5 84 bf e7 ab a5 e5 bf 
[Data] Char: ................
[File Read #98] 非系统文件，大小: 7200
[Extract] FileRead - 大小: 7200
[Data] Hex: 6b 26 71 75 6f 74 3b 3a 26 71 75 6f 74 3b 43 26 
[Data] Char: k&quot;:&quot;C&
[File Read #99] 非系统文件，大小: 4131
[Extract] FileRead - 大小: 4131
[Data] Hex: 20 30 30 3a 30 30 20 30 20 20 20 20 20 20 20 20 
[Data] Char:  00:00 0        
[File Read #100] 非系统文件，大小: 4144
[Extract] FileRead - 大小: 4144
[Data] Hex: 30 30 30 30 30 30 30 20 30 30 3a 30 31 20 39 32 
[Data] Char: 0000000 00:01 92
[File Read #101] 非系统文件，大小: 19782
[Extract] FileRead - 大小: 19782
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 91 9d 
[Data] Char: PK..........!8..
[File Read #102] 非系统文件，大小: 4112
[Extract] FileRead - 大小: 4112
[Data] Hex: 65 20 6c 69 76 65 2d 62 69 74 6d 61 70 20 33 20 
[Data] Char: e live-bitmap 3 
[File Read #103] 非系统文件，大小: 4096
[Extract] FileRead - 大小: 4096
[Data] Hex: 30 62 66 30 30 30 20 31 30 33 3a 31 37 20 35 39 
[Data] Char: 0bf000 103:17 59
[File Read #104] 非系统文件，大小: 4034
[Extract] FileRead - 大小: 4034
[Data] Hex: 20 2f 73 79 73 74 65 6d 2f 66 6f 6e 74 73 2f 4e 
[Data] Char:  /system/fonts/N
[File Read #105] 非系统文件，大小: 4092
[Extract] FileRead - 大小: 4092
[Data] Hex: 36 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 
[Data] Char: 6               
[File Read #106] 非系统文件，大小: 4095
[Extract] FileRead - 大小: 4095
[Data] Hex: 72 77 2d 70 20 30 30 30 30 30 30 30 30 20 30 30 
[Data] Char: rw-p 00000000 00
[File Read #107] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 50 72 6f 74 6f 5f 41 53 50 4b 01 02 14 00 14 00 
[Data] Char: Proto_ASPK......
[File Read #108] 非系统文件，大小: 10574
[Extract] FileRead - 大小: 10574
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #109] 非系统文件，大小: 10574
[Extract] FileRead - 大小: 10574
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #110] 非系统文件，大小: 4152
[Extract] FileRead - 大小: 4152
[Data] Hex: 30 30 30 30 30 30 20 30 30 3a 30 30 20 30 20 20 
[Data] Char: 000000 00:00 0  
[File Read #111] 非系统文件，大小: 4120
[Extract] FileRead - 大小: 4120
[Data] Hex: 38 30 30 30 20 72 2d 2d 70 20 30 30 30 30 30 30 
[Data] Char: 8000 r--p 000000
[File Read #112] 非系统文件，大小: 4059
[Extract] FileRead - 大小: 4059
[Data] Hex: 30 30 2d 37 66 61 33 32 64 61 30 30 30 20 2d 2d 
[Data] Char: 00-7fa32da000 --
[File Read #113] 非系统文件，大小: 4165
[Extract] FileRead - 大小: 4165
[Data] Hex: 30 30 30 36 63 30 30 30 20 31 30 33 3a 31 37 20 
[Data] Char: 0006c000 103:17 
[File Read #114] 非系统文件，大小: 17711
[Extract] FileRead - 大小: 17711
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #115] 非系统文件，大小: 4071
[Extract] FileRead - 大小: 4071
[Data] Hex: 36 34 2f 6c 69 62 7a 2e 73 6f 0a 37 66 61 34 31 
[Data] Char: 64/libz.so.7fa41
[File Read #116] 非系统文件，大小: 44467
[Extract] FileRead - 大小: 44467
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 85 7c 04 49 b7 8c 
[Data] Char: PK.........|.I..
[File Read #117] 非系统文件，大小: 9952
[Extract] FileRead - 大小: 9952
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 23 7c 04 49 b7 8c 
[Data] Char: PK........#|.I..
[File Read #118] 非系统文件，大小: 4101
[Extract] FileRead - 大小: 4101
[Data] Hex: 69 62 69 6e 70 75 74 66 6c 69 6e 67 65 72 2e 73 
[Data] Char: ibinputflinger.s
[File Read #119] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 73 2f 64 72 61 77 61 62 6c 65 2d 78 78 68 64 70 
[Data] Char: s/drawable-xxhdp
[File Read #120] 非系统文件，大小: 1579
[Extract] FileRead - 大小: 1579
[Data] Hex: 30 30 20 31 30 33 3a 31 37 20 37 37 38 20 20 20 
[Data] Char: 00 103:17 778   
[File Read #121] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 29 39 2f 32 96 6f 63 7d 76 48 da 23 97 04 0e 28 
[Data] Char: )9/2.oc}vH.#...(
[File Read #122] 非系统文件，大小: 748
[Extract] FileRead - 大小: 748
[Data] Hex: 50 72 6f 63 65 73 73 6f 72 09 3a 20 41 41 72 63 
[Data] Char: Processor.: AArc
[File Read #123] 非系统文件，大小: 19782
[Extract] FileRead - 大小: 19782
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 91 9d 
[Data] Char: PK..........!8..
[File Read #124] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 50 72 6f 74 6f 5f 41 53 50 4b 01 02 14 00 14 00 
[Data] Char: Proto_ASPK......
[File Read #125] 非系统文件，大小: 10574
[Extract] FileRead - 大小: 10574
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #126] 非系统文件，大小: 10574
[Extract] FileRead - 大小: 10574
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #127] 非系统文件，大小: 17711
[Extract] FileRead - 大小: 17711
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #128] 非系统文件，大小: 44467
[Extract] FileRead - 大小: 44467
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 85 7c 04 49 b7 8c 
[Data] Char: PK.........|.I..
[File Read #129] 非系统文件，大小: 9952
[Extract] FileRead - 大小: 9952
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 23 7c 04 49 b7 8c 
[Data] Char: PK........#|.I..
[File Read #130] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 73 2f 64 72 61 77 61 62 6c 65 2d 78 78 68 64 70 
[Data] Char: s/drawable-xxhdp
[File Read #131] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 29 39 2f 32 96 6f 63 7d 76 48 da 23 97 04 0e 28 
[Data] Char: )9/2.oc}vH.#...(
[File Read #132] 非系统文件，大小: 19782
[Extract] FileRead - 大小: 19782
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 91 9d 
[Data] Char: PK..........!8..
[File Read #133] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 50 72 6f 74 6f 5f 41 53 50 4b 01 02 14 00 14 00 
[Data] Char: Proto_ASPK......
[File Read #134] 非系统文件，大小: 10574
[Extract] FileRead - 大小: 10574
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #135] 非系统文件，大小: 10574
[Extract] FileRead - 大小: 10574
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #136] 非系统文件，大小: 17711
[Extract] FileRead - 大小: 17711
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #137] 非系统文件，大小: 122
[Extract] FileRead - 大小: 122
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #138] 非系统文件，大小: 44467
[Extract] FileRead - 大小: 44467
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 85 7c 04 49 b7 8c 
[Data] Char: PK.........|.I..
[File Read #139] 非系统文件，大小: 9952
[Extract] FileRead - 大小: 9952
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 23 7c 04 49 b7 8c 
[Data] Char: PK........#|.I..
[File Read #140] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 7f 45 4c 46 02 01 01 00 00 00 00 00 00 00 00 00 
[Data] Char: .ELF............
[File Read #141] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 6e 74 65 78 74 31 35 53 65 74 45 6d 62 65 64 64 
[Data] Char: ntext15SetEmbedd
[File Read #142] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 73 2f 64 72 61 77 61 62 6c 65 2d 78 78 68 64 70 
[Data] Char: s/drawable-xxhdp
[File Read #143] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 29 39 2f 32 96 6f 63 7d 76 48 da 23 97 04 0e 28 
[Data] Char: )9/2.oc}vH.#...(
[File Read #144] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 58 e3 a4 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: X...............
[File Read #145] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 f0 bb 3f 02 00 00 00 00 
[Data] Char: ..........?.....
[File Read #146] 非系统文件，大小: 119
[Extract] FileRead - 大小: 119
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #147] 非系统文件，大小: 11979
[Extract] FileRead - 大小: 11979
[Data] Hex: 44 2d 4d 4d 2c 31 39 37 30 2d 30 31 2d 30 34 20 
[Data] Char: D-MM,1970-01-04 
[File Read #148] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 64 fb 95 01 00 00 00 00 50 84 a6 03 00 00 00 00 
[Data] Char: d.......P.......
[File Read #149] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 58 4c a7 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: XL..............
[File Read #150] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 40 96 cd 01 00 00 00 00 
[Data] Char: ........@.......
[File Read #151] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 38 c3 52 01 00 00 00 00 00 e7 a8 03 00 00 00 00 
[Data] Char: 8.R.............
[File Read #152] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: b8 bb a9 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: ................
[File Read #153] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 00 c4 52 01 00 00 00 00 
[Data] Char: ..........R.....
[File Read #154] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 58 e9 7c 02 00 00 00 00 38 60 ab 03 00 00 00 00 
[Data] Char: X.|.....8`......
[File Read #155] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 30 18 ac 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: 0...............
[File Read #156] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 18 4f 52 01 00 00 00 00 
[Data] Char: .........OR.....
[File Read #157] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: f0 ad c5 02 00 00 00 00 a8 a3 ad 03 00 00 00 00 
[Data] Char: ................
[File Read #158] 非系统文件，大小: 19782
[Extract] FileRead - 大小: 19782
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 91 9d 
[Data] Char: PK..........!8..
[File Read #159] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 90 6e ae 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: .n..............
[File Read #160] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 94 e9 00 03 00 00 00 00 
[Data] Char: ................
[File Read #161] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 38 c3 52 01 00 00 00 00 78 e7 af 03 00 00 00 00 
[Data] Char: 8.R.....x.......
[File Read #162] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 10 98 b0 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: ................
[File Read #163] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 2c 39 2a 03 00 00 00 00 
[Data] Char: ........,9*.....
[File Read #164] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 9c 13 0a 03 00 00 00 00 08 f7 b1 03 00 00 00 00 
[Data] Char: ................
[File Read #165] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 60 ac b2 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: `...............
[File Read #166] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 70 9f 15 03 00 00 00 00 
[Data] Char: ........p.......
[File Read #167] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 10 1d 15 03 00 00 00 00 28 08 b4 03 00 00 00 00 
[Data] Char: ........(.......
[File Read #168] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 20 b7 b4 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char:  ...............
[File Read #169] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 68 c3 13 03 00 00 00 00 
[Data] Char: ........h.......
[File Read #170] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 40 c3 52 01 00 00 00 00 b8 13 b6 03 00 00 00 00 
[Data] Char: @.R.............
[File Read #171] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 40 cd b6 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: @...............
[File Read #172] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 38 c3 52 01 00 00 00 00 
[Data] Char: ........8.R.....
[File Read #173] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 38 c3 52 01 00 00 00 00 78 35 b8 03 00 00 00 00 
[Data] Char: 8.R.....x5......
[File Read #174] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 90 eb b8 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: ................
[File Read #175] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 18 4f 52 01 00 00 00 00 
[Data] Char: .........OR.....
[File Read #176] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 58 97 5a 01 00 00 00 00 28 64 ba 03 00 00 00 00 
[Data] Char: X.Z.....(d......
[File Read #177] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 80 28 bb 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: .(..............
[File Read #178] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 70 6c 75 03 00 00 00 00 
[Data] Char: ........plu.....
[File Read #179] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 04 67 07 03 00 00 00 00 38 ad bc 03 00 00 00 00 
[Data] Char: .g......8.......
[File Read #180] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 50 74 bd 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: Pt..............
[File Read #181] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 18 4f 52 01 00 00 00 00 
[Data] Char: .........OR.....
[File Read #182] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 50 72 6f 74 6f 5f 41 53 50 4b 01 02 14 00 14 00 
[Data] Char: Proto_ASPK......
[File Read #183] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: b2 d7 69 00 00 00 00 00 88 8a bf 03 00 00 00 00 
[Data] Char: ..i.............
[File Read #184] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 38 ae c0 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: 8...............
[File Read #185] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 f4 10 04 02 00 00 00 00 
[Data] Char: ................
[File Read #186] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: cc b0 75 00 00 00 00 00 68 71 c3 03 00 00 00 00 
[Data] Char: ..u.....hq......
[File Read #187] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 20 9c c4 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char:  ...............
[File Read #188] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 88 84 d0 03 00 00 00 00 
[Data] Char: ................
[File Read #189] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 14 1a 6a 00 00 00 00 00 98 b6 c6 03 00 00 00 00 
[Data] Char: ..j.............
[File Read #190] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 60 bb c7 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: `...............
[File Read #191] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 18 2b 60 03 00 00 00 00 
[Data] Char: .........+`.....
[File Read #192] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: f7 22 6a 00 00 00 00 00 a8 bd c9 03 00 00 00 00 
[Data] Char: ."j.............
[File Read #193] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 38 d5 ca 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: 8...............
[File Read #194] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 30 b8 78 03 00 00 00 00 
[Data] Char: ........0.x.....
[File Read #195] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: b8 9a a8 03 00 00 00 00 30 b1 cc 03 00 00 00 00 
[Data] Char: ........0.......
[File Read #196] 非系统文件，大小: 10574
[Extract] FileRead - 大小: 10574
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #197] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 90 7e cd 03 00 00 00 00 03 04 00 00 00 00 00 00 
[Data] Char: .~..............
[File Read #198] 非系统文件，大小: 10574
[Extract] FileRead - 大小: 10574
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #199] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 03 04 00 00 00 00 00 00 b0 d0 cf 03 00 00 00 00 
[Data] Char: ................
[File Read #200] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 20 68 00 00 08 00 00 00 20 da 11 00 c8 03 00 00 
[Data] Char:  h...... .......
[File Read #201] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 72 6b 65 72 53 74 61 72 74 00 77 65 62 6b 69 74 
[Data] Char: rkerStart.webkit
[File Read #202] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 38 2e 65 78 74 61 64 64 5f 70 61 69 72 77 69 73 
[Data] Char: 8.extadd_pairwis
[File Read #203] 非系统文件，大小: 17711
[Extract] FileRead - 大小: 17711
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 94 2c 
[Data] Char: PK..........!8.,
[File Read #204] 非系统文件，大小: 16384
[Extract] FileRead - 大小: 16384
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #205] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 64 65 72 6f 70 74 69 6f 6e 00 62 6c 6f 63 6b 65 
[Data] Char: deroption.blocke
[File Read #206] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 69 73 70 6c 61 79 4e 61 6d 65 73 50 72 6f 74 6f 
[Data] Char: isplayNamesProto
[File Read #207] 非系统文件，大小: 285
[Extract] FileRead - 大小: 285
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #208] 非系统文件，大小: 117
[Extract] FileRead - 大小: 117
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #209] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 55 6e 61 62 6c 65 20 74 6f 20 63 72 65 61 74 65 
[Data] Char: Unable to create
[File Read #210] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 69 6e 74 65 72 6e 61 6c 46 6f 6e 74 53 69 7a 65 
[Data] Char: internalFontSize
[File Read #211] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 49 4f 4e 5f 54 45 4d 50 4c 41 54 45 5f 52 41 52 
[Data] Char: ION_TEMPLATE_RAR
[File Read #212] 非系统文件，大小: 19782
[Extract] FileRead - 大小: 19782
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 00 00 21 38 91 9d 
[Data] Char: PK..........!8..
[File Read #213] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 32 54 6f 46 6c 6f 61 74 36 34 00 47 6f 6f 67 6c 
[Data] Char: 2ToFloat64.Googl
[File Read #214] 非系统文件，大小: 16384
[Extract] FileRead - 大小: 16384
[Data] Hex: 37 39 30 2c 26 71 75 6f 74 3b 6c 61 62 65 6c 26 
[Data] Char: 790,&quot;label&
[File Read #215] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 73 20 66 61 69 6c 65 64 2e 00 52 65 61 64 62 61 
[Data] Char: s failed..Readba
[File Read #216] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 20 20 20 20 20 20 68 61 6c 66 20 74 20 3d 20 68 
[Data] Char:       half t = h
[File Read #217] 非系统文件，大小: 44467
[Extract] FileRead - 大小: 44467
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 85 7c 04 49 b7 8c 
[Data] Char: PK.........|.I..
[File Read #218] 非系统文件，大小: 13609
[Extract] FileRead - 大小: 13609
[Data] Hex: 37 26 71 75 6f 74 3b 2c 26 71 75 6f 74 3b 6c 6f 
[Data] Char: 7&quot;,&quot;lo
[File Read #219] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: fe cf a9 d8 37 4e 7e ed 5d 3b 27 57 2d 18 a7 6f 
[Data] Char: ....7N~.];'W-..o
[File Read #220] 非系统文件，大小: 169
[Extract] FileRead - 大小: 169
[Data] Hex: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31 
[Data] Char: <?xml version='1
[File Read #221] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 02 00 00 00 01 00 00 00 03 00 00 00 68 00 00 00 
[Data] Char: ............h...
[File Read #222] 非系统文件，大小: 9952
[Extract] FileRead - 大小: 9952
[Data] Hex: 50 4b 03 04 14 00 00 08 08 00 23 7c 04 49 b7 8c 
[Data] Char: PK........#|.I..
[File Read #223] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 10 01 00 00 9c 04 00 00 00 01 00 00 ac 04 00 00 
[Data] Char: ................
[File Read #224] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 87 42 c6 81 13 b9 bc d3 94 6a de f2 c0 6b 0b 5d 
[Data] Char: .B.......j...k.]
[File Read #225] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 14 14 14 07 0a 0a 0a 0a 0a 0a 0a 0a 0a 0a 0a 0a 
[Data] Char: ................
[File Read #226] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 73 97 cf 5c b4 83 d4 2d 69 ca d3 4c d4 8b af 20 
[Data] Char: s..\...-i..L... 
[File Read #227] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 6f 66 20 74 68 65 20 66 69 72 73 74 63 61 6e 20 
[Data] Char: of the firstcan 
[File Read #228] 非系统文件，大小: 65557
[Extract] FileRead - 大小: 65557
[Data] Hex: 50 72 6f 74 6f 5f 41 53 50 4b 01 02 14 00 14 00 
[Data] Char: Proto_ASPK......
[File Read #229] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 72 65 72 00 75 74 69 6c 69 74 79 00 54 69 6d 69 
[Data] Char: rer.utility.Timi
[File Read #230] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: ff ff 6c 02 ff ff ff ff ff ff ff ff ff ff ff ff 
[Data] Char: ..l.............
[File Read #231] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 1b 00 00 00 1c 00 00 00 0a 00 00 00 0b 00 00 00 
[Data] Char: ................
[File Read #232] 非系统文件，大小: 131072
[Extract] FileRead - 大小: 131072
[Data] Hex: 08 dc 29 00 cc 29 df 00 28 dc 29 00 08 2a df 00 
[Data] Char: ..)..)..(.)..*..
[MAP DATA] 发现地图数据! 类型: Compressed
[HexDump] 完整数据预览:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  08 dc 29 00 cc 29 df 00 28 dc 29 00 08 2a df 00  ..)..)..(.)..*..
00000010  48 dc 29 00 7c 2a df 00 68 dc 29 00 88 2a df 00  H.).|*..h.)..*..
00000020  80 dc 29 00 90 2a df 00 98 dc 29 00 94 2b df 00  ..)..*....)..+..
00000030  c8 dc 29 00 bc 2b df 00 e8 dc 29 00 ec 2b df 00  ..)..+....)..+..
[Report] 文件读取: 232 次
[Report] 地图数据提取: 3/3
[Complete] 地图数据提取完成
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=0000020429295830)

Thread 0x000076c0 (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x00005340 (most recent call first):
<no Python frame>
