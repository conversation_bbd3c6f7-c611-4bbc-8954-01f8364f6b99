// ANS文件解析与渲染流程跟踪脚本 - 修复版

(function() {
    console.log("[ANS渲染分析] 启动...");

    var libamapnsq = null;
    var libamapr = null;
    var lastAnsData = null;
    var renderCalls = 0;
    var hookInterval = null;

    // 阶段性执行，降低启动压力
    setTimeout(function() {
        // 持续检查库是否加载
        hookInterval = setInterval(checkAndSetupHooks, 1000);
    }, 1000);

    function checkAndSetupHooks() {
        // 检查库是否已加载
        libamapnsq = Process.findModuleByName("libamapnsq.so");
        libamapr = Process.findModuleByName("libamapr.so");
        
        if (!libamapnsq) {
            console.log("[ANS渲染分析] 等待libamapnsq.so加载...");
            return;
        }
        
        if (!libamapr) {
            console.log("[ANS渲染分析] 等待libamapr.so加载...");
            return;
        }
        
        // 两个库都已加载，清除定时器并设置钩子
        clearInterval(hookInterval);
        console.log("[ANS渲染分析] 已找到关键库:");
        console.log("  libamapnsq.so: " + libamapnsq.base);
        console.log("  libamapr.so: " + libamapr.base);
        
        // 设置钩子
        setupHooks();
    }

    function setupHooks() {
        try {
            // 1. 钩住ANS文件解析函数
            var parserFuncOffset = 0xC654;  // sub_C654
            var parserFuncAddr = libamapnsq.base.add(parserFuncOffset);
            
            console.log("[ANS渲染分析] 设置解析函数钩子: " + parserFuncAddr);
            
            Interceptor.attach(parserFuncAddr, {
                onEnter: function(args) {
                    this.srcData = args[0];
                    this.destBuffer = args[1];
                    this.sizePtr = args[2];
                    this.controlStruct = args[3];
                    this.size = args[4].toInt32();
                    
                    console.log("[ANS解析] 调用解析函数 sub_C654");
                    console.log("  源数据: " + this.srcData);
                    console.log("  目标缓冲区: " + this.destBuffer);
                    console.log("  大小: " + this.size + " 字节");
                },
                onLeave: function(retval) {
                    if (retval.toInt32() === 0) {
                        try {
                            // 保存解析后数据的引用，供渲染函数分析
                            if (this.sizePtr) {
                                var size = Memory.readInt(this.sizePtr);
                                lastAnsData = {
                                    buffer: this.destBuffer,
                                    size: size,
                                    timestamp: Date.now()
                                };
                                
                                console.log("[ANS解析] 解析成功，解压后大小: " + size);
                                
                                // 分析解压后的数据头部
                                try {
                                    var headerBytes = Memory.readByteArray(this.destBuffer, Math.min(32, size));
                                    console.log("[ANS解析] 解析后数据头部:");
                                    console.log(hexdump(headerBytes, {length: 32}));
                                } catch(e) {
                                    console.log("[ANS解析] 读取头部失败: " + e);
                                }
                            }
                        } catch(e) {
                            console.log("[ANS解析] 读取解析数据失败: " + e);
                        }
                    } else {
                        console.log("[ANS解析] 解析失败，返回值: " + retval);
                    }
                }
            });
            
            // 2. 搜索渲染相关函数的字符串
            try {
                var renderStrings = [
                    "doEGLRequireMapRender_",
                    "doRenderASync",
                    "CMapRenderSystem::init"
                ];
                
                for (var i = 0; i < renderStrings.length; i++) {
                    var pattern = renderStrings[i];
                    console.log("[ANS渲染分析] 搜索字符串: " + pattern);
                    
                    Memory.scan(libamapr.base, libamapr.size, pattern, {
                        onMatch: function(address, size) {
                            console.log("[ANS渲染分析] 找到字符串: " + pattern + " 在 " + address);
                            
                            // 搜索引用这个字符串的代码
                            searchReferences(address);
                            return 'continue';
                        },
                        onComplete: function() {
                            console.log("[ANS渲染分析] 完成字符串搜索: " + pattern);
                        }
                    });
                }
            } catch(e) {
                console.log("[ANS渲染分析] 搜索字符串失败: " + e);
            }
            
            // 3. 直接钩住可能的渲染函数入口点
            try {
                // ARM64函数序言模式
                var funcPrologs = [
                    "FF 83 00 D1 FD 7B 01 A9", // sub sp, sp, #0x30; stp x29, x30, [sp, #0x20]
                    "FF 43 00 D1 F3 0B 00 F9", // sub sp, sp, #0x10; str x19, [sp, #8]
                    "F? 5F BD A9 F3 0B 00 F9"  // stp x29, x30, [sp, #-0x??]!; str x19, [sp, #8]
                ];
                
                for (var j = 0; j < funcPrologs.length; j++) {
                    var pattern = funcPrologs[j];
                    console.log("[ANS渲染分析] 搜索函数序言: " + pattern);
                    
                    // 限制搜索范围，提高性能
                    var searchRange = 0x10000; // 64KB
                    var searchStart = libamapr.base;
                    var searchEnd = searchStart.add(searchRange);
                    
                    while (searchStart.compare(libamapr.base.add(libamapr.size)) < 0) {
                        try {
                            Memory.scan(searchStart, Math.min(searchRange, libamapr.base.add(libamapr.size).sub(searchStart)), pattern, {
                                onMatch: function(address, size) {
                                    console.log("[ANS渲染分析] 找到可能的函数: " + address);
                                    
                                    // 尝试钩住这个函数
                                    try {
                                        Interceptor.attach(address, {
                                            onEnter: function(args) {
                                                // 检查是否有ANS数据刚被解析
                                                if (lastAnsData && (Date.now() - lastAnsData.timestamp < 1000)) {
                                                    console.log("[ANS渲染] 函数 " + address + " 被调用，可能与渲染相关");
                                                    console.log("  参数1: " + args[0]);
                                                    console.log("  参数2: " + args[1]);
                                                }
                                            }
                                        });
                                    } catch(e) {
                                        // 忽略无法钩住的地址
                                    }
                                    
                                    return 'continue';
                                },
                                onComplete: function() {}
                            });
                        } catch(e) {
                            console.log("[ANS渲染分析] 搜索区间错误: " + e);
                        }
                        
                        searchStart = searchEnd;
                        searchEnd = searchStart.add(searchRange);
                    }
                }
            } catch(e) {
                console.log("[ANS渲染分析] 搜索函数序言失败: " + e);
            }
            
            console.log("[ANS渲染分析] 钩子设置完成，请操作地图...");
        } catch(e) {
            console.log("[ANS渲染分析] 设置钩子时出错: " + e);
        }
    }
    
    function searchReferences(strAddr) {
        // 这个函数用于搜索引用字符串的代码
        // 简化实现，实际上这需要更复杂的逻辑
        console.log("[ANS渲染分析] 搜索引用字符串的代码: " + strAddr);
    }
})();
