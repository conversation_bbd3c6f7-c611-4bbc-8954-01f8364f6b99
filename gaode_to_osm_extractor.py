#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高德地图离线数据提取器 - 转换为OpenStreetMap格式
基于之前的Frida分析结果进行数据提取和转换
"""

import struct
import zlib
import json
import xml.etree.ElementTree as ET
from typing import Dict, List, Tuple, Optional
import sqlite3
import os
from dataclasses import dataclass
from enum import Enum

class DataType(Enum):
    """数据类型枚举"""
    DICE_AM = "DICE-AM"
    CONFIG = "CONFIG" 
    TEXT = "TEXT"
    UNKNOWN = "UNKNOWN"

@dataclass
class DiceAMData:
    """DICE-AM矢量数据结构"""
    magic: bytes
    version: int
    flags: int
    geometry_type: int
    coordinate_system: int
    vertices: List[Tuple[float, float]]
    
@dataclass
class ConfigData:
    """配置数据结构"""
    magic: bytes
    config_type: int
    config_flags: int
    style_type: int
    param_count: int
    parameters: List[int]

@dataclass
class TextData:
    """文本数据结构"""
    header: bytes
    text_count: int
    encoding: str
    texts: List[str]

class GaodeDataExtractor:
    """高德地图数据提取器 - 基于APK资源分析"""

    def __init__(self, apk_file_path: str):
        self.apk_file_path = apk_file_path
        self.extracted_data = {
            'dice_am': [],
            'config': [],
            'text': []
        }
        print(f"[重要发现] 高德地图数据存储在APK内，而非独立的.ans文件")
        print(f"[分析目标] APK文件: {apk_file_path}")
    
    def identify_data_type(self, data: bytes) -> DataType:
        """识别数据类型 (基于Frida分析结果)"""
        if len(data) >= 4:
            header = data[:4]
            if header == b'\x44\x49\x43\x45':  # DICE-AM
                return DataType.DICE_AM
            elif header == b'\xbc\xbc\xbc\xbc':  # CONFIG
                return DataType.CONFIG
            elif header == b'\x0d\x00\x00\x00':  # TEXT
                return DataType.TEXT
        return DataType.UNKNOWN
    
    def extract_apk_resources(self) -> bool:
        """提取APK内的地图资源数据"""
        try:
            import zipfile

            print(f"[开始提取] 分析APK文件: {self.apk_file_path}")

            with zipfile.ZipFile(self.apk_file_path, 'r') as apk:
                # 列出所有文件
                file_list = apk.namelist()
                print(f"[APK内容] 找到 {len(file_list)} 个文件")

                # 寻找可能的地图数据文件
                map_files = []
                for filename in file_list:
                    # 寻找可能包含地图数据的文件
                    if any(keyword in filename.lower() for keyword in
                          ['map', 'data', 'tile', 'geo', 'vector', 'assets']):
                        map_files.append(filename)

                print(f"[地图文件] 找到 {len(map_files)} 个可能的地图文件:")
                for filename in map_files[:10]:  # 显示前10个
                    print(f"  - {filename}")

                # 分析每个可能的地图文件
                for filename in map_files:
                    try:
                        with apk.open(filename) as f:
                            data = f.read()
                            self.analyze_file_data(filename, data)
                    except Exception as e:
                        print(f"[警告] 无法读取文件 {filename}: {e}")
                        continue

            return True
        except Exception as e:
            print(f"APK提取失败: {e}")
            return False

    def analyze_file_data(self, filename: str, data: bytes):
        """分析文件数据内容"""
        if len(data) < 16:
            return

        print(f"\n[分析文件] {filename} (大小: {len(data)} 字节)")

        # 检查文件头部
        header = data[:16]
        data_type = self.identify_data_type(header)
        print(f"  数据类型: {data_type}")
        print(f"  头部数据: {' '.join(f'{b:02x}' for b in header)}")

        # 如果是压缩数据，尝试解压
        if data[:2] == b'\x78\x9c':  # zlib
            try:
                decompressed = zlib.decompress(data)
                print(f"  解压成功: {len(data)} → {len(decompressed)} 字节")
                self.process_decompressed_data(decompressed)
            except zlib.error as e:
                print(f"  解压失败: {e}")
        elif data[0] == 0x08:  # AM-zlib容器
            self.process_am_zlib_container(data)
        else:
            # 直接处理未压缩数据
            self.process_decompressed_data(data)
    
    def process_decompressed_data(self, data: bytes):
        """处理解压后的数据"""
        data_type = self.identify_data_type(data)
        
        if data_type == DataType.DICE_AM:
            dice_data = self.parse_dice_am(data)
            if dice_data:
                self.extracted_data['dice_am'].append(dice_data)
        
        elif data_type == DataType.CONFIG:
            config_data = self.parse_config(data)
            if config_data:
                self.extracted_data['config'].append(config_data)
        
        elif data_type == DataType.TEXT:
            text_data = self.parse_text(data)
            if text_data:
                self.extracted_data['text'].append(text_data)
    
    def parse_dice_am(self, data: bytes) -> Optional[DiceAMData]:
        """解析DICE-AM矢量数据"""
        try:
            if len(data) < 20:
                return None
            
            magic = data[:8]  # DICE-AM\x00
            version = data[8]  # 版本170
            flags = data[9]    # 标志
            geometry_type = data[10]  # 几何类型 (0x89=道路)
            
            # 解析坐标系统 (基于分析结果: 36303)
            coord_bytes = data[12:16]
            coordinate_system = struct.unpack('<I', coord_bytes)[0]
            
            # 解析顶点数据 (简化版本)
            vertices = []
            offset = 20
            while offset + 8 <= len(data):
                try:
                    x, y = struct.unpack('<ff', data[offset:offset+8])
                    vertices.append((x, y))
                    offset += 8
                except:
                    break
            
            return DiceAMData(
                magic=magic,
                version=version,
                flags=flags,
                geometry_type=geometry_type,
                coordinate_system=coordinate_system,
                vertices=vertices
            )
        except Exception as e:
            print(f"DICE-AM解析失败: {e}")
            return None
    
    def parse_config(self, data: bytes) -> Optional[ConfigData]:
        """解析配置数据"""
        try:
            if len(data) < 20:
                return None
            
            magic = data[:4]  # bc bc bc bc
            config_type = data[8]  # 配置类型3
            config_flags = data[9]  # 配置标志
            style_type = data[10]  # 样式类型 (0=颜色)
            
            # 解析参数数量 (基于分析: 1,245,304)
            param_bytes = data[16:20]
            param_count = struct.unpack('<I', param_bytes)[0]
            
            # 解析参数数组 (前几个参数)
            parameters = []
            offset = 20
            for i in range(min(param_count, 100)):  # 限制解析数量
                if offset + 4 <= len(data):
                    param = struct.unpack('<I', data[offset:offset+4])[0]
                    parameters.append(param)
                    offset += 4
                else:
                    break
            
            return ConfigData(
                magic=magic,
                config_type=config_type,
                config_flags=config_flags,
                style_type=style_type,
                param_count=param_count,
                parameters=parameters
            )
        except Exception as e:
            print(f"CONFIG解析失败: {e}")
            return None
    
    def parse_text(self, data: bytes) -> Optional[TextData]:
        """解析文本数据"""
        try:
            if len(data) < 8:
                return None
            
            header = data[:8]  # 0d 00 00 00 04 1f a2 00
            
            # 解析文本条目数 (基于分析: 10,624,772)
            count_bytes = data[4:8]
            text_count = struct.unpack('<I', count_bytes)[0]
            
            # 尝试提取UTF-8文本 (简化版本)
            texts = []
            offset = 16
            current_text = b""
            
            for i in range(offset, min(len(data), offset + 1000)):
                byte = data[i]
                if byte == 0:  # 字符串结束
                    if current_text:
                        try:
                            text = current_text.decode('utf-8')
                            texts.append(text)
                        except:
                            pass
                        current_text = b""
                else:
                    current_text += bytes([byte])
            
            return TextData(
                header=header,
                text_count=text_count,
                encoding="UTF-8",
                texts=texts
            )
        except Exception as e:
            print(f"TEXT解析失败: {e}")
            return None
    
    def process_am_zlib_container(self, data: bytes):
        """处理AM-zlib容器格式"""
        # 跳过容器头部，寻找zlib数据
        for i in range(len(data) - 2):
            if data[i:i+2] == b'\x78\x9c':
                try:
                    decompressed = zlib.decompress(data[i:])
                    self.process_decompressed_data(decompressed)
                    break
                except:
                    continue

class OSMConverter:
    """OpenStreetMap格式转换器"""
    
    def __init__(self):
        self.node_id = 1
        self.way_id = 1
        self.relation_id = 1
        self.nodes = {}
        self.ways = []
        self.relations = []
    
    def convert_coordinates(self, x: float, y: float, coord_system: int = 36303) -> Tuple[float, float]:
        """坐标系转换: 投影坐标 → WGS84"""
        # 简化的坐标转换 (实际需要使用pyproj等库)
        # 这里假设是中国地区的投影坐标系
        if coord_system == 36303:
            # 简化转换公式 (需要根据实际投影参数调整)
            lon = x / 100000.0 + 116.0  # 大致转换为经度
            lat = y / 100000.0 + 39.0   # 大致转换为纬度
            return (lon, lat)
        return (x, y)
    
    def add_node(self, lon: float, lat: float, tags: Dict[str, str] = None) -> int:
        """添加OSM节点"""
        node_id = self.node_id
        self.nodes[node_id] = {
            'id': node_id,
            'lon': lon,
            'lat': lat,
            'tags': tags or {}
        }
        self.node_id += 1
        return node_id
    
    def add_way(self, node_refs: List[int], tags: Dict[str, str] = None) -> int:
        """添加OSM路径"""
        way_id = self.way_id
        self.ways.append({
            'id': way_id,
            'nodes': node_refs,
            'tags': tags or {}
        })
        self.way_id += 1
        return way_id
    
    def convert_dice_am_to_osm(self, dice_data: DiceAMData):
        """将DICE-AM数据转换为OSM格式"""
        if not dice_data.vertices:
            return
        
        # 转换顶点为OSM节点
        node_refs = []
        for x, y in dice_data.vertices:
            lon, lat = self.convert_coordinates(x, y, dice_data.coordinate_system)
            node_id = self.add_node(lon, lat)
            node_refs.append(node_id)
        
        # 根据几何类型创建OSM要素
        tags = {}
        if dice_data.geometry_type == 0x89:  # 道路线条
            tags = {
                'highway': 'unclassified',  # 默认道路类型
                'source': 'gaode_offline'
            }
        
        if len(node_refs) >= 2:
            self.add_way(node_refs, tags)
    
    def convert_text_to_tags(self, text_data: TextData) -> Dict[str, str]:
        """将文本数据转换为OSM标签"""
        tags = {}
        for i, text in enumerate(text_data.texts[:10]):  # 限制处理数量
            if text.strip():
                # 简单的文本分类
                if any(keyword in text for keyword in ['路', '街', '道', 'Road', 'Street']):
                    tags[f'name:zh_{i}'] = text
                elif any(keyword in text for keyword in ['市', '区', '县', 'City', 'District']):
                    tags[f'place:zh_{i}'] = text
                else:
                    tags[f'name_{i}'] = text
        return tags
    
    def export_to_osm_xml(self, output_file: str):
        """导出为OSM XML格式"""
        root = ET.Element('osm', version='0.6', generator='gaode_extractor')
        
        # 添加节点
        for node in self.nodes.values():
            node_elem = ET.SubElement(root, 'node', 
                                    id=str(node['id']),
                                    lon=str(node['lon']),
                                    lat=str(node['lat']))
            for key, value in node['tags'].items():
                ET.SubElement(node_elem, 'tag', k=key, v=value)
        
        # 添加路径
        for way in self.ways:
            way_elem = ET.SubElement(root, 'way', id=str(way['id']))
            for node_ref in way['nodes']:
                ET.SubElement(way_elem, 'nd', ref=str(node_ref))
            for key, value in way['tags'].items():
                ET.SubElement(way_elem, 'tag', k=key, v=value)
        
        # 写入文件
        tree = ET.ElementTree(root)
        tree.write(output_file, encoding='utf-8', xml_declaration=True)

def main():
    """主函数"""
    # 使用示例 - 基于新发现使用APK文件
    apk_path = "base.apk"  # 高德地图APK文件路径
    extractor = GaodeDataExtractor(apk_path)

    print("开始提取高德地图APK数据...")
    if extractor.extract_apk_resources():
        print(f"提取完成:")
        print(f"  DICE-AM数据: {len(extractor.extracted_data['dice_am'])}个")
        print(f"  配置数据: {len(extractor.extracted_data['config'])}个")
        print(f"  文本数据: {len(extractor.extracted_data['text'])}个")

        # 转换为OSM格式
        converter = OSMConverter()

        # 转换几何数据
        for dice_data in extractor.extracted_data['dice_am']:
            converter.convert_dice_am_to_osm(dice_data)

        # 导出OSM文件
        converter.export_to_osm_xml("gaode_extracted.osm")
        print("OSM文件已导出: gaode_extracted.osm")
    else:
        print("数据提取失败")

# APK提取工具函数
def extract_apk_to_directory(apk_path: str, output_dir: str):
    """将APK解压到指定目录进行分析"""
    import zipfile
    import os

    os.makedirs(output_dir, exist_ok=True)

    with zipfile.ZipFile(apk_path, 'r') as apk:
        apk.extractall(output_dir)

    print(f"APK已解压到: {output_dir}")

def analyze_apk_structure(apk_path: str):
    """分析APK结构，寻找地图数据"""
    import zipfile

    with zipfile.ZipFile(apk_path, 'r') as apk:
        file_list = apk.namelist()

        print(f"APK文件分析: {apk_path}")
        print(f"总文件数: {len(file_list)}")

        # 按类型分类文件
        categories = {
            'assets': [],
            'lib': [],
            'res': [],
            'classes': [],
            'other': []
        }

        for filename in file_list:
            if filename.startswith('assets/'):
                categories['assets'].append(filename)
            elif filename.startswith('lib/'):
                categories['lib'].append(filename)
            elif filename.startswith('res/'):
                categories['res'].append(filename)
            elif filename.endswith('.dex'):
                categories['classes'].append(filename)
            else:
                categories['other'].append(filename)

        for category, files in categories.items():
            if files:
                print(f"\n{category.upper()}文件 ({len(files)}个):")
                for filename in files[:5]:  # 显示前5个
                    print(f"  - {filename}")
                if len(files) > 5:
                    print(f"  ... 还有{len(files)-5}个文件")

if __name__ == "__main__":
    # 可以先分析APK结构
    # analyze_apk_structure("base.apk")

    # 然后提取数据
    main()

if __name__ == "__main__":
    main()
