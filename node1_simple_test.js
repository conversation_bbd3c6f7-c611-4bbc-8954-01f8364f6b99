/*
 * 节点1简化测试脚本
 * 测试基本的文件读取Hook功能
 */

console.log("=== 节点1简化测试 ===");

// 简单的统计
var stats = {
    fileOps: 0,
    ansFiles: 0
};

// 检查.ans文件
function isAnsFile(filename) {
    return filename && filename.indexOf('.ans') !== -1;
}

// 尝试Hook libc
try {
    var libc = Process.getModuleByName("libc.so");
    console.log("[✓] libc.so 找到");
    
    // Hook open
    var openPtr = libc.getExportByName("open");
    if (openPtr) {
        Interceptor.attach(openPtr, {
            onEnter: function(args) {
                var filename = args[0].readCString();
                if (isAnsFile(filename)) {
                    console.log("[ANS文件] 打开:", filename);
                    stats.ansFiles++;
                }
                stats.fileOps++;
            }
        });
        console.log("[✓] open() Hook 设置完成");
    }
    
    // Hook read
    var readPtr = libc.getExportByName("read");
    if (readPtr) {
        Interceptor.attach(readPtr, {
            onLeave: function(retval) {
                var bytes = retval.toInt32();
                if (bytes > 0) {
                    // 简单统计
                }
            }
        });
        console.log("[✓] read() Hook 设置完成");
    }
    
} catch (e) {
    console.log("[✗] Hook设置失败:", e);
}

// 定期报告
setInterval(function() {
    console.log("统计: 文件操作=" + stats.fileOps + ", ANS文件=" + stats.ansFiles);
}, 10000);

console.log("节点1简化测试启动完成");
