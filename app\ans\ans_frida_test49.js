// 高德地图 nativeAddMapGestureMsg 稳定追踪脚本 (V50) - 直接修改源文件
// 适用于 Frida 12.9.7，使用 ES5 语法
// 专注于稳定性和核心流程的确认

(function() {
    'use-strict';

    // 全局配置
    var config = {
        // 关键函数偏移量 (相对于 libamapr.so 基地址)
        nativeAddMapGestureMsg_offset: 0x6ee70c
    };

    // 工具函数 - 日志输出
    function log(message) {
        console.log("[+] " + message);
    }

    function logError(message) {
        console.log("[-] " + message);
    }

    // 工具函数 - 格式化地址
    function formatAddress(address) {
        if (!address) return "0x0";
        try {
            return "0x" + address.toString(16);
        } catch (e) {
            return "0x???";
        }
    }

    // 1. 钩住 Java 层的 nativeAddMapGestureMsg 方法
    function hookJavaGestureMethod() {
        log("准备监控 Java 层 nativeAddMapGestureMsg 方法...");
        
        Java.perform(function() {
            try {
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                        var startTime = new Date().getTime();
                        
                        log("\n>>> Java 层调用: nativeAddMapGestureMsg (手势类型: " + type + ")");
                        
                        // 调用原始方法
                        var result = this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                        
                        var totalTime = new Date().getTime() - startTime;
                        log("<<< Java 层调用结束，耗时: " + totalTime + "ms\n");
                        
                        return result;
                    };
                    log("成功钩住 Java 层 nativeAddMapGestureMsg 方法");
                } else {
                    logError("未找到 nativeAddMapGestureMsg 方法");
                }
            } catch (e) {
                logError("钩住 Java 层方法失败: " + e);
            }
        });
    }

    // 2. 钩住 Native 层的 nativeAddMapGestureMsg 函数
    function hookNativeGestureMethod() {
        log("准备钩住 Native 层 nativeAddMapGestureMsg 函数...");
        
        var libamapr = Process.findModuleByName("libamapr.so");
        if (!libamapr) {
            logError("未找到 libamapr.so 模块，无法钩住Native函数");
            return;
        }
        
        var targetAddr = libamapr.base.add(config.nativeAddMapGestureMsg_offset);
        log("目标函数地址: " + formatAddress(targetAddr));
        
        try {
            Interceptor.attach(targetAddr, {
                onEnter: function(args) {
                    this.startTime = new Date().getTime();
                    log("    ---> 进入 Native 函数: nativeAddMapGestureMsg (0x6ee70c)");
                    
                    // 简单打印参数，避免复杂操作
                    try {
                        log("         参数: type=" + args[4].toInt32() + 
                            ", p1=" + args[5].readFloat().toFixed(2) + 
                            ", p2=" + args[6].readFloat().toFixed(2));
                    } catch(e) {
                        logError("         读取Native参数失败: " + e.message);
                    }
                },
                
                onLeave: function(retval) {
                    var execTime = new Date().getTime() - this.startTime;
                    log("    <--- 退出 Native 函数: nativeAddMapGestureMsg (耗时: " + execTime + "ms)");
                }
            });
            log("成功钩住 Native 层 nativeAddMapGestureMsg 函数");
        } catch (e) {
            logError("钩住 Native 函数失败: " + e);
        }
    }

    // 主函数
    function main() {
        log("高德地图手势处理稳定追踪脚本 (V50) 启动");
        
        // 延迟执行，确保应用完全初始化
        setTimeout(function() {
            try {
                // 1. 先钩住Java层
                hookJavaGestureMethod();
                
                // 2. 再钩住Native层
                hookNativeGestureMethod();
                
                log("脚本设置完成，等待手势事件...");
            } catch (e) {
                logError("脚本初始化失败: " + e);
            }
        }, 3000); // 增加延迟以提高稳定性
    }
    
    // 启动脚本
    main();
})();