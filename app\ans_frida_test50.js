(function() {
    'use strict';
    
    // 配置
    var config = {
        libName: "libamapr.so",
        offsets: {
            // 核心函数地址
            nativeAddMapGestureMsg: 0x6ee70c,  // 手势消息入口点
            getMapEngineInstance: 0x6fb98c,    // 获取地图引擎实例
            validateEngine: 0x6f3430,          // 验证引擎有效性
            getEnginePointer: 0x6fb5e8,        // 获取引擎指针
            processGestureMessage: 0x6fb530,   // 处理手势消息
            triggerRenderUpdate: 0x6fbc78,     // 触发渲染更新
            finalizeProcessing: 0x6fb9e0       // 完成处理
        },
        debugLevel: 2,  // 0=仅错误, 1=基本信息, 2=详细信息
        includeBacktrace: true,
        maxBacktraceDepth: 10
    };
    
    // 全局变量
    var baseAddr = null;
    var addresses = {};
    var lastGestureInfo = null;
    var hookHandles = [];
    
    // 工具函数
    function log(message) {
        console.log("[高德地图手势分析] " + message);
    }
    
    function logError(message) {
        console.log("[高德地图手势分析-错误] " + message);
    }
    
    function logDebug(level, message) {
        if (config.debugLevel >= level) {
            console.log("[高德地图手势分析-调试] " + message);
        }
    }
    
    function getBacktrace() {
        if (!config.includeBacktrace) return "";
        var result = "";
        try {
            result = Thread.backtrace(this.context, Backtracer.ACCURATE)
                .map(DebugSymbol.fromAddress)
                .join("\n");
        } catch (e) {
            result = "获取调用栈失败: " + e;
        }
        return result;
    }
    
    function safeReadPointer(address) {
        try {
            if (!address || address.isNull()) return null;
            return Memory.readPointer(address);
        } catch (e) {
            return null;
        }
    }
    
    function safeReadInt(address) {
        try {
            if (!address || address.isNull()) return 0;
            return Memory.readInt(address);
        } catch (e) {
            return 0;
        }
    }
    
    function safeReadFloat(address) {
        try {
            if (!address || address.isNull()) return 0;
            return Memory.readFloat(address);
        } catch (e) {
            return 0;
        }
    }
    
    function hexdump(address, size) {
        if (!address || address.isNull()) return "[NULL]";
        try {
            return hexdump(address, {
                length: size || 64,
                header: true,
                ansi: false
            });
        } catch (e) {
            return "[无法读取内存]";
        }
    }
    
    function getGestureTypeName(type) {
        var types = {
            1: "单指按下",
            2: "单指抬起",
            3: "单指移动",
            4: "双指按下",
            5: "双指抬起",
            6: "双指移动",
            7: "双指缩放",
            8: "双指旋转"
        };
        return types[type] || "未知类型(" + type + ")";
    }
    
    // 初始化地址
    function initAddresses() {
        var modules = Process.enumerateModules();
        for (var i = 0; i < modules.length; i++) {
            if (modules[i].name === config.libName) {
                baseAddr = modules[i].base;
                break;
            }
        }
        
        if (!baseAddr) {
            logError(config.libName + " 未找到!");
            return false;
        }
        
        log(config.libName + " 基址: " + baseAddr);
        
        for (var name in config.offsets) {
            if (config.offsets.hasOwnProperty(name)) {
                addresses[name] = baseAddr.add(config.offsets[name]);
                logDebug(2, "函数 " + name + " 地址: " + addresses[name]);
            }
        }
        
        return true;
    }
    
    // 安装钩子
    function installHooks() {
        try {
            // 1. 主入口点: nativeAddMapGestureMsg
            var nativeAddMapGestureMsgPtr = addresses.nativeAddMapGestureMsg;
            var nativeAddMapGestureMsgHook = Interceptor.attach(nativeAddMapGestureMsgPtr, {
                onEnter: function(args) {
                    this.startTime = new Date().getTime();
                    this.engineId = args[0].toInt32();
                    this.nativePtr = args[1];
                    this.gestureType = args[2].toInt32();
                    this.param1 = args[3].toFloat();
                    this.param2 = args[4].toFloat();
                    this.param3 = args[5].toFloat();
                    this.param4 = args[6].toInt32();
                    
                    log("\n========== 手势事件开始 ==========");
                    log("函数调用: nativeAddMapGestureMsg");
                    log("引擎ID: " + this.engineId);
                    log("指针: " + this.nativePtr);
                    log("手势类型: " + getGestureTypeName(this.gestureType) + " (" + this.gestureType + ")");
                    log("参数: " + this.param1 + ", " + this.param2 + ", " + this.param3 + ", " + this.param4);
                    
                    lastGestureInfo = {
                        engineId: this.engineId,
                        nativePtr: this.nativePtr,
                        gestureType: this.gestureType,
                        param1: this.param1,
                        param2: this.param2,
                        param3: this.param3,
                        param4: this.param4
                    };
                    
                    if (config.includeBacktrace) {
                        log("调用栈:\n" + getBacktrace.call(this));
                    }
                },
                onLeave: function(retval) {
                    var duration = new Date().getTime() - this.startTime;
                    log("函数返回: " + retval);
                    log("执行时间: " + duration + "ms");
                    log("========== 手势事件结束 ==========\n");
                }
            });
            hookHandles.push(nativeAddMapGestureMsgHook);
            
            // 2. 获取地图引擎实例: getMapEngineInstance
            var getMapEngineInstancePtr = addresses.getMapEngineInstance;
            var getMapEngineInstanceHook = Interceptor.attach(getMapEngineInstancePtr, {
                onEnter: function(args) {
                    this.startTime = new Date().getTime();
                    this.engineId = args[1].toInt32();
                    
                    logDebug(2, "\n--- 获取地图引擎实例 ---");
                    logDebug(2, "引擎ID: " + this.engineId);
                    
                    if (config.includeBacktrace && config.debugLevel >= 2) {
                        logDebug(2, "调用栈:\n" + getBacktrace.call(this));
                    }
                },
                onLeave: function(retval) {
                    var duration = new Date().getTime() - this.startTime;
                    logDebug(2, "返回引擎实例: " + retval);
                    logDebug(2, "执行时间: " + duration + "ms");
                    
                    // 分析返回的引擎实例
                    if (!retval.isNull()) {
                        try {
                            var engineType = safeReadInt(retval);
                            logDebug(2, "引擎类型: " + engineType);
                            
                            // 读取引擎内部结构
                            var internalPtr = safeReadPointer(retval.add(232));
                            logDebug(2, "内部引擎指针: " + internalPtr);
                        } catch (e) {
                            logError("分析引擎实例失败: " + e);
                        }
                    }
                }
            });
            hookHandles.push(getMapEngineInstanceHook);
            
            // 3. 验证引擎有效性: validateEngine
            var validateEnginePtr = addresses.validateEngine;
            var validateEngineHook = Interceptor.attach(validateEnginePtr, {
                onEnter: function(args) {
                    this.startTime = new Date().getTime();
                    this.enginePtr = args[0];
                    
                    log("\n--- 验证引擎有效性 ---");
                    log("引擎指针: " + this.enginePtr);
                    
                    // 分析引擎结构
                    if (!this.enginePtr.isNull()) {
                        try {
                            var internalPtr = safeReadPointer(this.enginePtr.add(232));
                            log("内部引擎指针: " + internalPtr);
                            
                            if (internalPtr && !internalPtr.isNull()) {
                                var vtablePtr = safeReadPointer(internalPtr);
                                log("虚函数表指针: " + vtablePtr);
                                
                                if (vtablePtr && !vtablePtr.isNull()) {
                                    var validationFuncPtr = safeReadPointer(vtablePtr.add(368));
                                    log("验证函数指针: " + validationFuncPtr);
                                }
                            }
                        } catch (e) {
                            logError("分析引擎结构失败: " + e);
                        }
                    }
                },
                onLeave: function(retval) {
                    var duration = new Date().getTime() - this.startTime;
                    var isValid = retval.toInt32() !== 0;
                    log("引擎有效性: " + (isValid ? "有效" : "无效"));
                    log("执行时间: " + duration + "ms");
                }
            });
            hookHandles.push(validateEngineHook);
            
            // 4. 获取引擎指针: getEnginePointer
            var getEnginePointerPtr = addresses.getEnginePointer;
            var getEnginePointerHook = Interceptor.attach(getEnginePointerPtr, {
                onEnter: function(args) {
                    this.startTime = new Date().getTime();
                    this.enginePtr = args[0];
                    
                    log("\n--- 获取引擎指针 ---");
                    log("输入引擎指针: " + this.enginePtr);
                    
                    if (config.includeBacktrace) {
                        log("调用栈:\n" + getBacktrace.call(this));
                    }
                },
                onLeave: function(retval) {
                    var duration = new Date().getTime() - this.startTime;
                    log("返回引擎指针: " + retval);
                    log("执行时间: " + duration + "ms");
                    
                    // 分析返回的引擎指针
                    if (!retval.isNull()) {
                        try {
                            var vtablePtr = safeReadPointer(retval);
                            log("虚函数表指针: " + vtablePtr);
                        } catch (e) {
                            logError("分析引擎指针失败: " + e);
                        }
                    }
                }
            });
            hookHandles.push(getEnginePointerHook);
            
            // 5. 处理手势消息: processGestureMessage
            var processGestureMessagePtr = addresses.processGestureMessage;
            var processGestureMessageHook = Interceptor.attach(processGestureMessagePtr, {
                onEnter: function(args) {
                    this.startTime = new Date().getTime();
                    this.enginePtr = args[0];
                    
                    log("\n--- 处理手势消息 ---");
                    log("引擎指针: " + this.enginePtr);
                    
                    // 分析引擎指针
                    if (!this.enginePtr.isNull()) {
                        try {
                            var funcPtr = safeReadPointer(this.enginePtr.add(56));
                            log("处理函数指针: " + funcPtr);
                        } catch (e) {
                            logError("分析处理函数指针失败: " + e);
                        }
                    }
                    
                    if (config.includeBacktrace) {
                        log("调用栈:\n" + getBacktrace.call(this));
                    }
                },
                onLeave: function(retval) {
                    var duration = new Date().getTime() - this.startTime;
                    log("返回处理结果: " + retval);
                    log("执行时间: " + duration + "ms");
                }
            });
            hookHandles.push(processGestureMessageHook);
            
            // 6. 触发渲染更新: triggerRenderUpdate
            var triggerRenderUpdatePtr = addresses.triggerRenderUpdate;
            var triggerRenderUpdateHook = Interceptor.attach(triggerRenderUpdatePtr, {
                onEnter: function(args) {
                    this.startTime = new Date().getTime();
                    this.enginePtr = args[0];
                    
                    log("\n--- 触发渲染更新 ---");
                    log("引擎指针: " + this.enginePtr);
                    
                    if (config.includeBacktrace) {
                        log("调用栈:\n" + getBacktrace.call(this));
                    }
                },
                onLeave: function(retval) {
                    var duration = new Date().getTime() - this.startTime;
                    log("渲染更新结果: " + retval);
                    log("执行时间: " + duration + "ms");
                }
            });
            hookHandles.push(triggerRenderUpdateHook);
            
            // 7. 完成处理: finalizeProcessing
            var finalizeProcessingPtr = addresses.finalizeProcessing;
            var finalizeProcessingHook = Interceptor.attach(finalizeProcessingPtr, {
                onEnter: function(args) {
                    this.startTime = new Date().getTime();
                    this.enginePtr = args[0];
                    
                    log("\n--- 完成处理 ---");
                    log("引擎指针: " + this.enginePtr);
                    
                    if (config.includeBacktrace) {
                        log("调用栈:\n" + getBacktrace.call(this));
                    }
                },
                onLeave: function(retval) {
                    var duration = new Date().getTime() - this.startTime;
                    log("完成处理结果: " + retval);
                    log("执行时间: " + duration + "ms");
                }
            });
            hookHandles.push(finalizeProcessingHook);
            
            log("成功安装所有钩子");
            return true;
        } catch (e) {
            logError("安装钩子失败: " + e);
            return false;
        }
    }
    
    // 主函数
    function main() {
        log("高德地图手势处理流程分析脚本启动");
        
        if (!initAddresses()) {
            logError("初始化地址失败，退出");
            return;
        }
        
        if (!installHooks()) {
            logError("安装钩子失败，退出");
            return;
        }
        
        log("脚本初始化完成，等待手势事件...");
        log("请在地图上进行手势操作以触发分析流程");
    }
    
    // 执行主函数
    main();
})();
