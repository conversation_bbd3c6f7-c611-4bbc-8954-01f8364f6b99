# IDA Pro AM-zlib静态分析计划

## 🎯 **分析目标**

基于我们发现的AM-zlib数据特征，在IDA Pro中寻找解压算法：

### **已知特征**:
```
AM-zlib头部: 41 4d 2d 7a 6c 69 62 00 aa 00 89 8d cf 8d 00 00
压缩数据头: 00 01 00 00 [4字节] 00 00 00 00 fe fe 00 00 [4字节] ...
特征字节: 0xfefe (可能是分隔符)
数据熵: 5.81-5.86 bits/byte
```

## 📋 **IDA Pro分析步骤**

### **第1步: 搜索特征字节序列**

#### **搜索AM-zlib字符串**
```
IDA Pro操作:
1. Alt+B (Binary Search)
2. 搜索: 41 4D 2D 7A 6C 69 62 00
3. 查找所有引用这个字符串的函数
```

#### **搜索压缩特征模式**
```
搜索模式:
1. 00 01 00 00 (压缩数据开头)
2. FE FE (分隔符)
3. 00 00 00 01 00 00 00 04 (固定尾部)
```

### **第2步: 分析函数调用链**

#### **查找文件读取函数**
```
目标函数:
├─ read() 的交叉引用
├─ mmap() 的交叉引用  
├─ AAsset_read() 的交叉引用
└─ 处理.ans文件的函数
```

#### **查找内存操作函数**
```
目标函数:
├─ memcpy() 的大数据拷贝
├─ malloc() 的大内存分配
├─ 处理44MB+数据的函数
└─ 循环处理数据的函数
```

### **第3步: 分析数据处理逻辑**

#### **寻找解压算法特征**
```
算法特征:
├─ 循环处理数据块
├─ 位操作和移位
├─ 查找表或字典
├─ 状态机处理
└─ 输出缓冲区管理
```

#### **分析函数参数**
```
函数签名模式:
├─ decompress(src, src_size, dest, dest_size)
├─ am_zlib_decode(data, size, output)
├─ process_compressed_data(...)
└─ 返回解压后大小的函数
```

## 🔍 **具体分析任务**

### **任务1: 搜索字符串引用**

让我在IDA Pro中执行这些搜索：

#### **搜索AM-zlib字符串**
- 目标: 找到处理AM-zlib头部的函数
- 方法: 二进制搜索 + 交叉引用分析

#### **搜索压缩特征**
- 目标: 找到识别压缩数据的代码
- 方法: 搜索0xfefe等特征字节

### **任务2: 分析大数据处理函数**

#### **查找处理44MB+数据的函数**
- 目标: 找到处理大型.ans文件的函数
- 方法: 分析malloc大内存分配的调用者

#### **分析循环处理逻辑**
- 目标: 找到解压算法的核心循环
- 方法: 查找处理数据块的循环结构

### **任务3: 逆向解压算法**

#### **分析算法结构**
```
预期算法结构:
1. 读取AM-zlib头部 (16字节)
2. 验证魔数和版本
3. 解析压缩数据头部
4. 循环解压数据块
5. 输出到目标缓冲区
```

#### **识别关键函数**
```
关键函数类型:
├─ 头部解析函数
├─ 数据块解压函数
├─ 缓冲区管理函数
└─ 错误处理函数
```

## 🛠️ **实际操作计划**

### **立即执行的IDA Pro操作**

#### **1. 字符串搜索**
```
操作序列:
1. 打开IDA Pro
2. 加载高德地图的so文件
3. Alt+B 搜索 "41 4D 2D 7A 6C 69 62 00"
4. 记录所有匹配地址
5. 分析每个地址的交叉引用
```

#### **2. 函数分析**
```
分析目标:
1. 找到引用AM-zlib字符串的函数
2. 分析函数的参数和返回值
3. 跟踪数据流
4. 识别解压逻辑
```

#### **3. 算法逆向**
```
逆向步骤:
1. 理解函数的输入输出
2. 分析数据处理流程
3. 识别算法的关键步骤
4. 重构算法逻辑
```

### **预期发现**

#### **可能的函数名模式**
```
函数名可能包含:
├─ am_zlib, amzlib
├─ decompress, decode
├─ inflate, expand
├─ process, parse
└─ 高德内部命名规则
```

#### **算法特征**
```
预期算法特征:
├─ 自定义压缩格式
├─ 基于块的处理
├─ 可能使用LZ77或类似算法
├─ 针对地图数据优化
└─ 包含校验和验证
```

## 📊 **分析结果记录**

### **发现的函数地址**
```
记录格式:
├─ 函数地址: 0x...
├─ 函数名: ...
├─ 参数: ...
├─ 功能: ...
└─ 相关性: ...
```

### **算法逻辑**
```
记录内容:
├─ 输入格式
├─ 处理步骤
├─ 输出格式
├─ 关键算法
└─ 实现细节
```

## 🎯 **最终目标**

### **完整逆向AM-zlib算法**
1. ✅ 理解数据格式 (已完成)
2. 🔄 找到解压函数 (进行中)
3. ⏳ 逆向算法逻辑
4. ⏳ 实现Python版本
5. ⏳ 验证解压结果

### **成功标准**
- 找到AM-zlib解压函数
- 理解解压算法逻辑
- 成功解压真实数据
- 获得可读的地图数据

这个分析计划将帮助我们在IDA Pro中系统地寻找和逆向AM-zlib解压算法。
