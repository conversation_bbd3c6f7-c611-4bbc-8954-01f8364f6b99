     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
[Code Logic Analyzer] 启动代码执行逻辑分析器...
[Java] Java环境已准备就绪
[Main] 等待应用初始化完成...
[Remote::com.autonavi.minimap]-> [Main] 开始初始化分析器...
[Library] libamapnsq.so 已加载，基址: 0x7f75f3e000
[Analysis] 开始分析 libamapnsq.so 中的数据解析逻辑...
[Analysis] libamapnsq.so 基址: 0x7f75f3e000
[Parser] 开始分析 sub_10F88 (DICE-AM解析器)...
[Parser] sub_10F88 hook 已设置
[Code Logic Analyzer] 代码逻辑分析器已启动!
现在移动地图以触发数据解析，观察代码执行逻辑...
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f4ce6a6d8, args[1]=0x0, args[2]=0xffffffff
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f4ce6a6d8, args[1]=0x0, args[2]=0x4009
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f4ce6a6d8, args[1]=0x2, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 12ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f4ce6a6d8, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 4ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f4ce6a6d8, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 6ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 5ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 8ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功

=== 代码执行逻辑分析总结 ===
函数调用总数: 31
成功解析: 31
失败解析: 0
==========================

[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 4ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[ENTER]     sub_10F88: 开始解析DICE-AM数据块
[PARAM]     sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE]   sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT]   sub_10F88: 解析状态: 成功
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 16ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 8ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[ENTER]     sub_10F88: 开始解析DICE-AM数据块
[PARAM]     sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE]   sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT]   sub_10F88: 解析状态: 成功
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 11ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[ENTER]     sub_10F88: 开始解析DICE-AM数据块
[PARAM]     sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE]   sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT]   sub_10F88: 解析状态: 成功
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 8ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[ENTER]     sub_10F88: 开始解析DICE-AM数据块
[PARAM]     sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE]   sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT]   sub_10F88: 解析状态: 成功
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f745e3ec8, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 4ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 5ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x2, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 4ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 4ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功

=== 代码执行逻辑分析总结 ===
函数调用总数: 168
成功解析: 168
失败解析: 0
==========================

[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[ENTER]     sub_10F88: 开始解析DICE-AM数据块
[PARAM]     sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE]   sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT]   sub_10F88: 解析状态: 成功
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 7ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[ENTER]     sub_10F88: 开始解析DICE-AM数据块
[PARAM]     sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE]   sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT]   sub_10F88: 解析状态: 成功
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 4ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 10ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f67c3e248, args[1]=0x0, args[2]=0x4001
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f54c4c218, args[1]=0x0, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x2, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 3ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 2ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 1ms
[RESULT] sub_10F88: 解析状态: 成功
[ENTER]   sub_10F88: 开始解析DICE-AM数据块
[PARAM]   sub_10F88: args[0]=0x7f75d674d8, args[1]=0x1, args[2]=0x4000
[LEAVE] sub_10F88: 解析完成, 返回值: 0x0, 耗时: 0ms
[RESULT] sub_10F88: 解析状态: 成功

=== 代码执行逻辑分析总结 ===
函数调用总数: 223
成功解析: 223
失败解析: 0
==========================


[Remote::com.autonavi.minimap]-> exit

Thank you for using Frida!
