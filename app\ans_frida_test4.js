/* app 运行正常
Spawning `com.autonavi.minimap`...
[+] 高德地图ANS文件解析分析脚本 v2
[+] ANS文件解析分析脚本设置完成
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [+] 启动ANS文件解析分析模式
[类] 找到 AjxBLFactoryController
[Hook成功] init4WarmStart
[Hook成功] nativeInit4WarmStart
[Hook成功] uninit4WarmDestory
[Hook成功] nativeUninit4WarmDestory
[类] 找到: com.autonavi.minimap.offline.nativesupport.AmapCompat
[类] 找到: com.autonavi.ae.gmap.AMapController
  [方法] addMarkerRouteBoardBitmap
  [方法] createBitmapFromGLSurface
  [方法] getImmersiveCurrentModelBitmap
  [方法] recycleScreenShotBitmaps
  [方法] setScreenShotBitmapCompressRatio
[类] 找到: com.autonavi.ae.MapCloudBundleLoaderUtil
[+] Java层分析完成
[+] 开始扫描Native库中的ANS解析函数
[库] 发现可能的ANS处理库: libamapcrash.so
[库] 发现可能的ANS处理库: libamapnsq.so
[库] 发现可能的ANS处理库: libamapmain.so
[库] 发现可能的ANS处理库: libamapstore.so
[库] 发现可能的ANS处理库: libamaprsq.so
[库] 发现可能的ANS处理库: libamaplog.so
[库] 发现可能的ANS处理库: libamapr.so
[库] 发现可能的ANS处理库: libamapdsl.so
[库] 发现可能的ANS处理库: libamapaccount.so
*/
(function() {
  console.log("[+] 高德地图ANS文件解析分析脚本 v2");
  
  // 全局变量
  var ansFileFDs = {};  // 存储文件描述符到路径的映射
  var ansFileInfo = {}; // 存储ANS文件信息
  var m1AnsFound = false; // 是否找到m1.ans文件
  var m3AnsFound = false; // 是否找到m3.ans文件
  var methodCalled = {}; // 记录方法是否被调用
  
  // 1. 基础反调试 - 只处理strstr
  var strstr_ptr = Module.findExportByName("libc.so", "strstr");
  if (strstr_ptr) {
    Interceptor.attach(strstr_ptr, {
      onEnter: function(args) {
        try {
          var str = args[1].readUtf8String();
          if (str && (str.indexOf("frida") !== -1 || str.indexOf("gum") !== -1)) {
            this.shouldFake = true;
          }
        } catch (e) {}
      },
      onLeave: function(retval) {
        if (this.shouldFake) {
          retval.replace(0);
        }
      }
    });
  }
  
  // 2. 拦截可能导致崩溃的系统调用
  var exit_ptr = Module.findExportByName("libc.so", "exit");
  if (exit_ptr) {
    Interceptor.replace(exit_ptr, new NativeCallback(function() {
      return 0;
    }, 'void', ['int']));
  }

  // 3. 延迟启动分析模式 (10秒后启动，确保应用完全初始化)
  setTimeout(function() {
    console.log("[+] 启动ANS文件解析分析模式");
    
    // 3.1 监控文件打开操作
    var open_ptr = Module.findExportByName("libc.so", "open");
    if (open_ptr) {
      Interceptor.attach(open_ptr, {
        onEnter: function(args) {
          try {
            var path = args[0].readUtf8String();
            if (path) {
              if (path.indexOf("m1.ans") !== -1) {
                this.isAnsFile = true;
                this.path = path;
                this.isM1 = true;
                console.log("[ANS文件] 打开m1.ans: " + path);
                m1AnsFound = true;
              } else if (path.indexOf("m3.ans") !== -1) {
                this.isAnsFile = true;
                this.path = path;
                this.isM3 = true;
                console.log("[ANS文件] 打开m3.ans: " + path);
                m3AnsFound = true;
              }
            }
          } catch (e) {}
        },
        onLeave: function(retval) {
          if (this.isAnsFile && retval.toInt32() > 0) {
            var fd = retval.toInt32();
            console.log("[ANS文件] 文件描述符: " + fd);
            ansFileFDs[fd] = {
              path: this.path,
              isM1: this.isM1,
              isM3: this.isM3
            };
            
            // 获取调用栈
            try {
              var stack = Thread.backtrace(this.context, Backtracer.ACCURATE)
                .map(DebugSymbol.fromAddress);
              console.log("[调用栈] 打开ANS文件:");
              stack.forEach(function(symbol, i) {
                if (symbol && symbol.name && !symbol.name.includes("libc.so")) {
                  console.log("  " + i + ": " + symbol.name);
                }
              });
            } catch(e) {}
          }
        }
      });
    }
    
    // 3.2 监控mmap调用 - 轻量级实现
    var mmap_ptr = Module.findExportByName("libc.so", "mmap");
    if (mmap_ptr) {
      Interceptor.attach(mmap_ptr, {
        onEnter: function(args) {
          this.fd = args[4].toInt32();
          if (ansFileFDs[this.fd]) {
            this.isAnsFile = true;
            this.fileInfo = ansFileFDs[this.fd];
            this.size = args[1].toInt32();
          }
        },
        onLeave: function(retval) {
          if (!this.isAnsFile) return;
          
          var fileName = this.fileInfo.path.split("/").pop();
          var fileType = this.fileInfo.isM1 ? "m1.ans" : "m3.ans";
          console.log("[ANS映射] " + fileType + " 映射到内存");
          console.log("  地址: " + retval);
          console.log("  大小: " + this.size + " 字节");
          
          // 存储映射信息
          ansFileInfo[fileName] = {
            path: this.fileInfo.path,
            addr: retval,
            size: this.size,
            fd: this.fd,
            isM1: this.fileInfo.isM1,
            isM3: this.fileInfo.isM3
          };
          
          // 分析文件头 (读取32字节以获取更多信息)
          try {
            console.log("[文件头] " + fileName + ":");
            console.log(hexdump(retval, {length: Math.min(32, this.size)}));
            
            // 分析ANS文件头结构
            var header = new Uint8Array(Memory.readByteArray(retval, Math.min(32, this.size)));
            console.log("[ANS头部解析]");
            console.log("  Magic: " + header[0].toString(16) + header[1].toString(16) + " " + 
                                     header[2].toString(16) + header[3].toString(16));
            console.log("  Version: " + header[4].toString(16) + header[5].toString(16) + " " + 
                                       header[6].toString(16) + header[7].toString(16));
            console.log("  Flags: " + header[8].toString(16) + header[9].toString(16) + " " + 
                                     header[10].toString(16) + header[11].toString(16));
          } catch(e) {}
        }
      });
    }
    
    // 3.3 Java层分析
    Java.perform(function() {
      try {
        // 基本反调试
        var Debug = Java.use("android.os.Debug");
        Debug.isDebuggerConnected.implementation = function() {
          return false;
        };
        
        // 重点监控AjxBLFactoryController类
        try {
          var AjxBLFactoryController = Java.use("com.autonavi.jni.ajx3.bl.AjxBLFactoryController");
          console.log("[类] 找到 AjxBLFactoryController");
          
          // 监控init4WarmStart方法 - 这是ANS文件初始化的关键方法
          if (AjxBLFactoryController.init4WarmStart) {
            AjxBLFactoryController.init4WarmStart.implementation = function() {
              console.log("[关键调用] init4WarmStart()");
              console.log("  参数数量: " + arguments.length);
              // 记录调用时间
              var startTime = new Date().getTime();
              var result = this.init4WarmStart.apply(this, arguments);
              var endTime = new Date().getTime();
              console.log("[关键返回] init4WarmStart() 返回: " + result + ", 耗时: " + (endTime - startTime) + "ms");
              methodCalled["init4WarmStart"] = true;
              
              // 检查ANS文件状态
              if (m1AnsFound || m3AnsFound) {
                console.log("[关联] init4WarmStart被调用后，已加载ANS文件:");
                if (m1AnsFound) console.log("  - m1.ans 已加载");
                if (m3AnsFound) console.log("  - m3.ans 已加载");
              }
              
              return result;
            };
            console.log("[Hook成功] init4WarmStart");
          }
          
          // 监控nativeInit4WarmStart方法 - 这是Native层初始化ANS的入口
          if (AjxBLFactoryController.nativeInit4WarmStart) {
            AjxBLFactoryController.nativeInit4WarmStart.implementation = function(arg1, arg2) {
              console.log("[关键调用] nativeInit4WarmStart(" + arg1 + ", " + arg2 + ")");
              var result = this.nativeInit4WarmStart(arg1, arg2);
              console.log("[关键返回] nativeInit4WarmStart() 返回: " + result);
              methodCalled["nativeInit4WarmStart"] = true;
              return result;
            };
            console.log("[Hook成功] nativeInit4WarmStart");
          }
          
          // 其他可能相关的方法
          var methodsToHook = ["uninit4WarmDestory", "nativeUninit4WarmDestory"];
          for (var i = 0; i < methodsToHook.length; i++) {
            var methodName = methodsToHook[i];
            if (AjxBLFactoryController[methodName]) {
              (function(name) {
                var original = AjxBLFactoryController[name];
                AjxBLFactoryController[name].implementation = function() {
                  console.log("[方法调用] " + name + "()");
                  var result = original.apply(this, arguments);
                  console.log("[方法返回] " + name + "() 返回: " + result);
                  methodCalled[name] = true;
                  return result;
                };
                console.log("[Hook成功] " + name);
              })(methodName);
            }
          }
        } catch(e) {
          console.log("[错误] AjxBLFactoryController类监控失败: " + e);
        }
        
        // 尝试找出其他ANS文件解析相关类
        try {
          var targetClasses = [
            "com.autonavi.minimap.offline.nativesupport.AmapCompat",
            "com.autonavi.ae.gmap.AMapController"
          ];
          
          for (var i = 0; i < targetClasses.length; i++) {
            try {
              var clazz = Java.use(targetClasses[i]);
              console.log("[类] 找到: " + targetClasses[i]);
              
              // 只列出可能与ANS相关的方法名
              var methods = clazz.class.getDeclaredMethods();
              for (var j = 0; j < methods.length; j++) {
                var methodName = methods[j].getName();
                if (methodName.indexOf("ans") !== -1 || 
                    methodName.indexOf("parse") !== -1 || 
                    methodName.indexOf("load") !== -1 ||
                    methodName.indexOf("init") !== -1 ||
                    methodName.indexOf("map") !== -1 ||
                    methodName.indexOf("file") !== -1) {
                  console.log("  [方法] " + methodName);
                  
                  // 尝试Hook这些可能与ANS相关的方法
                  try {
                    (function(cls, name, className) {
                      var original = cls[name];
                      if (original) {
                        cls[name].implementation = function() {
                          console.log("[调用] " + className + "." + name + "()");
                          var result = original.apply(this, arguments);
                          console.log("[返回] " + className + "." + name + "() 返回: " + result);
                          return result;
                        };
                      }
                    })(clazz, methodName, targetClasses[i]);
                  } catch(e) {}
                }
              }
            } catch(e) {}
          }
        } catch(e) {}
        
        // 监控MapCloudBundleLoaderUtil类 - 可能负责加载ANS资源
        try {
          var MapCloudBundleLoaderUtil = Java.use("com.autonavi.ae.MapCloudBundleLoaderUtil");
          if (MapCloudBundleLoaderUtil) {
            console.log("[类] 找到: com.autonavi.ae.MapCloudBundleLoaderUtil");
            
            // 监控loadFromFile方法
            if (MapCloudBundleLoaderUtil.loadFromFile) {
              MapCloudBundleLoaderUtil.loadFromFile.implementation = function(path) {
                console.log("[资源加载] loadFromFile(" + path + ")");
                var result = this.loadFromFile(path);
                console.log("[资源加载] loadFromFile 返回: " + result);
                return result;
              };
              console.log("[Hook成功] MapCloudBundleLoaderUtil.loadFromFile");
            }
            
            // 尝试Hook其他可能的加载方法
            var loaderMethods = ["loadFromAssets", "loadFromStream", "loadFromMemory"];
            for (var i = 0; i < loaderMethods.length; i++) {
              var methodName = loaderMethods[i];
              if (MapCloudBundleLoaderUtil[methodName]) {
                (function(name) {
                  var original = MapCloudBundleLoaderUtil[name];
                  MapCloudBundleLoaderUtil[name].implementation = function() {
                    console.log("[资源加载] " + name + "(" + JSON.stringify(arguments) + ")");
                    var result = original.apply(this, arguments);
                    console.log("[资源加载] " + name + " 返回: " + result);
                    return result;
                  };
                  console.log("[Hook成功] MapCloudBundleLoaderUtil." + name);
                })(methodName);
              }
            }
          }
        } catch(e) {
          console.log("[错误] MapCloudBundleLoaderUtil类监控失败");
        }
        
        console.log("[+] Java层分析完成");
      } catch(e) {}
    });
    
    // 3.4 监控ANS文件读取操作 (轻量级实现)
    var read_ptr = Module.findExportByName("libc.so", "read");
    if (read_ptr) {
      Interceptor.attach(read_ptr, {
        onEnter: function(args) {
          this.fd = args[0].toInt32();
          if (ansFileFDs[this.fd]) {
            this.isAnsFile = true;
            this.fileInfo = ansFileFDs[this.fd];
            this.size = args[2].toInt32();
            this.buffer = args[1];
          }
        },
        onLeave: function(retval) {
          if (!this.isAnsFile) return;
          
          var bytesRead = retval.toInt32();
          if (bytesRead <= 0) return;
          
          var fileName = this.fileInfo.path.split("/").pop();
          var fileType = this.fileInfo.isM1 ? "m1.ans" : "m3.ans";
          console.log("[ANS读取] " + fileType + " 读取 " + bytesRead + " 字节");
          
          // 只分析小块数据，避免性能问题
          if (bytesRead <= 16) {
            try {
              console.log(hexdump(this.buffer, {length: bytesRead}));
            } catch(e) {}
          }
          
          // 检查是否在关键方法调用后
          if (methodCalled["init4WarmStart"] || methodCalled["nativeInit4WarmStart"]) {
            console.log("[关联] ANS文件读取发生在init方法调用后");
          }
        }
      });
    }
    
    // 3.5 监控Native库中可能的ANS解析函数
    setTimeout(function() {
      try {
        console.log("[+] 开始扫描Native库中的ANS解析函数");
        
        Process.enumerateModules({
          onMatch: function(module) {
            // 只关注可能包含ANS解析代码的库
            if (module.name.indexOf("amapnsq") !== -1 || 
                module.name.indexOf("gdamapv4") !== -1 || 
                module.name.indexOf("amap") !== -1) {
              console.log("[库] 发现可能的ANS处理库: " + module.name);
              
              // 查找可能的ANS相关函数
              module.enumerateExports({
                onMatch: function(exp) {
                  if (exp.name.indexOf("ans") !== -1 || 
                      exp.name.indexOf("parse") !== -1 || 
                      exp.name.indexOf("load") !== -1 ||
                      exp.name.indexOf("init") !== -1) {
                    console.log("[导出函数] " + module.name + "!" + exp.name);
                    
                    // 尝试Hook这些函数
                    try {
                      Interceptor.attach(exp.address, {
                        onEnter: function(args) {
                          console.log("[Native调用] " + exp.name);
                        },
                        onLeave: function(retval) {
                          console.log("[Native返回] " + exp.name + " = " + retval);
                        }
                      });
                    } catch(e) {}
                  }
                },
                onComplete: function() {}
              });
            }
          },
          onComplete: function() {
            console.log("[+] Native库扫描完成");
          }
        });
      } catch(e) {
        console.log("[错误] Native库扫描失败: " + e);
      }
    }, 15000); // 延迟15秒执行，避免干扰应用启动
  }, 10000); // 延迟10秒，确保应用完全启动
  
  console.log("[+] ANS文件解析分析脚本设置完成");
})(); 