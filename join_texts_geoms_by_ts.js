#!/usr/bin/env node
/**
 * Join 25xx texts with geom_bin centroids by nearest timestamp.
 * Usage:
 *   node join_texts_geoms_by_ts.js md-file/gaode_dump
 * Output:
 *   md-file/gaode_dump/joined_texts_geoms.csv
 */
const fs = require('fs');
const path = require('path');

const inputDir = process.argv[2] ? path.resolve(process.argv[2]) : path.resolve('./md-file/gaode_dump');
const outputCsv = path.join(inputDir, 'joined_texts_geoms.csv');
const GEOM_MAX_FILES = 1000;     // safety cap
const MATCH_THRESHOLD_MS = 5000; // 5s time window for nearest match

function exists(p) { try { fs.accessSync(p); return true; } catch { return false; } }
function readJSONLLines(file) {
  if (!exists(file)) return [];
  return fs.readFileSync(file, 'utf8').split(/\r?\n/).filter(Boolean).map(l => { try { return JSON.parse(l); } catch { return null; } }).filter(Boolean);
}
function writeCSV(file, rows, header) {
  const s = [header.join(',')].concat(rows.map(r => header.map(h => (r[h] != null ? String(r[h]).replace(/[\r\n,]/g, ' ') : '')).join(','))).join('\n');
  fs.writeFileSync(file, s, 'utf8');
  console.log('[write]', file, rows.length, 'rows');
}

// GCJ-02 utils
const a = 6378245.0, ee = 0.00669342162296594323;
function outOfChina(lon, lat) { return lon < 72.004 || lon > 137.8347 || lat < 0.8293 || lat > 55.8271; }
function tLat(x, y){let r=-100+2*x+3*y+0.2*y*y+0.1*x*y+0.2*Math.sqrt(Math.abs(x));r+=(20*Math.sin(6*x*Math.PI)+20*Math.sin(2*x*Math.PI))*2/3;r+=(20*Math.sin(y*Math.PI)+40*Math.sin(y/3*Math.PI))*2/3;r+=(160*Math.sin(y/12*Math.PI)+320*Math.sin(y*Math.PI/30))*2/3;return r;}
function tLon(x, y){let r=300+x+2*y+0.1*x*x+0.1*x*y+0.1*Math.sqrt(Math.abs(x));r+=(20*Math.sin(6*x*Math.PI)+20*Math.sin(2*x*Math.PI))*2/3;r+=(20*Math.sin(x*Math.PI)+40*Math.sin(x/3*Math.PI))*2/3;r+=(150*Math.sin(x/12*Math.PI)+300*Math.sin(x/30*Math.PI))*2/3;return r;}
function wgs84ToGcj02(lon, lat){
  if(outOfChina(lon,lat)) return [lon,lat];
  let dLat=tLat(lon-105,lat-35), dLon=tLon(lon-105,lat-35);
  const radLat=lat/180*Math.PI; let magic=Math.sin(radLat); magic=1-ee*magic*magic; const sqrtMagic=Math.sqrt(magic);
  dLat=(dLat*180)/((a*(1-ee))/(magic*sqrtMagic)*Math.PI); dLon=(dLon*180)/(a/sqrtMagic*Math.cos(radLat)*Math.PI);
  return [lon+dLon, lat+dLat];
}

// world coords to lon/lat (best-effort)
function estimateZoomFromWorldXY(v){const absMax=Math.max(Math.abs(v),1); return Math.max(0, Math.min(30, Math.round(Math.log2(absMax/256))));}
function worldToLonLat(x,y,z){const W=256*Math.pow(2,z); const lon=(x/W)*360-180; const n=Math.PI-2*Math.PI*(y/W); const lat=180/Math.PI*Math.atan(0.5*(Math.exp(n)-Math.exp(-n))); return [lon,lat];}

function parseGeomBinBuffer(buf){
  const dv = new DataView(buf.buffer, buf.byteOffset, buf.byteLength);
  const total = Math.floor(buf.byteLength/4);
  const ints = new Int32Array(total);
  for(let i=0;i<total;i++) ints[i]=dv.getInt32(i*4,true);
  const pts=[]; for(let i=0;i+1<ints.length;i+=2){ pts.push([ints[i],ints[i+1]]); }
  if(pts.length===0) return {centroidWGS84:null, centroidGCJ02:null, count:0};
  const sample = pts.slice(0, Math.min(pts.length, 2000));
  const absVals = []; for(const [x,y] of sample){ absVals.push(Math.abs(x),Math.abs(y)); }
  absVals.sort((a,b)=>a-b);
  const median = absVals[absVals.length>>1] || 256;
  const z = estimateZoomFromWorldXY(median);
  const lonlat = pts.map(([x,y])=>worldToLonLat(x,y,z));
  const sum = lonlat.reduce((a,[lon,lat])=>[a[0]+lon,a[1]+lat],[0,0]);
  const cW = [sum[0]/lonlat.length, sum[1]/lonlat.length];
  const cG = wgs84ToGcj02(cW[0], cW[1]);
  return {centroidWGS84:cW, centroidGCJ02:cG, count: lonlat.length};
}

function main(){
  if(!exists(inputDir)){ console.error('[error] inputDir not found', inputDir); process.exit(1); }
  console.log('[inputDir]', inputDir);

  const textLines = readJSONLLines(path.join(inputDir,'25xx_text.jsonl'));
  const texts=[];
  for(const rec of textLines){
    const tsStr = rec.ts || '';
    const ts = Number(tsStr)||0;
    const type = rec.type_id_hex||'';
    if(Array.isArray(rec.groups)){
      for(const g of rec.groups){
        if(g && g.text && g.text.trim()){
          texts.push({ ts, type, text: g.text.trim() });
        }
      }
    }
  }
  texts.sort((a,b)=>a.ts-b.ts);

  const geomDir = path.join(inputDir,'geom_bin');
  const geomList=[];
  if(exists(geomDir)){
    let files = fs.readdirSync(geomDir).filter(x=>x.toLowerCase().endsWith('.bin'));
    files = files.slice(0, GEOM_MAX_FILES);
    for(const f of files){
      const parts = f.split('_'); // ts_magic_size.bin
      let ts = 0; if(parts.length>=1){ const n=Number(parts[0]); if(Number.isFinite(n)) ts = n; }
      let magic = (parts.length>=2)? parts[1] : '';
      try{
        const buf = fs.readFileSync(path.join(geomDir,f));
        const {centroidWGS84, centroidGCJ02, count} = parseGeomBinBuffer(buf);
        geomList.push({
          ts, magic, file:f,
          lonW: centroidWGS84? centroidWGS84[0] : '',
          latW: centroidWGS84? centroidWGS84[1] : '',
          lonG: centroidGCJ02? centroidGCJ02[0] : '',
          latG: centroidGCJ02? centroidGCJ02[1] : '',
          count
        });
      }catch(e){
        // ignore
      }
    }
  }
  geomList.sort((a,b)=>a.ts-b.ts);

  // two-pointer nearest join within threshold
  const rows=[];
  let j=0;
  for(const t of texts){
    while(j+1<geomList.length && Math.abs(geomList[j+1].ts - t.ts) <= Math.abs(geomList[j].ts - t.ts)) j++;
    const g = geomList[j] || null;
    if(g && Math.abs(g.ts - t.ts) <= MATCH_THRESHOLD_MS){
      rows.push({
        tsText: t.ts, type: t.type, text: t.text,
        tsGeom: g.ts, binFile: g.file, magic: g.magic,
        lonWGS84: g.lonW, latWGS84: g.latW,
        lonGCJ02: g.lonG, latGCJ02: g.latG,
        pointCount: g.count
      });
    }else{
      rows.push({
        tsText: t.ts, type: t.type, text: t.text,
        tsGeom: '', binFile: '', magic: '',
        lonWGS84: '', latWGS84: '', lonGCJ02: '', latGCJ02: '', pointCount: ''
      });
    }
  }

  writeCSV(outputCsv, rows, [
    'tsText','type','text',
    'tsGeom','binFile','magic',
    'lonWGS84','latWGS84','lonGCJ02','latGCJ02','pointCount'
  ]);

  console.log('[done] joined ->', outputCsv);
}

main();
