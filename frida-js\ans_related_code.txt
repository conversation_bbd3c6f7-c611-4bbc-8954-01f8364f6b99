//----- (0000000000145634) ----------------------------------------------------
void __fastcall sub_145634(_QWORD *a1)
{
  __int64 *v2; // x22
  char *v3; // x0
  _QWORD *v4; // x0
  __int64 v5; // x0
  __int64 v6; // x20
  __int64 v7; // x1
  char v8; // w8
  char *v9; // x1
  __int64 v10; // x1
  __int64 v11; // x2
  __int64 v12; // x3
  __int64 v13; // x4
  __int64 v14; // x5
  __int64 v15; // x6
  __int64 v16; // x7
  __int64 v17; // x2
  __int64 v18; // x3
  __int64 v19; // x4
  __int64 v20; // x5
  __int64 v21; // x6
  __int64 v22; // x7
  __int64 v23; // x2
  __int64 v24; // x3
  __int64 v25; // x4
  __int64 v26; // x5
  __int64 v27; // x6
  __int64 v28; // x7
  char v29; // [xsp+0h] [xbp-60h]
  char v30; // [xsp+0h] [xbp-60h]
  char v31; // [xsp+0h] [xbp-60h]
  __int64 v32[3]; // [xsp+8h] [xbp-58h] BYREF
  _QWORD v33[4]; // [xsp+20h] [xbp-40h] BYREF

  v33[3] = *(_QWORD *)(_ReadStatusReg(TPIDR_EL0) + 40);
  sub_B27E8((__int64)a1);
  a1[3] = 0;
  v2 = a1 + 3;
  a1[7] = 0;
  a1[8] = 0;
  a1[5] = 0;
  a1[6] = 0;
  a1[4] = 0;
  a1[9] = -1;
  v3 = (char *)sub_290EAC();
  if ( v3 )
  {
    v4 = sub_B1E98(a1, v3);
    sub_B1E98(v4, "PosAoi.ans");
    v5 = operator new(0x60u);
    v6 = v5;
    if ( (*(_BYTE *)a1 & 1) != 0 )
      v7 = a1[2];
    else
      v7 = (__int64)a1 + 1;
    sub_58AF1C(v5, v7);
    v8 = *(_BYTE *)a1;
    a1[3] = v6;
    if ( (v8 & 1) != 0 )
      v9 = (char *)a1[2];
    else
      v9 = (char *)a1 + 1;
    sub_58B3C0(v6, v9);
    sub_58B8EC(*v2, v10, v11, v12, v13, v14, v15, v16, v29);
    sub_656F40(
      "CREATE TABLE if not exists %s (id INTEGER PRIMARY KEY, tileID INTEGER, aoiIDHash INTEGER, aoiID TEXT, version LONG"
      ", timeStamp TIMESTAMP);",
      v33);
    sub_14891C(*v2, v17, v18, v19, v20, v21, v22, v33[0], v30);
    sub_656F40(
      "CREATE TABLE if not exists %s (aoiIDHash INTEGER PRIMARY KEY, aoiID TEXT, aoiType TEXT, aoiPoints BLOB);",
      v32);
    sub_B2994(v33, v32);
    sub_AD4D0((__int64)v32);
    sub_14891C(*v2, v23, v24, v25, v26, v27, v28, v33[0], v31);
    sub_58BC74(*v2);
    sub_AD4D0((__int64)v33);
  }
}
// 1456E8: variable 'v10' is possibly undefined
// 1456E8: variable 'v11' is possibly undefined
// 1456E8: variable 'v12' is possibly undefined
// 1456E8: variable 'v13' is possibly undefined
// 1456E8: variable 'v14' is possibly undefined
// 1456E8: variable 'v15' is possibly undefined
// 1456E8: variable 'v16' is possibly undefined
// 1456E8: variable 'v29' is possibly undefined
// 145718: variable 'v17' is possibly undefined
// 145718: variable 'v18' is possibly undefined
// 145718: variable 'v19' is possibly undefined
// 145718: variable 'v20' is possibly undefined
// 145718: variable 'v21' is possibly undefined
// 145718: variable 'v22' is possibly undefined
// 145718: variable 'v30' is possibly undefined
// 145754: variable 'v23' is possibly undefined
// 145754: variable 'v24' is possibly undefined
// 145754: variable 'v25' is possibly undefined
// 145754: variable 'v26' is possibly undefined
// 145754: variable 'v27' is possibly undefined
--
  return (*(__int64 (__fastcall **)(__int64))(a1 + 16))(v1);
}
// 2C4CA4: variable 'v1' is possibly undefined

//----- (00000000002C4CA8) ----------------------------------------------------
__int64 __fastcall sub_2C4CA8(unsigned __int8 *a1, unsigned __int8 a2)
{
  __int64 v4; // x0
  __int64 v5; // x0
  int v6; // w0
  __int64 v7; // x20
  void *v8; // x20
  bool v9; // w0

  v4 = sub_480280();
  v5 = sub_B0DB4(v4, 0xA3u);
  v6 = (*(__int64 (__fastcall **)(__int64))(*(_QWORD *)v5 + 72LL))(v5);
  v7 = qword_919E18;
  byte_919E10 = (v6 == 1) & a2;
  if ( ((v6 == 1) & a2) == 0 )
  {
    if ( qword_919E18 )
    {
      v9 = sub_1255B4((unsigned __int8 *)(qword_919E18 + 8), a1);
      v8 = (void *)qword_919E18;
      if ( v9 )
      {
        if ( qword_919E18 )
        {
          sub_2C4F78((__int64 **)qword_919E18);
          operator delete(v8);
        }
        qword_919E18 = 0;
      }
      else if ( qword_919E18 )
      {
        return (__int64)v8;
      }
    }
    v8 = (void *)operator new(0x28u);
    sub_2C4E84((__int64)v8, (__int64)a1);
    qword_919E18 = (__int64)v8;
    return (__int64)v8;
  }
  if ( !qword_919E18 )
  {
    v7 = operator new(0x28u);
    sub_2C4F50(v7);
    qword_919E18 = v7;
  }
  sub_10676C((_QWORD *)(v7 + 8), "files/location/wb_data/rnwb_v3.ans");
  sub_2C4D9C((unsigned __int8 *)(qword_919E18 + 8));
  return qword_919E18;
}
// 919E10: using guessed type char byte_919E10;
// 919E18: using guessed type __int64 qword_919E18;

//----- (00000000002C4D9C) ----------------------------------------------------
void *__fastcall sub_2C4D9C(unsigned __int8 *a1)
{
  bool v2; // w0
  void *v3; // x20

  if ( !qword_919E08 )
    goto LABEL_7;
  v2 = sub_1255B4(a1, (unsigned __int8 *)(qword_919E08 + 8));
  v3 = (void *)qword_919E08;
  if ( v2 )
  {
    if ( qword_919E08 )
    {
      sub_2C5F64((__int64 *)qword_919E08);
      operator delete(v3);
    }
    qword_919E08 = 0;
    goto LABEL_7;
  }
  if ( !qword_919E08 )
  {
LABEL_7:
    v3 = (void *)operator new(0x20u);
    sub_2C6118((__int64 *)v3, a1);
    qword_919E08 = (__int64)v3;
  }
  return v3;
}
// 919E08: using guessed type __int64 qword_919E08;

//----- (00000000002C4E14) ----------------------------------------------------
void sub_2C4E14()
{
  void *v0; // x19
  void *v1; // x19

  v0 = (void *)qword_919E18;
  if ( qword_919E18 )
  {
    sub_2C4F78((__int64 **)qword_919E18);
    operator delete(v0);
    qword_919E18 = 0;
  }
--
        JUMPOUT(0x2CA270);
    }
  }
  return 0;
}
// 2C8080: control flows out of bounds to 2CA270
// 2C8060: variable 'v3' is possibly undefined
// 2C8060: variable 'v4' is possibly undefined
// 2C8068: variable 'v2' is possibly undefined

//----- (00000000002C8094) ----------------------------------------------------
__int64 sub_2C8094()
{
  __int64 v0; // x0
  __int64 v1; // x19
  __int64 v2; // x8
  __int64 v3; // x8
  char v4; // nf
  char v5; // vf
  char v6; // w19
  __int64 v7; // x0
  __int64 v8; // x1
  __int64 v9; // x2
  __int64 v10; // x3
  __int64 v11; // x4
  __int64 v12; // x5
  __int64 v13; // x6
  __int64 v14; // x7
  char v15; // w0
  _QWORD *v16; // x0
  __int64 v18; // [xsp+0h] [xbp-1F0h]
  unsigned __int64 v19[3]; // [xsp+8h] [xbp-1E8h] BYREF
  __int64 v20[6]; // [xsp+20h] [xbp-1D0h] BYREF
  __int64 v21[42]; // [xsp+50h] [xbp-1A0h] BYREF
  unsigned __int64 s[5]; // [xsp+1A0h] [xbp-50h] BYREF
  __int64 v23; // [xsp+1C8h] [xbp-28h]

  sub_2C9F78();
  v1 = v0;
  v23 = v2;
  sub_59D0D4();
  sub_2C9E50();
  if ( v4 != v5 )
  {
    v6 = 0;
  }
  else
  {
    *(_QWORD *)(v1 + 88) = v3;
    sub_6548F4((__int64)v20, v1 + 32);
    sub_AEB54((unsigned __int64 *)v21, "version_wb.ans");
    v7 = ((__int64 (__fastcall *)(__int64 *, __int64 *))loc_2009FC)(v20, v21);
    sub_2C9F50(
      v7,
      v8,
      v9,
      v10,
      v11,
      v12,
      v13,
      v14,
      v18,
      v19[0],
      v19[1],
      v19[2],
      v20[0],
      v20[1],
      v20[2],
      v20[3],
      v20[4],
      v20[5],
      v21[0]);
    v15 = sub_655114();
    if ( (v15 & 1) != 0 )
    {
      sub_654BD4((unsigned __int8 *)v20, s);
      sub_2C8790(v21, (__int64)s, 8);
      sub_AD4D0((__int64)s);
      if ( v21[17] && (v16 = sub_2C883C(v21, s, 40), (*((_BYTE *)v16 + *(_QWORD *)(*v16 - 24LL) + 32) & 5) == 0) )
      {
        sub_AEB54(v19, (char *)s);
        v6 = sub_2C888C(v1, (unsigned __int8 *)v19);
        sub_AD4D0((__int64)v19);
      }
      else
      {
        v6 = 0;
      }
      sub_239584(v21);
    }
    else
    {
      v6 = 0;
    }
    sub_16CA14((__int64)v20);
  }
  return v6 & 1;
}
// 2C80AC: variable 'v0' is possibly undefined
// 2C80B0: variable 'v2' is possibly undefined
// 2C80BC: variable 'v4' is possibly undefined
--
      sub_656F40("itm < %ld", v14);
      v12 = sub_2C9F58(v4, v5, v6, v7, v8, v9, v10, v11, v13, v14[0], v14[1], v14[2], v15[0]);
      sub_2C5664(v12, (__int64)v14);
      sub_AD4D0((__int64)v14);
    }
    sub_AD4D0((__int64)v15);
  }
}
// 2C820C: variable 'v0' is possibly undefined
// 2C8210: variable 'v1' is possibly undefined
// 2C8228: variable 'v3' is possibly undefined
// 2C825C: variable 'v4' is possibly undefined
// 2C825C: variable 'v5' is possibly undefined
// 2C825C: variable 'v6' is possibly undefined
// 2C825C: variable 'v7' is possibly undefined
// 2C825C: variable 'v8' is possibly undefined
// 2C825C: variable 'v9' is possibly undefined
// 2C825C: variable 'v10' is possibly undefined
// 2C825C: variable 'v11' is possibly undefined
// 2C825C: variable 'v13' is possibly undefined

//----- (00000000002C829C) ----------------------------------------------------
__int64 __fastcall sub_2C829C(__int64 a1, char a2, _QWORD *a3)
{
  unsigned __int64 StatusReg; // x23
  unsigned __int8 *v4; // x22
  _QWORD *v8; // x0
  __int64 v9; // x1
  __int64 v10; // x2
  __int64 v11; // x3
  __int64 v12; // x4
  __int64 v13; // x5
  __int64 v14; // x6
  __int64 v15; // x7
  __int64 v16[3]; // [xsp+0h] [xbp-80h] BYREF
  _BYTE v17[48]; // [xsp+18h] [xbp-68h] BYREF
  __int64 v18; // [xsp+48h] [xbp-38h]

  StatusReg = _ReadStatusReg(TPIDR_EL0);
  if ( (a2 & 1) != 0 )
    v4 = (unsigned __int8 *)(a1 + 56);
  else
    v4 = (unsigned __int8 *)(a1 + 32);
  v18 = *(_QWORD *)(StatusReg + 40);
  if ( sub_1258A4(v4) )
    return 0;
  sub_6548F4((__int64)v17, (__int64)v4);
  if ( (sub_655114() & 1) == 0 )
    sub_655074((__int64)v17);
  if ( (a2 & 1) != 0 )
    sub_AEB54((unsigned __int64 *)v16, "rnwb_v2.ans");
  else
    sub_656F40("%u.ans", v16);
  ((void (__fastcall *)(_BYTE *, __int64 *))loc_2009FC)(v17, v16);
  sub_AD4D0((__int64)v16);
  sub_654B38((__int64)v17, (unsigned __int64 *)v16);
  v8 = sub_B2994(a3, v16);
  sub_2C9EA4((__int64)v8, v9, v10, v11, v12, v13, v14, v15, v16[0]);
  sub_16CA14((__int64)v17);
  return 1;
}
// 2C8374: variable 'v9' is possibly undefined
// 2C8374: variable 'v10' is possibly undefined
// 2C8374: variable 'v11' is possibly undefined
// 2C8374: variable 'v12' is possibly undefined
// 2C8374: variable 'v13' is possibly undefined
// 2C8374: variable 'v14' is possibly undefined
// 2C8374: variable 'v15' is possibly undefined

//----- (00000000002C83B0) ----------------------------------------------------
__int64 __fastcall sub_2C83B0(__int64 result)
{
  __int64 v1; // x20
  __int64 v2; // x0
  __int64 v3; // x1
  __int64 v4; // x8

  if ( !*(_BYTE *)(result + 1) )
  {
    if ( *(_QWORD *)(result + 24) )
    {
      *(_BYTE *)(result + 1) = 1;
      v2 = sub_2C9F24();
      v3 = sub_2C9F04(v2, (__int64)&off_8F1498, COERCE_DOUBLE(0x4B00000008LL));
      return (*(__int64 (__fastcall **)(__int64, __int64))(v4 + 16))(v1, v3);
    }
  }
  return result;
}
// 2C8408: variable 'v4' is possibly undefined
// 2C8410: variable 'v1' is possibly undefined
// 8F1498: using guessed type __int64 (__fastcall *off_8F1498)();

//----- (00000000002C8414) ----------------------------------------------------
void sub_2C8414()
{
  _BYTE *v0; // x0
  __int64 v1; // x8
  _BYTE *v2; // x19
  __int64 v3; // x0
  __int64 v4; // x1
  __int64 v5; // x0
  __int64 v6; // x1
--
  __int64 v11; // x5
  __int64 v12; // x6
  __int64 v13; // x7
  _QWORD *v14; // x0
  __int64 i; // x8
  char *v16; // x20
  __int64 v17; // x0
  int v18; // w21
  __int64 v19; // x1
  __int64 v20; // x2
  __int64 v21; // x3
  __int64 v22; // x4
  __int64 v23; // x5
  __int64 v24; // x6
  __int64 v25; // x7
  __int64 v26; // x0
  __int64 v27; // x1
  __int64 v28; // x2
  __int64 v29; // x3
  __int64 v30; // x4
  __int64 v31; // x5
  __int64 v32; // x6
  __int64 v33; // x7
  __int64 v34; // x0
  int v35; // w20
  __int64 v36; // [xsp+0h] [xbp-210h] BYREF
  __int64 v37; // [xsp+8h] [xbp-208h]
  __int64 v38; // [xsp+10h] [xbp-200h]
  __int64 v39; // [xsp+18h] [xbp-1F8h]
  __int64 v40; // [xsp+20h] [xbp-1F0h]
  __int64 v41; // [xsp+28h] [xbp-1E8h]
  __int64 v42; // [xsp+30h] [xbp-1E0h] BYREF
  unsigned __int64 v43[3]; // [xsp+38h] [xbp-1D8h] BYREF
  char s[40]; // [xsp+50h] [xbp-1C0h] BYREF
  __int64 v45[43]; // [xsp+78h] [xbp-198h] BYREF

  v45[42] = *(_QWORD *)(_ReadStatusReg(TPIDR_EL0) + 40);
  HIDWORD(v42) = a2;
  v3 = *(_QWORD **)(a1 + 120);
  if ( !v3 )
  {
    v5 = operator new(0x28u);
    *(_QWORD *)v5 = 0;
    *(_QWORD *)(v5 + 8) = 0;
    *(_QWORD *)(v5 + 16) = 0;
    *(_QWORD *)(v5 + 24) = 0;
    *(_DWORD *)(v5 + 32) = 1065353216;
    *(_QWORD *)(a1 + 120) = v5;
    sub_2C97EC((_QWORD *)v5);
    sub_6548F4((__int64)&v36, a1 + 32);
    sub_AEB54((unsigned __int64 *)v45, "global_wb.ans");
    ((void (__fastcall *)(__int64 *, __int64 *))loc_2009FC)(&v36, v45);
    sub_AD4D0((__int64)v45);
    if ( (sub_655114() & 1) != 0 )
    {
      sub_654BD4((unsigned __int8 *)&v36, (unsigned __int64 *)s);
      v6 = sub_2C8790(v45, (__int64)s, 8);
      sub_2C9F50(v6, v7, v8, v9, v10, v11, v12, v13, v36, v37, v38, v39, v40, v41, v42, v43[0], v43[1], v43[2], s[0]);
      if ( v45[17] )
      {
LABEL_6:
        while ( 1 )
        {
          v14 = sub_2C883C(v45, s, 40);
          if ( (*((_BYTE *)v14 + *(_QWORD *)(*v14 - 24LL) + 32) & 5) != 0 )
            break;
          for ( i = 0; i != 20; ++i )
          {
            if ( s[i] == 9 )
            {
              s[i] = 0;
              v16 = &s[i + 1];
              sub_AEB54(v43, s);
              v17 = std::stol(v43, 0, 10);
              v18 = v17;
              sub_2C9FAC(v17, v19, v20, v21, v22, v23, v24, v25, v36, v37, v38, v39, v40, v41, v42, v43[0]);
              sub_AEB54(v43, v16);
              v26 = std::stoi(v43, 0, 10);
              LOWORD(v16) = v26;
              sub_2C9FAC(v26, v27, v28, v29, v30, v31, v32, v33, v36, v37, v38, v39, v40, v41, v42, v43[0]);
              v34 = *(_QWORD *)(a1 + 120);
              LODWORD(v43[0]) = v18;
              WORD2(v43[0]) = (_WORD)v16;
              sub_2C8BE4(v34, (unsigned int *)v43);
              goto LABEL_6;
            }
          }
        }
        v35 = 0;
      }
      else
      {
        v35 = 1;
      }
      sub_239584(v45);
    }
    else
    {
      v35 = 1;
    }
    sub_16CA14((__int64)&v36);
