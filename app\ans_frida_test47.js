// 高德地图 nativeAddMapGestureMsg Native层执行流程分析脚本
// 适用于 Frida 12.9.7，使用 ES5 语法

(function() {
    'use strict';

    // 全局配置
    var config = {
        // 关键函数偏移量 (相对于 libamapr.so 基地址)
        functionOffsets: {
            nativeAddMapGestureMsg: 0x6ee70c,  // 主要的手势处理入口
            getMapEngineInstance: 0x6FB98C,    // 获取地图引擎实例
            validateEngine: 0x6F3430,          // 验证引擎状态
            processGestureMessage: 0x6FB530,   // 处理手势消息
            triggerRenderUpdate: 0x6FBC78,     // 触发渲染更新
            updateMapView: 0x6FB9E0,           // 更新地图视图
            finalizeProcessing: 0x6FB550       // 完成处理
        },
        // 手势类型映射
        gestureTypes: {
            1: "单指按下",
            2: "单指移动",
            3: "单指抬起",
            4: "双指按下",
            5: "双指移动",
            6: "双指抬起",
            7: "放大",
            8: "缩小",
            9: "旋转",
            10: "长按",
            11: "双击",
            12: "倾斜"
        }
    };

    // 全局变量
    var gModuleMap = {};              // 模块映射 {模块名: {base, size, path}}
    var gCallSequence = [];           // 函数调用序列
    var gExceptionCount = {};         // 异常计数
    var gNativeAddMapGestureMsgAddr = null; // nativeAddMapGestureMsg函数地址

    // 工具函数 - 日志输出
    function log(message) {
        console.log("[+] " + message);
    }

    function logError(message) {
        console.log("[-] " + message);
    }

    // 工具函数 - 格式化地址
    function formatAddress(address) {
        if (!address) return "0x0";
        return "0x" + address.toString(16);
    }

    // 工具函数 - 安全获取模块信息
    function safeGetModuleByAddress(address) {
        try {
            if (!address) return null;
            return Process.findModuleByAddress(address);
        } catch (e) {
            return null;
        }
    }

    // 工具函数 - 格式化地址为模块+偏移格式
    function formatAddressWithModule(address) {
        if (!address) return "0x0";
        
        try {
            var module = safeGetModuleByAddress(address);
            if (module) {
                var offset = address.sub(module.base);
                return module.name + "!" + formatAddress(offset);
            }
        } catch (e) {}
        
        return formatAddress(address);
    }

    // 工具函数 - 获取手势类型名称
    function getGestureTypeName(type) {
        return config.gestureTypes[type] || "未知手势(" + type + ")";
    }

    // 1. 枚举所有已加载模块
    function enumerateLoadedModules() {
        log("开始枚举关键模块...");
        
        var modules = Process.enumerateModules();
        
        // 存储所有模块信息
        for (var i = 0; i < modules.length; i++) {
            var module = modules[i];
            gModuleMap[module.name] = {
                base: module.base,
                size: module.size,
                path: module.path
            };
            
            // 只输出关键模块信息
            if (module.name === "libamapr.so" || module.name === "libamapnsq.so") {
                log("加载模块: " + module.name + " @ " + formatAddress(module.base));
            }
        }
        
        log("模块枚举完成，共 " + modules.length + " 个模块");
    }

    // 2. 查找和钩住 Native 层关键函数
    function hookNativeFunctions() {
        log("开始查找和钩住 Native 层关键函数...");
        
        // 查找 libamapr.so 模块
        var libamapr = Process.findModuleByName("libamapr.so");
        if (!libamapr) {
            logError("未找到 libamapr.so 模块");
            return;
        }
        
        log("找到 libamapr.so 模块，基地址: " + formatAddress(libamapr.base));
        
        // 计算关键函数地址
        gNativeAddMapGestureMsgAddr = libamapr.base.add(config.functionOffsets.nativeAddMapGestureMsg);
        var getMapEngineInstanceAddr = libamapr.base.add(config.functionOffsets.getMapEngineInstance);
        var validateEngineAddr = libamapr.base.add(config.functionOffsets.validateEngine);
        var processGestureMessageAddr = libamapr.base.add(config.functionOffsets.processGestureMessage);
        var triggerRenderUpdateAddr = libamapr.base.add(config.functionOffsets.triggerRenderUpdate);
        var finalizeProcessingAddr = libamapr.base.add(config.functionOffsets.finalizeProcessing);
        
        log("关键函数地址:");
        log("  nativeAddMapGestureMsg: " + formatAddress(gNativeAddMapGestureMsgAddr));
        log("  getMapEngineInstance: " + formatAddress(getMapEngineInstanceAddr));
        log("  validateEngine: " + formatAddress(validateEngineAddr));
        log("  processGestureMessage: " + formatAddress(processGestureMessageAddr));
        log("  triggerRenderUpdate: " + formatAddress(triggerRenderUpdateAddr));
        log("  finalizeProcessing: " + formatAddress(finalizeProcessingAddr));
        
        // 尝试钩住关键函数
        try {
            // 1. 钩住 getMapEngineInstance
            Interceptor.attach(getMapEngineInstanceAddr, {
                onEnter: function(args) {
                    log("\n[调用] getMapEngineInstance(nativePtr=" + formatAddress(args[0]) + ", type=" + args[1] + ")");
                    this.args = [args[0], args[1]];
                    this.startTime = new Date().getTime();
                },
                onLeave: function(retval) {
                    log("[返回] getMapEngineInstance => " + formatAddress(retval) + 
                       " (耗时: " + (new Date().getTime() - this.startTime) + "ms)");
                    
                    // 记录返回的引擎实例地址
                    if (retval && !retval.isNull()) {
                        log("  获取到地图引擎实例: " + formatAddress(retval));
                    }
                }
            });
            log("成功钩住 getMapEngineInstance 函数");
            
            // 2. 钩住 validateEngine
            Interceptor.attach(validateEngineAddr, {
                onEnter: function(args) {
                    log("\n[调用] validateEngine(enginePtr=" + formatAddress(args[0]) + ")");
                    this.startTime = new Date().getTime();
                },
                onLeave: function(retval) {
                    log("[返回] validateEngine => " + retval + 
                       " (耗时: " + (new Date().getTime() - this.startTime) + "ms)");
                    
                    // 解释返回值
                    if (retval.toInt32() !== 0) {
                        log("  引擎验证成功");
                    } else {
                        log("  引擎验证失败");
                    }
                }
            });
            log("成功钩住 validateEngine 函数");
            
            // 3. 钩住 processGestureMessage
            Interceptor.attach(processGestureMessageAddr, {
                onEnter: function(args) {
                    log("\n[调用] processGestureMessage(enginePtr=" + formatAddress(args[0]) + ")");
                    this.startTime = new Date().getTime();
                },
                onLeave: function(retval) {
                    log("[返回] processGestureMessage => " + formatAddress(retval) + 
                       " (耗时: " + (new Date().getTime() - this.startTime) + "ms)");
                    
                    // 记录手势处理对象
                    if (retval && !retval.isNull()) {
                        log("  手势处理对象: " + formatAddress(retval));
                        
                        // 尝试读取对象内容
                        try {
                            var firstField = Memory.readPointer(retval);
                            log("  对象首字段: " + formatAddress(firstField));
                        } catch (e) {}
                    }
                }
            });
            log("成功钩住 processGestureMessage 函数");
            
            // 4. 钩住 triggerRenderUpdate
            Interceptor.attach(triggerRenderUpdateAddr, {
                onEnter: function(args) {
                    log("\n[调用] triggerRenderUpdate(ptr=" + formatAddress(args[0]) + ")");
                    this.startTime = new Date().getTime();
                },
                onLeave: function(retval) {
                    log("[返回] triggerRenderUpdate => " + formatAddress(retval) + 
                       " (耗时: " + (new Date().getTime() - this.startTime) + "ms)");
                }
            });
            log("成功钩住 triggerRenderUpdate 函数");
            
            // 5. 钩住 finalizeProcessing
            Interceptor.attach(finalizeProcessingAddr, {
                onEnter: function(args) {
                    log("\n[调用] finalizeProcessing(ptr=" + formatAddress(args[0]) + ")");
                    this.startTime = new Date().getTime();
                },
                onLeave: function(retval) {
                    log("[返回] finalizeProcessing => " + formatAddress(retval) + 
                       " (耗时: " + (new Date().getTime() - this.startTime) + "ms)");
                }
            });
            log("成功钩住 finalizeProcessing 函数");
            
        } catch (e) {
            logError("钩住 Native 函数失败: " + e);
        }
    }

    // 3. 钩住 Java 层的 nativeAddMapGestureMsg 方法以触发 Native 调用
    function hookJavaGestureMethod() {
        log("开始监控 Java 层 nativeAddMapGestureMsg 方法...");
        
        Java.perform(function() {
            try {
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                        var startTime = new Date().getTime();
                        
                        log("\n========== 手势事件开始 ==========");
                        log("类型: " + getGestureTypeName(type) + " (" + type + ")");
                        log("参数: engineId=" + engineId + 
                           ", nativePtr=0x" + nativePtr.toString(16) + 
                           ", param1=" + param1.toFixed(2) + 
                           ", param2=" + param2.toFixed(2) + 
                           ", param3=" + param3.toFixed(2) + 
                           ", param4=" + param4);
                        
                        // 调用原始方法
                        var result = this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                        
                        // 计算总处理时间
                        var totalTime = new Date().getTime() - startTime;
                        log("手势处理完成，耗时: " + totalTime + "ms");
                        log("========== 手势事件结束 ==========\n");
                        
                        return result;
                    };
                    log("成功钩住 Java 层 nativeAddMapGestureMsg 方法");
                } else {
                    logError("未找到 nativeAddMapGestureMsg 方法");
                }
            } catch (e) {
                logError("钩住 Java 层方法失败: " + e);
            }
        });
    }

    // 设置异常处理器
    function setupExceptionHandler() {
        Process.setExceptionHandler(function(exception) {
            // 限制每种异常的输出次数
            var exceptionKey = exception.type + ":" + exception.address;
            
            if (!gExceptionCount[exceptionKey]) {
                gExceptionCount[exceptionKey] = 0;
            }
            
            gExceptionCount[exceptionKey]++;
            
            // 只输出前2次异常
            if (gExceptionCount[exceptionKey] <= 2) {
                logError("异常(" + gExceptionCount[exceptionKey] + "/2): " + 
                        exception.type + " @ " + formatAddress(exception.address));
                
                // 只输出第一次的详细信息
                if (gExceptionCount[exceptionKey] === 1) {
                    logError("详细信息: " + JSON.stringify({
                        type: exception.type,
                        address: formatAddress(exception.address)
                    }));
                }
            }
            
            // 返回 true 表示已处理异常，脚本将继续运行
            return true;
        });
    }

    // 主函数
    function main() {
        log("高德地图 nativeAddMapGestureMsg Native层执行流程分析脚本");
        log("适用于 Frida 12.9.7，使用 ES5 语法");
        
        // 设置异常处理
        setupExceptionHandler();
        
        try {
            // 1. 枚举所有已加载模块
            enumerateLoadedModules();
            
            // 延迟执行其他操作，确保模块信息已获取
            setTimeout(function() {
                try {
                    // 2. 查找和钩住 Native 层关键函数
                    hookNativeFunctions();
                    
                    // 再次延迟执行，确保 JNI 环境已准备好
                    setTimeout(function() {
                        try {
                            // 3. 钩住 Java 层的 nativeAddMapGestureMsg 方法以触发 Native 调用
                            hookJavaGestureMethod();
                        } catch (e) {
                            logError("设置 Java 层钩子失败: " + e);
                        }
                    }, 1000);
                } catch (e) {
                    logError("设置 Native 层钩子失败: " + e);
                }
            }, 2000);
        } catch (e) {
            logError("脚本初始化失败: " + e);
        }
        
        log("脚本设置完成，等待手势事件...");
    }
    
    // 启动脚本
    main();
})();
