// 使用 Java.perform 将所有代码包裹起来，这是与Java交互的最佳实践
Java.perform(function () {

    /**
     * Frida脚本，用于深度打印一个Java类的所有信息。
     * 针对 Frida v12.9.7 和 ES5 语法进行了兼容性重写。
     * by Gemini
     */
    function dumpClassInfo(className) {
        try {
            console.log("\n[*] 开始分析类: " + className);
            console.log("==================================================");

            // 显式获取所有需要的Java反射类
            var Modifier = Java.use('java.lang.reflect.Modifier');

            // 获取目标类的包装器和Class对象
            var clazz = Java.use(className);
            var javaClass = clazz.class;

            // --- 1. 基本信息 ---
            console.log("\n[+] 1. 基本信息 (Basic Information)");
            console.log("--------------------------------------------------");
            var modifiers = Modifier.toString(javaClass.getModifiers());
            console.log("    修饰符 (Modifiers): " + modifiers);
            console.log("    类名 (Simple Name): " + javaClass.getSimpleName());
            console.log("    完整类名 (Full Name): " + javaClass.getName());
            if (javaClass.getPackage()) {
                console.log("    包名 (Package Name): " + javaClass.getPackage().getName());
            }

            // --- 2. 继承关系 ---
            console.log("\n[+] 2. 继承关系 (Inheritance Hierarchy)");
            console.log("--------------------------------------------------");
            var superclass = javaClass.getSuperclass();
            console.log("    父类 (Superclass): " + (superclass ? superclass.getName() : "无"));

            var interfaces = javaClass.getInterfaces();
            console.log("    实现的接口 (Implemented Interfaces):");
            if (interfaces.length > 0) {
                interfaces.forEach(function(iface) {
                    console.log("        - " + iface.getName());
                });
            } else {
                console.log("        无");
            }

            // --- 3. 字段信息 ---
            console.log("\n[+] 3. 字段信息 (Fields)");
            console.log("--------------------------------------------------");
            var fields = javaClass.getDeclaredFields();
            if (fields.length > 0) {
                fields.forEach(function(field) {
                    field.setAccessible(true);
                    var fieldModifiers = Modifier.toString(field.getModifiers());
                    var fieldType = field.getType().getName();
                    var fieldName = field.getName();
                    var staticValue = "";
                    if (fieldModifiers.indexOf("static") !== -1) {
                        try {
                            staticValue = " = " + field.get(null);
                        } catch (e) {
                            staticValue = " = [获取值失败]";
                        }
                    }
                    console.log("    " + fieldModifiers + " " + fieldType + " " + fieldName + staticValue);
                });
            } else {
                console.log("    该类自身未声明任何字段。");
            }

            // --- 4. 构造函数信息 ---
            console.log("\n[+] 4. 构造函数 (Constructors)");
            console.log("--------------------------------------------------");
            var constructors = javaClass.getDeclaredConstructors();
            if (constructors.length > 0) {
                constructors.forEach(function(constructor) {
                    var constructorModifiers = Modifier.toString(constructor.getModifiers());
                    var params = constructor.getParameterTypes().map(function(p) { return p.getName(); }).join(', ');
                    console.log("    " + constructorModifiers + " " + javaClass.getSimpleName() + "(" + params + ")");
                });
            } else {
                console.log("    无显式声明的构造函数。");
            }

            // --- 5. 方法信息 ---
            console.log("\n[+] 5. 方法信息 (Methods)");
            console.log("--------------------------------------------------");
            var methods = javaClass.getDeclaredMethods();
            if (methods.length > 0) {
                methods.forEach(function(method) {
                    var methodModifiers = Modifier.toString(method.getModifiers());
                    var returnType = method.getReturnType().getName();
                    var methodName = method.getName();
                    var params = method.getParameterTypes().map(function(p) { return p.getName(); }).join(', ');
                    var exceptions = method.getExceptionTypes().map(function(e) { return e.getName(); }).join(', ');
                    var throwsClause = exceptions ? " throws " + exceptions : "";
                    console.log("    " + methodModifiers + " " + returnType + " " + methodName + "(" + params + ")" + throwsClause);
                });
            } else {
                console.log("    该类自身未声明任何方法。");
            }

            // --- 6. 内部类信息 ---
            console.log("\n[+] 6. 内部类 (Inner Classes)");
            console.log("--------------------------------------------------");
            var innerClasses = javaClass.getDeclaredClasses();
            if (innerClasses.length > 0) {
                innerClasses.forEach(function(innerClass) {
                    console.log("    " + Modifier.toString(innerClass.getModifiers()) + " " + innerClass.getName());
                });
            } else {
                console.log("    无内部类。");
            }

            // --- 7. 类加载器 ---
            console.log("\n[+] 7. 类加载器 (ClassLoader)");
            console.log("--------------------------------------------------");
            var classLoader = javaClass.getClassLoader();
            if (classLoader) {
                console.log("    " + classLoader.toString());
            } else {
                console.log("    由引导类加载器 (Bootstrap ClassLoader) 加载。");
            }

            console.log("\n==================================================");
            console.log("[*] 类分析完成: " + className);

        } catch (e) {
            console.error("\n[!] 分析类 '" + className + "' 时出错: " + e);
        }
    }

    // ==================================================
    //               --- 使用示例 ---
    // ==================================================

    // 在这里修改为你想要分析的目标类名
    var targetClassName = 'tgo.ngo.mockgps.ui.MainActivity';

    // 调用主函数
    dumpClassInfo(targetClassName);

}); // Java.perform 结束