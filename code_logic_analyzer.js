/*
 * 高德地图代码执行逻辑分析器
 * 目标: 分析应用代码如何解析DICE-AM数据的具体逻辑流程
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[Code Logic Analyzer] 启动代码执行逻辑分析器...");
    
    // 全局配置
    var CONFIG = {
        TRACE_DEPTH: 3,           // 函数调用追踪深度
        LOG_REGISTERS: false,     // 暂时关闭寄存器记录防止崩溃
        LOG_MEMORY_ACCESS: false, // 暂时关闭内存访问监控
        LOG_BRANCHES: true,       // 保持分支跳转记录
        INIT_DELAY: 3000         // 初始化延迟(毫秒)
    };
    
    var callStack = [];
    var executionLog = [];
    var isLibraryReady = false;
    
    // === 辅助函数 ===
    function getCurrentTimestamp() {
        return new Date().toLocaleTimeString();
    }
    
    function formatAddress(addr) {
        if (!addr) return "null";
        return "0x" + addr.toString(16);
    }
    
    function logExecution(type, func, details) {
        var entry = {
            timestamp: getCurrentTimestamp(),
            type: type,
            function: func,
            details: details,
            stackDepth: callStack.length
        };
        executionLog.push(entry);
        
        var indent = "";
        for (var i = 0; i < callStack.length; i++) {
            indent += "  ";
        }
        
        console.log("[" + type + "] " + indent + func + ": " + details);
    }
    
    // === 安全的库检查函数 ===
    function waitForLibrary(libraryName, callback) {
        var maxAttempts = 30;  // 最多尝试30次
        var attempt = 0;
        
        function checkLibrary() {
            try {
                var lib = Module.findBaseAddress(libraryName);
                if (lib) {
                    console.log("[Library] " + libraryName + " 已加载，基址: " + formatAddress(lib));
                    callback(lib);
                    return;
                }
            } catch (e) {
                // 继续等待
            }
            
            attempt++;
            if (attempt < maxAttempts) {
                setTimeout(checkLibrary, 1000); // 1秒后重试
            } else {
                console.log("[Error] " + libraryName + " 加载超时");
            }
        }
        
        checkLibrary();
    }
    
    // === 主要分析目标函数 (简化版) ===
    function analyzeLibamapnsq(libBase) {
        console.log("[Analysis] 开始分析 libamapnsq.so 中的数据解析逻辑...");
        console.log("[Analysis] libamapnsq.so 基址: " + formatAddress(libBase));
        
        try {
            // 只分析核心的DICE-AM解析器，避免复杂的Stalker操作
            analyzeDiceAmParserSafe(libBase);
        } catch (e) {
            console.log("[Error] 分析过程出错: " + e);
        }
    }
    
    // === 安全的DICE-AM解析器分析 ===
    function analyzeDiceAmParserSafe(libBase) {
        try {
            var sub_10F88 = libBase.add(0x10F88);
            
            console.log("[Parser] 开始分析 sub_10F88 (DICE-AM解析器)...");
            
            Interceptor.attach(sub_10F88, {
                onEnter: function(args) {
                    this.startTime = Date.now();
                    callStack.push("sub_10F88");
                    
                    logExecution("ENTER", "sub_10F88", "开始解析DICE-AM数据块");
                    
                    // 安全地记录参数
                    try {
                        logExecution("PARAM", "sub_10F88", 
                                   "args[0]=" + formatAddress(args[0]) + 
                                   ", args[1]=" + formatAddress(args[1]) + 
                                   ", args[2]=" + formatAddress(args[2]));
                        this.args = [args[0], args[1], args[2]];
                    } catch (e) {
                        logExecution("ERROR", "sub_10F88", "参数读取失败: " + e);
                    }
                },
                
                onLeave: function(retval) {
                    try {
                        var duration = Date.now() - this.startTime;
                        callStack.pop();
                        
                        logExecution("LEAVE", "sub_10F88", 
                                   "解析完成, 返回值: " + formatAddress(retval) + 
                                   ", 耗时: " + duration + "ms");
                        
                        // 分析返回值含义
                        var retCode = retval.toInt32();
                        var status = "";
                        switch(retCode) {
                            case 0: status = "成功"; break;
                            case 8: status = "版本不兼容"; break;
                            case 11: status = "数据校验失败"; break;
                            case 26: status = "内存分配失败"; break;
                            default: status = "未知错误(" + retCode + ")";
                        }
                        
                        logExecution("RESULT", "sub_10F88", "解析状态: " + status);
                    } catch (e) {
                        console.log("[Error] onLeave处理失败: " + e);
                    }
                }
            });
            
            console.log("[Parser] sub_10F88 hook 已设置");
            
        } catch (e) {
            console.log("[Error] 设置DICE-AM解析器hook失败: " + e);
        }
    }
    
    // === 执行流程总结 ===
    function generateExecutionSummary() {
        console.log("\n=== 代码执行逻辑分析总结 ===");
        
        var summary = {
            totalFunctions: 0,
            successfulParses: 0,
            failedParses: 0,
            averageExecutionTime: 0,
            commonErrorCodes: {},
            executionPaths: []
        };
        
        // 分析执行日志
        for (var i = 0; i < executionLog.length; i++) {
            var entry = executionLog[i];
            
            if (entry.type === "ENTER") {
                summary.totalFunctions++;
            }
            
            if (entry.type === "RESULT") {
                if (entry.details.indexOf("成功") !== -1) {
                    summary.successfulParses++;
                } else {
                    summary.failedParses++;
                }
            }
        }
        
        console.log("函数调用总数: " + summary.totalFunctions);
        console.log("成功解析: " + summary.successfulParses);
        console.log("失败解析: " + summary.failedParses);
        console.log("==========================\n");
        
        return summary;
    }
    
    // === 定期输出分析报告 ===
    function setupReporting() {
        setInterval(function() {
            if (executionLog.length > 0) {
                generateExecutionSummary();
            }
        }, 15000); // 每15秒输出一次分析报告
    }
    
    // === 安全的主入口 ===
    function main() {
        console.log("[Main] 等待应用初始化完成...");
        
        // 延迟初始化，等待应用完全启动
        setTimeout(function() {
            console.log("[Main] 开始初始化分析器...");
            
            try {
                // 等待库加载
                waitForLibrary("libamapnsq.so", function(libBase) {
                    try {
                        analyzeLibamapnsq(libBase);
                        setupReporting();
                        
                        console.log("[Code Logic Analyzer] 代码逻辑分析器已启动!");
                        console.log("现在移动地图以触发数据解析，观察代码执行逻辑...");
                        
                        isLibraryReady = true;
                        
                    } catch (e) {
                        console.log("[Error] 分析器初始化失败: " + e);
                    }
                });
                
            } catch (e) {
                console.log("[Error] 主初始化失败: " + e);
            }
        }, CONFIG.INIT_DELAY);
    }
    
    // === 启动分析器 ===
    try {
        // 确保在Java环境准备好后再启动
        Java.perform(function() {
            console.log("[Java] Java环境已准备就绪");
            main();
        });
    } catch (e) {
        console.log("[Error] Java环境初始化失败: " + e);
        // 如果Java环境失败，直接尝试启动
        main();
    }
    
})(); 