/*
 * 正确的Frida Hook脚本 - 基于APP代码逻辑获取解压后数据
 * 参考: aggressive_early_hook.js 成功结构
 * 目标: Hook zlib uncompress 获取真正的解压后数据
 */

console.log("[正确Hook] 启动基于APP逻辑的数据获取脚本...");

var dataFound = 0;
var maxData = 10;
var isReady = false;

// 立即设置基础Hook - 使用成功的模式
function setupImmediateHooks() {
    console.log("[Hook] 立即设置基础Hook...");
    
    try {
        // Hook 1: 基础read hook
        var readPtr = Module.findExportByName("libc.so", "read");
        if (readPtr) {
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    this.fd = args[0].toInt32();
                    this.buffer = args[1];
                    this.size = args[2].toInt32();
                },
                onLeave: function(retval) {
                    if (this.size > 1000 && this.size < 50000 && dataFound < maxData && isReady) {
                        try {
                            var preview = this.buffer.readByteArray(32);
                            var bytes = new Uint8Array(preview);
                            var hasZlib = (bytes[0] === 0x78 && bytes[1] === 0x9c);
                            var hasDICE = String.fromCharCode.apply(null, bytes.slice(0, 7)) === "DICE-AM";
                            
                            if (hasZlib || hasDICE) {
                                console.log("[读取] 发现目标数据: " + this.size + " 字节");
                                if (hasZlib) console.log("  -> 包含zlib压缩数据");
                                if (hasDICE) console.log("  -> 包含DICE-AM数据");
                            }
                        } catch (e) {
                            // 静默处理
                        }
                    }
                }
            });
            console.log("✅ [Hook] read() 设置成功");
        }

    } catch (e) {
        console.log("❌ [Hook] 基础Hook失败: " + e.message);
    }
}

// 核心：Hook zlib uncompress - 这里获取真正的解压后数据
function setupZlibHook() {
    console.log("[Hook] 设置zlib解压Hook...");
    
    try {
        var zlibModule = Process.findModuleByName("libz.so");
        if (!zlibModule) {
            console.log("⚠️ [Hook] libz.so未找到，延迟重试...");
            setTimeout(setupZlibHook, 1000);
            return;
        }
        
        var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
        if (uncompressPtr) {
            console.log("✅ [Hook] 找到uncompress: " + uncompressPtr);
            
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    // 保存uncompress参数
                    this.destBuffer = args[0];    // 解压目标缓冲区
                    this.destLenPtr = args[1];    // 解压后长度指针
                    this.sourceBuffer = args[2];  // 压缩源数据
                    this.sourceLen = args[3].toInt32(); // 压缩数据长度
                    
                    console.log("[解压] 开始解压: " + this.sourceLen + " 字节");
                },
                onLeave: function(retval) {
                    // 检查解压是否成功
                    if (retval.toInt32() === 0 && dataFound < maxData) {
                        try {
                            // 读取解压后的实际长度
                            var decompressedLen = this.destLenPtr.readU32();
                            
                            console.log("\n" + "=".repeat(70));
                            console.log(" [解压成功] 这就是你要的解压后数据！");
                            console.log("压缩前: " + this.sourceLen + " 字节");
                            console.log("解压后: " + decompressedLen + " 字节");
                            console.log("=".repeat(70));
                            
                            if (decompressedLen > 0 && decompressedLen < 100000) {
                                // 读取解压后的数据
                                var safeLen = Math.min(decompressedLen, 8192);
                                var decompressedData = this.destBuffer.readByteArray(safeLen);
                                
                                // 分析解压后的数据内容
                                analyzeDecompressedData(decompressedData, dataFound, decompressedLen);
                                dataFound++;
                            }
                            
                        } catch (e) {
                            console.log("❌ [解压] 读取解压数据失败: " + e.message);
                        }
                    } else if (retval.toInt32() !== 0) {
                        console.log("⚠️ [解压] 解压失败，错误码: " + retval.toInt32());
                    }
                }
            });
            
            console.log("✅ [Hook] uncompress Hook设置成功");
        } else {
            console.log("❌ [Hook] 未找到uncompress函数");
        }
        
    } catch (e) {
        console.log("❌ [Hook] zlib Hook失败: " + e.message);
    }
}

// 分析解压后数据的真实内容
function analyzeDecompressedData(data, index, totalLen) {
    console.log(" 解压后数据分析 #" + index + ":");
    
    try {
        var bytes = new Uint8Array(data);
        
        // 1. 显示原始头部信息
        var headerHex = "";
        var headerText = "";
        for (var i = 0; i < Math.min(32, bytes.length); i++) {
            headerHex += bytes[i].toString(16).padStart(2, '0') + " ";
            if (bytes[i] >= 32 && bytes[i] < 127) {
                headerText += String.fromCharCode(bytes[i]);
            } else {
                headerText += ".";
            }
        }
        
        console.log(" 数据头部 (hex): " + headerHex);
        console.log(" 数据头部 (text): " + headerText);
        
        // 2. 检查DICE-AM格式
        if (headerText.startsWith("DICE-AM")) {
            console.log(" 数据类型: DICE-AM矢量地图数据");
            analyzeDICEAMData(bytes);
        }
        // 3. 检查中文文本
        else if (hasChineseContent(bytes)) {
            console.log(" 数据类型: 包含中文文本");
            analyzeChineseText(bytes);
        }
        // 4. 检查JSON/配置
        else if (headerText.indexOf("{") >= 0) {
            console.log(" 数据类型: JSON配置数据");
            analyzeJSONData(bytes);
        }
        // 5. 二进制数据
        else {
            console.log(" 数据类型: 二进制数据");
            analyzeBinaryData(bytes);
        }
        
        console.log(" 数据大小: " + bytes.length + " / " + totalLen + " 字节");
        console.log("-".repeat(70));
        
    } catch (e) {
        console.log(" 数据分析失败: " + e.message);
    }
}

// 分析DICE-AM数据
function analyzeDICEAMData(bytes) {
    try {
        console.log(" DICE-AM详细分析:");
        
        if (bytes.length >= 16) {
            // 解析DICE-AM头部
            var magic = String.fromCharCode.apply(null, bytes.slice(0, 7));
            var version = bytes[7];
            
            // 这里是关键：APP中的版本验证逻辑
            var versionCheck = version ^ 0xAB;
            
            var view = new DataView(bytes.buffer);
            var dataLen = view.getUint32(8, true);   // 小端序
            var pointCount = view.getUint32(12, true);
            
            console.log("   魔数: " + magic);
            console.log("   版本: " + version + " (验证: " + versionCheck + ")");
            console.log("   数据长度: " + dataLen);
            console.log("   坐标点数: " + pointCount);
            
            // 解析坐标点
            var coordCount = 0;
            for (var i = 0; i < Math.min(pointCount, 5); i++) {
                var offset = 16 + i * 12;  // 基于APP逻辑的步长
                if (offset + 8 <= bytes.length) {
                    try {
                        var x = view.getFloat32(offset, true);
                        var y = view.getFloat32(offset + 4, true);
                        
                        // 检查是否为有效经纬度
                        if (isValidCoordinate(x, y)) {
                            console.log("    坐标" + i + ": (" + x.toFixed(6) + ", " + y.toFixed(6) + ") ← 真实经纬度!");
                            coordCount++;
                        } else {
                            console.log("    坐标" + i + ": (" + x.toFixed(6) + ", " + y.toFixed(6) + ") (投影坐标)");
                        }
                    } catch (e) {
                        break;
                    }
                }
            }
            
            console.log("   ✅ 发现 " + coordCount + " 个有效经纬度坐标");
        }
        
    } catch (e) {
        console.log("    DICE-AM分析失败: " + e.message);
    }
}

// 分析中文文本
function analyzeChineseText(bytes) {
    try {
        console.log("🔍 中文文本详细分析:");
        
        // UTF-8解码
        var text = new TextDecoder('utf-8').decode(bytes);
        var placenames = extractChinesePlacenames(text);
        
        if (placenames.length > 0) {
            console.log("   地名数量: " + placenames.length + " 个");
            console.log("   前5个地名: " + placenames.slice(0, 5).join(", "));
            
            // 分类统计
            var roads = placenames.filter(function(name) {
                return name.indexOf("路") >= 0 || name.indexOf("街") >= 0 || name.indexOf("道") >= 0;
            });
            var areas = placenames.filter(function(name) {
                return name.indexOf("市") >= 0 || name.indexOf("区") >= 0 || name.indexOf("村") >= 0;
            });
            
            console.log("   道路名: " + roads.length + " 个");
            console.log("   行政区: " + areas.length + " 个");
            
            if (roads.length > 0) {
                console.log("   道路示例: " + roads.slice(0, 3).join(", "));
            }
            if (areas.length > 0) {
                console.log("   地区示例: " + areas.slice(0, 3).join(", "));
            }
        }
        
    } catch (e) {
        console.log("    中文分析失败: " + e.message);
    }
}

// 分析JSON数据
function analyzeJSONData(bytes) {
    try {
        console.log("🔍 JSON数据详细分析:");
        
        var text = new TextDecoder('utf-8').decode(bytes);
        var jsonStart = text.indexOf('{');
        var jsonEnd = text.lastIndexOf('}');
        
        if (jsonStart >= 0 && jsonEnd > jsonStart) {
            var jsonText = text.substring(jsonStart, jsonEnd + 1);
            console.log("   JSON内容: " + jsonText.substring(0, 100) + "...");
            
            // 检查配置类型
            if (jsonText.indexOf("style") >= 0) {
                console.log("   包含样式配置");
            }
            if (jsonText.indexOf("color") >= 0) {
                console.log("   包含颜色设置");
            }
            if (jsonText.indexOf("zoom") >= 0) {
                console.log("   包含缩放配置");
            }
        }
        
    } catch (e) {
        console.log("  JSON分析失败: " + e.message);
    }
}

// 分析二进制数据
function analyzeBinaryData(bytes) {
    try {
        console.log(" 二进制数据分析:");
        
        // 检查是否包含浮点数坐标
        var view = new DataView(bytes.buffer);
        var coordCount = 0;
        
        for (var i = 0; i < bytes.length - 8; i += 4) {
            try {
                var x = view.getFloat32(i, true);
                var y = view.getFloat32(i + 4, true);
                
                if (isValidCoordinate(x, y)) {
                    console.log("    发现坐标: (" + x.toFixed(6) + ", " + y.toFixed(6) + ") at 偏移 " + i);
                    coordCount++;
                    if (coordCount >= 3) break;  // 限制输出
                }
            } catch (e) {
                continue;
            }
        }
        
        if (coordCount === 0) {
            console.log("    数据模式: 非坐标二进制数据");
            // 显示数据模式
            var header = view.getUint32(0, true);
            console.log("   头部整数: 0x" + header.toString(16));
        }
        
    } catch (e) {
        console.log("    二进制分析失败: " + e.message);
    }
}

// 辅助函数
function hasChineseContent(bytes) {
    try {
        var text = new TextDecoder('utf-8').decode(bytes);
        for (var i = 0; i < Math.min(text.length, 100); i++) {
            if (text.charCodeAt(i) >= 0x4e00 && text.charCodeAt(i) <= 0x9fff) {
                return true;
            }
        }
        return false;
    } catch (e) {
        return false;
    }
}

function extractChinesePlacenames(text) {
    var placenames = [];
    var currentName = "";
    
    for (var i = 0; i < text.length; i++) {
        var char = text[i];
        if (char >= '\u4e00' && char <= '\u9fff') {
            currentName += char;
        } else {
            if (currentName.length >= 2) {
                placenames.push(currentName);
            }
            currentName = "";
        }
    }
    
    if (currentName.length >= 2) {
        placenames.push(currentName);
    }
    
    return placenames;
}

function isValidCoordinate(x, y) {
    // 中国境内经纬度范围检查
    return (70 <= x && x <= 140 && 15 <= y && y <= 60) || 
           (70 <= y && y <= 140 && 15 <= x && x <= 60);
}

// 设置库特定Hook
function setupLibraryHooks() {
    console.log("[Hook] 设置库特定Hook...");
    
    setTimeout(function() {
        try {
            var libamapnsq = Process.findModuleByName("libamapnsq.so");
            if (libamapnsq) {
                console.log("[Hook] 找到libamapnsq.so: " + libamapnsq.base);
                
                // 可选：Hook SQLite绑定
                var sqlitePtr = libamapnsq.base.add(0x15000);
                Interceptor.attach(sqlitePtr, {
                    onEnter: function(args) {
                        this.dataSize = args[3].toInt32();
                    },
                    onLeave: function(retval) {
                        if (this.dataSize > 100 && this.dataSize < 10000 && dataFound < maxData) {
                            console.log("[SQLite] 数据绑定: " + this.dataSize + " 字节");
                        }
                    }
                });
                
                console.log(" [Hook] 库特定Hook设置成功");
            }
        } catch (e) {
            console.log(" [Hook] 库Hook失败: " + e.message);
        }
    }, 1500);
}

// 主程序启动
console.log("[Hook] 开始设置所有Hook...");
setupImmediateHooks();
setupZlibHook();
setupLibraryHooks();

// 延迟激活
setTimeout(function() {
    isReady = true;
    console.log(" [正确Hook] 脚本就绪，开始监控解压数据...");
    console.log(" 请在地图中移动以触发数据解压和实时分析");
    console.log(" 重点关注uncompress Hook的输出结果");
}, 3000);

console.log("[正确Hook] 脚本加载完成，等待激活..."); 