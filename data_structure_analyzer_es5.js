// 数据结构分析脚本 - ES5兼容版本
// 深入分析DICE-AM、配置数据和文本数据的内部结构

console.log("[数据结构分析] 开始深入分析离线地图数据结构...");

// 数据结构统计
var dataStructures = {
    diceAM: {
        count: 0,
        samples: [],
        geometryTypes: {},
        coordinateSystems: {}
    },
    config: {
        count: 0,
        samples: [],
        renderParams: {},
        styleTypes: {}
    },
    text: {
        count: 0,
        samples: [],
        encodings: {},
        languages: {}
    }
};

// 字节转十六进制字符串 (ES5兼容)
function bytesToHex(bytes, maxLen) {
    var hex = [];
    var len = Math.min(maxLen || bytes.length, bytes.length);
    for (var i = 0; i < len; i++) {
        var h = bytes[i].toString(16);
        if (h.length === 1) h = '0' + h;
        hex.push(h);
    }
    return hex.join(' ');
}

// 安全的字节数组切片 (替代Uint8Array.slice)
function safeSlice(uint8Array, start, end) {
    var result = [];
    var actualEnd = end || uint8Array.length;
    for (var i = start; i < Math.min(actualEnd, uint8Array.length); i++) {
        result.push(uint8Array[i]);
    }
    return new Uint8Array(result);
}

// 分析DICE-AM矢量数据结构
function analyzeDiceAM(data, size) {
    var view = new Uint8Array(data);
    var analysis = {
        magic: bytesToHex(safeSlice(view, 0, 8)),
        version: view[8],
        flags: view[9],
        geometryType: "unknown",
        coordinateSystem: "unknown",
        dataBlocks: 0
    };
    
    console.log("[DICE-AM分析] 开始分析矢量数据...");
    console.log("  魔数: " + analysis.magic);
    console.log("  版本: " + analysis.version);
    console.log("  标志: 0x" + analysis.flags.toString(16));
    
    // 分析几何类型
    if (view.length >= 16) {
        var geometryFlag = view[10];
        if (geometryFlag === 0x89) {
            analysis.geometryType = "道路线条";
        } else if (geometryFlag === 0x8d) {
            analysis.geometryType = "建筑多边形";
        } else if (geometryFlag === 0xcf) {
            analysis.geometryType = "水域面片";
        } else {
            analysis.geometryType = "未知几何(" + geometryFlag.toString(16) + ")";
        }
        console.log("  几何类型: " + analysis.geometryType);
    }
    
    // 分析坐标系统
    if (view.length >= 20) {
        var coordBytes = safeSlice(view, 12, 16);
        var coordValue = (coordBytes[3] << 24) | (coordBytes[2] << 16) | (coordBytes[1] << 8) | coordBytes[0];
        if (coordValue > 0) {
            analysis.coordinateSystem = "投影坐标系(" + coordValue + ")";
        }
        console.log("  坐标系统: " + analysis.coordinateSystem);
    }
    
    // 统计数据块
    var blockCount = 0;
    for (var i = 20; i < Math.min(view.length, 100); i += 4) {
        if (view[i] === 0xfe && view[i+1] === 0xfe) {
            blockCount++;
        }
    }
    analysis.dataBlocks = blockCount;
    console.log("  数据块数量: " + analysis.dataBlocks);
    
    dataStructures.diceAM.count++;
    if (dataStructures.diceAM.samples.length < 5) {
        dataStructures.diceAM.samples.push(analysis);
    }
    
    // 更新统计
    if (!dataStructures.diceAM.geometryTypes[analysis.geometryType]) {
        dataStructures.diceAM.geometryTypes[analysis.geometryType] = 0;
    }
    dataStructures.diceAM.geometryTypes[analysis.geometryType]++;
}

// 分析配置数据结构
function analyzeConfig(data, size) {
    var view = new Uint8Array(data);
    var analysis = {
        magic: bytesToHex(safeSlice(view, 0, 4)),
        configType: view[8],
        configFlags: view[9],
        paramCount: 0,
        styleType: "unknown"
    };
    
    console.log("[CONFIG分析] 开始分析配置数据...");
    console.log("  魔数: " + analysis.magic);
    console.log("  配置类型: " + analysis.configType);
    console.log("  配置标志: 0x" + analysis.configFlags.toString(16));
    
    // 分析样式类型
    if (view.length >= 16) {
        var styleFlag = view[10];
        if (styleFlag === 0x00) {
            analysis.styleType = "颜色配置";
        } else if (styleFlag === 0x01) {
            analysis.styleType = "线条样式";
        } else if (styleFlag === 0x03) {
            analysis.styleType = "填充样式";
        } else {
            analysis.styleType = "未知样式(" + styleFlag.toString(16) + ")";
        }
        console.log("  样式类型: " + analysis.styleType);
    }
    
    // 统计参数数量
    if (view.length >= 20) {
        var paramBytes = safeSlice(view, 16, 20);
        analysis.paramCount = (paramBytes[3] << 24) | (paramBytes[2] << 16) | (paramBytes[1] << 8) | paramBytes[0];
        console.log("  参数数量: " + analysis.paramCount);
    }

    // 分析具体参数
    if (view.length >= 32) {
        console.log("  参数预览:");
        for (var i = 0; i < Math.min(4, analysis.paramCount); i++) {
            var offset = 20 + i * 4;
            if (offset + 4 <= view.length) {
                var paramBytes = safeSlice(view, offset, offset + 4);
                var paramValue = (paramBytes[3] << 24) | (paramBytes[2] << 16) | (paramBytes[1] << 8) | paramBytes[0];
                console.log("    参数" + i + ": 0x" + paramValue.toString(16) + " (" + paramValue + ")");
            }
        }
    }
    
    dataStructures.config.count++;
    if (dataStructures.config.samples.length < 5) {
        dataStructures.config.samples.push(analysis);
    }
    
    // 更新统计
    if (!dataStructures.config.styleTypes[analysis.styleType]) {
        dataStructures.config.styleTypes[analysis.styleType] = 0;
    }
    dataStructures.config.styleTypes[analysis.styleType]++;
}

// 分析文本数据结构
function analyzeText(data, size) {
    var view = new Uint8Array(data);
    var analysis = {
        header: bytesToHex(safeSlice(view, 0, 8)),
        textCount: 0,
        encoding: "unknown",
        language: "unknown"
    };
    
    console.log("[TEXT分析] 开始分析文本数据...");
    console.log("  数据头部: " + analysis.header);
    
    // 分析文本数量
    if (view.length >= 8) {
        var countBytes = safeSlice(view, 4, 8);
        analysis.textCount = (countBytes[3] << 24) | (countBytes[2] << 16) | (countBytes[1] << 8) | countBytes[0];
        console.log("  文本条目数: " + analysis.textCount);
    }
    
    // 检测编码格式
    var hasUTF8 = false;
    var hasASCII = true;
    for (var i = 8; i < Math.min(view.length, 100); i++) {
        if (view[i] > 127) {
            hasUTF8 = true;
            hasASCII = false;
        }
        if (view[i] === 0) break;
    }
    
    if (hasUTF8) {
        analysis.encoding = "UTF-8";
    } else if (hasASCII) {
        analysis.encoding = "ASCII";
    }
    console.log("  编码格式: " + analysis.encoding);
    
    // 尝试提取文本样本
    if (view.length >= 16) {
        try {
            var textStart = 16;
            var textSample = "";
            for (var j = textStart; j < Math.min(view.length, textStart + 32); j++) {
                if (view[j] === 0) break;
                if (view[j] >= 32 && view[j] <= 126) {
                    textSample += String.fromCharCode(view[j]);
                }
            }
            if (textSample.length > 0) {
                console.log("  文本样本: \"" + textSample + "\"");
                
                // 简单语言检测
                if (/[\u4e00-\u9fff]/.test(textSample)) {
                    analysis.language = "中文";
                } else if (/[a-zA-Z]/.test(textSample)) {
                    analysis.language = "英文";
                }
            }
        } catch (e) {
            console.log("  文本提取失败: " + e.message);
        }
    }
    
    console.log("  语言: " + analysis.language);
    
    dataStructures.text.count++;
    if (dataStructures.text.samples.length < 5) {
        dataStructures.text.samples.push(analysis);
    }
}

// 数据类型识别
function identifyAndAnalyze(data, size) {
    var view = new Uint8Array(data);
    
    if (view.length >= 4) {
        // DICE-AM矢量数据
        if (view[0] === 0x44 && view[1] === 0x49 && view[2] === 0x43 && view[3] === 0x45) {
            analyzeDiceAM(data, size);
            return "DICE-AM";
        }
        // 配置数据
        if (view[0] === 0xbc && view[1] === 0xbc && view[2] === 0xbc && view[3] === 0xbc) {
            analyzeConfig(data, size);
            return "CONFIG";
        }
        // 文本数据
        if (view[0] === 0x0d && view[1] === 0x00 && view[2] === 0x00 && view[3] === 0x00) {
            analyzeText(data, size);
            return "TEXT";
        }
    }
    return "OTHER";
}

// Hook zlib解压来分析数据结构
var libz = null;
try {
    libz = Process.getModuleByName("libz.so");
    console.log("[✓] libz.so 已加载");
    
    var uncompressPtr = libz.getExportByName("uncompress");
    if (uncompressPtr) {
        Interceptor.attach(uncompressPtr, {
            onEnter: function(args) {
                this.dest = args[0];
                this.destLen = args[1];
                this.source = args[2];
                this.sourceLen = args[3];
            },
            onLeave: function(retval) {
                if (retval.toInt32() === 0) {
                    var decompressedSize = this.destLen.readU32();

                    try {
                        var data = this.dest.readByteArray(Math.min(200, decompressedSize));
                        if (data) {
                            var dataType = identifyAndAnalyze(data, decompressedSize);
                            console.log("[数据结构] 分析完成，类型: " + dataType + "\n");
                        }
                    } catch (e) {
                        console.log("[警告] 数据结构分析失败: " + e.message);
                    }
                }
            }
        });
        console.log("[✓] 数据结构分析Hook设置成功");
    }
} catch (e) {
    console.log("[✗] libz.so 未找到");
}

// 定期输出数据结构统计
setInterval(function() {
    console.log("\n[数据结构统计] ==========================================");
    console.log("DICE-AM矢量数据:");
    console.log("  总数: " + dataStructures.diceAM.count);
    console.log("  几何类型分布:");
    for (var geomType in dataStructures.diceAM.geometryTypes) {
        console.log("    " + geomType + ": " + dataStructures.diceAM.geometryTypes[geomType]);
    }
    
    console.log("\n配置数据:");
    console.log("  总数: " + dataStructures.config.count);
    console.log("  样式类型分布:");
    for (var styleType in dataStructures.config.styleTypes) {
        console.log("    " + styleType + ": " + dataStructures.config.styleTypes[styleType]);
    }
    
    console.log("\n文本数据:");
    console.log("  总数: " + dataStructures.text.count);
    console.log("===============================================\n");
}, 20000);

console.log("[数据结构分析] 分析脚本已启动，等待数据解压...");
console.log("[提示] 请移动地图以触发数据结构分析");
