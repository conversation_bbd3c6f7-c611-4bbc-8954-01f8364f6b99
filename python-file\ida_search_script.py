# IDA Pro 地图数据搜索脚本
# 搜索DICE-AM、JSON、XML等地图数据格式

import idaapi
import ida_bytes
import ida_search
import os

print("[IDA Search] 开始搜索地图数据...")

# 搜索模式列表
search_patterns = [
    ("DICE-AM", "DICE-AM header"),
    ("<?xml", "XML configuration"),
    ('{"res_list"', "JSON resource list"),
    ("\x44\x49\x43\x45\x2d\x41\x4d", "DICE-AM binary"),
    ("\xe4\xb8\xad", "Chinese UTF-8"),
]

results = []

# 搜索每个模式
for pattern, description in search_patterns:
    print(f"[搜索] {description}: {pattern}")
    
    ea = 0
    count = 0
    while ea != idaapi.BADADDR and count < 10:  # 限制每个模式最多10个结果
        if isinstance(pattern, str):
            # 字符串搜索
            ea = ida_search.find_text(ea + 1, idaapi.BADADDR, pattern, 
                                    ida_search.SEARCH_DOWN | ida_search.SEARCH_CASE)
        else:
            # 二进制搜索
            ea = ida_bytes.find_bytes(ea + 1, idaapi.BADADDR, pattern, 16)
        
        if ea != idaapi.BADADDR:
            count += 1
            print(f"  找到 #{count}: 0x{ea:X}")
            
            # 提取数据
            try:
                data = ida_bytes.get_bytes(ea, 512)  # 读取512字节
                if data:
                    # 保存到文件
                    filename = f"extracted_{description.replace(' ', '_')}_{ea:X}.bin"
                    with open(filename, "wb") as f:
                        f.write(data)
                    
                    # 尝试转换为可读文本
                    text_data = ""
                    for byte in data[:256]:  # 只显示前256字节
                        if 32 <= byte < 127:
                            text_data += chr(byte)
                        elif byte == 10:
                            text_data += "\n"
                        elif byte == 9:
                            text_data += "\t"
                        else:
                            text_data += "."
                    
                    print(f"    预览: {text_data[:100]}")
                    print(f"    保存到: {filename}")
                    
                    results.append({
                        'address': hex(ea),
                        'type': description,
                        'filename': filename,
                        'preview': text_data[:200]
                    })
            except Exception as e:
                print(f"    提取失败: {e}")

print(f"\n[完成] 共找到 {len(results)} 个数据块")

# 生成报告
with open("map_data_report.txt", "w", encoding="utf-8") as f:
    f.write("# 高德地图数据提取报告\n\n")
    f.write(f"总计发现: {len(results)} 个数据块\n\n")
    
    for i, result in enumerate(results, 1):
        f.write(f"## 数据块 #{i}\n")
        f.write(f"地址: {result['address']}\n")
        f.write(f"类型: {result['type']}\n")
        f.write(f"文件: {result['filename']}\n")
        f.write(f"预览:\n```\n{result['preview']}\n```\n\n")

print("详细报告保存到: map_data_report.txt")
print("[IDA Search] 搜索完成！") 