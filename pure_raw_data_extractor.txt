     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
启动纯原生数据提取器
目标：提取APP处理的完全未修改的原始数据
 启动纯原生数据提取
 目标：零修改的原始数据提取
Hook文件读取操作
Hook zlib解压前数据
 Hook SQLite blob绑定
 所有Hook已设置，等待原始数据...
 在地图App中操作以触发数据处理
 原始数据将保存为 raw_data_*.bin 文件
[Remote::com.autonavi.minimap]->  Hook girf_sqlite3_bind_blob at: 0x7f5f96b000
 发现zlib压缩原始数据，大小: 139
 保存失败: undefined not callable (property 'padStart' of '0')
备用保存到内存: undefined
 发现原始.ans数据，文件描述符: 197 大小: 3030
 保存失败: undefined not callable (property 'padStart' of '1')
备用保存到内存: undefined
 发现zlib压缩原始数据，大小: 3030
 保存失败: undefined not callable (property 'padStart' of '2')
备用保存到内存: undefined

================================================================================
 纯原生数据提取报告
================================================================================
 提取统计:
   总提取数量: 3
 按来源统计:
   zlib_compressed: 2 个文件
   file_read: 1 个文件

 重要说明:
    所有数据都是APP处理的原始字节，未经任何修改
    保存为.bin二进制文件，可用十六进制编辑器查看
    数据来源：文件读取、zlib压缩、SQLite绑定
    这些就是APP实际处理的纯原生数据
 发现原始.ans数据，文件描述符: 197 大小: 5645
 保存失败: undefined not callable (property 'padStart' of '3')
备用保存到内存: undefined
 发现zlib压缩原始数据，大小: 5645
 保存失败: undefined not callable (property 'padStart' of '4')
备用保存到内存: undefined
 发现原始.ans数据，文件描述符: 197 大小: 1699
 保存失败: undefined not callable (property 'padStart' of '5')
备用保存到内存: undefined
 发现zlib压缩原始数据，大小: 1699
 保存失败: undefined not callable (property 'padStart' of '6')
备用保存到内存: undefined
 发现原始.ans数据，文件描述符: 197 大小: 2791
 保存失败: undefined not callable (property 'padStart' of '7')
备用保存到内存: undefined
 发现zlib压缩原始数据，大小: 2791
 保存失败: undefined not callable (property 'padStart' of '8')
备用保存到内存: undefined
 发现原始.ans数据，文件描述符: 197 大小: 3323
 保存失败: undefined not callable (property 'padStart' of '9')
备用保存到内存: undefined
 发现zlib压缩原始数据，大小: 3323
 保存失败: undefined not callable (property 'padStart' of '10')
备用保存到内存: undefined
 发现原始.ans数据，文件描述符: 197 大小: 3418
 保存失败: undefined not callable (property 'padStart' of '11')
备用保存到内存: undefined
 发现zlib压缩原始数据，大小: 3418
 保存失败: undefined not callable (property 'padStart' of '12')
备用保存到内存: undefined
 发现原始.ans数据，文件描述符: 197 大小: 3422
 保存失败: undefined not callable (property 'padStart' of '13')
备用保存到内存: undefined
 发现zlib压缩原始数据，大小: 3422
 保存失败: undefined not callable (property 'padStart' of '14')
备用保存到内存: undefined
 发现原始.ans数据，文件描述符: 197 大小: 3212
 保存失败: undefined not callable (property 'padStart' of '15')
备用保存到内存: undefined
 发现zlib压缩原始数据，大小: 3212
 保存失败: undefined not callable (property 'padStart' of '16')
备用保存到内存: undefined
 发现原始.ans数据，文件描述符: 197 大小: 3530
 保存失败: undefined not callable (property 'padStart' of '17')
备用保存到内存: undefined
 发现zlib压缩原始数据，大小: 3530
 保存失败: undefined not callable (property 'padStart' of '18')
备用保存到内存: undefined
 发现原始.ans数据，文件描述符: 197 大小: 5519
 保存失败: undefined not callable (property 'padStart' of '19')
备用保存到内存: undefined

================================================================================
 纯原生数据提取报告
================================================================================
 提取统计:
   总提取数量: 20
 按来源统计:
   zlib_compressed: 10 个文件
   file_read: 10 个文件

 重要说明:
    所有数据都是APP处理的原始字节，未经任何修改
    保存为.bin二进制文件，可用十六进制编辑器查看
    数据来源：文件读取、zlib压缩、SQLite绑定
    这些就是APP实际处理的纯原生数据
Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=000001C859258F00)

Thread 0x0000026c (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x00003510 (most recent call first):
<no Python frame>
