#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高德地图.ans文件代码逻辑解析器
按照实际APP代码逻辑进行解析，而非简单的字符串搜索
基于IDA Pro分析的实际解析流程
"""

import os
import zlib
import struct
import json

class GaodeAnsParser:
    """高德地图.ans文件解析器 - 基于实际代码逻辑"""
    
    def __init__(self, filename):
        self.filename = filename
        self.file_size = os.path.getsize(filename)
        self.data = None
        self.header = None
        
    def parse_file(self):
        """按照实际代码逻辑解析文件"""
        print(f"🎯 按代码逻辑解析: {self.filename}")
        print("=" * 80)
        
        with open(self.filename, 'rb') as f:
            # 1. 读取并解析文件头 (模拟sub_C654逻辑)
            self.parse_header(f)
            
            # 2. 解析数据块结构 (模拟AM-zlib格式)
            self.parse_data_blocks(f)
            
            # 3. 解压和解析数据 (模拟zlib解压和sub_5C394调度)
            self.decompress_and_parse(f)
    
    def parse_header(self, f):
        """解析AM-zlib文件头部 - 基于实际文件格式"""
        print("\n📋 解析文件头部 (基于实际代码逻辑):")
        print("-" * 50)
        
        f.seek(0)
        self.header = f.read(64)  # 读取头部
        
        # AM-zlib格式解析
        if self.header.startswith(b'AM-zlib\x00'):
            print("✅ 确认AM-zlib格式")
            
            # 解析版本信息 (offset 8-15)
            version_data = self.header[8:16]
            version_info = struct.unpack('<2I', version_data)
            print(f"📦 版本信息: {version_info}")
            
            # 解析数据块大小 (offset 16-19) 
            block_size = struct.unpack('<I', self.header[16:20])[0]
            print(f"📦 主数据块大小: {block_size} 字节")
            
            # 解析压缩标志 (offset 20-23)
            compression_flags = struct.unpack('<I', self.header[20:24])[0]
            print(f"📦 压缩标志: 0x{compression_flags:08X}")
            
            return {
                'format': 'AM-zlib',
                'version': version_info,
                'block_size': block_size,
                'compression': compression_flags
            }
        else:
            print("❌ 未识别的文件格式")
            return None
    
    def parse_data_blocks(self, f):
        """解析数据块结构 - 模拟实际代码的数据块读取逻辑"""
        print("\n🔧 解析数据块结构:")
        print("-" * 50)
        
        # 跳过头部，开始读取数据块
        f.seek(64)  # 假设头部是64字节
        
        blocks = []
        block_index = 0
        
        while f.tell() < self.file_size:
            current_pos = f.tell()
            
            # 尝试读取数据块头部 (8字节)
            block_header = f.read(8)
            if len(block_header) < 8:
                break
                
            # 解析数据块头部
            block_type, block_size = struct.unpack('<2I', block_header)
            
            if block_size == 0 or block_size > self.file_size:
                # 无效的块大小，尝试下一个位置
                f.seek(current_pos + 1)
                continue
                
            print(f"📦 数据块 #{block_index}:")
            print(f"    偏移: 0x{current_pos:08X}")
            print(f"    类型: 0x{block_type:08X}")
            print(f"    大小: {block_size} 字节")
            
            # 读取数据块内容
            block_data = f.read(block_size)
            if len(block_data) != block_size:
                print(f"    ⚠️  读取不完整: 期望{block_size}字节，实际{len(block_data)}字节")
                break
            
            # 分析数据块内容
            self.analyze_block_content(block_data, block_type, current_pos)
            
            blocks.append({
                'index': block_index,
                'offset': current_pos,
                'type': block_type,
                'size': block_size,
                'data': block_data
            })
            
            block_index += 1
            
            # 限制解析的块数量，避免无限循环
            if block_index >= 20:
                print("📊 达到最大解析块数限制")
                break
        
        print(f"\n📊 总计解析了 {len(blocks)} 个数据块")
        return blocks
    
    def analyze_block_content(self, data, block_type, offset):
        """分析数据块内容 - 模拟sub_10F88的解析逻辑"""
        if len(data) < 16:
            return
            
        print(f"    🔍 内容分析:")
        
        # 1. 检查是否为压缩数据 (zlib magic: 78 9C)
        if data.startswith(b'\x78\x9c'):
            print(f"        ✅ 发现zlib压缩数据")
            try:
                decompressed = zlib.decompress(data)
                print(f"        📦 解压后大小: {len(decompressed)} 字节")
                self.analyze_decompressed_data(decompressed, offset)
            except:
                print(f"        ❌ zlib解压失败")
                
        # 2. 检查特定的数据格式标识
        elif data.startswith(b'DICE-AM'):
            print(f"        ✅ 发现DICE-AM矢量数据")
            self.parse_dice_am_data(data, offset)
            
        # 3. 检查JSON数据
        elif b'{"' in data[:100] or b'"}' in data[-100:]:
            print(f"        ✅ 可能包含JSON数据")
            self.extract_json_data(data, offset)
            
        # 4. 检查XML数据  
        elif b'<' in data[:100] and b'>' in data[:100]:
            print(f"        ✅ 可能包含XML数据")
            self.extract_xml_data(data, offset)
            
        # 5. 检查UTF-8中文文本
        elif self.contains_chinese_utf8(data):
            print(f"        ✅ 发现UTF-8中文文本")
            self.extract_chinese_text(data, offset)
        else:
            # 显示数据的十六进制预览
            preview = data[:32]
            hex_preview = ' '.join(f'{b:02X}' for b in preview)
            print(f"        📄 数据预览: {hex_preview}")
    
    def decompress_and_parse(self, f):
        """解压和解析数据 - 模拟完整的解压解析流程"""
        print("\n🗜️  执行解压和深度解析:")
        print("-" * 50)
        
        # 搜索压缩数据块
        f.seek(0)
        compressed_blocks = []
        
        chunk_size = 1024 * 1024  # 1MB chunks
        offset = 0
        
        while offset < self.file_size:
            f.seek(offset)
            chunk = f.read(chunk_size)
            if not chunk:
                break
                
            # 搜索zlib压缩标识
            pos = 0
            while True:
                zlib_pos = chunk.find(b'\x78\x9c', pos)
                if zlib_pos == -1:
                    break
                    
                actual_offset = offset + zlib_pos
                print(f"📦 发现压缩数据块，偏移: 0x{actual_offset:08X}")
                
                # 尝试确定压缩数据的长度
                f.seek(actual_offset)
                compressed_data = f.read(min(64*1024, self.file_size - actual_offset))  # 最大64KB
                
                # 尝试解压
                try:
                    decompressed = zlib.decompress(compressed_data)
                    print(f"    ✅ 解压成功: {len(compressed_data)} → {len(decompressed)} 字节")
                    
                    # 保存解压后的数据
                    self.save_decompressed_data(decompressed, actual_offset)
                    
                    compressed_blocks.append({
                        'offset': actual_offset,
                        'compressed_size': len(compressed_data),
                        'decompressed_size': len(decompressed),
                        'data': decompressed
                    })
                    
                except Exception as e:
                    # 可能是部分数据，尝试不同长度
                    for try_size in [1024, 4096, 16384]:
                        if try_size > len(compressed_data):
                            continue
                        try:
                            partial_data = compressed_data[:try_size]
                            decompressed = zlib.decompress(partial_data)
                            print(f"    ✅ 部分解压成功: {try_size} → {len(decompressed)} 字节")
                            self.save_decompressed_data(decompressed, actual_offset, partial=True)
                            break
                        except:
                            continue
                    else:
                        print(f"    ❌ 解压失败: {str(e)[:50]}")
                
                pos = zlib_pos + 1
            
            offset += chunk_size - 10  # 重叠搜索
        
        print(f"\n📊 总计处理了 {len(compressed_blocks)} 个压缩数据块")
        return compressed_blocks
    
    def analyze_decompressed_data(self, data, source_offset):
        """分析解压后的数据 - 模拟sub_5C394调度逻辑"""
        print(f"        🔍 解压数据分析 (来源: 0x{source_offset:08X}):")
        
        # 1. DICE-AM格式检查
        if data.startswith(b'DICE-AM'):
            print(f"            ✅ DICE-AM矢量数据")
            self.parse_dice_am_data(data, source_offset)
            
        # 2. JSON格式检查
        elif data.strip().startswith(b'{') and data.strip().endswith(b'}'):
            print(f"            ✅ JSON配置数据")
            self.extract_json_data(data, source_offset)
            
        # 3. XML格式检查
        elif data.strip().startswith(b'<'):
            print(f"            ✅ XML配置数据") 
            self.extract_xml_data(data, source_offset)
            
        # 4. 地理坐标数据检查 (float序列)
        elif self.contains_coordinate_data(data):
            print(f"            ✅ 地理坐标数据")
            self.extract_coordinate_data(data, source_offset)
            
        # 5. 中文文本检查
        elif self.contains_chinese_utf8(data):
            print(f"            ✅ 中文地名数据")
            self.extract_chinese_text(data, source_offset)
        else:
            # 二进制数据分析
            print(f"            📊 二进制数据，大小: {len(data)} 字节")
    
    def parse_dice_am_data(self, data, offset):
        """解析DICE-AM格式数据 - 模拟sub_10F88逻辑"""
        if len(data) < 32:
            return
            
        print(f"            🎯 DICE-AM结构解析:")
        
        # DICE-AM头部解析
        header = data[:32]
        magic = header[:7]  # "DICE-AM"
        version = header[7]
        
        print(f"                标识: {magic.decode('ascii', errors='ignore')}")
        print(f"                版本: 0x{version:02X}")
        
        if len(data) > 32:
            # 数据块计数
            block_count = struct.unpack('<I', data[16:20])[0] if len(data) >= 20 else 0
            print(f"                数据块数: {block_count}")
            
            # 保存解析结果
            output_file = f"dice_am_parsed_{offset:08X}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"# DICE-AM数据解析结果\n")
                f.write(f"# 来源偏移: 0x{offset:08X}\n")
                f.write(f"# 数据大小: {len(data)} 字节\n\n")
                f.write(f"标识: {magic.decode('ascii', errors='ignore')}\n")
                f.write(f"版本: 0x{version:02X}\n")
                f.write(f"数据块数: {block_count}\n\n")
                
                # 十六进制转储
                f.write("=== 十六进制数据 ===\n")
                for i in range(0, min(512, len(data)), 16):
                    hex_line = " ".join(f"{data[i+j]:02X}" for j in range(min(16, len(data)-i)))
                    ascii_line = "".join(chr(data[i+j]) if 32 <= data[i+j] < 127 else "." for j in range(min(16, len(data)-i)))
                    f.write(f"{i:04X}: {hex_line:<48} |{ascii_line}|\n")
            
            print(f"                💾 保存到: {output_file}")
    
    def extract_json_data(self, data, offset):
        """提取JSON数据"""
        try:
            # 查找JSON边界
            text = data.decode('utf-8', errors='ignore')
            start = text.find('{')
            end = text.rfind('}') + 1
            
            if start != -1 and end > start:
                json_text = text[start:end]
                
                # 尝试解析JSON
                try:
                    parsed = json.loads(json_text)
                    print(f"            ✅ 有效JSON，键数: {len(parsed) if isinstance(parsed, dict) else 'N/A'}")
                    
                    # 保存JSON数据
                    output_file = f"json_data_{offset:08X}.json"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(parsed, f, ensure_ascii=False, indent=2)
                    print(f"                💾 保存到: {output_file}")
                    
                except json.JSONDecodeError:
                    # 保存为文本
                    output_file = f"json_text_{offset:08X}.txt"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(f"# JSON文本数据 (解析失败)\n")
                        f.write(f"# 来源偏移: 0x{offset:08X}\n\n")
                        f.write(json_text)
                    print(f"                💾 保存JSON文本到: {output_file}")
                    
        except Exception as e:
            print(f"            ❌ JSON提取失败: {e}")
    
    def extract_xml_data(self, data, offset):
        """提取XML数据"""
        try:
            text = data.decode('utf-8', errors='ignore')
            
            # 查找XML标签
            if '<' in text and '>' in text:
                # 简单的XML提取
                start = text.find('<')
                end = text.rfind('>') + 1
                
                if start != -1 and end > start:
                    xml_text = text[start:end]
                    
                    output_file = f"xml_data_{offset:08X}.xml"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(f"<!-- XML数据 -->\n")
                        f.write(f"<!-- 来源偏移: 0x{offset:08X} -->\n")
                        f.write(xml_text)
                    print(f"                💾 保存到: {output_file}")
                    
        except Exception as e:
            print(f"            ❌ XML提取失败: {e}")
    
    def extract_chinese_text(self, data, offset):
        """提取中文文本"""
        try:
            text = data.decode('utf-8', errors='ignore')
            
            # 提取中文字符
            chinese_chars = ''.join(c for c in text if '\u4e00' <= c <= '\u9fff')
            
            if len(chinese_chars) > 5:  # 至少5个中文字符
                output_file = f"chinese_text_{offset:08X}.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 中文文本数据\n")
                    f.write(f"# 来源偏移: 0x{offset:08X}\n")
                    f.write(f"# 中文字符数: {len(chinese_chars)}\n\n")
                    f.write(chinese_chars)
                    f.write(f"\n\n=== 原始文本 ===\n")
                    f.write(text[:1000])  # 前1000字符
                
                print(f"                💾 中文文本保存到: {output_file}")
                print(f"                📄 中文字符数: {len(chinese_chars)}")
                print(f"                📄 预览: {chinese_chars[:50]}")
                
        except Exception as e:
            print(f"            ❌ 中文文本提取失败: {e}")
    
    def contains_chinese_utf8(self, data):
        """检查是否包含UTF-8中文字符"""
        # 检查常见的中文UTF-8字节序列
        chinese_patterns = [
            b'\xe4\xb8\xad',  # 中
            b'\xe5\x9c\xb0',  # 地  
            b'\xe5\x9b\xbe',  # 图
            b'\xe5\x8c\x97',  # 北
            b'\xe4\xb8\x8a',  # 上
            b'\xe5\xb9\xbf',  # 广
        ]
        
        return any(pattern in data for pattern in chinese_patterns)
    
    def contains_coordinate_data(self, data):
        """检查是否包含地理坐标数据 (float序列)"""
        if len(data) < 16:
            return False
            
        # 检查是否有连续的float值 (经纬度范围)
        try:
            for i in range(0, len(data) - 7, 4):
                value = struct.unpack('<f', data[i:i+4])[0]
                # 检查是否在合理的经纬度范围内
                if -180.0 <= value <= 180.0:
                    return True
        except:
            pass
        return False
    
    def extract_coordinate_data(self, data, offset):
        """提取地理坐标数据"""
        coordinates = []
        
        try:
            for i in range(0, len(data) - 7, 4):
                value = struct.unpack('<f', data[i:i+4])[0]
                if -180.0 <= value <= 180.0:
                    coordinates.append(value)
                    
            if len(coordinates) > 4:  # 至少2个坐标点
                output_file = f"coordinates_{offset:08X}.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 地理坐标数据\n")
                    f.write(f"# 来源偏移: 0x{offset:08X}\n")
                    f.write(f"# 坐标点数: {len(coordinates)//2}\n\n")
                    
                    for i in range(0, len(coordinates) - 1, 2):
                        f.write(f"坐标 {i//2 + 1}: {coordinates[i]:.6f}, {coordinates[i+1]:.6f}\n")
                
                print(f"                💾 坐标数据保存到: {output_file}")
                print(f"                📍 坐标点数: {len(coordinates)//2}")
                
        except Exception as e:
            print(f"            ❌ 坐标提取失败: {e}")
    
    def save_decompressed_data(self, data, offset, partial=False):
        """保存解压后的数据"""
        suffix = "_partial" if partial else ""
        output_file = f"decompressed_data_{offset:08X}{suffix}.bin"
        
        with open(output_file, 'wb') as f:
            f.write(data)
        
        print(f"        💾 解压数据保存到: {output_file}")
        
        # 同时分析内容
        self.analyze_decompressed_data(data, offset)

def main():
    """主函数 - 按代码逻辑解析m1.ans和m3.ans"""
    print("🎯 高德地图.ans文件代码逻辑解析器")
    print("基于IDA Pro分析的实际解析流程")
    print("=" * 80)
    
    files = ["file/m1.ans", "file/m3.ans"]
    
    for filename in files:
        if os.path.exists(filename):
            parser = GaodeAnsParser(filename)
            parser.parse_file()
            print(f"\n✅ {filename} 解析完成\n")
        else:
            print(f"❌ 文件不存在: {filename}")
    
    print("🎉 所有文件解析完成！")

if __name__ == "__main__":
    main() 