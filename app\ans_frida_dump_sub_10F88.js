(function () {
    'use strict';

    // 配置：可根据需要调整
    var MODULE_NAME = 'libamapnsq.so';
    var SUB_10F88_OFFSET = 0x10F88; // IDA: sub_10F88 => 基址 + 0x10F88
    var DUMP_BYTES = 1024; // hexdump 输出长度（兜底）
    var CHECK_INTERVAL_MS = 500;
    var ENABLE_SMART_LENGTH = true; // 尝试从首4字节推断长度（若合理）
    var HUMAN_READABLE_PREVIEW = true; // 结构化预览（int/float/字符串）
    var PREVIEW_BYTES = 1024; // 用于结构化预览的最大字节数
    var STRING_MIN_LEN = 4; // 提取可打印字符串的最小长度

    function logSeparator() {
        console.log('\n==============================================================');
    }

    function safeHexdump(ptrValue, length) {
        try {
            console.log(hexdump(ptrValue, {
                length: length,
                header: true,
                ansi: false
            }));
        } catch (e) {
            console.log('  [!] 无法读取内存: ' + e.message);
        }
    }

    function safeReadAsciiPreview(ptrValue, maxLen) {
        if (!ptrValue) return;
        var bytes = [];
        var i;
        try {
            for (i = 0; i < maxLen; i++) {
                var b = Memory.readU8(ptrValue.add(i));
                if (b === 0) break;
                if (b >= 0x20 && b <= 0x7E) {
                    bytes.push(String.fromCharCode(b));
                } else {
                    bytes.push('.');
                }
            }
            if (bytes.length > 0) {
                console.log('  [ASCII预览] ' + bytes.join(''));
            }
        } catch (e) {
            console.log('  [!] ASCII 预览失败: ' + e.message);
        }
    }

    function guessDataLength(ptrValue) {
        if (!ENABLE_SMART_LENGTH) return null;
        try {
            var v = Memory.readU32(ptrValue);
            // 经验阈值：1B ~ 1MB 之间认为合理
            if (v > 0 && v <= (1024 * 1024)) {
                return v;
            }
        } catch (e) {
            // 忽略
        }
        return null;
    }

    // 结构化预览：将前若干 4 字节字段同时按 int32 与 float32 展示
    function printIntFloatPreview(ptrValue, numWords) {
        try {
            console.log('[结构化预览] 前 ' + numWords + ' 个 4B 字段（int32/hex/float）');
            for (var i = 0; i < numWords; i++) {
                var off = i * 4;
                var p = ptrValue.add(off);
                var i32 = 0;
                var f32 = 0.0;
                try { i32 = Memory.readS32(p); } catch (e1) {}
                try { f32 = Memory.readFloat(p); } catch (e2) {}
                console.log('  [+] off 0x' + off.toString(16) + ': int32=' + i32 + ' (0x' + (i32 >>> 0).toString(16) + '), float=' + f32);
            }
        } catch (e) {
            console.log('  [!] 结构化预览失败: ' + e.message);
        }
    }

    // 字符串提取：扫描可打印 ASCII，输出长度 >= minLen 的片段
    function extractPrintableStrings(ptrValue, maxLen, minLen) {
        try {
            console.log('[字符串提取] 扫描前 ' + maxLen + ' 字节（ASCII 可打印，长度>=' + minLen + '）');
            var start = -1;
            var i;
            for (i = 0; i < maxLen; i++) {
                var c = 0;
                try { c = Memory.readU8(ptrValue.add(i)); } catch (e1) { break; }
                var printable = (c >= 0x20 && c <= 0x7E);
                if (printable) {
                    if (start === -1) start = i;
                } else {
                    if (start !== -1) {
                        var end = i;
                        var len = end - start;
                        if (len >= minLen) {
                            var s = '';
                            for (var j = start; j < end; j++) {
                                var cc = Memory.readU8(ptrValue.add(j));
                                s += String.fromCharCode(cc);
                            }
                            console.log('  [str] off=0x' + start.toString(16) + ', len=' + len + ': ' + s);
                        }
                        start = -1;
                    }
                }
            }
            // 末尾收尾
            if (start !== -1) {
                var end2 = i;
                var len2 = end2 - start;
                if (len2 >= minLen) {
                    var s2 = '';
                    for (var k = start; k < end2; k++) {
                        var cc2 = Memory.readU8(ptrValue.add(k));
                        s2 += String.fromCharCode(cc2);
                    }
                    console.log('  [str] off=0x' + start.toString(16) + ', len=' + len2 + ': ' + s2);
                }
            }
        } catch (e) {
            console.log('  [!] 字符串提取失败: ' + e.message);
        }
    }

    function hookParser(module) {
        var parserAddr = module.base.add(SUB_10F88_OFFSET);
        console.log('[解析数据] Hooking sub_10F88 at: ' + parserAddr);

        Interceptor.attach(parserAddr, {
            onEnter: function (args) {
                logSeparator();
                console.log('********** 命中解析函数 sub_10F88 **********');
                console.log('[线程] tid=' + Process.getCurrentThreadId());

                this.dataPtr = args[0]; // 推测: 待解析的数据缓冲区
                this.ctxPtr = args[1];  // 推测: 解析上下文/控制结构

                console.log('[参数] arg0 数据缓冲区: ' + this.dataPtr);
                console.log('[参数] arg1 上下文指针: ' + this.ctxPtr);

                // 可选：打印调用栈（准确模式）
                try {
                    var bt = Thread.backtrace(this.context, Backtracer.ACCURATE);
                    console.log('[回溯]');
                    for (var i = 0; i < bt.length && i < 10; i++) {
                        console.log('  ' + DebugSymbol.fromAddress(bt[i]));
                    }
                } catch (e) {
                    console.log('  [!] 回溯失败: ' + e.message);
                }

                var smartLen = guessDataLength(this.dataPtr);
                var dumpLen = smartLen !== null ? Math.min(smartLen, DUMP_BYTES) : DUMP_BYTES;
                if (smartLen !== null) {
                    console.log('[长度] 猜测数据长度(首4字节): ' + smartLen + ' 字节 (实际dump取前 ' + dumpLen + ' 字节)');
                }

                console.log('\n[Hexdump] arg0 (解压后的原始数据块, 前 ' + dumpLen + ' 字节)');
                safeHexdump(this.dataPtr, dumpLen);

                console.log('\n[预览] arg0 ASCII (最多 256 字符，仅作可视参考)');
                safeReadAsciiPreview(this.dataPtr, 256);

                if (HUMAN_READABLE_PREVIEW) {
                    var previewLen = Math.min(dumpLen, PREVIEW_BYTES);
                    var words = Math.max(1, Math.min(16, Math.floor(previewLen / 4)));
                    console.log('\n[人类可读度增强]');
                    printIntFloatPreview(this.dataPtr, words);
                    extractPrintableStrings(this.dataPtr, previewLen, STRING_MIN_LEN);
                }
            },
            onLeave: function (retval) {
                // 返回值含义未知：打印原值与 as int 便于分析
                try {
                    console.log('[返回] sub_10F88 return: ' + retval + ' (int: ' + retval.toInt32() + ')');
                } catch (e) {
                    console.log('[返回] sub_10F88 return: ' + retval);
                }
                logSeparator();
            }
        });

        console.log('[解析数据] Hook 设置完毕。请在 App 内触发解析流程。');
    }

    console.log('[解析数据] 脚本启动中... (Frida 12.9.7 / ES5)');

    var hooked = false;
    var timer = setInterval(function () {
        if (hooked) {
            clearInterval(timer);
            return;
        }
        var mod = Process.findModuleByName(MODULE_NAME);
        if (!mod) return;
        hooked = true;
        clearInterval(timer);

        console.log('[解析数据] 目标模块已加载: ' + MODULE_NAME + ', 基址: ' + mod.base);

        // Java.perform 不是必须，但可确保在 Java 环境已就绪时输出更清晰
        try {
            Java.perform(function () {
                console.log('[解析数据] Java 环境就绪。');
                hookParser(mod);
            });
        } catch (e) {
            // 某些场景纯 Native 也可直接 Hook
            console.log('[解析数据] Java.perform 不可用/失败，直接 Hook Native: ' + e.message);
            hookParser(mod);
        }
    }, CHECK_INTERVAL_MS);
})();
