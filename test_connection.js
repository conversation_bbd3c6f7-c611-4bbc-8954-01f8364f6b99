/**
 * Frida 连接测试脚本
 * 用于验证连接是否正常
 */
(function() {
    console.log("🔌 [Test] Frida 连接测试开始...");
    
    // 获取进程信息
    console.log("📱 [Test] 进程ID: " + Process.id);
    console.log("📱 [Test] 架构: " + Process.arch);
    console.log("📱 [Test] 平台: " + Process.platform);
    
    // 枚举模块
    console.log("📍 [Test] 扫描已加载的模块...");
    var modules = Process.enumerateModules();
    var targetModules = [];
    
    for (var i = 0; i < modules.length; i++) {
        var mod = modules[i];
        if (mod.name.indexOf("amap") !== -1 || 
            mod.name.indexOf("gaode") !== -1 ||
            mod.name === "libamapr.so" ||
            mod.name === "libamapnsq.so") {
            targetModules.push(mod);
            console.log("✅ [Test] 找到目标模块: " + mod.name + " @ " + mod.base);
        }
    }
    
    if (targetModules.length === 0) {
        console.log("⚠️ [Test] 未找到高德相关模块，可能需要等待应用完全启动");
    }
    
    // 简单的Java层测试
    setTimeout(function() {
        try {
            Java.perform(function() {
                console.log("☕ [Test] Java 环境测试成功");
                
                // 尝试获取应用包名
                var context = Java.use("android.app.ActivityThread").currentApplication().getApplicationContext();
                var packageName = context.getPackageName();
                console.log("📦 [Test] 应用包名: " + packageName);
            });
        } catch (e) {
            console.log("❌ [Test] Java 环境测试失败: " + e);
        }
    }, 1000);
    
    console.log("✅ [Test] 连接测试脚本加载成功！");
})(); 