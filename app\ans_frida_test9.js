(function() {
  console.log("[高德地图ANS文件解析分析] 脚本启动");
  
  // 全局变量
  var ansFiles = {};
  var dataProcessFlow = [];
  
  // 防止应用崩溃
  var exit_ptr = Module.findExportByName("libc.so", "exit");
  if (exit_ptr) {
    Interceptor.replace(exit_ptr, new NativeCallback(function() {
      return 0;
    }, 'void', ['int']));
    console.log("[+] 拦截exit()调用");
  }
  
  // 监控文件打开操作
  var open_ptr = Module.findExportByName("libc.so", "open");
  if (open_ptr) {
    Interceptor.attach(open_ptr, {
      onEnter: function(args) {
        try {
          var path = args[0].readUtf8String();
          if (path && path.indexOf(".ans") !== -1) {
            this.isAnsFile = true;
            this.path = path;
            this.fileName = path.split("/").pop();
          }
        } catch (e) {}
      },
      onLeave: function(retval) {
        if (this.isAnsFile && retval.toInt32() > 0) {
          console.log("[ANS文件] 打开: " + this.path + ", fd: " + retval.toInt32());
          ansFiles[retval.toInt32()] = {
            path: this.path,
            fileName: this.fileName,
            openTime: new Date().getTime(),
            reads: [],
            mmaps: []
          };
          
          // 记录调用栈
          try {
            var stack = Thread.backtrace(this.context, Backtracer.ACCURATE);
            var stackInfo = [];
            for (var i = 0; i < stack.length && i < 10; i++) {
              var symbol = DebugSymbol.fromAddress(stack[i]);
              if (symbol && symbol.name && symbol.name.indexOf("libc.so") === -1) {
                stackInfo.push(symbol.name);
              }
            }
            
            console.log("[调用栈] ANS文件打开:");
            for (var i = 0; i < stackInfo.length; i++) {
              console.log("  " + i + ": " + stackInfo[i]);
            }
          } catch(e) {}
        }
      }
    });
    console.log("[+] 监控文件打开操作");
  }

  // 监控内存映射
  var mmap_ptr = Module.findExportByName("libc.so", "mmap");
  if (mmap_ptr) {
    Interceptor.attach(mmap_ptr, {
      onEnter: function(args) {
        this.size = args[1].toInt32();
        this.fd = args[4].toInt32();
        if (ansFiles[this.fd]) {
          this.isAnsFile = true;
          this.fileInfo = ansFiles[this.fd];
        }
      },
      onLeave: function(retval) {
        if (!this.isAnsFile) return;
        
        console.log("[ANS映射] " + this.fileInfo.fileName + " 被映射到内存");
        console.log("  地址: " + retval);
        console.log("  大小: " + this.size);
        
        // 记录映射信息
        this.fileInfo.mmaps.push({
          address: retval,
          size: this.size,
          time: new Date().getTime()
        });
        
        // 尝试分析ANS文件头 - 使用兼容方法
        try {
          var headerData = Memory.readByteArray(retval, Math.min(32, this.size));
          var header = new Uint8Array(headerData);
          
          // 兼容方式处理字节数组
          function byteToHex(b) {
            var hex = b.toString(16);
            return hex.length === 1 ? "0" + hex : hex;
          }
          
          var magicBytes = "";
          for (var i = 0; i < 4 && i < header.length; i++) {
            magicBytes += byteToHex(header[i]) + " ";
          }
          
          var versionBytes = "";
          for (var i = 4; i < 8 && i < header.length; i++) {
            versionBytes += byteToHex(header[i]) + " ";
          }
          
          console.log("[ANS文件结构] 头部分析:");
          console.log("  魔数: " + magicBytes);
          console.log("  版本: " + versionBytes);
          
          // 记录到数据流程，兼容方式
          var headerHex = "";
          for (var i = 0; i < 16 && i < header.length; i++) {
            headerHex += byteToHex(header[i]) + " ";
          }
          
          dataProcessFlow.push({
            stage: "文件映射",
            file: this.fileInfo.fileName,
            time: new Date().getTime(),
            data: {
              address: retval.toString(),
              size: this.size,
              header: headerHex
            }
          });
        } catch(e) {
          console.log("[错误] 解析ANS文件头: " + e);
        }
      }
    });
  }
  
  // Java层Hook设置
  setTimeout(function() {
    Java.perform(function() {
      console.log("[+] 设置Java层分析");
      
      // 监控AjxBLFactoryController
      try {
        var AjxBLFactoryController = Java.use("com.autonavi.jni.ajx3.bl.AjxBLFactoryController");
        console.log("[类] 找到AjxBLFactoryController");
        
        if (AjxBLFactoryController.init4WarmStart) {
          AjxBLFactoryController.init4WarmStart.implementation = function() {
            console.log("[Java调用] AjxBLFactoryController.init4WarmStart");
            
            for (var i = 0; i < arguments.length; i++) {
              console.log("  参数" + i + ": " + arguments[i]);
            }
            
            // 获取调用栈
            try {
              var exception = Java.use("java.lang.Exception").$new();
              var stackElements = exception.getStackTrace();
              console.log("[Java栈] init4WarmStart调用者:");
              for (var i = 0; i < 5 && i < stackElements.length; i++) {
                var element = stackElements[i];
                console.log("  " + element.getClassName() + "." + element.getMethodName());
              }
            } catch(e) {}
            
            var startTime = new Date().getTime();
            var result = this.init4WarmStart.apply(this, arguments);
            var duration = new Date().getTime() - startTime;
            
            console.log("[Java返回] init4WarmStart完成，耗时: " + duration + "ms, 结果: " + result);
            
            dataProcessFlow.push({
              stage: "初始化",
              method: "init4WarmStart",
              time: new Date().getTime(),
              duration: duration,
              result: String(result)
            });
            
            return result;
          };
        }
        
        if (AjxBLFactoryController.nativeInit4WarmStart) {
          AjxBLFactoryController.nativeInit4WarmStart.implementation = function() {
            console.log("[Native调用] nativeInit4WarmStart");
            
            for (var i = 0; i < arguments.length; i++) {
              console.log("  参数" + i + ": " + arguments[i]);
            }
            
            var startTime = new Date().getTime();
            var result = this.nativeInit4WarmStart.apply(this, arguments);
            var duration = new Date().getTime() - startTime;
            
            console.log("[Native返回] nativeInit4WarmStart完成，耗时: " + duration + "ms, 结果: " + result);
            
            dataProcessFlow.push({
              stage: "Native初始化",
              method: "nativeInit4WarmStart",
              time: new Date().getTime(),
              duration: duration,
              result: String(result)
            });
            
            return result;
          };
        }
      } catch(e) {
        console.log("[错误] AjxBLFactoryController监控失败: " + e);
      }
      
      // 监控AMapController
      try {
        var AMapController = Java.use("com.autonavi.ae.gmap.AMapController");
        console.log("[类] 找到AMapController");
        
        if (AMapController.setAppResourceLoader) {
          AMapController.setAppResourceLoader.implementation = function(loader) {
            console.log("[Java调用] AMapController.setAppResourceLoader");
            console.log("  加载器: " + loader);
            
            if (loader) {
              try {
                console.log("  加载器类: " + loader.getClass().getName());
              } catch(e) {}
            }
            
            var result = this.setAppResourceLoader(loader);
            console.log("[Java] 资源加载器设置完成");
            
            dataProcessFlow.push({
              stage: "资源加载器设置",
              time: new Date().getTime(),
              loader: loader ? loader.toString() : "null"
            });
            
            return result;
          };
        }
        
        if (AMapController.createMapView) {
          AMapController.createMapView.implementation = function() {
            console.log("[Java调用] AMapController.createMapView");
            
            console.log("  参数数量: " + arguments.length);
            if (arguments.length >= 3) {
              console.log("  宽度: " + arguments[2] + ", 高度: " + arguments[3]);
            }
            
            var startTime = new Date().getTime();
            var result = this.createMapView.apply(this, arguments);
            var duration = new Date().getTime() - startTime;
            
            console.log("[Java返回] createMapView完成，耗时: " + duration + "ms, 结果: " + result);
            
            dataProcessFlow.push({
              stage: "地图视图创建",
              method: "createMapView",
              time: new Date().getTime(),
              duration: duration,
              result: String(result)
            });
            
            return result;
          };
        }
      } catch(e) {
        console.log("[错误] AMapController监控失败: " + e);
      }
      
      // 监控InterfaceAppImpl
      try {
        var InterfaceAppImpl = Java.use("com.amap.jni.app.InterfaceAppImpl");
        console.log("[类] 找到InterfaceAppImpl");
        
        if (InterfaceAppImpl.getNativeResourceLoader) {
          InterfaceAppImpl.getNativeResourceLoader.implementation = function() {
            console.log("[Java调用] InterfaceAppImpl.getNativeResourceLoader");
            var result = this.getNativeResourceLoader();
            console.log("[Java返回] getNativeResourceLoader: " + result);
            
            if (result) {
              try {
                console.log("  加载器类: " + result.getClass().getName());
              } catch(e) {}
            }
            
            dataProcessFlow.push({
              stage: "获取资源加载器",
              time: new Date().getTime(),
              loader: result ? result.toString() : "null"
            });
            
            return result;
          };
        }
      } catch(e) {
        console.log("[错误] InterfaceAppImpl监控失败: " + e);
      }
      
      // 监控文件操作
      try {
        var FileInputStream = Java.use("java.io.FileInputStream");
        FileInputStream.$init.overload("java.io.File").implementation = function(file) {
          var path = file.getAbsolutePath();
          if (path && path.indexOf(".ans") !== -1) {
            console.log("[Java读取] 打开ANS文件流: " + path);
            
            try {
              var exception = Java.use("java.lang.Exception").$new();
              var stackElements = exception.getStackTrace();
              console.log("[Java栈] ANS文件流打开者:");
              for (var i = 0; i < 5 && i < stackElements.length; i++) {
                var element = stackElements[i];
                console.log("  " + element.getClassName() + "." + element.getMethodName());
              }
            } catch(e) {}
          }
          return this.$init(file);
        };
      } catch(e) {
        console.log("[错误] 文件操作监控失败: " + e);
      }
      
      // 监控系统库加载
      try {
        var System = Java.use("java.lang.System");
        System.loadLibrary.implementation = function(libName) {
          console.log("[库加载] System.loadLibrary(" + libName + ")");
          
          if (libName.indexOf("amapnsq") !== -1 || 
              libName.indexOf("map") !== -1 || 
              libName.indexOf("ae") !== -1) {
            console.log("[关键库] 可能处理ANS文件的库被加载: " + libName);
            
            dataProcessFlow.push({
              stage: "库加载",
              library: libName,
              time: new Date().getTime()
            });
            
            try {
              var exception = Java.use("java.lang.Exception").$new();
              var stackElements = exception.getStackTrace();
              console.log("[Java栈] 库加载调用者:");
              for (var i = 0; i < 5 && i < stackElements.length; i++) {
                var element = stackElements[i];
                console.log("  " + element.getClassName() + "." + element.getMethodName());
              }
            } catch(e) {}
          }
          
          return this.loadLibrary(libName);
        };
      } catch(e) {
        console.log("[错误] 库加载监控失败: " + e);
      }
      
      console.log("[+] Java层分析设置完成");
    });
  }, 3000);
  
  // 数据流程摘要
  setTimeout(function() {
    console.log("\n[数据流程摘要] ANS文件解析过程:");
    
    // 按时间排序
    dataProcessFlow.sort(function(a, b) {
      return a.time - b.time;
    });
    
    // 打印流程
    for (var i = 0; i < dataProcessFlow.length; i++) {
      var entry = dataProcessFlow[i];
      var date = new Date(entry.time);
      var timeStr = date.getHours() + ":" + date.getMinutes() + ":" + date.getSeconds();
      console.log((i+1) + ". [" + timeStr + "] " + entry.stage + (entry.method ? " - " + entry.method : ""));
      
      // 打印详细信息
      for (var key in entry) {
        if (key !== "time" && key !== "stage" && key !== "method") {
          var value = entry[key];
          if (typeof value === "string" && value.length > 100) {
            value = value.substring(0, 100) + "...";
          }
          console.log("   " + key + ": " + value);
        }
      }
    }
    
    console.log("\n[总结] ANS文件解析与展示流程:");
    
    // 计算ANS文件数量
    var fileCount = 0;
    for (var fd in ansFiles) {
      fileCount++;
    }
    
    console.log("1. 发现的ANS文件: " + fileCount + "个");
    
    for (var fd in ansFiles) {
      console.log("   - " + ansFiles[fd].fileName + " (" + ansFiles[fd].path + ")");
    }
    
    // 提取数据流转阶段 (兼容方式)
    var stages = [];
    var stageMap = {};
    for (var i = 0; i < dataProcessFlow.length; i++) {
      var stage = dataProcessFlow[i].stage;
      if (!stageMap[stage]) {
        stageMap[stage] = true;
        stages.push(stage);
      }
    }
    
    console.log("2. 数据流转阶段: " + stages.join(" → "));
    
    console.log("[分析完成] ANS文件解析过程追踪");
  }, 30000);
  
  console.log("[高德地图ANS文件解析分析] 脚本设置完成");
})();
