// 高德地图GPS模拟器积分破解脚本 v5
// 功能：修复崩溃问题，专注于积分修改和绕过检查
Java.perform(function() {
    console.log("\n[+] 积分破解脚本已启动...");
    console.log("[+] 崩溃修复版：简化代码，避免递归调用");

    // 定位用户模型类
    try {
        var GpsUserModel = Java.use("tgo.ngo.mockgps.model.app.GpsUserModel");
        console.log("[+] 已找到用户模型类: GpsUserModel");
        
        // Integer类型
        var Integer = Java.use('java.lang.Integer');
        // 创建常量积分值
        var FAKE_POINTS = Integer.valueOf(10);
        
        // 修改积分值 - 修复类型问题
        GpsUserModel.getCount.implementation = function() {
            // 这里不调用原始方法，直接返回假值，避免递归
            console.log("[*] 拦截到积分查询getCount");
            console.log("[!] 直接返回积分值: 99999");
            return FAKE_POINTS;
        };
        
        // 修改积分不足提示
        GpsUserModel.getNotCanUseMag.implementation = function() {
            console.log("[*] 拦截到积分不足提示");
            console.log("[!] 已清空积分不足提示");
            return "";
        };
        
        // 修改可用状态 - 修复类型问题
        if (GpsUserModel.getCanUse) {
            GpsUserModel.getCanUse.implementation = function() {
                console.log("[*] 拦截到积分可用状态检查");
                console.log("[!] 强制返回可用状态: 1");
                return Integer.valueOf(1);
            };
        }

    } catch(e) {
        console.log("[!] 钩住积分类时出错: " + e);
    }
    
    // 修改MainActivity中的启动方法，但不钩住initData方法（避免崩溃）
    try {
        var MainActivity = Java.use("tgo.ngo.mockgps.ui.MainActivity");
        
        // 保存原始方法引用
        var originalStartMock = MainActivity.startMock.implementation;
        
        // 重新实现startMock，避免递归调用
        MainActivity.startMock.implementation = function() {
            console.log("[*] 拦截到启动模拟位置方法");
            console.log("[+] 正在绕过启动前的积分检查...");
            
            // 这里使用Java.perform创建一个新的执行上下文，避免递归
            Java.perform(function() {
                try {
                    // 尝试直接调用非公开方法，绕过积分检查
                    var instance = Java.retain(this); // 保留this引用
                    
                    // 查看是否有startLocation方法（可能是真正执行定位的方法）
                    if (MainActivity.startLocation) {
                        console.log("[*] 尝试直接调用startLocation方法");
                        MainActivity.startLocation.call(instance);
                    } else {
                        console.log("[*] 没找到startLocation方法，尝试其他方式...");
                        // 可以尝试调用其他可能的方法
                    }
                } catch(e) {
                    console.log("[!] 调用方法失败: " + e);
                }
            });
            
            // 返回undefined，避免调用原始方法
            return;
        };
        
    } catch(e) {
        console.log("[!] 钩住MainActivity失败: " + e);
    }
    
    // 简化对话框处理，只监控不修改
    try {
        var AlertDialog = Java.use("androidx.appcompat.app.AlertDialog");
        if (AlertDialog.Builder) {
            AlertDialog.Builder.setMessage.implementation = function(message) {
                if (message && message.toString().includes("积分")) {
                    console.log("[*] 检测到积分相关对话框: " + message);
                }
                return this.setMessage(message);
            };
        }
    } catch(e) {
        console.log("[!] 监控对话框失败: " + e);
    }
    
    console.log("[*] 积分破解脚本加载完成");
    console.log("[*] 请谨慎点击启动按钮，注意应用状态");
});
