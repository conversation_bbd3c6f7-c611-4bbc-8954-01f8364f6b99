// 简化测试脚本 - ES5兼容版本 (Frida 12.9.7)
// 专门用于验证基本Hook功能

console.log("[简化测试] 开始基本功能验证...");

// 统计计数器
var hookCount = 0;

// 1. 验证库加载
var libamapnsq = null;
try {
    libamapnsq = Process.getModuleByName("libamapnsq.so");
    console.log("[✓] libamapnsq.so 已加载，基址: " + libamapnsq.base);
} catch (e) {
    console.log("[✗] libamapnsq.so 未找到: " + e.message);
    return;
}

// 2. 测试基本Hook - girf_sqlite3_bind_blob
if (libamapnsq) {
    var bindBlobAddr = libamapnsq.base.add(0x15000);
    console.log("[测试] Hook地址: " + bindBlobAddr);
    
    try {
        Interceptor.attach(bindBlobAddr, {
            onEnter: function(args) {
                hookCount++;
                console.log("[Hook成功] girf_sqlite3_bind_blob 调用 #" + hookCount);
                console.log("  参数1: " + args[0]);
                console.log("  参数2: " + args[1]);
                console.log("  参数3: " + args[2]);
                console.log("  参数4: " + args[3]);
                
                // 简单的数据检查
                if (args[2] && args[3].toInt32() > 0) {
                    var size = Math.min(args[3].toInt32(), 16);
                    try {
                        var data = args[2].readByteArray(size);
                        if (data) {
                            var view = new Uint8Array(data);
                            var hexStr = "";
                            for (var i = 0; i < view.length; i++) {
                                var hex = view[i].toString(16);
                                if (hex.length === 1) hex = "0" + hex;
                                hexStr += hex + " ";
                            }
                            console.log("  数据预览: " + hexStr);
                            
                            // 检查DICE-AM标识
                            if (view.length >= 4 && 
                                view[0] === 0x44 && view[1] === 0x49 && 
                                view[2] === 0x43 && view[3] === 0x45) {
                                console.log("  [发现] DICE-AM 矢量数据!");
                            }
                        }
                    } catch (e) {
                        console.log("  [警告] 数据读取失败: " + e.message);
                    }
                }
            },
            onLeave: function(retval) {
                console.log("[Hook] 返回值: " + retval);
            }
        });
        console.log("[✓] Hook设置成功");
    } catch (e) {
        console.log("[✗] Hook设置失败: " + e.message);
    }
}

// 3. 测试zlib Hook
var libz = null;
try {
    libz = Process.getModuleByName("libz.so");
    console.log("[✓] libz.so 已加载");
    
    var uncompressPtr = libz.getExportByName("uncompress");
    if (uncompressPtr) {
        Interceptor.attach(uncompressPtr, {
            onEnter: function(args) {
                console.log("[Hook] zlib uncompress 被调用");
                console.log("  源大小: " + args[3].readU32());
                console.log("  目标大小: " + args[1].readU32());
            },
            onLeave: function(retval) {
                if (retval.toInt32() === 0) {
                    console.log("[Hook] zlib 解压成功");
                } else {
                    console.log("[Hook] zlib 解压失败: " + retval.toInt32());
                }
            }
        });
        console.log("[✓] zlib Hook设置成功");
    }
} catch (e) {
    console.log("[警告] libz.so 处理失败: " + e.message);
}

// 4. 定期状态报告
setInterval(function() {
    console.log("[状态] Hook调用次数: " + hookCount);
}, 5000);

console.log("[简化测试] 脚本启动完成，等待函数调用...");
console.log("[提示] 请操作地图以触发数据处理");
