// 高德地图ANS文件加载链路追踪脚本 - 增强版
(function() {
    'use strict';
    
    // 设置全局异常处理
    Process.setExceptionHandler(function(exception) {
        console.log("[!] 异常: " + exception.message);
        return true;
    });
    
    // 立即拦截所有可能的终止函数
    Interceptor.replace(Module.findExportByName(null, 'exit'), new NativeCallback(function() {
        console.log("[!] 拦截到exit()调用，忽略");
        return 0;
    }, 'void', ['int']));
    
    Interceptor.replace(Module.findExportByName(null, 'abort'), new NativeCallback(function() {
        console.log("[!] 拦截到abort()调用，忽略");
        return 0;
    }, 'void', []));
    
    // 拦截kill系统调用
    Interceptor.attach(Module.findExportByName(null, 'kill'), {
        onEnter: function(args) {
            var pid = args[0].toInt32();
            var signal = args[1].toInt32();
            console.log("[!] 拦截到kill()调用, pid: " + pid + ", signal: " + signal);
            if (pid == Process.id) {
                args[1] = ptr(0); // 将信号改为0，仅检查进程是否存在
            }
        }
    });
    
    // 拦截tgkill系统调用
    var tgkill = Module.findExportByName(null, 'tgkill');
    if (tgkill) {
        Interceptor.attach(tgkill, {
            onEnter: function(args) {
                console.log("[!] 拦截到tgkill()调用");
                args[2] = ptr(0); // 将信号改为0
            }
        });
    }
    
    // 拦截pthread_kill
    var pthread_kill = Module.findExportByName(null, 'pthread_kill');
    if (pthread_kill) {
        Interceptor.attach(pthread_kill, {
            onEnter: function(args) {
                console.log("[!] 拦截到pthread_kill()调用");
                args[1] = ptr(0); // 将信号改为0
            }
        });
    }
    
    // 添加对敏感文件访问的拦截
    var fopen = Module.findExportByName(null, 'fopen');
    if (fopen) {
        Interceptor.attach(fopen, {
            onEnter: function(args) {
                var path = args[0].readUtf8String();
                // 检测是否在读取/proc/self/maps或/proc/self/status等敏感文件
                if (path && (path.indexOf('/proc/self/maps') >= 0 || 
                            path.indexOf('/proc/self/status') >= 0)) {
                    console.log("[!] 拦截到对敏感文件的访问: " + path);
                    // 可以选择返回一个假的文件描述符
                }
            }
        });
    }
    
    // 减少等待时间
    setTimeout(function() {
        console.log("[+] 开始跟踪ANS文件加载链路...");
        
        if (Java.available) {
            Java.perform(function() {
                try {
                    // 只保留最关键的钩子
                    hookFileOperations();
                    console.log("[+] 文件操作钩子设置完成");
                    
                    // 延迟设置其他钩子
                    setTimeout(function() {
                        try {
                            hookAMapController();
                            hookMapCloudBundleLoader();
                            console.log("[+] 地图控制器钩子设置完成");
                        } catch (e) {
                            console.log("[!] 设置地图控制器钩子时出错: " + e);
                        }
                    }, 5000);
                    
                    // 更长延迟设置不太关键的钩子
                    setTimeout(function() {
                        try {
                            hookMapEventService();
                            hookMapManager();
                            console.log("[+] 地图事件钩子设置完成");
                        } catch (e) {
                            console.log("[!] 设置地图事件钩子时出错: " + e);
                        }
                    }, 10000);
                } catch (e) {
                    console.log("[!] 设置钩子时出错: " + e);
                }
            });
        } else {
            console.log("[!] Java接口不可用");
        }
    }, 1000);
    
    // 添加周期性状态检查
    setInterval(function() {
        console.log("[*] 脚本仍在运行中...");
    }, 5000);
    
    // Hook IMapEventService相关方法
    function hookMapEventService() {
        var IMapEventService = Java.use("com.autonavi.bundle.mapevent.IMapEventService");
        var MapEventServiceImpl = Java.use("com.amap.bundle.mapevent.impl.MapEventServiceImpl");
        
        // Hook设置触摸事件的方法
        if (MapEventServiceImpl.setTouchEvent) {
            MapEventServiceImpl.setTouchEvent.implementation = function(motionEvent) {
                console.log("[MapEventService] setTouchEvent 被调用");
                var result = this.setTouchEvent(motionEvent);
                
                if (motionEvent) {
                    var action = motionEvent.getAction();
                    console.log("[MapEventService] 触摸事件类型: " + action);
                }
                
                return result;
            };
        }
        
        // Hook手势事件处理方法
        if (MapEventServiceImpl.onEngineActionGesture) {
            MapEventServiceImpl.onEngineActionGesture.overload('int', 'com.autonavi.ae.gmap.gesture.GLGestureCallbackParam').implementation = function(i, param) {
                console.log("[MapEventService] onEngineActionGesture 被调用, 参数: " + i);
                if (param) {
                    console.log("[MapEventService] 手势类型: " + param.toString());
                }
                return this.onEngineActionGesture(i, param);
            };
        }
    }
    
    // Hook t45类 (IMapManager实现)
    function hookMapManager() {
        var t45Class = Java.use("defpackage.t45");
        
        // 移动开始
        if (t45Class.onMontionStart) {
            t45Class.onMontionStart.implementation = function(i, f1, f2) {
                console.log("[t45] onMontionStart 被调用, 坐标: " + f1 + ", " + f2);
                return this.onMontionStart(i, f1, f2);
            };
        }
        
        // 移动结束
        if (t45Class.onMontionFinish) {
            t45Class.onMontionFinish.implementation = function(i) {
                console.log("[t45] onMontionFinish 被调用");
                var result = this.onMontionFinish(i);
                console.log("[t45] onMontionFinish 返回值: " + result);
                return result;
            };
        }
        
        // 地图级别变化
        if (t45Class.onMapLevelChange) {
            t45Class.onMapLevelChange.implementation = function(i, z) {
                console.log("[t45] onMapLevelChange 被调用, 缩放: " + z);
                var result = this.onMapLevelChange(i, z);
                console.log("[t45] onMapLevelChange 返回值: " + result);
                return result;
            };
        }
        
        // 地图渲染完成
        if (t45Class.onMapRenderCompleted) {
            t45Class.onMapRenderCompleted.implementation = function(i) {
                console.log("[t45] onMapRenderCompleted 被调用");
                this.onMapRenderCompleted(i);
            };
        }
        
        // 异步任务类
        try {
            var dClass = Java.use("defpackage.t45$d");
            if (dClass.doBackground) {
                dClass.doBackground.implementation = function() {
                    console.log("[t45$d] 异步数据加载任务启动");
                    var result = this.doBackground();
                    console.log("[t45$d] 异步数据加载任务完成");
                    return result;
                };
            }
        } catch (e) {
            console.log("无法hook t45$d类: " + e);
        }
    }
    
    // Hook AMapController
    function hookAMapController() {
        var AMapController = Java.use("com.autonavi.ae.gmap.AMapController");
        
        // Hook设置数据加载器的方法
        if (AMapController.setCloudBundleLoader) {
            AMapController.setCloudBundleLoader.implementation = function(loader) {
                console.log("[AMapController] setCloudBundleLoader 被调用");
                console.log("[AMapController] 加载器类型: " + loader.$className);
                return this.setCloudBundleLoader(loader);
            };
        }
        
        // Hook地图数据请求方法
        if (AMapController.requestData) {
            AMapController.requestData.overload('java.lang.String', 'java.lang.String', 'java.lang.String').implementation = function(str1, str2, str3) {
                console.log("[AMapController] requestData 被调用");
                console.log("[AMapController] 参数1: " + str1);
                console.log("[AMapController] 参数2: " + str2);
                console.log("[AMapController] 参数3: " + str3);
                var result = this.requestData(str1, str2, str3);
                console.log("[AMapController] requestData 返回值: " + result);
                return result;
            };
        }
    }
    
    // Hook MapCloudBundleLoaderUtil
    function hookMapCloudBundleLoader() {
        try {
            var MapCloudBundleLoaderUtil = Java.use("com.autonavi.ae.gmap.utils.MapCloudBundleLoaderUtil");
            
            // Hook所有可能的方法
            var methods = MapCloudBundleLoaderUtil.class.getDeclaredMethods();
            for (var i = 0; i < methods.length; i++) {
                var method = methods[i];
                var methodName = method.getName();
                
                // 跳过一些基本方法
                if (methodName === 'wait' || methodName === 'equals' || 
                    methodName === 'toString' || methodName === 'hashCode' || 
                    methodName === 'getClass' || methodName === 'notify' || 
                    methodName === 'notifyAll') {
                    continue;
                }
                
                try {
                    var originalMethod = MapCloudBundleLoaderUtil[methodName];
                    if (originalMethod) {
                        hookMethod(MapCloudBundleLoaderUtil, methodName, "MapCloudBundleLoaderUtil");
                    }
                } catch (e) {
                    console.log("无法hook方法 " + methodName + ": " + e);
                }
            }
        } catch (e) {
            console.log("无法找到MapCloudBundleLoaderUtil类: " + e);
        }
    }
    
    // 通用方法钩子
    function hookMethod(clazz, methodName, className) {
        try {
            var overloads = clazz[methodName].overloads;
            for (var i = 0; i < overloads.length; i++) {
                overloads[i].implementation = function() {
                    var args = [];
                    for (var j = 0; j < arguments.length; j++) {
                        if (arguments[j] && arguments[j].toString) {
                            args.push(arguments[j].toString());
                        } else {
                            args.push(arguments[j]);
                        }
                    }
                    
                    console.log("[" + className + "] " + methodName + " 被调用, 参数: " + JSON.stringify(args));
                    
                    var result = this[methodName].apply(this, arguments);
                    
                    if (result && result.toString) {
                        console.log("[" + className + "] " + methodName + " 返回值: " + result.toString());
                    } else {
                        console.log("[" + className + "] " + methodName + " 返回值: " + result);
                    }
                    
                    return result;
                };
            }
        } catch (e) {
            console.log("无法hook方法 " + className + "." + methodName + ": " + e);
        }
    }
    
    // Hook文件操作
    function hookFileOperations() {
        // Hook Java层文件操作
        var FileInputStream = Java.use("java.io.FileInputStream");
        
        FileInputStream.$init.overload('java.lang.String').implementation = function(path) {
            if (path && path.endsWith(".ans")) {
                console.log("[FileInputStream] 打开ANS文件: " + path);
            }
            return this.$init(path);
        };
        
        FileInputStream.$init.overload('java.io.File').implementation = function(file) {
            var path = file.getAbsolutePath();
            if (path && path.endsWith(".ans")) {
                console.log("[FileInputStream] 打开ANS文件: " + path);
            }
            return this.$init(file);
        };
        
        // Hook Native层文件操作
        Interceptor.attach(Module.findExportByName(null, 'open'), {
            onEnter: function(args) {
                var path = args[0].readUtf8String();
                if (path && path.endsWith(".ans")) {
                    console.log("[Native] open() 打开ANS文件: " + path);
                    this.isAnsFile = true;
                    this.path = path;
                }
            },
            onLeave: function(retval) {
                if (this.isAnsFile) {
                    console.log("[Native] open() 返回文件描述符: " + retval.toInt32() + " 路径: " + this.path);
                }
            }
        });
        
        // 跟踪读取操作
        Interceptor.attach(Module.findExportByName(null, 'read'), {
            onEnter: function(args) {
                this.fd = args[0].toInt32();
                this.buffer = args[1];
                this.size = args[2].toInt32();
            },
            onLeave: function(retval) {
                var bytesRead = retval.toInt32();
                if (bytesRead > 0 && bytesRead <= 50) {  // 只显示小块读取，可能是文件头
                    try {
                        var data = this.buffer.readByteArray(bytesRead);
                        // 检查是否可能是ANS文件头
                        if (data[0] === 0x41 && data[1] === 0x4E && data[2] === 0x53) {  // "ANS"
                            console.log("[Native] read() 从fd " + this.fd + " 读取可能的ANS文件头: " + 
                                hexdump(data, {length: Math.min(bytesRead, 16)}));
                        }
                    } catch (e) {
                        // 忽略读取错误
                    }
                }
            }
        });
    }
    
    // Hook JNI桥接
    function hookJNIBridge() {
        try {
            // 尝试查找和钩住可能的JNI注册方法
            var libamapmapJNI = Process.findModuleByName("libamapmap.so");
            if (libamapmapJNI) {
                console.log("[+] 找到libamapmap.so，基址: " + libamapmapJNI.base);
                
                // 搜索JNI_OnLoad函数
                var jniOnLoad = Module.findExportByName("libamapmap.so", "JNI_OnLoad");
                if (jniOnLoad) {
                    console.log("[+] 找到JNI_OnLoad，地址: " + jniOnLoad);
                    Interceptor.attach(jniOnLoad, {
                        onEnter: function() {
                            console.log("[JNI] JNI_OnLoad 被调用");
                        },
                        onLeave: function(retval) {
                            console.log("[JNI] JNI_OnLoad 返回: " + retval);
                        }
                    });
                }
                
                // 钩住RegisterNatives函数来捕获JNI方法注册
                var RegisterNatives = Module.findExportByName(null, "RegisterNatives");
                if (RegisterNatives) {
                    Interceptor.attach(RegisterNatives, {
                        onEnter: function(args) {
                            var env = args[0];
                            var clazz = args[1];
                            var methods = args[2];
                            var size = args[3].toInt32();
                            
                            var className = Java.vm.tryGetEnv().getClassName(clazz);
                            console.log("[JNI] RegisterNatives 被调用: 类 = " + className + ", 方法数 = " + size);
                            
                            // 尝试解析注册的方法
                            for (var i = 0; i < size; i++) {
                                var methodsPtr = methods.add(i * Process.pointerSize * 3);
                                var namePtr = methodsPtr.readPointer();
                                var sigPtr = methodsPtr.add(Process.pointerSize).readPointer();
                                var fnPtrPtr = methodsPtr.add(Process.pointerSize * 2);
                                var fnPtr = fnPtrPtr.readPointer();
                                
                                var name = namePtr.readUtf8String();
                                var sig = sigPtr.readUtf8String();
                                
                                console.log("[JNI] 注册方法: " + name + ", 签名: " + sig + ", 函数地址: " + fnPtr);
                                
                                // 如果方法名包含与ANS相关的关键字，钩住它
                                if (name.toLowerCase().indexOf("ans") !== -1 || 
                                    name.toLowerCase().indexOf("bundle") !== -1 || 
                                    name.toLowerCase().indexOf("load") !== -1 || 
                                    name.toLowerCase().indexOf("map") !== -1) {
                                    
                                    console.log("[JNI] 发现可能的ANS相关方法: " + name);
                                    
                                    // 替换函数指针为我们的拦截器
                                    Interceptor.attach(fnPtr, {
                                        onEnter: function(args) {
                                            console.log("[JNI-Method] " + name + " 被调用");
                                            // 尝试解析JNI参数 (根据签名)
                                            // 这里只是简单示例，实际解析需要更复杂的逻辑
                                            if (args[1] && Java.available) {
                                                try {
                                                    var obj = Java.cast(args[1], Java.use("java.lang.Object"));
                                                    console.log("[JNI-Method] 参数: " + obj.toString());
                                                } catch (e) {
                                                    // 忽略转换错误
                                                }
                                            }
                                        },
                                        onLeave: function(retval) {
                                            console.log("[JNI-Method] " + name + " 返回: " + retval);
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
            }
            
            // 尝试查找libamapnsq.so (可能负责ANS文件解析)
            var libamapnsq = Process.findModuleByName("libamapnsq.so");
            if (libamapnsq) {
                console.log("[+] 找到libamapnsq.so，基址: " + libamapnsq.base);
                
                // 搜索可能的ANS解析函数
                // 这需要逆向分析来确定确切的函数名或偏移量
                // 这里只是一个示例
                var symbols = libamapnsq.enumerateSymbols();
                for (var i = 0; i < symbols.length; i++) {
                    var sym = symbols[i];
                    if (sym.name.toLowerCase().indexOf("ans") !== -1 || 
                        sym.name.toLowerCase().indexOf("parse") !== -1 || 
                        sym.name.toLowerCase().indexOf("load") !== -1 || 
                        sym.name.toLowerCase().indexOf("read") !== -1) {
                        
                        console.log("[+] 可能的ANS处理函数: " + sym.name + " @ " + sym.address);
                        
                        Interceptor.attach(sym.address, {
                            onEnter: function(args) {
                                console.log("[libamapnsq] " + sym.name + " 被调用");
                            },
                            onLeave: function(retval) {
                                console.log("[libamapnsq] " + sym.name + " 返回: " + retval);
                            }
                        });
                    }
                }
            }
        } catch (e) {
            console.log("JNI钩子错误: " + e);
        }
    }
    
    // 辅助函数：将字节数组转换为十六进制字符串
    function hexdump(array, options) {
        options = options || {};
        var result = [];
        var bytes = new Uint8Array(array);
        var length = options.length || bytes.length;
        
        for (var i = 0; i < length; i++) {
            result.push(bytes[i].toString(16).padStart(2, '0'));
        }
        
        return result.join(' ');
    }
})();
