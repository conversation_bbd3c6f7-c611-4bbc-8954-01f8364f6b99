     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
[Data Extractor] 启动数据提取器...
[Data Extractor] 脚本加载完成，3秒后开始Hook设置
[Remote::com.autonavi.minimap]-> [Setup] 开始设置数据Hook...
[Library] 库基址: 0x7f5f956000
 File read() Hook设置成功
[Hook] SQLite实现Hook设置成功
[Ready] 所有Hook设置完成，请移动地图到新区域
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 32 字节

 分析 SQLite-Impl 数据: 32 字节
[提取错误] access violation accessing 0x8
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 49 字节

 分析 SQLite-Impl 数据: 49 字节
[提取错误] access violation accessing 0xf
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 32 字节

 分析 SQLite-Impl 数据: 32 字节
[提取错误] access violation accessing 0x8
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 49 字节

 分析 SQLite-Impl 数据: 49 字节
[提取错误] access violation accessing 0xf
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 32 字节

 分析 SQLite-Impl 数据: 32 字节
[提取错误] access violation accessing 0x8
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 19 字节

 分析 SQLite-Impl 数据: 19 字节
[提取错误] access violation accessing 0xffffffff
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 49 字节

 分析 SQLite-Impl 数据: 49 字节
[提取错误] access violation accessing 0xf
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 32 字节

 分析 SQLite-Impl 数据: 32 字节
[提取错误] access violation accessing 0x3
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 48 字节

 分析 SQLite-Impl 数据: 48 字节
[提取错误] access violation accessing 0x2
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 46 字节

 分析 SQLite-Impl 数据: 46 字节
[提取错误] access violation accessing 0x2f
[SQLite Impl] bind_blob实现调用
[File Read #1] 非系统文件，大小: 8192

 分析 FileRead 数据: 8192 字节
预览: ..:1s.H...:D......:U..N...:ff.....:|_.?...:...J...:.}.....:.......:._.....:.r.b...:.<.....:.a.....;.
========================
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 141 字节

 分析 SQLite-Impl 数据: 141 字节
[提取错误] access violation accessing 0x1
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 141 字节

 分析 SQLite-Impl 数据: 141 字节
[提取错误] access violation accessing 0x1
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 32 字节

 分析 SQLite-Impl 数据: 32 字节
[提取错误] access violation accessing 0x3
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 48 字节

 分析 SQLite-Impl 数据: 48 字节
[提取错误] access violation accessing 0x2
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 141 字节

 分析 SQLite-Impl 数据: 141 字节
[提取错误] access violation accessing 0x1
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 141 字节

 分析 SQLite-Impl 数据: 141 字节
[提取错误] access violation accessing 0x1
[File Read #2] 非系统文件，大小: 8192

 分析 FileRead 数据: 8192 字节
预览: ......D..x.D........................................................................................
========================
[File Read #3] 非系统文件，大小: 8192

 分析 FileRead 数据: 8192 字节
预览: ....4.y............j.Q.8...............p.W.>.%.............v.].D.+.............|.c.J.1..............
 发现中文文本数据
 类型: 地名或标注信息
========================
[File Read #4] 非系统文件，大小: 8192

 分析 FileRead 数据: 8192 字节
预览: ....4..............j.Q.8...............p.W.>.%.............v.].D.+.............|.c.J.1..............
 发现中文文本数据
 类型: 地名或标注信息
========================
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 32 字节

 分析 SQLite-Impl 数据: 32 字节
[提取错误] access violation accessing 0x3
[File Read #5] 非系统文件，大小: 8192

 分析 FileRead 数据: 8192 字节
预览: .........`..........................................................................................
========================
[SQLite Impl] bind_blob实现调用
[SQLite Impl] bind_blob实现调用
[SQLite Data] 发现数据: 48 字节

 分析 SQLite-Impl 数据: 48 字节
[提取错误] access violation accessing 0x2
[File Read #6] 非系统文件，大小: 8192

 分析 FileRead 数据: 8192 字节
预览: ....5.~............j.Q.8...............p.W.>.%.............v.].D.+.............|.c.J.1..............
 发现中文文本数据
 类型: 地名或标注信息
========================

 === 人类可读数据报告 ===
 文件读取: 6 次
 发现数据: 3/3
 人类可读数据提取完成
============================


[Remote::com.autonavi.minimap]-> exit

Thank you for using Frida!
