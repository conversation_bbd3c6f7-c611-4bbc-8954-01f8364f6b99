// AM-zlib解压算法分析脚本
// 目标: 逆向高德地图的AM-zlib解压算法

console.log("[AM-zlib分析] 开始分析AM-zlib解压算法...");
console.log("[目标] 找到并Hook AM-zlib解压函数");

// AM-zlib分析状态
var amZlibAnalysis = {
    decompressCalls: [],      // 解压调用记录
    amZlibFiles: {},          // AM-zlib文件映射
    decompressPatterns: [],   // 解压模式
    algorithmCalls: [],       // 算法调用
    startTime: Date.now()
};

// 安全的字节数组处理
function safeByteArrayToHex(byteArray, maxLen) {
    var hexBytes = [];
    var len = Math.min(maxLen || 32, byteArray.length);
    for (var i = 0; i < len; i++) {
        hexBytes.push(('0' + byteArray[i].toString(16)).slice(-2));
    }
    return hexBytes.join(' ');
}

// 检查是否是AM-zlib数据
function isAMZlibData(data) {
    if (data.length >= 8) {
        // AM-zlib魔数: 41 4d 2d 7a 6c 69 62 00
        return data[0] === 0x41 && data[1] === 0x4d && data[2] === 0x2d && 
               data[3] === 0x7a && data[4] === 0x6c && data[5] === 0x69 && 
               data[6] === 0x62 && data[7] === 0x00;
    }
    return false;
}

// 分析AM-zlib头部
function analyzeAMZlibHeader(data) {
    if (data.length < 16) return null;
    
    return {
        magic: safeByteArrayToHex(data.slice(0, 8)),
        version: data[8],
        flags: data[9],
        geometryType: data[10],
        geometryFlags: data[11],
        reserved: safeByteArrayToHex(data.slice(12, 16))
    };
}

// 1. Hook文件读取 - 专门跟踪AM-zlib文件
console.log("[1] Hook文件读取，跟踪AM-zlib文件...");

try {
    var libc = Process.getModuleByName("libc.so");
    var readPtr = libc.getExportByName("read");
    
    Interceptor.attach(readPtr, {
        onEnter: function(args) {
            this.fd = args[0].toInt32();
            this.buf = args[1];
            this.count = args[2].toInt32();
        },
        onLeave: function(retval) {
            var bytesRead = retval.toInt32();
            if (bytesRead > 16) {
                try {
                    var data = this.buf.readByteArray(Math.min(64, bytesRead));
                    var header = new Uint8Array(data);
                    
                    // 检查是否是AM-zlib数据
                    if (isAMZlibData(header)) {
                        var headerInfo = analyzeAMZlibHeader(header);
                        
                        console.log("[AM-zlib文件读取] fd=" + this.fd + ", 大小=" + bytesRead);
                        console.log("  头部: " + safeByteArrayToHex(header, 16));
                        console.log("  版本: " + headerInfo.version);
                        console.log("  几何类型: 0x" + headerInfo.geometryType.toString(16));
                        console.log("  几何标志: 0x" + headerInfo.geometryFlags.toString(16));
                        
                        amZlibAnalysis.amZlibFiles[this.fd] = {
                            fd: this.fd,
                            size: bytesRead,
                            header: headerInfo,
                            timestamp: Date.now()
                        };
                    }
                } catch (e) {
                    // 忽略读取错误
                }
            }
        }
    });
    
    console.log("[✓] 文件读取Hook设置成功");
} catch (e) {
    console.log("[✗] 文件读取Hook失败: " + e.message);
}

// 2. Hook标准zlib函数
console.log("[2] Hook标准zlib解压函数...");

try {
    var libz = Process.getModuleByName("libz.so");
    
    // Hook inflate函数
    var inflatePtr = libz.getExportByName("inflate");
    Interceptor.attach(inflatePtr, {
        onEnter: function(args) {
            this.strm = args[0];
            this.flush = args[1].toInt32();
            
            // 读取zlib流结构
            try {
                var nextIn = this.strm.readPointer();
                var availIn = this.strm.add(8).readU32();
                
                if (nextIn && availIn > 0 && availIn < 1000000) {
                    var inputData = nextIn.readByteArray(Math.min(32, availIn));
                    var inputHeader = new Uint8Array(inputData);
                    
                    console.log("[zlib inflate] 输入数据:");
                    console.log("  大小: " + availIn + " 字节");
                    console.log("  头部: " + safeByteArrayToHex(inputHeader, 16));
                    
                    this.inputSize = availIn;
                    this.inputHeader = safeByteArrayToHex(inputHeader, 16);
                }
            } catch (e) {
                // 忽略读取错误
            }
        },
        onLeave: function(retval) {
            var result = retval.toInt32();
            console.log("[zlib inflate] 返回值: " + result);
            
            if (result === 0 || result === 1) { // Z_OK or Z_STREAM_END
                try {
                    var nextOut = this.strm.add(12).readPointer();
                    var availOut = this.strm.add(20).readU32();
                    var totalOut = this.strm.add(24).readU32();
                    
                    console.log("  解压成功: " + this.inputSize + " → " + totalOut + " 字节");
                    
                    if (nextOut && totalOut > 0) {
                        var outputData = nextOut.readByteArray(Math.min(32, totalOut));
                        var outputHeader = new Uint8Array(outputData);
                        console.log("  输出头部: " + safeByteArrayToHex(outputHeader, 16));
                        
                        amZlibAnalysis.decompressCalls.push({
                            inputSize: this.inputSize,
                            outputSize: totalOut,
                            inputHeader: this.inputHeader,
                            outputHeader: safeByteArrayToHex(outputHeader, 16),
                            result: result,
                            timestamp: Date.now()
                        });
                    }
                } catch (e) {
                    console.log("  [错误] 读取输出数据失败: " + e.message);
                }
            }
        }
    });
    
    // Hook uncompress函数
    var uncompressPtr = libz.getExportByName("uncompress");
    Interceptor.attach(uncompressPtr, {
        onEnter: function(args) {
            this.dest = args[0];
            this.destLen = args[1];
            this.source = args[2];
            this.sourceLen = args[3].toInt32();
            
            if (this.sourceLen > 0 && this.sourceLen < 10000000) {
                try {
                    var sourceData = this.source.readByteArray(Math.min(32, this.sourceLen));
                    var sourceHeader = new Uint8Array(sourceData);
                    
                    console.log("[zlib uncompress] 压缩数据:");
                    console.log("  大小: " + this.sourceLen + " 字节");
                    console.log("  头部: " + safeByteArrayToHex(sourceHeader, 16));
                    
                    this.sourceHeader = safeByteArrayToHex(sourceHeader, 16);
                } catch (e) {
                    // 忽略读取错误
                }
            }
        },
        onLeave: function(retval) {
            var result = retval.toInt32();
            console.log("[zlib uncompress] 返回值: " + result);
            
            if (result === 0) { // Z_OK
                try {
                    var destLen = this.destLen.readU32();
                    var destData = this.dest.readByteArray(Math.min(32, destLen));
                    var destHeader = new Uint8Array(destData);
                    
                    console.log("  解压成功: " + this.sourceLen + " → " + destLen + " 字节");
                    console.log("  输出头部: " + safeByteArrayToHex(destHeader, 16));
                    
                    amZlibAnalysis.decompressCalls.push({
                        inputSize: this.sourceLen,
                        outputSize: destLen,
                        inputHeader: this.sourceHeader,
                        outputHeader: safeByteArrayToHex(destHeader, 16),
                        result: result,
                        method: "uncompress",
                        timestamp: Date.now()
                    });
                } catch (e) {
                    console.log("  [错误] 读取输出数据失败: " + e.message);
                }
            }
        }
    });
    
    console.log("[✓] zlib函数Hook设置成功");
} catch (e) {
    console.log("[✗] zlib函数Hook失败: " + e.message);
}

// 3. Hook内存分配 - 寻找大块内存分配（可能用于解压）
console.log("[3] Hook内存分配，寻找解压缓冲区...");

try {
    var mallocPtr = libc.getExportByName("malloc");
    Interceptor.attach(mallocPtr, {
        onEnter: function(args) {
            this.size = args[0].toInt32();
        },
        onLeave: function(retval) {
            var ptr = retval;
            var size = this.size;
            
            // 只关注大块内存分配（可能用于解压）
            if (size > 100000 && size < 200000000) {
                console.log("[大内存分配] 大小: " + size + " 字节, 地址: " + ptr);
                
                // 记录大内存分配
                amZlibAnalysis.algorithmCalls.push({
                    type: "malloc",
                    size: size,
                    address: ptr.toString(),
                    timestamp: Date.now()
                });
            }
        }
    });
    
    console.log("[✓] 内存分配Hook设置成功");
} catch (e) {
    console.log("[✗] 内存分配Hook失败: " + e.message);
}

// 4. Hook内存拷贝函数 - 寻找AM-zlib数据处理
console.log("[4] Hook内存操作，寻找AM-zlib数据处理...");

try {
    var memcpyPtr = libc.getExportByName("memcpy");
    Interceptor.attach(memcpyPtr, {
        onEnter: function(args) {
            this.dest = args[0];
            this.src = args[1];
            this.n = args[2].toInt32();
        },
        onLeave: function(retval) {
            var size = this.n;

            // 只关注较大的内存拷贝
            if (size > 1000 && size < 10000000) {
                try {
                    var srcData = this.src.readByteArray(Math.min(32, size));
                    var srcHeader = new Uint8Array(srcData);

                    // 检查是否包含AM-zlib特征
                    if (isAMZlibData(srcHeader)) {
                        console.log("[AM-zlib内存拷贝] 大小: " + size + " 字节");
                        console.log("  源地址: " + this.src);
                        console.log("  目标地址: " + this.dest);
                        console.log("  头部: " + safeByteArrayToHex(srcHeader, 16));

                        amZlibAnalysis.algorithmCalls.push({
                            type: "memcpy_am_zlib",
                            size: size,
                            src: this.src.toString(),
                            dest: this.dest.toString(),
                            timestamp: Date.now()
                        });
                    }

                    // 检查是否包含压缩数据特征模式
                    if (srcHeader[0] === 0x00 && srcHeader[1] === 0x01 &&
                        srcHeader[10] === 0xfe && srcHeader[11] === 0xfe) {
                        console.log("[压缩数据内存拷贝] 大小: " + size + " 字节");
                        console.log("  特征模式: " + safeByteArrayToHex(srcHeader, 16));
                        console.log("  源地址: " + this.src);
                        console.log("  目标地址: " + this.dest);
                    }
                } catch (e) {
                    // 忽略读取错误
                }
            }
        }
    });

    console.log("[✓] 内存拷贝Hook设置成功");
} catch (e) {
    console.log("[✗] 内存拷贝Hook失败: " + e.message);
}

// 5. Hook mmap - 寻找文件映射
try {
    var mmapPtr = libc.getExportByName("mmap");
    Interceptor.attach(mmapPtr, {
        onEnter: function(args) {
            this.addr = args[0];
            this.length = args[1].toInt32();
            this.prot = args[2].toInt32();
            this.flags = args[3].toInt32();
            this.fd = args[4].toInt32();
            this.offset = args[5].toInt32();
        },
        onLeave: function(retval) {
            var mappedAddr = retval;
            var length = this.length;
            var fd = this.fd;

            if (!mappedAddr.equals(ptr(-1)) && length > 1000000) {
                console.log("[大文件映射] fd=" + fd + ", 大小=" + length + " 字节, 地址=" + mappedAddr);

                // 检查映射的内容是否包含AM-zlib数据
                try {
                    var data = mappedAddr.readByteArray(Math.min(64, length));
                    var header = new Uint8Array(data);

                    if (isAMZlibData(header)) {
                        console.log("  [发现] 映射的AM-zlib文件!");
                        console.log("  头部: " + safeByteArrayToHex(header, 16));

                        amZlibAnalysis.amZlibFiles[fd] = {
                            fd: fd,
                            mappedAddr: mappedAddr.toString(),
                            size: length,
                            header: analyzeAMZlibHeader(header),
                            timestamp: Date.now()
                        };
                    }
                } catch (e) {
                    // 忽略读取错误
                }
            }
        }
    });

    console.log("[✓] mmap Hook设置成功");
} catch (e) {
    console.log("[✗] mmap Hook失败: " + e.message);
}

// 6. 搜索特征字节序列
function searchCompressionPatterns() {
    console.log("[搜索] 寻找压缩数据特征模式...");

    try {
        var modules = Process.enumerateModules();
        for (var i = 0; i < modules.length; i++) {
            var module = modules[i];
            if (module.name.indexOf("libgaode") !== -1 ||
                module.name.indexOf("libamap") !== -1 ||
                module.name.indexOf("base.odex") !== -1) {

                console.log("[搜索模块] " + module.name);

                try {
                    // 搜索压缩数据特征: 00 01 00 00 ... fe fe
                    Memory.scan(module.base, module.size, "00 01 00 00 ?? ?? 00 00 00 00 fe fe", {
                        onMatch: function(address, size) {
                            console.log("[发现压缩特征] 地址: " + address);
                            try {
                                var data = address.readByteArray(32);
                                var header = new Uint8Array(data);
                                console.log("  数据: " + safeByteArrayToHex(header, 32));
                            } catch (e) {
                                // 忽略读取错误
                            }
                        },
                        onError: function(reason) {
                            // 忽略搜索错误
                        },
                        onComplete: function() {
                            console.log("[完成] " + module.name + " 搜索完成");
                        }
                    });
                } catch (e) {
                    // 忽略模块搜索错误
                }
            }
        }
    } catch (e) {
        console.log("[错误] 搜索压缩模式失败: " + e.message);
    }
}

// 延迟搜索，等待应用完全加载
setTimeout(searchCompressionPatterns, 8000);

// 5. 定期输出分析报告
setInterval(function() {
    var runtime = Math.floor((Date.now() - amZlibAnalysis.startTime) / 1000);
    
    console.log("\n[AM-zlib分析报告] ==========================================");
    console.log("运行时间: " + runtime + "s");
    console.log("");
    
    console.log("AM-zlib文件统计:");
    var amZlibCount = Object.keys(amZlibAnalysis.amZlibFiles).length;
    console.log("  发现的AM-zlib文件: " + amZlibCount + " 个");
    
    for (var fd in amZlibAnalysis.amZlibFiles) {
        var file = amZlibAnalysis.amZlibFiles[fd];
        console.log("  fd=" + fd + ": 版本=" + file.header.version + 
                   ", 几何类型=0x" + file.header.geometryType.toString(16));
    }
    
    console.log("");
    console.log("解压调用统计:");
    console.log("  解压调用次数: " + amZlibAnalysis.decompressCalls.length);
    
    for (var i = 0; i < Math.min(5, amZlibAnalysis.decompressCalls.length); i++) {
        var call = amZlibAnalysis.decompressCalls[i];
        console.log("  调用" + (i+1) + ": " + call.inputSize + "→" + call.outputSize + 
                   " 字节 (" + call.method + ")");
        console.log("    输入: " + call.inputHeader);
        console.log("    输出: " + call.outputHeader);
    }
    
    console.log("");
    console.log("内存分配统计:");
    console.log("  大内存分配: " + amZlibAnalysis.algorithmCalls.length + " 次");
    
    console.log("===============================================\n");
}, 30000);

console.log("[AM-zlib分析] 分析脚本已启动...");
console.log("[目标] 逆向AM-zlib解压算法");
console.log("[提示] 请打开包含AM-zlib文件的地图功能");
