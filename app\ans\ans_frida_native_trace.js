// FINAL SCRIPT v6 - The Working Version
// This script neutralizes the true anti-Frida check and then traces the file operations.
//
// ==============================================================================
//                              FINAL INSTRUCTIONS
// ==============================================================================
// 1. Run THIS EXACT command (NO --no-pause):
//    frida -H 127.0.0.1:6667 -f com.autonavi.minimap -l ./ans/ans_frida_native_trace.js
//
// 2. At the Frida prompt, type %resume and press Enter.
//
// 3. WAIT for about 10 seconds for the file hooks to attach, then use the map.
// ==============================================================================

console.log("--- [ GaoDe Final Analysis Script v6 ] ---");
console.log("[*] Process is PAUSED. Setting hooks.");
console.log(">>> Type '%resume' and press Enter in this console to start the app. <<<");

var TAG = "AnsFinalTracer";
var fileHooksAttached = false;

// --- Primary Anti-Crash Hook ---
// We replace art::Runtime::Abort() and simply do nothing. This is the key.
try {
    var artAbortPtr = Module.findExportByName("libart.so", "_ZN3art7Runtime5AbortEv");
    if (artAbortPtr) {
        Interceptor.replace(artAbortPtr, new NativeCallback(function() {
            console.log("[!!!] art::Runtime::Abort() was called! Neutralizing call and continuing execution.");
            // By simply returning, we ignore the crash signal.
        }, 'void', []));
        console.log("[+] Main anti-crash hook on art::Runtime::Abort() is set.");
    } else {
        console.log("[-] Could not find art::Runtime::Abort().");
    }
} catch(e) {
    console.log("[-] CRITICAL: Failed to hook art::Runtime::Abort(): " + e.message);
}

// --- Java and File Tracing Hooks ---
Java.perform(function() {
    console.log("[*] Java.perform() is now running.");
    
    // Using a simple, robust timer to attach file hooks.
    var delayInMs = 10000;
    console.log("[+] File tracing will begin in " + (delayInMs / 1000) + " seconds.");

    setTimeout(function() {
        if (!fileHooksAttached) {
            fileHooksAttached = true;
            attachFileHooks();
        }
    }, delayInMs);
});

function attachFileHooks() {
    console.log(TAG + ": Attaching native file operation hooks...");
    var tracked_fds = {};
    var libc = Process.getModuleByName("libc.so");

    if (!libc) {
        console.log(TAG + ": ERROR - libc.so not found when attaching file hooks.");
        return;
    }

    Interceptor.attach(libc.getExportByName("open"), {
        onEnter: function (args) { try { var path = args[0].readCString(); if (path && path.indexOf(".ans") !== -1) this.is_ans = {path: path}; } catch(e) {} },
        onLeave: function (retval) { if (this.is_ans) { var fd = retval.toInt32(); if (fd !== -1) { console.log(TAG + ": open() -> '" + this.is_ans.path + "' with fd: " + fd); tracked_fds[fd] = this.is_ans.path; } } }
    });

    Interceptor.attach(libc.getExportByName("read"), {
        onEnter: function (args) {
            var fd = args[0].toInt32();
            if (tracked_fds[fd]) {
                console.log("\n" + TAG + ": read() on .ans file: " + tracked_fds[fd]);
                var nsqModule = Process.findModuleByName("libamapnsq.so");
                if (nsqModule) console.log(TAG + ": libamapnsq.so base address: " + nsqModule.base);
                console.log(TAG + ": Native Stack Trace:\n" + Thread.backtrace(this.context, Backtracer.ACCURATE).map(DebugSymbol.fromAddress).join('\n') + '\n');
            }
        }
    });
    console.log(TAG + ": Native file hooks attached successfully. Please interact with the map.");
}

console.log("[*] All startup hooks are now set. The app will resume when you type %resume.");