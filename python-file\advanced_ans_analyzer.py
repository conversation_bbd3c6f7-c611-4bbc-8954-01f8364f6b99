#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高德地图高级.ans文件分析器
专门处理大型地图数据文件 (m1.ans, m3.ans)
提取完整的人类可读数据
"""

import os
import zlib
import struct

def analyze_large_ans_file(filename):
    """分析大型.ans文件"""
    print(f"\n🎯 分析大型地图文件: {filename}")
    print("=" * 80)
    
    if not os.path.exists(filename):
        print(f"[错误] 文件不存在: {filename}")
        return
    
    file_size = os.path.getsize(filename)
    print(f"[信息] 文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)")
    
    with open(filename, 'rb') as f:
        # 读取文件头
        header = f.read(1024)
        
        # 分析文件结构
        analyze_file_header(header, filename)
        
        # 搜索人类可读数据
        search_readable_data(f, filename, file_size)

def analyze_file_header(header, filename):
    """分析文件头部"""
    print(f"\n📋 文件头部分析:")
    print("-" * 40)
    
    # 检查DICE-AM标识
    if header.startswith(b'DICE-AM'):
        print("✅ 发现DICE-AM标识 - 这是高德地图矢量数据文件")
        
        # 解析DICE-AM头部
        print("🔍 DICE-AM头部结构:")
        print(f"  标识: {header[0:7].decode('ascii', errors='ignore')}")
        
        # 显示版本信息
        version_bytes = header[7:11]
        print(f"  版本字节: {' '.join(f'{b:02X}' for b in version_bytes)}")
        
        # 显示数据块信息
        if len(header) >= 20:
            block_count = struct.unpack('<I', header[16:20])[0]
            print(f"  数据块数量: {block_count}")
    
    # 显示十六进制头部
    print(f"\n🔍 文件头部十六进制 (前64字节):")
    for i in range(0, min(64, len(header)), 16):
        hex_part = " ".join(f"{header[i+j]:02X}" for j in range(min(16, len(header)-i)))
        ascii_part = "".join(chr(header[i+j]) if 32 <= header[i+j] < 127 else "." for j in range(min(16, len(header)-i)))
        print(f"  {i:04X}: {hex_part:<48} |{ascii_part}|")

def search_readable_data(f, filename, file_size):
    """搜索人类可读数据"""
    print(f"\n🔍 搜索人类可读数据...")
    print("-" * 40)
    
    # 搜索模式
    patterns = [
        (b'DICE-AM', '地图矢量数据头'),
        (b'<?xml', 'XML配置文件'),
        (b'{"', 'JSON数据'),
        (b'<', 'XML标签'),
        (b'\xe4\xb8\xad', '中文"中"字'),
        (b'\xe5\x9c\xb0', '中文"地"字'),
        (b'\xe5\x9b\xbe', '中文"图"字'),
        (b'\xe5\x8c\x97\xe4\xba\xac', '中文"北京"'),
        (b'\xe4\xb8\xba\xe6\xb5\xb7', '中文"上海"'),
        (b'\xe5\xb9\xbf\xe5\xb7\x9e', '中文"广州"'),
        (b'\xe6\xb7\xb1\xe5\x9c\xb3', '中文"深圳"'),
    ]
    
    findings = []
    chunk_size = 1024 * 1024  # 每次读取1MB
    
    for pattern, description in patterns:
        print(f"\n[搜索] {description}: {pattern}")
        
        f.seek(0)
        offset = 0
        count = 0
        
        while offset < file_size and count < 10:  # 限制每个模式最多10个结果
            # 读取数据块
            f.seek(offset)
            chunk = f.read(chunk_size)
            if not chunk:
                break
            
            # 在数据块中搜索
            pos = chunk.find(pattern)
            if pos != -1:
                actual_offset = offset + pos
                count += 1
                
                print(f"  ✅ 找到 #{count}: 偏移 0x{actual_offset:08X} ({actual_offset})")
                
                # 提取上下文数据
                context_size = 512
                f.seek(max(0, actual_offset - 50))
                context = f.read(context_size)
                
                # 保存发现的数据
                save_found_data(context, pattern, description, actual_offset, filename)
                
                findings.append({
                    'pattern': description,
                    'offset': actual_offset,
                    'context': context[:200]
                })
                
                offset = actual_offset + len(pattern)
            else:
                offset += chunk_size - len(pattern)  # 重叠搜索
        
        if count == 0:
            print("  ❌ 未找到")
        else:
            print(f"  📊 总计: {count} 个")
    
    # 生成报告
    generate_detailed_report(filename, file_size, findings)
    
    return findings

def save_found_data(context, pattern, description, offset, filename):
    """保存发现的数据"""
    # 转换为可读文本
    readable = ""
    for byte in context:
        if 32 <= byte < 127:
            readable += chr(byte)
        elif byte == 10:
            readable += '\n'
        elif byte == 9:
            readable += '\t'
        else:
            readable += '.'
    
    # 修复文件名 - 移除特殊字符
    safe_desc = description.replace(' ', '_').replace('/', '_').replace('"', '').replace(':', '').replace('\\', '_').replace('?', '').replace('*', '').replace('<', '').replace('>', '').replace('|', '')
    safe_filename = os.path.basename(filename).replace('.', '_')
    output_file = f"found_{safe_desc}_{offset:08X}_{safe_filename}.txt"
    
    with open(output_file, 'w', encoding='utf-8') as out:
        out.write(f"# {description} 数据\n")
        out.write(f"# 来源文件: {filename}\n")
        out.write(f"# 偏移: 0x{offset:08X} ({offset})\n")
        out.write(f"# 匹配模式: {pattern}\n\n")
        out.write("=== 可读文本内容 ===\n")
        out.write(readable)
        out.write("\n\n=== 十六进制数据 ===\n")
        
        # 添加十六进制数据
        for i in range(0, min(256, len(context)), 16):
            hex_line = " ".join(f"{context[i+j]:02X}" for j in range(min(16, len(context)-i)))
            ascii_line = "".join(chr(context[i+j]) if 32 <= context[i+j] < 127 else "." for j in range(min(16, len(context)-i)))
            out.write(f"{i:04X}: {hex_line:<48} |{ascii_line}|\n")
    
    print(f"    💾 保存到: {output_file}")
    
    # 显示预览
    preview = readable[:150]
    if preview.strip():
        print(f"    📄 预览: {preview}")

def generate_detailed_report(filename, file_size, findings):
    """生成详细报告"""
    report_file = f"detailed_report_{os.path.basename(filename)}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"# 高德地图 {filename} 详细分析报告\n\n")
        f.write(f"文件: {filename}\n")
        f.write(f"大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)\n")
        f.write(f"发现数据: {len(findings)} 个\n\n")
        
        for i, finding in enumerate(findings, 1):
            f.write(f"## 发现 #{i}\n")
            f.write(f"类型: {finding['pattern']}\n")
            f.write(f"偏移: 0x{finding['offset']:08X} ({finding['offset']})\n")
            f.write(f"上下文预览:\n```\n{finding['context']}\n```\n\n")
    
    print(f"\n📊 详细报告保存到: {report_file}")

def extract_chinese_text_chunks(filename):
    """专门提取中文文本块"""
    print(f"\n🈲 专门提取中文文本数据")
    print("-" * 40)
    
    chinese_patterns = [
        b'\xe4\xb8\xad\xe5\x9b\xbd',  # 中国
        b'\xe5\x8c\x97\xe4\xba\xac',  # 北京
        b'\xe4\xb8\x8a\xe6\xb5\xb7',  # 上海
        b'\xe5\xb9\xbf\xe5\xb7\x9e',  # 广州
        b'\xe6\xb7\xb1\xe5\x9c\xb3',  # 深圳
        b'\xe5\xa4\xa9\xe6\xb4\xa5',  # 天津
        b'\xe9\x87\x8d\xe5\xba\x86',  # 重庆
        b'\xe8\xa5\xbf\xe5\xae\x89',  # 西安
        b'\xe6\x88\x90\xe9\x83\xbd',  # 成都
        b'\xe6\xad\xa6\xe6\xb1\x89',  # 武汉
    ]
    
    with open(filename, 'rb') as f:
        file_size = os.path.getsize(filename)
        
        for pattern in chinese_patterns:
            f.seek(0)
            chunk_size = 1024 * 1024
            offset = 0
            
            while offset < file_size:
                f.seek(offset)
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                
                pos = chunk.find(pattern)
                if pos != -1:
                    actual_offset = offset + pos
                    
                    # 提取更大的中文文本块
                    f.seek(max(0, actual_offset - 100))
                    text_chunk = f.read(2000)  # 读取2KB的中文文本
                    
                    # 转换并保存
                    try:
                        decoded = text_chunk.decode('utf-8', errors='ignore')
                        chinese_text = ''.join(c for c in decoded if '\u4e00' <= c <= '\u9fff' or c in ' \n\t')
                        
                        if len(chinese_text) > 10:
                            city_name = pattern.decode('utf-8', errors='ignore')
                            output_file = f"chinese_text_{city_name}_{actual_offset:08X}.txt"
                            
                            with open(output_file, 'w', encoding='utf-8') as out:
                                out.write(f"# 中文文本提取 - {city_name}\n")
                                out.write(f"# 文件: {filename}\n")
                                out.write(f"# 偏移: 0x{actual_offset:08X}\n\n")
                                out.write(chinese_text)
                            
                            print(f"✅ 找到中文文本: {city_name} -> {output_file}")
                            print(f"   预览: {chinese_text[:100]}")
                    except:
                        pass
                    
                    break
                
                offset += chunk_size - 10  # 重叠搜索

def main():
    """主函数"""
    print("🎯 高德地图高级.ans文件分析器")
    print("专门分析 m1.ans 和 m3.ans 大型地图数据文件")
    print("=" * 80)
    
    # 分析两个文件
    files_to_analyze = ["file/m1.ans", "file/m3.ans"]
    
    for filename in files_to_analyze:
        if os.path.exists(filename):
            # 基本分析
            findings = analyze_large_ans_file(filename)
            
            # 专门提取中文文本
            extract_chinese_text_chunks(filename)
            
            print(f"\n✅ {filename} 分析完成")
        else:
            print(f"❌ 文件不存在: {filename}")
    
    print(f"\n🎉 所有分析完成！")
    print("📁 检查生成的输出文件以查看完整的人类可读数据")

if __name__ == "__main__":
    main() 