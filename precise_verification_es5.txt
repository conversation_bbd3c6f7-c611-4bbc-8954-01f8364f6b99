     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[精确验证模式] 开始深度分析高德地图离线数据处理流程...
[目标] 精确定位.ans文件读取、解压、解析的完整调用链
[1] 设置文件操作Hook (精确模式)...
[\u2713] open() Hook设置成功
[\u2713] read() Hook设置成功
[2] 设置内存操作Hook...
[\u2713] mmap() Hook设置成功
[3] 搜索并Hook zlib函数...
[搜索] 在 libz.so 中搜索zlib函数...
[发现] libz.so::uncompress @ 0x7f8cac667c
[发现] libz.so::inflate @ 0x7f8cabf858
[发现] libz.so::inflateEnd @ 0x7f8cac1ae8
[搜索] 在 libc.so 中搜索zlib函数...
[4] 分析libamapmain.so中的关键函数...
[警告] 无法分析libamapmain.so: unable to find module 'libamapmain.so'
[精确验证] 验证脚本已启动，等待地图操作...
[提示] 请移动地图、缩放或切换图层以触发数据处理
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0xfffffffe
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/base.apk (fd=20)
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 65557 字节
  头部数据: 2e 78 6d 6c 50 4b 01 02 14 00 14 00 00 08 08 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f691a3000 (大小: 557657)
  映射数据头部: 50 4b 01 02 14 00 14 00 00 08 08 00 a4 00 e2 5a
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f6919a000 (大小: 36153)
  映射数据头部: 30 05 79 57 33 5e cd b3 15 0e bd 0e 8c 03 d7 d3
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0xfffffffe
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68e79000 (大小: 2908604)
  映射数据头部: e4 1c 8c ef 41 e4 0e 32 be 67 9b f8 8e b6 e1 db
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f8a6e1000 (大小: 3889)
  映射数据头部: 70 5f 77 69 64 67 65 74 5f 72 65 73 74 72 69 63
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x1
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x0
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f69184000 (大小: 39524)
  映射数据头部: 62 77 61 79 5f 64 61 72 6b 2e 77 65 62 70 00 00
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/base.apk (fd=21)
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 4 字节
  头部数据: 50 4b 03 04
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 65557 字节
  头部数据: 2e 78 6d 6c 50 4b 01 02 14 00 14 00 00 08 08 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f6918a000 (大小: 15124)
  映射数据头部: 00 34 00 03 00 72 65 73 2f 64 72 61 77 61 62 6c
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68c37000 (大小: 557657)
  映射数据头部: 50 4b 01 02 14 00 14 00 00 08 08 00 a4 00 e2 5a
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/base.apk (fd=21)
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 4 字节
  头部数据: 50 4b 03 04
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 65557 字节
  头部数据: 2e 78 6d 6c 50 4b 01 02 14 00 14 00 00 08 08 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68df0000 (大小: 557657)
  映射数据头部: 50 4b 01 02 14 00 14 00 00 08 08 00 a4 00 e2 5a
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/base.apk (fd=21)
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 4 字节
  头部数据: 50 4b 03 04
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 65557 字节
  头部数据: 2e 78 6d 6c 50 4b 01 02 14 00 14 00 00 08 08 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68df0000 (大小: 557657)
  映射数据头部: 50 4b 01 02 14 00 14 00 00 08 08 00 a4 00 e2 5a
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/base.apk (fd=21)
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 4 字节
  头部数据: 50 4b 03 04
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 65557 字节
  头部数据: 2e 78 6d 6c 50 4b 01 02 14 00 14 00 00 08 08 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68df0000 (大小: 557657)
  映射数据头部: 50 4b 01 02 14 00 14 00 00 08 08 00 a4 00 e2 5a
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/base.apk (fd=21)
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 4 字节
  头部数据: 50 4b 03 04
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 65557 字节
  头部数据: 2e 78 6d 6c 50 4b 01 02 14 00 14 00 00 08 08 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68df0000 (大小: 557657)
  映射数据头部: 50 4b 01 02 14 00 14 00 00 08 08 00 a4 00 e2 5a
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/base.apk (fd=21)
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 4 字节
  头部数据: 50 4b 03 04
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 65557 字节
  头部数据: 2e 78 6d 6c 50 4b 01 02 14 00 14 00 00 08 08 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68df0000 (大小: 557657)
  映射数据头部: 50 4b 01 02 14 00 14 00 00 08 08 00 a4 00 e2 5a
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/base.apk (fd=21)
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 4 字节
  头部数据: 50 4b 03 04
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 65557 字节
  头部数据: 2e 78 6d 6c 50 4b 01 02 14 00 14 00 00 08 08 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68df0000 (大小: 557657)
  映射数据头部: 50 4b 01 02 14 00 14 00 00 08 08 00 a4 00 e2 5a
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68e59000 (大小: 131072)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f737c6000 (大小: 8192)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/shared_prefs/appLanguage.xml (fd=21)
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 326 字节
  头部数据: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/launchTime (fd=21)
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/crashCounter (fd=21)
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 1 字节
  头部数据: 30
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/crashCounter (fd=21)
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68e2a000 (大小: 131072)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68e0a000 (大小: 131072)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68dea000 (大小: 131072)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f737c6000 (大小: 8192)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/shared_prefs/AfpSplashEvents.xml (fd=21)
[文件读取] /data/app/com.autonavi.minimap-1/base.apk 读取 2784 字节
  头部数据: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f737c6000 (大小: 8192)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68dc8000 (大小: 8192)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68dc6000 (大小: 8192)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/lib/arm64/libmmkv.so (fd=21)
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f8a6e1000 (大小: 813)
  映射数据头部: 29 7b 24 e5 62 68 b7 39 bc d2 14 c5 0e 06 83 99
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x1
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x0
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/files/boot/bootbiz/d3ec0a15fe6c9fc3b985bf0ecff5d529 (fd=21)
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f8a6e1000 (大小: 2576)
  映射数据头部: bd 7a 29 da e0 55 04 7b 54 ac 50 0f e2 79 e9 2e
[zlib调用] undefined 被调用
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/lib/arm64/libserverkey.so (fd=22)
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68d44000 (大小: 65536)
  映射数据头部: 1b ab 00 00 ff ff ff 07 19 4b 65 79 5f 61 70 70
[zlib返回] undefined 返回: 0x1
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x0
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f8a6e1000 (大小: 2828)
  映射数据头部: bd 7a 29 da e0 55 04 7b 54 ac 50 0f e2 79 e9 2e
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x1
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x0
[文件读取] /data/app/com.autonavi.minimap-1/lib/arm64/libserverkey.so 读取 12 字节
  头部数据: 34 30 31 00 00 00 00 04 07 00 00 00
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/files/boot/bootbiz/d3ec0a15fe6c9fc3b985bf0ecff5d529.crc (fd=23)
[内存映射] /data/user/0/com.autonavi.minimap/files/boot/bootbiz/d3ec0a15fe6c9fc3b985bf0ecff5d529.crc 映射到 0x7f8a6e1000 (大小: 4096)
  映射数据头部: f9 73 25 3d 03 00 00 00 03 00 00 00 00 00 00 00
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/lib/arm64/libamapcrash.so (fd=24)
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/shared_prefs/SharedPreferences.xml (fd=25)
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f68d74000 (大小: 3740)
  映射数据头部: 1d d2 d4 99 72 7d f0 c8 32 b3 2b 9d 5d 97 ac 0a
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x1
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x0
[内存映射] /data/app/com.autonavi.minimap-1/lib/arm64/libamapcrash.so 映射到 0x7f5220f000 (大小: 131072)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[内存映射] /data/app/com.autonavi.minimap-1/base.apk 映射到 0x7f5dd00000 (大小: 5026)
  映射数据头部: 71 86 b0 80 36 54 4a a8 3a e1 5e 27 3c 80 2a 3d
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x1
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x0
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x1
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x0
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x1
[zlib调用] undefined 被调用
[zlib返回] undefined 返回: 0x0
[文件读取] /data/user/0/com.autonavi.minimap/shared_prefs/SharedPreferences.xml 读取 11614 字节
  头部数据: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31
[内存映射] /data/user/0/com.autonavi.minimap/shared_prefs/SharedPreferences.xml 映射到 0x7f68d40000 (大小: 8192)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[内存映射] /data/user/0/com.autonavi.minimap/shared_prefs/SharedPreferences.xml 映射到 0x7f5dd00000 (大小: 8192)
  映射数据头部: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/shared_prefs/com.amap.bundle.location.locator.module.LocationStorage.xml (fd=25)
[文件读取] /data/user/0/com.autonavi.minimap/shared_prefs/SharedPreferences.xml 读取 418 字节
  头部数据: 3c 3f 78 6d 6c 20 76 65 72 73 69 6f 6e 3d 27 31
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/files/boot/speedup/5d3830e454f6b0d0c3b0106103275177 (fd=40)
[内存映射] /data/user/0/com.autonavi.minimap/files/boot/speedup/5d3830e454f6b0d0c3b0106103275177 映射到 0x7f51f3a000 (大小: 131072)
  映射数据头部: 2e 63 01 00 ff ff ff 07 12 61 6c 63 5f 72 65 63
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/lib/arm64/libamapnsq.so (fd=38)
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/files/boot/speedup/5d3830e454f6b0d0c3b0106103275177.crc (fd=25)
[内存映射] /data/user/0/com.autonavi.minimap/shared_prefs/SharedPreferences.xml 映射到 0x7f68d74000 (大小: 4096)
  映射数据头部: f5 a7 02 41 03 00 00 00 6c 00 00 00 2f 99 8a 4d
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/lib/arm64/libc++_shared.so (fd=38)
[文件操作] OPEN: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/journal (fd=44)
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_all_value.xml (fd=41)
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_white_list_key_value.xml (fd=43)
[文件操作] OPEN: /data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_value.xml (fd=42)
[文件操作] OPEN: /data/app/com.autonavi.minimap-1/lib/arm64/libamapmain.so (fd=38)
Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=000001C2E93D8C40)

Thread 0x000031e8 (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x00003ee0 (most recent call first):
<no Python frame>
