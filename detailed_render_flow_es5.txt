     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[详细渲染流程] 开始分析离线地图渲染的详细步骤...
[\u2713] libz.so 已加载
[\u2713] libc.so 已加载
[\u2713] libGLESv2.so 已加载
[\u2713] 文件读取Hook设置成功
[\u2713] zlib解压Hook设置成功
[\u2713] OpenGL渲染Hook设置成功
[详细渲染流程] 分析脚本已启动，等待地图操作...
[提示] 请移动地图以触发完整的渲染流程分析
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [阶段1-数据读取] #1
  文件描述符: 47
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #2
  文件描述符: 47
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #3
  文件描述符: 47
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #4
  文件描述符: 47
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段2-数据解压] #1
  解压后大小: 4700 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 4700 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #2
  解压后大小: 2684 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 2684 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #3
  解压后大小: 10408 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 10408 字节
[阶段3-着色器] glUseProgram #1
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #4
  解压后大小: 5300 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 5300 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #5
  解压后大小: 3096 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 3096 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #6
  解压后大小: 11712 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 11712 字节
[阶段2-数据解压] #7
  解压后大小: 5304 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 5304 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #8
  解压后大小: 3336 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 3336 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #9
  解压后大小: 11760 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 11760 字节
[阶段2-数据解压] #10
  解压后大小: 5304 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 5304 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #11
  解压后大小: 3712 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 3712 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #12
  解压后大小: 12024 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 12024 字节
[阶段3-着色器] glUseProgram #6
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #13
  解压后大小: 6304 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 6304 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #14
  解压后大小: 3612 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 3612 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #15
  解压后大小: 13288 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 13288 字节
[阶段2-数据解压] #16
  解压后大小: 5924 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 5924 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #17
  解压后大小: 4372 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 4372 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #18
  解压后大小: 13144 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 13144 字节
[阶段3-着色器] glUseProgram #11
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #19
  解压后大小: 5300 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 5300 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #20
  解压后大小: 3096 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 3096 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #21
  解压后大小: 11712 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 11712 字节
[阶段2-数据解压] #22
  解压后大小: 4700 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 4700 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #23
  解压后大小: 2684 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 2684 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #24
  解压后大小: 10408 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 10408 字节
[阶段3-着色器] glUseProgram #16
  着色器程序ID: 24
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #5
  文件描述符: 114
  数据大小: 531 字节
  数据类型: ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段2-数据解压] #25
  解压后大小: 8192 字节
  解压数据类型: DICE-AM
[数据分析] 类型: DICE-AM, 大小: 8192 字节
  [DICE-AM] 版本: 170, 标志: 0x0
  [DICE-AM] 可能包含: 道路/建筑/水域几何数据
[阶段1-数据读取] #6
  文件描述符: 114
  数据大小: 531 字节
  数据类型: ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段2-数据解压] #26
  解压后大小: 8192 字节
  解压数据类型: DICE-AM
[数据分析] 类型: DICE-AM, 大小: 8192 字节
  [DICE-AM] 版本: 170, 标志: 0x0
  [DICE-AM] 可能包含: 道路/建筑/水域几何数据
[阶段1-数据读取] #7
  文件描述符: 114
  数据大小: 139 字节
  数据类型: ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段2-数据解压] #27
  解压后大小: 8192 字节
  解压数据类型: TEXT
[数据分析] 类型: TEXT, 大小: 8192 字节
  [TEXT] 可能包含: 地名/道路名/POI标注
[阶段3-着色器] glUseProgram #21
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #26
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #31
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #36
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #41
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #46
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #51
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #56
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #8
  文件描述符: 114
  数据大小: 531 字节
  数据类型: ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段2-数据解压] #28
  解压后大小: 8192 字节
  解压数据类型: DICE-AM
[数据分析] 类型: DICE-AM, 大小: 8192 字节
  [DICE-AM] 版本: 170, 标志: 0x0
  [DICE-AM] 可能包含: 道路/建筑/水域几何数据
[阶段3-纹理] glBindTexture #1
  纹理目标: 0xde1
  纹理ID: 1
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段2-数据解压] #29
  解压后大小: 4572 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 4572 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #30
  解压后大小: 3920 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 3920 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #31
  解压后大小: 12136 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 12136 字节
[阶段3-渲染] glDrawArrays #1
  渲染模式: 0x5
  起始顶点: 0
  顶点数量: 4
  [渲染类型] GL_TRIANGLE_STRIP - 三角形带 (道路)
[阶段2-数据解压] #32
  解压后大小: 8700 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 8700 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #33
  解压后大小: 6356 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 6356 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #34
  解压后大小: 15376 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 15376 字节
[阶段3-着色器] glUseProgram #61
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #66
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #71
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #76
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #81
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #11
  纹理目标: 0xde1
  纹理ID: 9
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #86
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #35
  解压后大小: 3436 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 3436 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #36
  解压后大小: 2972 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 2972 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #37
  解压后大小: 9136 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 9136 字节
[阶段3-着色器] glUseProgram #91
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #96
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #101
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #106
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #21
  纹理目标: 0xde1
  纹理ID: 9
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #111
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #116
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-渲染] glDrawArrays #11
  渲染模式: 0x3
  起始顶点: 0
  顶点数量: 362
  [渲染类型] GL_LINE_STRIP - 线条带 (轮廓)
[阶段3-着色器] glUseProgram #121
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #126
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #31
  纹理目标: 0xde1
  纹理ID: 9
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段2-数据解压] #38
  解压后大小: 10664 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 10664 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #39
  解压后大小: 6340 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 6340 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #40
  解压后大小: 17480 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 17480 字节
[阶段3-着色器] glUseProgram #131
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #136
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #141
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #146
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #41
  解压后大小: 10200 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 10200 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #42
  解压后大小: 6748 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 6748 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #43
  解压后大小: 17664 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 17664 字节
[阶段1-数据读取] #9
  文件描述符: 145
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段3-着色器] glUseProgram #151
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #156
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #161
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #166
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #10
  文件描述符: 139
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #11
  文件描述符: 160
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段3-纹理] glBindTexture #41
  纹理目标: 0xde1
  纹理ID: 2
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #171
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #176
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #181
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #186
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-渲染] glDrawArrays #21
  渲染模式: 0x3
  起始顶点: 0
  顶点数量: 362
  [渲染类型] GL_LINE_STRIP - 线条带 (轮廓)
[阶段3-着色器] glUseProgram #191
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #51
  纹理目标: 0xde1
  纹理ID: 10
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #196
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #201
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #206
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #211
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #61
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段1-数据读取] #12
  文件描述符: 160
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段3-着色器] glUseProgram #216
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #221
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #226
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #231
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #236
  着色器程序ID: 18
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #71
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段2-数据解压] #44
  解压后大小: 6440 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 6440 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #45
  解压后大小: 5332 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 5332 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #46
  解压后大小: 14952 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 14952 字节
[阶段3-着色器] glUseProgram #241
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #246
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #251
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #256
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #261
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-渲染] glDrawArrays #31
  渲染模式: 0x3
  起始顶点: 0
  顶点数量: 362
  [渲染类型] GL_LINE_STRIP - 线条带 (轮廓)
[阶段3-着色器] glUseProgram #266
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #81
  纹理目标: 0xde1
  纹理ID: 9
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #271
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #276
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #281
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #286
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #291
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #296
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #13
  文件描述符: 153
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #14
  文件描述符: 179
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #301
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #306
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #91
  纹理目标: 0xde1
  纹理ID: 10
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段1-数据读取] #15
  文件描述符: 153
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #311
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #316
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #321
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #326
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #101
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #331
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #16
  文件描述符: 192
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #336
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #341
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-渲染] glDrawArrays #41
  渲染模式: 0x3
  起始顶点: 0
  顶点数量: 362
  [渲染类型] GL_LINE_STRIP - 线条带 (轮廓)
[阶段3-纹理] glBindTexture #111
  纹理目标: 0xde1
  纹理ID: 9
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #346
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #351
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #356
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #361
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #366
  着色器程序ID: 18
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #371
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #376
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #17
  文件描述符: 175
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #381
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #18
  文件描述符: 175
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-纹理] glBindTexture #121
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #386
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #19
  文件描述符: 160
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #391
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #396
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #401
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #406
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #411
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #416
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #131
  纹理目标: 0xde1
  纹理ID: 12
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段1-数据读取] #20
  文件描述符: 196
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #421
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #426
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #431
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #436
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #441
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #446
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #451
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #456
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #461
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #466
  着色器程序ID: 24
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #21
  文件描述符: 196
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #471
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #141
  纹理目标: 0xde1
  纹理ID: 10
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #476
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-渲染] glDrawArrays #51
  渲染模式: 0x3
  起始顶点: 0
  顶点数量: 362
  [渲染类型] GL_LINE_STRIP - 线条带 (轮廓)
[阶段3-着色器] glUseProgram #481
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #486
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #491
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #496
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #501
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #22
  文件描述符: 199
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段1-数据读取] #23
  文件描述符: 98
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #506
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #151
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #511
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #516
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #521
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #24
  文件描述符: 199
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段1-数据读取] #25
  文件描述符: 199
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段3-着色器] glUseProgram #526
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #531
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #26
  文件描述符: 199
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段3-着色器] glUseProgram #536
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #27
  文件描述符: 199
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段3-着色器] glUseProgram #541
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #546
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #161
  纹理目标: 0xde1
  纹理ID: 16
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #551
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #28
  文件描述符: 199
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段3-着色器] glUseProgram #556
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #561
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #47
  解压后大小: 5456 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 5456 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #48
  解压后大小: 4656 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 4656 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段1-数据读取] #29
  文件描述符: 199
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段2-数据解压] #49
  解压后大小: 15080 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 15080 字节
[阶段3-纹理] glBindTexture #171
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段1-数据读取] #30
  文件描述符: 175
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #566
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #31
  文件描述符: 175
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #571
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #576
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #50
  解压后大小: 4776 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 4776 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #51
  解压后大小: 4380 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 4380 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #52
  解压后大小: 13112 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 13112 字节
[阶段3-着色器] glUseProgram #581
  着色器程序ID: 21
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #181
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #586
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #591
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #191
  纹理目标: 0xde1
  纹理ID: 18
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #596
  着色器程序ID: 18
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #601
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #606
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #611
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #32
  文件描述符: 199
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段3-着色器] glUseProgram #616
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #621
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #33
  文件描述符: 199
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段3-着色器] glUseProgram #626
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #631
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #636
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #641
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #646
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #651
  着色器程序ID: 18
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #656
  着色器程序ID: 21
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #34
  文件描述符: 199
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段3-着色器] glUseProgram #661
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #666
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #671
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #676
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #681
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #686
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #691
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #35
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #696
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #701
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #706
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #36
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #711
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #716
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #721
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #726
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #731
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #736
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #741
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #746
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #751
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #756
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #37
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #761
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #766
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #771
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #38
  文件描述符: 199
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #776
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #781
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #786
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #791
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #796
  着色器程序ID: 21
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #801
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #201
  纹理目标: 0xde1
  纹理ID: 15
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #806
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #811
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #816
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #821
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #211
  纹理目标: 0xde1
  纹理ID: 10
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #826
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-渲染] glDrawArrays #61
  渲染模式: 0x3
  起始顶点: 0
  顶点数量: 362
  [渲染类型] GL_LINE_STRIP - 线条带 (轮廓)
[阶段3-着色器] glUseProgram #831
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #221
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #836
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #841
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #231
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #846
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #851
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #856
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #861
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #241
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #866
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #871
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #39
  文件描述符: 104
  数据大小: 8 字节
  数据类型: AM-ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段3-着色器] glUseProgram #876
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #251
  纹理目标: 0xde1
  纹理ID: 18
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #881
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #886
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #891
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #896
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #261
  纹理目标: 0xde1
  纹理ID: 15
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #901
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #906
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #911
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #916
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #271
  纹理目标: 0xde1
  纹理ID: 10
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-纹理] glBindTexture #281
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #921
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #926
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #931
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #936
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #941
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #946
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #40
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #951
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #956
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #41
  文件描述符: 160
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #961
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #42
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #966
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #971
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #976
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #981
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #986
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #991
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #996
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #43
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1001
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染

[详细渲染流程统计] ==========================================
运行时间: 15s

阶段1 - 数据读取:
  文件读取次数: 43
  总数据大小: 217 KB

阶段2 - 数据处理:
  解压操作: 52
  解压后大小: 397 KB
  解压比率: 1.83:1
  DICE-AM矢量: 3 (几何数据)
  配置数据: 32 (样式参数)
  文本数据: 1 (地名标注)
  未知数据: 16 (索引/元数据)

阶段3 - 渲染输出:
  顶点渲染: 66 (几何绘制)
  着色器切换: 1004 (渲染状态)
  纹理绑定: 282 (材质资源)

性能分析:
  数据处理效率: 120.9% (解压/读取比)
  渲染效率: 6.6% (绘制/着色器比)
  数据类型分布:
    几何数据: 5.8%
    配置数据: 61.5%
    文本数据: 1.9%
    其他数据: 30.8%
===============================================

[阶段3-着色器] glUseProgram #1006
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1011
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1016
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1021
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1026
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1031
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1036
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1041
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1046
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1051
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1056
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #291
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1061
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1066
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1071
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1076
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1081
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1086
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1091
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #44
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1096
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1101
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #301
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1106
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #45
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1111
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1116
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1121
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #46
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #47
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1126
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1131
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1136
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1141
  着色器程序ID: 24
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1146
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1151
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1156
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1161
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1166
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1171
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1176
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #48
  文件描述符: 236
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1181
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #49
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1186
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1191
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1196
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1201
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1206
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1211
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1216
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1221
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1226
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1231
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1236
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1241
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #50
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #51
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1246
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1251
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1256
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1261
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #52
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #53
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1266
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1271
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1276
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1281
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1286
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #54
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1291
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #311
  纹理目标: 0xde1
  纹理ID: 18
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1296
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1301
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1306
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #55
  文件描述符: 239
  数据大小: 987 字节
  数据类型: ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段2-数据解压] #53
  解压后大小: 8192 字节
  解压数据类型: DICE-AM
[数据分析] 类型: DICE-AM, 大小: 8192 字节
  [DICE-AM] 版本: 170, 标志: 0x0
  [DICE-AM] 可能包含: 道路/建筑/水域几何数据
[阶段1-数据读取] #56
  文件描述符: 239
  数据大小: 987 字节
  数据类型: ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段2-数据解压] #54
  解压后大小: 8192 字节
  解压数据类型: DICE-AM
[数据分析] 类型: DICE-AM, 大小: 8192 字节
  [DICE-AM] 版本: 170, 标志: 0x0
  [DICE-AM] 可能包含: 道路/建筑/水域几何数据
[阶段3-着色器] glUseProgram #1311
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #57
  文件描述符: 239
  数据大小: 2813 字节
  数据类型: ZLIB
[阶段3-着色器] glUseProgram #1316
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #55
  解压后大小: 8192 字节
  解压数据类型: TEXT
[数据分析] 类型: TEXT, 大小: 8192 字节
  [TEXT] 可能包含: 地名/道路名/POI标注
[阶段3-着色器] glUseProgram #1321
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1326
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #321
  纹理目标: 0xde1
  纹理ID: 15
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1331
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #58
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1336
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #331
  纹理目标: 0xde1
  纹理ID: 10
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段1-数据读取] #59
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1341
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-渲染] glDrawArrays #71
  渲染模式: 0x3
  起始顶点: 0
  顶点数量: 362
  [渲染类型] GL_LINE_STRIP - 线条带 (轮廓)
[阶段3-着色器] glUseProgram #1346
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1351
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1356
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #341
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1361
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1366
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #60
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1371
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1376
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1381
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #351
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1386
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1391
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1396
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1401
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #361
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1406
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1411
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1416
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1421
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #371
  纹理目标: 0xde1
  纹理ID: 18
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1426
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1431
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1436
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #381
  纹理目标: 0xde1
  纹理ID: 15
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1441
  着色器程序ID: 18
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #61
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1446
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1451
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1456
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #62
  文件描述符: 160
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #63
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1461
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1466
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1471
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1476
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1481
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #64
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #65
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1486
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1491
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1496
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1501
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1506
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #66
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1511
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1516
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1521
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1526
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1531
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1536
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1541
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1546
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1551
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1556
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1561
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1566
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1571
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #391
  纹理目标: 0xde1
  纹理ID: 10
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-纹理] glBindTexture #401
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1576
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #67
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1581
  着色器程序ID: 18
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #411
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段1-数据读取] #68
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #69
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1586
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #421
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1591
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1596
  着色器程序ID: 21
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #431
  纹理目标: 0xde1
  纹理ID: 18
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段1-数据读取] #70
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1601
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #441
  纹理目标: 0xde1
  纹理ID: 15
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1606
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1611
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #451
  纹理目标: 0xde1
  纹理ID: 10
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-渲染] glDrawArrays #81
  渲染模式: 0x3
  起始顶点: 0
  顶点数量: 362
  [渲染类型] GL_LINE_STRIP - 线条带 (轮廓)
[阶段3-纹理] glBindTexture #461
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1616
  着色器程序ID: 0
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1621
  着色器程序ID: 18
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #471
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段1-数据读取] #71
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1626
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #481
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1631
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #72
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1636
  着色器程序ID: 21
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #491
  纹理目标: 0xde1
  纹理ID: 18
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1641
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #73
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-纹理] glBindTexture #501
  纹理目标: 0xde1
  纹理ID: 15
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #1646
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #74
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #75
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1651
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1656
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1661
  着色器程序ID: 27
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1666
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #76
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1671
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1676
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1681
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1686
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #77
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1691
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #56
  解压后大小: 5588 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 5588 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段1-数据读取] #78
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段2-数据解压] #57
  解压后大小: 3980 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 3980 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #58
  解压后大小: 12536 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 12536 字节
[阶段2-数据解压] #59
  解压后大小: 6580 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 6580 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #60
  解压后大小: 5428 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 5428 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #61
  解压后大小: 15288 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 15288 字节
[阶段2-数据解压] #62
  解压后大小: 7196 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 7196 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #63
  解压后大小: 6452 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 6452 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段1-数据读取] #79
  文件描述符: 250
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段2-数据解压] #64
  解压后大小: 16752 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 16752 字节
[阶段2-数据解压] #65
  解压后大小: 6096 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 6096 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #66
  解压后大小: 4868 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 4868 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #67
  解压后大小: 13856 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 13856 字节
[阶段3-着色器] glUseProgram #1696
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1701
  着色器程序ID: 18
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1706
  着色器程序ID: 21
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #80
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #81
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1711
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1716
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1721
  着色器程序ID: 39
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #68
  解压后大小: 5304 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 5304 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #69
  解压后大小: 3704 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 3704 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #70
  解压后大小: 12024 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 12024 字节
[阶段3-着色器] glUseProgram #1726
  着色器程序ID: 42
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1731
  着色器程序ID: 24
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #82
  文件描述符: 160
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #83
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1736
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1741
  着色器程序ID: 36
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1746
  着色器程序ID: 21
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1751
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1756
  着色器程序ID: 33
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #71
  解压后大小: 6096 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 6096 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #72
  解压后大小: 4544 字节
  解压数据类型: CONFIG
[数据分析] 类型: CONFIG, 大小: 4544 字节
  [CONFIG] 配置类型: 3, 标志: 0x0
  [CONFIG] 可能包含: 颜色/样式/渲染参数
[阶段2-数据解压] #73
  解压后大小: 13640 字节
  解压数据类型: UNKNOWN
[数据分析] 类型: UNKNOWN, 大小: 13640 字节
[阶段3-着色器] glUseProgram #1761
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1766
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #84
  文件描述符: 160
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1771
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1776
  着色器程序ID: 39
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1781
  着色器程序ID: 24
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1786
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1791
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #85
  文件描述符: 114
  数据大小: 531 字节
  数据类型: ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段2-数据解压] #74
  解压后大小: 8192 字节
  解压数据类型: DICE-AM
[数据分析] 类型: DICE-AM, 大小: 8192 字节
  [DICE-AM] 版本: 170, 标志: 0x0
  [DICE-AM] 可能包含: 道路/建筑/水域几何数据
[阶段1-数据读取] #86
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1796
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1801
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #87
  文件描述符: 251
  数据大小: 345 字节
  数据类型: ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段2-数据解压] #75
  解压后大小: 8192 字节
  解压数据类型: DICE-AM
[数据分析] 类型: DICE-AM, 大小: 8192 字节
  [DICE-AM] 版本: 170, 标志: 0x0
  [DICE-AM] 可能包含: 道路/建筑/水域几何数据
[阶段1-数据读取] #88
  文件描述符: 251
  数据大小: 345 字节
  数据类型: ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段3-着色器] glUseProgram #1806
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1811
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段2-数据解压] #76
  解压后大小: 8192 字节
  解压数据类型: DICE-AM
[数据分析] 类型: DICE-AM, 大小: 8192 字节
  [DICE-AM] 版本: 170, 标志: 0x0
  [DICE-AM] 可能包含: 道路/建筑/水域几何数据
[阶段1-数据读取] #89
  文件描述符: 251
  数据大小: 139 字节
  数据类型: ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段2-数据解压] #77
  解压后大小: 8192 字节
  解压数据类型: TEXT
[数据分析] 类型: TEXT, 大小: 8192 字节
  [TEXT] 可能包含: 地名/道路名/POI标注
[阶段3-着色器] glUseProgram #1816
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #90
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1821
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1826
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1831
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #91
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1836
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1841
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1846
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #92
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1851
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1856
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #93
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1861
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #94
  文件描述符: 91
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1866
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1871
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1876
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1881
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1886
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1891
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #95
  文件描述符: 255
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #96
  文件描述符: 255
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1896
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1901
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1906
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1911
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1916
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #97
  文件描述符: 220
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #98
  文件描述符: 160
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #1921
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1926
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1931
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1936
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1941
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1946
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #99
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1951
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1956
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1961
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1966
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #100
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #1971
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1976
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1981
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1986
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1991
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #1996
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2001
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2006
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #101
  文件描述符: 104
  数据大小: 8 字节
  数据类型: AM-ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段3-着色器] glUseProgram #2011
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #102
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2016
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2021
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #103
  文件描述符: 255
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #2026
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2031
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2036
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2041
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2046
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2051
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2056
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2061
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2066
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #104
  文件描述符: 257
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #105
  文件描述符: 255
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #2071
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #106
  文件描述符: 262
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段3-着色器] glUseProgram #2076
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2081
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2086
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #107
  文件描述符: 160
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #2091
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2096
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #108
  文件描述符: 262
  数据大小: 8192 字节
  数据类型: AM-ZLIB
  [读取模式] 标准8KB块 - 高密度数据
[阶段1-数据读取] #109
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2101
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2106
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2111
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2116
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #110
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2121
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #111
  文件描述符: 257
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #2126
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2131
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #112
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #113
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2136
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #114
  文件描述符: 258
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #2141
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2146
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #511
  纹理目标: 0xde1
  纹理ID: 10
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #2151
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #115
  文件描述符: 267
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #116
  文件描述符: 267
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #2156
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #521
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #2161
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2166
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #117
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #118
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-纹理] glBindTexture #531
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #2171
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2176
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #119
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2181
  着色器程序ID: 18
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #541
  纹理目标: 0xde1
  纹理ID: 3
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #2186
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2191
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2196
  着色器程序ID: 21
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #551
  纹理目标: 0xde1
  纹理ID: 18
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #2201
  着色器程序ID: 6
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #561
  纹理目标: 0xde1
  纹理ID: 15
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #2206
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2211
  着色器程序ID: 15
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #571
  纹理目标: 0xde1
  纹理ID: 10
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-渲染] glDrawArrays #91
  渲染模式: 0x3
  起始顶点: 0
  顶点数量: 362
  [渲染类型] GL_LINE_STRIP - 线条带 (轮廓)
[阶段3-着色器] glUseProgram #2216
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-纹理] glBindTexture #581
  纹理目标: 0xde1
  纹理ID: 8
  [用途] 可能用于: 地图瓦片/图标/字体
[阶段3-着色器] glUseProgram #2221
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2226
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2231
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2236
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2241
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2246
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2251
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #120
  文件描述符: 267
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #121
  文件描述符: 267
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #2256
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2261
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2266
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #122
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #123
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2271
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #124
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #125
  文件描述符: 258
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段3-着色器] glUseProgram #2276
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2281
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #126
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #127
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2286
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2291
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2296
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2301
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #128
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #129
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2306
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2311
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2316
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #130
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2321
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2326
  着色器程序ID: 33
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #131
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2331
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2336
  着色器程序ID: 9
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2341
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2346
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2351
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2356
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #132
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2361
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #133
  文件描述符: 10
  数据大小: 16 字节
  数据类型: AM-ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段3-着色器] glUseProgram #2366
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2371
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2376
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #134
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #135
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2381
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #136
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2386
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2391
  着色器程序ID: 18
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2396
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染

[Remote::com.autonavi.minimap]-> [阶段3-着色器] glUseProgram #2401
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #137
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2406
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #138
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2411
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2416
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #139
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #140
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2421
  着色器程序ID: 45
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段3-着色器] glUseProgram #2426
  着色器程序ID: 12
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #141
  文件描述符: 104
  数据大小: 8 字节
  数据类型: AM-ZLIB
  [读取模式] 小块压缩 - 精确数据
[阶段1-数据读取] #142
  文件描述符: 180
  数据大小: 4096 字节
  数据类型: AM-ZLIB
  [读取模式] 标准4KB块 - 常规地图数据
[阶段1-数据读取] #143
  文件描述符: 241
  数据大小: 1024 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2431
  着色器程序ID: 3
  [用途] 可能用于: 矢量/文本/纹理渲染
[阶段1-数据读取] #144
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #145
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段1-数据读取] #146
  文件描述符: 177
  数据大小: 8000 字节
  数据类型: AM-ZLIB
[阶段3-着色器] glUseProgram #2436
  着色器程序ID: 30
  [用途] 可能用于: 矢量/文本/纹理渲染
exit

Thank you for using Frida!
