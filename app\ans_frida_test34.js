// # Frida脚本：ANS文件解析器 (基于IDA Pro分析)

// 基于IDA Pro分析的截图，我可以编写一个更精确的Frida脚本来追踪ANS文件的解析逻辑。这个脚本将直接钩住我们现在已确认的关键函数`sub_C654`，这是调用zlib解压缩处理ANS数据的包装函数。


(function() {
    console.log("[ANS解析器-V40] 针对IDA分析的精确钩子版本...");

    var libamapnsq = null;
    var ansFileBuffers = {};    // 存储ANS文件数据在内存中的地址
    var fileDescriptors = {};   // 存储文件描述符到文件路径的映射
    var lastGestureTime = 0;    // 记录最后手势时间
    
    // 阶段性执行，减轻启动压力
    setTimeout(function() {
        // 第1阶段：基础监控
        setupFileMonitoring();
        
        // 第2阶段：精确钩子
        setTimeout(function() {
            setupPreciseHooks();
            
            // 第3阶段：手势监控
            setTimeout(function() {
                setupGestureTracking();
            }, 1000);
        }, 2000);
    }, 1000);

    // 设置文件监控
    function setupFileMonitoring() {
        // 监控文件打开
        var openPtr = Module.findExportByName("libc.so", "open");
        if (openPtr) {
            Interceptor.attach(openPtr, {
                onEnter: function(args) {
                    try {
                        this.path = args[0].readUtf8String();
                        if (this.path && (this.path.indexOf("m1.ans") !== -1 || 
                                         this.path.indexOf("m3.ans") !== -1)) {
                            this.isAnsFile = true;
                            console.log("[ANS文件] 打开: " + this.path);
                        }
                    } catch(e) {}
                },
                onLeave: function(result) {
                    if (this.isAnsFile && result.toInt32() > 0) {
                        var fd = result.toInt32();
                        fileDescriptors[fd] = this.path;
                        console.log("[ANS文件] 文件描述符: " + fd + " => " + this.path);
                    }
                }
            });
        }
        
        // 监控文件读取
        var readPtr = Module.findExportByName("libc.so", "read");
        if (readPtr) {
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    var fd = args[0].toInt32();
                    if (fileDescriptors[fd]) {
                        this.fd = fd;
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                        this.path = fileDescriptors[fd];
                    }
                },
                onLeave: function(result) {
                    if (this.fd && result.toInt32() > 0) {
                        var bytesRead = result.toInt32();
                        
                        // 记录ANS文件数据在内存中的位置
                        if (this.path) {
                            ansFileBuffers[this.buffer] = {
                                path: this.path,
                                size: bytesRead,
                                time: Date.now()
                            };
                            
                            console.log("[ANS读取] " + this.path.split("/").pop() + 
                                       " 读取 " + bytesRead + " 字节到地址 " + this.buffer);
                            
                            // 检查与手势的时间关系
                            if (lastGestureTime) {
                                var timeSinceGesture = (Date.now() - lastGestureTime) / 1000;
                                console.log("[ANS关联] 手势后 " + timeSinceGesture.toFixed(2) + 
                                          " 秒读取文件数据");
                            }
                            
                            // 为避免内存泄漏，在一段时间后清除记录
                            var currentBuffer = this.buffer;
                            setTimeout(function() {
                                delete ansFileBuffers[currentBuffer];
                            }, 10000); // 10秒后清除
                        }
                    }
                }
            });
        }
        
        console.log("[ANS文件] 基础监控已设置");
    }
    
    // 设置精确钩子
    function setupPreciseHooks() {
        // 1. 首先找到libamapnsq.so
        libamapnsq = Process.findModuleByName("libamapnsq.so");
        if (!libamapnsq) {
            console.log("[ANS解析器-V40] 未找到libamapnsq.so，等待模块加载...");
            
            // 尝试延迟再次查找
            setTimeout(function() {
                libamapnsq = Process.findModuleByName("libamapnsq.so");
                if (libamapnsq) {
                    console.log("[ANS解析器-V40] 已找到libamapnsq.so: " + libamapnsq.base);
                    hookParserFunction();
                } else {
                    console.log("[ANS解析器-V40] 仍未找到libamapnsq.so，改为等待其他模块加载事件...");
                }
            }, 3000);
            return;
        }
        
        console.log("[ANS解析器-V40] 已找到libamapnsq.so: " + libamapnsq.base);
        hookParserFunction();
    }
    
    // 钩住核心解析函数
    function hookParserFunction() {
        // 基于IDA分析找到的sub_C654函数
        var parserFuncOffset = 0xC654; // 从IDA Pro截图中看到的偏移量
        var parserFuncAddr = libamapnsq.base.add(parserFuncOffset);
        
        console.log("[ANS解析器-V40] 目标函数地址: " + parserFuncAddr);
        
        Interceptor.attach(parserFuncAddr, {
            onEnter: function(args) {
                this.a1 = args[0];
                this.a2 = args[1];
                this.a3 = args[2];
                this.a4 = args[3];
                this.a5 = args[4];
                
                console.log("\n[ANS解析] *** 调用解析函数 sub_C654 ***");
                console.log("[ANS解析] 参数1 (a1): " + this.a1);
                console.log("[ANS解析] 参数2 (a2): " + this.a2);
                console.log("[ANS解析] 参数3 (a3): " + this.a3);
                console.log("[ANS解析] 参数4 (a4): " + this.a4);
                console.log("[ANS解析] 参数5 (a5): " + this.a5.toInt32());
                
                // 分析调用上下文
                try {
                    // 尝试读取参数3指向的内容(可能是大小或其他控制值)
                    if (this.a3 && !this.a3.isNull()) {
                        var value = Memory.readInt(this.a3);
                        console.log("[ANS解析] 参数3指向的值: " + value);
                    }
                    
                    // 检查a2是否可能是一个缓冲区，如果是，打印前几个字节
                    try {
                        if (this.a2 && !this.a2.isNull()) {
                            console.log("[ANS解析] 参数2(可能的缓冲区)前16字节:");
                            console.log(hexdump(this.a2, {length: 16}));
                        }
                    } catch(e) {}
                    
                    // 记录调用栈
                    var backtrace = Thread.backtrace(this.context, Backtracer.ACCURATE);
                    if (backtrace && backtrace.length > 0) {
                        console.log("[ANS解析] 调用栈:");
                        for (var i = 0; i < Math.min(8, backtrace.length); i++) {
                            var symbol = DebugSymbol.fromAddress(backtrace[i]);
                            var module = Process.findModuleByAddress(backtrace[i]);
                            console.log("  " + i + ": " + (symbol.name || "???") + 
                                       " (" + (module ? module.name : "unknown") + ")");
                        }
                    }
                } catch(e) {
                    console.log("[ANS解析] 分析参数时出错: " + e);
                }
            },
            onLeave: function(retval) {
                console.log("[ANS解析] 函数返回值: " + retval);
                console.log("[ANS解析] *** 结束调用 ***\n");
            }
        });
        
        // 钩住IDA分析中看到的sub_C39C函数，它在sub_C654中被调用
        var helperFuncOffset = 0xC39C; 
        var helperFuncAddr = libamapnsq.base.add(helperFuncOffset);
        
        console.log("[ANS解析器-V40] 辅助函数地址: " + helperFuncAddr);
        
        Interceptor.attach(helperFuncAddr, {
            onEnter: function(args) {
                console.log("[ANS辅助] 调用辅助函数sub_C39C");
            },
            onLeave: function(retval) {
                console.log("[ANS辅助] 辅助函数返回: " + retval);
            }
        });
        
        // 监控uncompress函数
        var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
        if (uncompressPtr) {
            console.log("[ANS解析器-V40] 找到uncompress函数: " + uncompressPtr);
            
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    this.destBuffer = args[0];
                    this.destLenPtr = args[1];
                    this.sourceBuffer = args[2];
                    this.sourceLen = args[3].toInt32();
                    
                    // 记录解压前参数
                    console.log("[ANS解压] uncompress(dest=" + this.destBuffer + 
                               ", destLen=" + this.destLenPtr.readULong() + 
                               ", source=" + this.sourceBuffer + 
                               ", sourceLen=" + this.sourceLen + ")");
                    
                    // 检查源数据
                    if (this.sourceBuffer && this.sourceLen > 0) {
                        try {
                            var firstBytes = Memory.readByteArray(this.sourceBuffer, Math.min(4, this.sourceLen));
                            var view = new Uint8Array(firstBytes);
                            
                            // 检查是否有zlib压缩标记
                            if (view[0] === 0x78 && view[1] === 0x9c) {
                                console.log("[ANS解压] 检测到zlib头部: 78 9c");
                            }
                        } catch(e) {}
                    }
                },
                onLeave: function(retval) {
                    var ret = retval.toInt32();
                    if (ret === 0) {
                        var decompressedSize = this.destLenPtr.readULong();
                        console.log("[ANS解压] 解压成功! 大小: " + decompressedSize + " 字节");
                        
                        // 打印解压后数据的前32字节
                        if (this.destBuffer && !this.destBuffer.isNull()) {
                            console.log("[ANS解压] 解压后数据:");
                            console.log(hexdump(this.destBuffer, {
                                length: Math.min(32, decompressedSize)
                            }));
                            
                            // 尝试解析解压后的数据结构
                            try {
                                var headerData = Memory.readByteArray(this.destBuffer, Math.min(8, decompressedSize));
                                var headerView = new Uint8Array(headerData);
                                var headerHex = "";
                                
                                for (var i = 0; i < headerData.byteLength; i++) {
                                    var byteHex = headerView[i].toString(16);
                                    if (byteHex.length < 2) byteHex = "0" + byteHex;
                                    headerHex += byteHex + " ";
                                }
                                
                                console.log("[ANS解析] 数据块头部: " + headerHex.trim());
                                
                                // 尝试识别块类型
                                if (headerView[0] === 0x0d && headerView[1] === 0x00) {
                                    console.log("[ANS解析] 识别为头部/索引块类型");
                                } else if (headerView[0] === 0x00 && headerView[1] === 0x00 && 
                                           headerView[2] === 0x26) {
                                    console.log("[ANS解析] 识别为数据块类型 #" + headerView[3]);
                                }
                            } catch(e) {}
                        }
                    } else {
                        console.log("[ANS解压] 解压失败，返回码: " + ret);
                    }
                }
            });
        }
        
        console.log("[ANS解析器-V40] 核心函数钩子设置完成");
    }
    
    // 设置手势监控
    function setupGestureTracking() {
        try {
            Java.perform(function() {
                try {
                    var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                    if (GLMapEngine.addGestureMessage) {
                        GLMapEngine.addGestureMessage.implementation = function(engineId, gestureMessage) {
                            try {
                                if (gestureMessage) {
                                    var type = gestureMessage.getType();
                                    lastGestureTime = Date.now();
                                    
                                    if (type == 0) { // 移动手势
                                        var moveMsg = Java.cast(gestureMessage, 
                                                              Java.use("com.autonavi.ae.gmap.MoveGestureMapMessage"));
                                        var dx = moveMsg.mTouchDeltaX.value;
                                        var dy = moveMsg.mTouchDeltaY.value;
                                        
                                        if (Math.abs(dx) > 10 || Math.abs(dy) > 10) {
                                            console.log("[ANS手势] 移动: dx=" + dx + ", dy=" + dy);
                                        }
                                    } else if (type == 1) { // 缩放手势
                                        try {
                                            var scaleMsg = Java.cast(gestureMessage, 
                                                                   Java.use("com.autonavi.ae.gmap.ScaleGestureMapMessage"));
                                            var factor = scaleMsg.mScaleFactor.value;
                                            console.log("[ANS手势] 缩放: factor=" + factor.toFixed(2));
                                        } catch(e) {}
                                    }
                                }
                            } catch(e) {}
                            return this.addGestureMessage(engineId, gestureMessage);
                        };
                        console.log("[ANS解析器-V40] 手势监控设置完成");
                    }
                } catch(e) {
                    console.log("[ANS解析器-V40] 设置手势监控时出错: " + e);
                }
            });
        } catch(e) {
            console.log("[ANS解析器-V40] Java环境尚未就绪: " + e);
        }
    }
    
    console.log("[ANS解析器-V40] 设置完成，请操作地图...");
})();


// ## 使用说明

// 1. 将此脚本保存为`ans_frida_accurate.js`
// 2. 使用以下命令执行:
//    ```
//    frida -U -f com.autonavi.minimap -l ans_frida_accurate.js --no-pause
//    ```
//    或者通过Frida客户端注入

// 3. 在地图上进行平移和缩放操作，触发ANS文件的读取和解析

// 该脚本基于IDA Pro分析，直接钩住了`sub_C654`函数，这是ANS文件解析的核心函数。它将提供更精确的解析逻辑信息，包括函数参数、返回值、调用栈和数据结构分析。
