/******************************************************************************
 * (ES5-兼容) Frida脚本 (V14 - “修正之路”最终版)
 *
 * 此版本基于V13的成功日志，只修正了一个由于我的疏忽导致的、致命的API拼写错误。
 * 错误：`readUlong` -> 正确：`readULong`
 * 
 * 我为之前的低级错误致以最诚挚的歉意。此版本旨在最终解决问题。
 * 使用方法:
 * frida -U -f com.autonavi.minimap -l extract_ans_parsing_logic.js --no-pause
 ******************************************************************************/

(function() {

    console.log("[ANS解析器-V14] 正在启动 (“修正之路”最终版)...");

    /**
     * 核心Hook逻辑: 监控zlib的uncompress函数
     * 函数原型: int uncompress(Bytef *dest, uLongf *destLen, const Bytef *source, uLong sourceLen);
     */
    function hookZlibUncompress() {
        var uncompressPtr = Module.findExportByName("libz.so", "uncompress");

        if (uncompressPtr) {
            console.log("[ANS解析器-V14] 找到核心解压函数 zlib.uncompress! 地址: " + uncompressPtr);
            
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    this.destBuffer = args[0];
                    this.destLenPtr = args[1];
                    this.sourceBuffer = args[2];
                    this.sourceLen = args[3].toInt32();

                    console.log("\n[zlib解压] >>>>> 调用 uncompress <<<<<");
                    console.log("[zlib解压] 压缩数据大小: " + this.sourceLen + " 字节");
                    
                    if (this.sourceBuffer && !this.sourceBuffer.isNull() && this.sourceLen > 0) {
                        console.log("[zlib解压] 压缩数据 (前64字节):");
                        try {
                            console.log(hexdump(this.sourceBuffer, { length: Math.min(64, this.sourceLen) }));
                        } catch(e) {}
                    }
                },

                onLeave: function(retval) {
                    if (retval.toInt32() === 0) {
                        // 关键修正：将 readUlong 修正为 readULong (大写L)
                        var decompressedSize = this.destLenPtr.readULong();
                        console.log("[zlib解压] 解压成功! 输出大小: " + decompressedSize + " 字节");

                        if (this.destBuffer && !this.destBuffer.isNull() && decompressedSize > 0) {
                            console.log("[zlib解压] 解压后数据 (前64字节):");
                            try {
                                console.log(hexdump(this.destBuffer, { length: Math.min(64, decompressedSize) }));
                            } catch(e) {}
                        }
                    } else {
                        console.log("[zlib解压] 解压失败，返回码: " + retval.toInt32());
                    }
                    console.log("[zlib解压] >>>>> 完成调用 <<<<<\n");
                }
            });

        } else {
            console.log("[!!!][ANS解析器-V14] 致命错误: 未能在libz.so中找到 uncompress 函数！");
        }
    }

    // --- 主执行逻辑 ---
    setTimeout(function() {
        Java.perform(function() {
            console.log("[ANS解析器-V14] Java环境就绪，准备Hook zlib...");
            hookZlibUncompress();
            console.log("[ANS解析器-V14] 设置完成。请与地图交互以查看zlib解压日志。");
        });
    }, 1500);

})();