// 精确mmap分析脚本 - 专门分析大文件映射的内容
// 目标: 检查大文件映射是否包含AM-zlib数据

console.log("[精确mmap分析] 开始分析大文件映射内容...");
console.log("[目标] 检查映射文件的实际内容和结构");

// 精确分析状态
var preciseAnalysis = {
    largeMappings: [],        // 大文件映射记录
    amZlibFound: [],          // 发现的AM-zlib数据
    mappingAnalysis: [],      // 映射内容分析
    startTime: Date.now()
};

// 安全的字节数组处理
function safeByteArrayToHex(byteArray, maxLen) {
    var hexBytes = [];
    var len = Math.min(maxLen || 16, byteArray.length);
    for (var i = 0; i < len; i++) {
        hexBytes.push(('0' + byteArray[i].toString(16)).slice(-2));
    }
    return hexBytes.join(' ');
}

// 检查是否是AM-zlib数据
function isAMZlibData(data) {
    if (data.length >= 8) {
        // AM-zlib魔数: 41 4d 2d 7a 6c 69 62 00
        return data[0] === 0x41 && data[1] === 0x4d && data[2] === 0x2d && 
               data[3] === 0x7a && data[4] === 0x6c && data[5] === 0x69 && 
               data[6] === 0x62 && data[7] === 0x00;
    }
    return false;
}

// 深度分析映射内容
function analyzeMapping(address, size, fd) {
    console.log("[深度分析映射] 地址=" + address + ", 大小=" + (size/1024/1024).toFixed(1) + "MB, fd=" + fd);
    
    try {
        // 检查映射开头
        var headerData = address.readByteArray(64);
        var header = new Uint8Array(headerData);
        console.log("  开头64字节: " + safeByteArrayToHex(header, 32));
        
        if (isAMZlibData(header)) {
            console.log("  [发现] 开头就是AM-zlib数据!");
            preciseAnalysis.amZlibFound.push({
                address: address.toString(),
                offset: 0,
                fd: fd,
                size: size
            });
            return;
        }
        
        // 搜索AM-zlib魔数在映射中的位置
        var searchSize = Math.min(size, 1024 * 1024); // 搜索前1MB
        var found = false;
        
        for (var offset = 0; offset < searchSize - 8; offset += 4096) { // 每4KB检查一次
            try {
                var data = address.add(offset).readByteArray(64);
                var chunk = new Uint8Array(data);
                
                if (isAMZlibData(chunk)) {
                    console.log("  [发现AM-zlib] 偏移=" + offset + " (0x" + offset.toString(16) + ")");
                    console.log("    数据: " + safeByteArrayToHex(chunk, 16));
                    
                    // 分析AM-zlib头部
                    var version = chunk[8];
                    var flags = chunk[9];
                    var geometryType = chunk[10];
                    var geometryFlags = chunk[11];
                    
                    console.log("    版本: " + version + " (0x" + version.toString(16) + ")");
                    console.log("    几何类型: " + geometryType + " (0x" + geometryType.toString(16) + ")");
                    console.log("    几何标志: " + geometryFlags + " (0x" + geometryFlags.toString(16) + ")");
                    
                    preciseAnalysis.amZlibFound.push({
                        address: address.add(offset).toString(),
                        offset: offset,
                        fd: fd,
                        size: size,
                        version: version,
                        geometryType: geometryType,
                        geometryFlags: geometryFlags
                    });
                    
                    found = true;
                    break;
                }
            } catch (e) {
                // 忽略读取错误，继续搜索
            }
        }
        
        if (!found) {
            console.log("  [未发现] 在前1MB中未找到AM-zlib数据");
            
            // 检查是否有其他已知格式
            if (header[0] === 0x44 && header[1] === 0x49 && header[2] === 0x43 && header[3] === 0x45) {
                console.log("  [发现] DICE格式数据");
            } else if (header[0] === 0x78 && header[1] === 0x9c) {
                console.log("  [发现] 标准zlib数据");
            } else {
                console.log("  [未知] 未知格式，头部: " + safeByteArrayToHex(header, 16));
            }
        }
        
    } catch (e) {
        console.log("  [错误] 分析映射失败: " + e.message);
    }
}

// Hook mmap - 专注于大文件映射的详细分析
console.log("[1] Hook mmap，详细分析大文件映射...");

try {
    var libc = Process.getModuleByName("libc.so");
    var mmapPtr = libc.getExportByName("mmap");
    
    Interceptor.attach(mmapPtr, {
        onEnter: function(args) {
            this.addr = args[0];
            this.length = args[1].toInt32();
            this.prot = args[2].toInt32();
            this.flags = args[3].toInt32();
            this.fd = args[4].toInt32();
            this.offset = args[5].toInt32();
        },
        onLeave: function(retval) {
            var mappedAddr = retval;
            var length = this.length;
            var fd = this.fd;
            var offset = this.offset;
            
            // 只关注大文件映射（可能是.ans文件）
            if (!mappedAddr.equals(ptr(-1)) && length > 20000000) { // 20MB+
                console.log("[大文件映射] fd=" + fd + ", 大小=" + (length/1024/1024).toFixed(1) + "MB");
                console.log("  地址=" + mappedAddr + ", 偏移=" + offset);
                
                preciseAnalysis.largeMappings.push({
                    address: mappedAddr.toString(),
                    fd: fd,
                    size: length,
                    offset: offset,
                    timestamp: Date.now()
                });
                
                // 延迟分析，避免阻塞
                setTimeout(function() {
                    analyzeMapping(mappedAddr, length, fd);
                }, 1000);
            }
        }
    });
    
    console.log("[✓] mmap Hook设置成功");
} catch (e) {
    console.log("[✗] mmap Hook失败: " + e.message);
}

// 2. Hook文件读取 - 轻量级监控
console.log("[2] 轻量级文件读取监控...");

try {
    var readPtr = libc.getExportByName("read");
    var readCount = 0;
    
    Interceptor.attach(readPtr, {
        onEnter: function(args) {
            this.fd = args[0].toInt32();
            this.count = args[2].toInt32();
        },
        onLeave: function(retval) {
            var bytesRead = retval.toInt32();
            readCount++;
            
            // 每1000次读取输出一次统计
            if (readCount % 1000 === 0) {
                console.log("[读取统计] 总读取次数: " + readCount);
            }
            
            // 只检查大文件读取
            if (bytesRead > 1000000) { // 1MB+
                console.log("[大文件读取] fd=" + this.fd + ", 大小=" + (bytesRead/1024/1024).toFixed(1) + "MB");
            }
        }
    });
    
    console.log("[✓] 文件读取监控设置成功");
} catch (e) {
    console.log("[✗] 文件读取监控失败: " + e.message);
}

// 3. 定期输出精确分析报告
setInterval(function() {
    var runtime = Math.floor((Date.now() - preciseAnalysis.startTime) / 1000);
    
    console.log("\n[精确分析报告] ==========================================");
    console.log("运行时间: " + runtime + "s");
    console.log("");
    
    console.log("大文件映射统计:");
    console.log("  映射数量: " + preciseAnalysis.largeMappings.length + " 个");
    
    var totalSize = 0;
    for (var i = 0; i < preciseAnalysis.largeMappings.length; i++) {
        var mapping = preciseAnalysis.largeMappings[i];
        totalSize += mapping.size;
        console.log("  映射" + (i+1) + ": fd=" + mapping.fd + ", " + 
                   (mapping.size/1024/1024).toFixed(1) + "MB, 偏移=" + mapping.offset);
    }
    console.log("  总大小: " + (totalSize/1024/1024).toFixed(1) + "MB");
    
    console.log("");
    console.log("AM-zlib发现:");
    console.log("  发现数量: " + preciseAnalysis.amZlibFound.length + " 个");
    
    for (var i = 0; i < preciseAnalysis.amZlibFound.length; i++) {
        var found = preciseAnalysis.amZlibFound[i];
        console.log("  发现" + (i+1) + ": fd=" + found.fd + ", 偏移=" + found.offset);
        console.log("    版本=" + found.version + ", 几何类型=0x" + found.geometryType.toString(16));
    }
    
    if (preciseAnalysis.amZlibFound.length > 0) {
        console.log("");
        console.log("结论: 成功发现AM-zlib数据!");
    } else if (preciseAnalysis.largeMappings.length > 0) {
        console.log("");
        console.log("结论: 发现大文件映射，但未找到AM-zlib数据");
        console.log("建议: 可能需要检查更大的偏移范围或不同的搜索策略");
    }
    
    console.log("===============================================\n");
}, 30000);

console.log("[精确分析] 脚本已启动...");
console.log("[目标] 精确分析大文件映射内容");
console.log("[优化] 减少Hook频率，避免性能问题");
console.log("[提示] 请打开地图并移动以触发文件映射");
