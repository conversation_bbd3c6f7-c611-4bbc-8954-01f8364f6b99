/******************************************************************************
 * (ES5-兼容) Frida脚本: ans_trace_parser_chain.js
 *
 * 目标: 追踪从高层协调函数到具体解析函数的调用链，捕获原始数据。
 *
 * 核心策略:
 * 1. 同时 Hook sub_5C394 (协调者) 和 sub_10F88 (DICE-AM块解析者)。
 * 2. 验证调用顺序，确认是前者调用后者。
 * 3. 在 sub_10F88 被调用时，捕获并 hexdump 其输入参数，这极有可能是
 *    最原始的、解压后的数据缓冲区。
 *
 * 使用方法:
 * frida -U -f com.autonavi.minimap -l ans_trace_parser_chain.js --no-pause
 ******************************************************************************/

(function() {
    'use strict';

    console.log("[解析链追踪] 脚本启动中... (ES5 兼容)");

    var hooked = false;
    var checkModuleInterval = setInterval(function() {
        if (hooked) {
            clearInterval(checkModuleInterval);
            return;
        }

        var libamapnsqModule = Process.findModuleByName("libamapnsq.so");
        if (libamapnsqModule) {
            hooked = true;
            clearInterval(checkModuleInterval);
            
            Java.perform(function() {
                console.log("[解析链追踪] Java环境就绪。");
                console.log("[解析链追踪] 目标模块 libamapnsq.so 已加载，基地址: " + libamapnsqModule.base);
                hookFunctions(libamapnsqModule);
            });
        }
    }, 500);

    function hookFunctions(module) {
        var coordinatorAddr = module.base.add(0x5C394);
        var parserAddr = module.base.add(0x10F88);

        console.log("[解析链追踪] Hooking 协调者 (sub_5C394) at: " + coordinatorAddr);
        Interceptor.attach(coordinatorAddr, {
            onEnter: function(args) {
                console.log("\n-------------------------------------------------------------");
                console.log("[协调者] sub_5C394 进入");
            },
            onLeave: function(retval) {
                 console.log("[协调者] sub_5C394 退出");
                 console.log("-------------------------------------------------------------\n");
            }
        });

        console.log("[解析链追踪] Hooking 具体解析者 (sub_10F88) at: " + parserAddr);
        Interceptor.attach(parserAddr, {
            onEnter: function(args) {
                console.log("\n*************************************************************");
                console.log("********** 解析器 sub_10F88 命中! (DICE-AM?) **********");
                console.log("*************************************************************");

                var arg0 = args[0];
                var arg1 = args[1];

                console.log("--- sub_10F88 输入参数 ---");
                console.log("  arg0 (数据缓冲区?): " + arg0);
                console.log("  arg1 (上下文?): " + arg1);
                console.log("------------------------------------\n");

                console.log("--- Hexdump arg0 (解压后的原始数据块!) ---");
                try {
                    console.log(hexdump(arg0, {
                        length: 256, // 打印足够多的数据以供分析
                        header: true,
                        ansi: false
                    }));
                } catch (e) {
                    console.log("  无法读取 arg0 指向的内存: " + e.message);
                }
                console.log("*************************************************************\n");
            }
        });

        console.log("\n[解析链追踪] Hook 设置完毕。请操作App以触发。");
    }

})();