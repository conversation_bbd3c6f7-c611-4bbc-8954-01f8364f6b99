// ANS文件处理全链路跟踪脚本 - 从手势到底层解析
// 专注于跟踪从Java层的手势事件到Native层的ANS文件解析和渲染流程

(function() {
    console.log("[ANS全链路分析] 启动...");

    // 全局变量
    var libamapnsq = null;
    var libamapr = null;
    var libamapmain = null;
    var libz = null;
    var fileOperations = {};
    var callDepth = 0;
    var callStack = [];
    var lastGestureTime = 0;
    var lastFileAccessTime = 0;
    var lastDecompressTime = 0;
    var nativeGestureAddr = null;

    // 工具函数 - 兼容ES5的padStart
    function padZero(str, len) {
        str = String(str);
        while (str.length < len) {
            str = '0' + str;
        }
        return str;
    }

    // 延迟执行，确保应用完全启动
    setTimeout(function() {
        try {
            setupHooks();
        } catch (e) {
            console.log("[ANS全链路分析] 设置钩子失败: " + e);
        }
    }, 5000);

    function setupHooks() {
        // 查找关键库
        libamapnsq = Process.findModuleByName("libamapnsq.so");
        libamapr = Process.findModuleByName("libamapr.so");
        libamapmain = Process.findModuleByName("libamapmain.so");
        libz = Process.findModuleByName("libz.so");

        if (!libamapnsq || !libamapr || !libamapmain || !libz) {
            console.log("[ANS全链路分析] 未找到所有关键库");
            return;
        }

        console.log("[ANS全链路分析] 找到所有关键库:");
        console.log("- libamapnsq.so: " + libamapnsq.base);
        console.log("- libamapr.so: " + libamapr.base);
        console.log("- libamapmain.so: " + libamapmain.base);
        console.log("- libz.so: " + libz.base);

        // 设置各种钩子
        setupFileOperationHooks();
        setupANSParserHooks();
        setupJavaHooks();

        // 延迟设置更复杂的钩子，避免应用启动时崩溃
        setTimeout(function() {
            findNativeAddMapGestureMsg();
            setupRenderHooks();
        }, 3000);
    }

    // 尝试找到nativeAddMapGestureMsg函数的地址
    function findNativeAddMapGestureMsg() {
        console.log("[ANS全链路分析] 尝试查找nativeAddMapGestureMsg函数...");
        
        try {
            // 在libamapr.so中查找"nativeAddMapGestureMsg"字符串
            var pattern = "6e 61 74 69 76 65 41 64 64 4d 61 70 47 65 73 74 75 72 65 4d 73 67"; // "nativeAddMapGestureMsg"的十六进制
            
            Memory.scan(libamapr.base, libamapr.size, pattern, {
                onMatch: function(address, size) {
                    console.log("[ANS全链路分析] 找到字符串: nativeAddMapGestureMsg @ " + address);
                    
                    // 查找引用这个字符串的地方
                    Memory.scan(libamapr.base, libamapr.size, address.toString().split("0x")[1], {
                        onMatch: function(refAddress, refSize) {
                            console.log("[ANS全链路分析] 找到对字符串的引用 @ " + refAddress);
                            
                            // 向上查找可能的函数开始
                            var potentialFuncStart = refAddress.sub(0x100);
                            var funcSize = 0x200;
                            
                            console.log("[ANS全链路分析] 搜索函数入口点: " + potentialFuncStart + " - " + potentialFuncStart.add(funcSize));
                            
                            // 查找常见的ARM64函数序言
                            Memory.scan(potentialFuncStart, funcSize, "F? 4? BD A9", { // 常见的ARM64函数序言
                                onMatch: function(funcAddress, funcSize) {
                                    console.log("[ANS全链路分析] 可能的函数入口点 @ " + funcAddress);
                                    
                                    // 设置钩子
                                    try {
                                        nativeGestureAddr = funcAddress;
                                        Interceptor.attach(funcAddress, {
                                            onEnter: function(args) {
                                                console.log("[Native手势] 调用可能的nativeAddMapGestureMsg @ " + funcAddress);
                                                console.log("[Native手势] 参数: " + 
                                                    "arg0=" + args[0] + 
                                                    ", arg1=" + args[1] + 
                                                    ", arg2=" + args[2] + 
                                                    ", arg3=" + args[3]);
                                                
                                                // 记录调用栈
                                                console.log("[Native手势] 调用栈:");
                                                var bt = Thread.backtrace(this.context, Backtracer.ACCURATE);
                                                bt.forEach(function(addr, i) {
                                                    console.log("\t" + i + ": " + addr + " " + 
                                                        DebugSymbol.fromAddress(addr));
                                                });
                                                
                                                lastGestureTime = Date.now();
                                            },
                                            onLeave: function(retval) {
                                                console.log("[Native手势] 返回值: " + retval);
                                            }
                                        });
                                        console.log("[ANS全链路分析] 成功钩住可能的nativeAddMapGestureMsg函数");
                                        return "stop";
                                    } catch (e) {
                                        console.log("[ANS全链路分析] 钩住函数失败: " + e);
                                    }
                                },
                                onComplete: function() {
                                    console.log("[ANS全链路分析] 函数入口点搜索完成");
                                }
                            });
                            return "stop";
                        },
                        onComplete: function() {
                            console.log("[ANS全链路分析] 字符串引用搜索完成");
                        }
                    });
                    return "stop";
                },
                onComplete: function() {
                    console.log("[ANS全链路分析] 字符串搜索完成");
                }
            });
        } catch (e) {
            console.log("[ANS全链路分析] 查找nativeAddMapGestureMsg函数失败: " + e);
        }
    }

    function setupFileOperationHooks() {
        console.log("[ANS全链路分析] 设置文件操作钩子...");
        
        // 钩住open函数
        Interceptor.attach(Module.findExportByName(null, "open"), {
            onEnter: function(args) {
                this.path = Memory.readUtf8String(args[0]);
                this.flags = args[1].toInt32();
                this.time = Date.now();
                
                // 只关注ANS文件
                if (this.path && this.path.endsWith(".ans")) {
                    console.log("[文件] 打开ANS文件: " + this.path + ", flags: " + this.flags);
                    lastFileAccessTime = this.time;
                }
            },
            onLeave: function(retval) {
                if (this.path && this.path.endsWith(".ans")) {
                    var fd = retval.toInt32();
                    if (fd >= 0) {
                        fileOperations[fd] = {
                            path: this.path,
                            time: this.time,
                            reads: []
                        };
                        console.log("[文件] 成功打开ANS文件, fd: " + fd);
                    } else {
                        console.log("[文件] 打开ANS文件失败: " + this.path);
                    }
                }
            }
        });

        // 钩住read函数
        Interceptor.attach(Module.findExportByName(null, "read"), {
            onEnter: function(args) {
                this.fd = args[0].toInt32();
                this.buffer = args[1];
                this.size = args[2].toInt32();
                this.fileOp = fileOperations[this.fd];
                
                // 只关注已跟踪的文件描述符
                if (this.fileOp && this.fileOp.path.endsWith(".ans")) {
                    console.log("[文件] 读取ANS文件: fd=" + this.fd + ", size=" + this.size);
                }
            },
            onLeave: function(retval) {
                var bytesRead = retval.toInt32();
                if (bytesRead > 0 && this.fileOp && this.fileOp.path.endsWith(".ans")) {
                    var data = Memory.readByteArray(this.buffer, bytesRead < 32 ? bytesRead : 32);
                    var dataView = new Uint8Array(data);
                    var hexData = "";
                    for (var i = 0; i < dataView.length; i++) {
                        hexData += padZero(dataView[i].toString(16), 2) + " ";
                    }
                    
                    console.log("[文件] 读取ANS数据: " + bytesRead + " 字节, 头部: " + hexData);
                    
                    this.fileOp.reads.push({
                        time: Date.now(),
                        size: bytesRead,
                        buffer: this.buffer,
                        header: hexData
                    });
                    
                    // 检查是否有DICE-AM头部
                    if (bytesRead >= 7) {
                        var header = Memory.readUtf8String(this.buffer, 7);
                        if (header === "DICE-AM") {
                            console.log("[文件] 检测到DICE-AM头部!");
                        }
                    }
                    
                    lastFileAccessTime = Date.now();
                }
            }
        });

        // 钩住close函数
        Interceptor.attach(Module.findExportByName(null, "close"), {
            onEnter: function(args) {
                this.fd = args[0].toInt32();
                this.fileOp = fileOperations[this.fd];
                
                if (this.fileOp && this.fileOp.path.endsWith(".ans")) {
                    console.log("[文件] 关闭ANS文件: fd=" + this.fd + ", path=" + this.fileOp.path);
                    delete fileOperations[this.fd];
                }
            }
        });
    }

    function setupANSParserHooks() {
        console.log("[ANS全链路分析] 设置ANS解析钩子...");
        
        // 钩住sub_C654函数 - ANS解析核心函数
        Interceptor.attach(libamapnsq.base.add(0xC654), {
            onEnter: function(args) {
                callDepth++;
                this.a1 = args[0];
                this.a2 = args[1];
                this.a3 = args[2];
                this.a4 = args[3];
                this.a5 = args[4].toInt32();
                this.startTime = Date.now();
                
                // 读取a3指向的大小值
                var size = Memory.readInt(this.a3);
                
                var indent = "  ".repeat(callDepth);
                console.log(indent + "[解析] 调用解析函数 sub_C654: a1=" + this.a1 + 
                          ", a2=" + this.a2 + ", a3=" + this.a3 + 
                          " (size=" + size + "), a4=" + this.a4 + 
                          ", a5=" + this.a5);
                
                // 检查是否有源数据
                if (this.a2) {
                    var sourceData = Memory.readByteArray(this.a2, 16);
                    var dataView = new Uint8Array(sourceData);
                    var hexData = "";
                    for (var i = 0; i < dataView.length; i++) {
                        hexData += padZero(dataView[i].toString(16), 2) + " ";
                    }
                    console.log(indent + "[解析] 源数据头部: " + hexData);
                    
                    // 检查是否为zlib压缩数据 (78 9C 头)
                    if (dataView[0] === 0x78 && dataView[1] === 0x9C) {
                        console.log(indent + "[解析] 检测到zlib压缩数据");
                    }
                }
                
                callStack.push("sub_C654");
                
                // 记录调用栈
                var timeSinceGesture = Date.now() - lastGestureTime;
                if (timeSinceGesture < 1000) {
                    console.log(indent + "[解析] 距离上次手势: " + timeSinceGesture + "ms");
                    console.log(indent + "[解析] 调用栈:");
                    var bt = Thread.backtrace(this.context, Backtracer.ACCURATE);
                    bt.forEach(function(addr, i) {
                        console.log(indent + "\t" + i + ": " + addr + " " + 
                            DebugSymbol.fromAddress(addr));
                    });
                }
            },
            onLeave: function(retval) {
                var indent = "  ".repeat(callDepth);
                console.log(indent + "[解析] 解析函数返回: 0x" + retval.toString(16));
                
                // 读取a3指向的大小值
                if (this.a3) {
                    var size = Memory.readInt(this.a3);
                    console.log(indent + "[解析] 解压后大小: " + size);
                }
                
                // 检查解压后的数据
                if (this.a2) {
                    var decompressedData = Memory.readByteArray(this.a2, 32);
                    var dataView = new Uint8Array(decompressedData);
                    var hexData = "";
                    for (var i = 0; i < dataView.length; i++) {
                        hexData += padZero(dataView[i].toString(16), 2) + " ";
                    }
                    console.log(indent + "[解析] 解压后数据头部: " + hexData);
                    
                    // 检查是否为DICE-AM头部
                    var header = Memory.readUtf8String(this.a2, 7);
                    if (header === "DICE-AM") {
                        console.log(indent + "[解析] 解压后数据包含DICE-AM头部!");
                    }
                    
                    // 尝试提取中文文本
                    try {
                        var textData = Memory.readByteArray(this.a2, 1024);
                        var dataView = new Uint8Array(textData);
                        var utf8Text = "";
                        for (var i = 0; i < dataView.length; i += 3) {
                            // 简单检测UTF-8中文字符 (0xE4-0xE9 范围)
                            if (i + 2 < dataView.length && 
                                dataView[i] >= 0xE4 && dataView[i] <= 0xE9 &&
                                dataView[i+1] >= 0x80 && dataView[i+1] <= 0xBF &&
                                dataView[i+2] >= 0x80 && dataView[i+2] <= 0xBF) {
                                utf8Text += String.fromCharCode(
                                    ((dataView[i] & 0x0F) << 12) |
                                    ((dataView[i+1] & 0x3F) << 6) |
                                    (dataView[i+2] & 0x3F)
                                );
                            }
                        }
                        if (utf8Text.length > 0) {
                            console.log(indent + "[解析] 可能的中文文本: " + utf8Text);
                        }
                    } catch (e) {
                        // 忽略错误
                    }
                }
                
                var duration = Date.now() - this.startTime;
                console.log(indent + "[解析] 解析耗时: " + duration + "ms");
                lastDecompressTime = Date.now();
                
                callStack.pop();
                callDepth--;
            }
        });
        
        // 钩住sub_C39C函数 - 预处理函数
        Interceptor.attach(libamapnsq.base.add(0xC39C), {
            onEnter: function(args) {
                callDepth++;
                this.a1 = args[0];
                this.a2 = args[1];
                this.a3 = args[2].toInt32();
                
                var indent = "  ".repeat(callDepth);
                console.log(indent + "[解析] 调用预处理函数 sub_C39C: a1=" + this.a1 + 
                          ", a2=" + this.a2 + ", a3=" + this.a3);
                
                callStack.push("sub_C39C");
            },
            onLeave: function(retval) {
                var indent = "  ".repeat(callDepth);
                console.log(indent + "[解析] 预处理函数返回: " + retval);
                
                callStack.pop();
                callDepth--;
            }
        });
        
        // 钩住uncompress函数 - zlib解压缩
        Interceptor.attach(Module.findExportByName("libz.so", "uncompress"), {
            onEnter: function(args) {
                callDepth++;
                this.dest = args[0];
                this.destLen = args[1];
                this.source = args[2];
                this.sourceLen = Memory.readULong(args[3]);
                this.startTime = Date.now();
                
                var indent = "  ".repeat(callDepth);
                console.log(indent + "[解压] 调用uncompress函数: dest=" + this.dest + 
                          ", destLen=" + Memory.readULong(this.destLen) + 
                          ", source=" + this.source + 
                          ", sourceLen=" + this.sourceLen);
                
                // 检查源数据
                if (this.source) {
                    try {
                        var sourceData = Memory.readByteArray(this.source, 16);
                        var dataView = new Uint8Array(sourceData);
                        var hexData = "";
                        for (var i = 0; i < dataView.length; i++) {
                            hexData += padZero(dataView[i].toString(16), 2) + " ";
                        }
                        console.log(indent + "[解压] 源数据头部: " + hexData);
                        
                        // 检查是否为zlib压缩数据 (78 9C 头)
                        if (dataView[0] === 0x78 && dataView[1] === 0x9C) {
                            console.log(indent + "[解压] 确认为zlib压缩数据");
                        }
                    } catch (e) {
                        console.log(indent + "[解压] 读取源数据失败: " + e);
                    }
                }
                
                callStack.push("uncompress");
            },
            onLeave: function(retval) {
                var indent = "  ".repeat(callDepth);
                console.log(indent + "[解压] uncompress返回: 0x" + retval.toString(16));
                
                // 读取destLen的值
                if (this.destLen) {
                    try {
                        var destSize = Memory.readULong(this.destLen);
                        console.log(indent + "[解压] 解压后大小: " + destSize);
                    } catch (e) {
                        console.log(indent + "[解压] 读取解压后大小失败: " + e);
                    }
                }
                
                // 检查解压后的数据
                if (this.dest) {
                    try {
                        var decompressedData = Memory.readByteArray(this.dest, 32);
                        var dataView = new Uint8Array(decompressedData);
                        var hexData = "";
                        for (var i = 0; i < dataView.length; i++) {
                            hexData += padZero(dataView[i].toString(16), 2) + " ";
                        }
                        console.log(indent + "[解压] 解压后数据头部: " + hexData);
                    } catch (e) {
                        console.log(indent + "[解压] 读取解压后数据失败: " + e);
                    }
                }
                
                var duration = Date.now() - this.startTime;
                console.log(indent + "[解压] 解压耗时: " + duration + "ms");
                
                callStack.pop();
                callDepth--;
            }
        });
    }

    function setupJavaHooks() {
        console.log("[ANS全链路分析] 设置Java层钩子...");
        
        Java.perform(function() {
            try {
                // 钩住AMapController类的addGestureMapMessage方法
                var AMapController = Java.use("com.autonavi.ae.gmap.AMapController");
                if (AMapController.addGestureMapMessage) {
                    console.log("[Java] 找到AMapController.addGestureMapMessage方法");
                    AMapController.addGestureMapMessage.implementation = function() {
                        console.log("[Java] 调用AMapController.addGestureMapMessage");
                        console.log("[Java] 参数: " + JSON.stringify(arguments));
                        
                        // 打印调用栈
                        console.log("[Java] Java调用栈:");
                        console.log(Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()));
                        
                        lastGestureTime = Date.now();
                        var result = this.addGestureMapMessage.apply(this, arguments);
                        console.log("[Java] AMapController.addGestureMapMessage返回: " + result);
                        return result;
                    };
                } else {
                    console.log("[Java] 未找到AMapController.addGestureMapMessage方法");
                }
                
                // 钩住GLMapEngine类的nativeAddMapGestureMsg方法
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine.nativeAddMapGestureMsg) {
                    console.log("[Java] 找到GLMapEngine.nativeAddMapGestureMsg方法");
                    
                    // 这是一个native方法，我们不能直接替换它的实现，但可以在调用前后添加日志
                    var nativeAddMapGestureMsg = GLMapEngine.nativeAddMapGestureMsg;
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function() {
                        console.log("[Java->Native] 调用GLMapEngine.nativeAddMapGestureMsg");
                        console.log("[Java->Native] 参数: " + JSON.stringify(arguments));
                        
                        // 打印调用栈
                        console.log("[Java->Native] Java调用栈:");
                        console.log(Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()));
                        
                        lastGestureTime = Date.now();
                        var result = nativeAddMapGestureMsg.apply(this, arguments);
                        console.log("[Java->Native] GLMapEngine.nativeAddMapGestureMsg返回: " + result);
                        return result;
                    };
                } else {
                    console.log("[Java] 未找到GLMapEngine.nativeAddMapGestureMsg方法");
                }
                
                // 尝试钩住其他相关的Java方法
                try {
                    var MapCore = Java.use("com.autonavi.amap.mapcore.MapCore");
                    if (MapCore.nativeRender) {
                        console.log("[Java] 找到MapCore.nativeRender方法");
                        MapCore.nativeRender.implementation = function() {
                            var timeSinceGesture = Date.now() - lastGestureTime;
                            var timeSinceFileAccess = Date.now() - lastFileAccessTime;
                            var timeSinceDecompress = Date.now() - lastDecompressTime;
                            
                            // 只在与手势、文件访问或解压缩时间接近时记录
                            if (timeSinceGesture < 1000 || timeSinceFileAccess < 1000 || timeSinceDecompress < 1000) {
                                console.log("[Java] 调用MapCore.nativeRender (距离上次手势: " + 
                                          timeSinceGesture + "ms, 距离上次文件访问: " + 
                                          timeSinceFileAccess + "ms, 距离上次解压缩: " + 
                                          timeSinceDecompress + "ms)");
                            }
                            
                            var result = this.nativeRender.apply(this, arguments);
                            return result;
                        };
                    }
                } catch (e) {
                    console.log("[Java] 钩住MapCore失败: " + e);
                }
                
            } catch (e) {
                console.log("[Java] 设置Java钩子失败: " + e);
            }
        });
    }

    function setupRenderHooks() {
        console.log("[ANS全链路分析] 设置渲染钩子...");
        
        // 尝试在libamapr.so中找到渲染相关的函数
        try {
            // 可能的渲染函数名称
            var renderFunctionNames = [
                "dice::CMapRenderSystem::init",
                "dice::CAnAmapController::doRenderASync",
                "dice::doEGLRequireMapRender_"
            ];
            
            for (var i = 0; i < renderFunctionNames.length; i++) {
                var name = renderFunctionNames[i];
                try {
                    var address = Module.findExportByName("libamapr.so", name);
                    if (address) {
                        console.log("[渲染] 找到渲染函数: " + name + " @ " + address);
                        
                        Interceptor.attach(address, {
                            onEnter: function(args) {
                                var timeSinceGesture = Date.now() - lastGestureTime;
                                var timeSinceFileAccess = Date.now() - lastFileAccessTime;
                                var timeSinceDecompress = Date.now() - lastDecompressTime;
                                
                                // 只在与手势、文件访问或解压缩时间接近时记录
                                if (timeSinceGesture < 1000 || timeSinceFileAccess < 1000 || timeSinceDecompress < 1000) {
                                    console.log("[渲染] 调用渲染函数: " + name + 
                                              " (距离上次手势: " + timeSinceGesture + 
                                              "ms, 距离上次文件访问: " + timeSinceFileAccess + 
                                              "ms, 距离上次解压缩: " + timeSinceDecompress + "ms)");
                                    
                                    // 打印调用栈
                                    console.log("[渲染] 调用栈:");
                                    var bt = Thread.backtrace(this.context, Backtracer.ACCURATE);
                                    bt.forEach(function(addr, i) {
                                        console.log("\t" + i + ": " + addr + " " + 
                                            DebugSymbol.fromAddress(addr));
                                    });
                                }
                            }
                        });
                    }
                } catch (e) {
                    console.log("[渲染] 钩住 " + name + " 失败: " + e);
                }
            }
        } catch (e) {
            console.log("[渲染] 设置渲染钩子失败: " + e);
        }
    }
})(); 