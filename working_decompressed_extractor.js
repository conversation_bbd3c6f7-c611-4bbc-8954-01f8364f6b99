/*
 * 高德地图解压后数据提取器 - 基于成功脚本模式
 * 结合 sqlite_data_extractor.js 和 real_data_format_analyzer.js 的成功特性
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[解压数据提取器] 启动基于成功模式的解压后数据提取器...");
    
    var extractorStats = {
        totalCalls: 0,
        dataFound: 0,
        successfulExtractions: 0,
        extractedSamples: []
    };
    
    var CONFIG = {
        SAMPLE_RATE: 5,            // 每5次调用采样一次（更频繁）
        MAX_SAMPLES: 5,            // 最多收集5个样本
        MIN_DATA_SIZE: 50,         // 最小数据大小
        MAX_DATA_SIZE: 1024 * 50,  // 最大数据大小
        INIT_DELAY: 3000
    };
    
    var dataPatterns = {
        compressed1: '.C.U.',  // 从成功脚本中观察到的模式
        compressed2: '. .f.',  // 另一种压缩模式
        uncompressed: '.i...'  // 未压缩数据模式
    };
    
    var analysisResults = {
        totalCalls: 0,
        dataTypes: {
            compressed1: 0,
            compressed2: 0,
            uncompressed: 0,
            unknown: 0
        },
        parameterPatterns: {},
        extractedData: []
    };
    
    // === 数据格式分析器（基于成功脚本）===
    function analyzeDataFormat(dataPtr, size, context) {
        try {
            if (!dataPtr || dataPtr.isNull()) {
                return null;
            }
            
            // 读取数据头部
            var headerData = dataPtr.readByteArray(Math.min(32, size || 32));
            var headerView = new Uint8Array(headerData);
            
            var analysis = {
                firstByte: headerView[0],
                headerPattern: "",
                dataType: "unknown",
                hasCompression: false,
                estimatedFormat: "custom",
                rawBytes: headerView,
                context: context
            };
            
            // 构建可读的头部字符串
            for (var i = 0; i < Math.min(8, headerView.length); i++) {
                if (headerView[i] >= 32 && headerView[i] < 127) {
                    analysis.headerPattern += String.fromCharCode(headerView[i]);
                } else if (headerView[i] === 0) {
                    analysis.headerPattern += "\\0";
                } else {
                    analysis.headerPattern += ".";
                }
            }
            
            // 识别数据类型（基于成功脚本观察到的模式）
            if (analysis.headerPattern.indexOf('C.U') !== -1) {
                analysis.dataType = "compressed1";
                analysis.estimatedFormat = "compressed_map_data";
                analysis.hasCompression = true;
            } else if (analysis.headerPattern.indexOf('.f') !== -1) {
                analysis.dataType = "compressed2"; 
                analysis.estimatedFormat = "compressed_feature_data";
                analysis.hasCompression = true;
            } else if (analysis.headerPattern.indexOf('.i') !== -1) {
                analysis.dataType = "uncompressed";
                analysis.estimatedFormat = "uncompressed_data";
            }
            
            // 检查是否为压缩数据
            if (headerView[0] === 0x8) {
                analysis.hasCompression = true;
            }
            
            // 分析数据结构
            if (headerView.length >= 16) {
                analysis.structureHints = {
                    possibleSize: (headerView[8] | (headerView[9] << 8) | (headerView[10] << 16) | (headerView[11] << 24)),
                    possibleFlags: (headerView[12] | (headerView[13] << 8)),
                    possibleVersion: headerView[14],
                    possibleChecksum: headerView[15]
                };
            }
            
            return analysis;
            
        } catch (e) {
            console.log("[Error] 数据格式分析失败: " + e);
            return null;
        }
    }
    
    // === 参数模式分析 ===
    function analyzeParameterPattern(args) {
        var pattern = {
            arg0: args[0] ? args[0].toString() : "null",
            arg1: args[1] ? args[1].toInt32() : 0,
            arg2: args[2] ? args[2].toInt32() : 0,
            patternKey: ""
        };
        
        // 创建参数模式键
        pattern.patternKey = "arg1:" + pattern.arg1 + "_arg2:0x" + pattern.arg2.toString(16);
        
        return pattern;
    }
    
    // === 解压后数据提取（核心功能）===
    function extractDecompressedData(dataPtr, dataSize, sampleName, context) {
        try {
            // 读取完整数据
            var extractSize = Math.min(dataSize, 1024); // 限制提取大小
            var rawData = dataPtr.readByteArray(extractSize);
            var dataView = new Uint8Array(rawData);
            
            var sample = {
                name: sampleName,
                address: dataPtr,
                fullSize: dataSize,
                extractedSize: extractSize,
                timestamp: Date.now(),
                context: context,
                format: "unknown",
                isCompressed: false,
                hexHeader: "",
                signature: "",
                magicBytes: [],
                coordinates: [],
                chineseText: [],
                structuredData: null
            };
            
            // 提取前16字节作为魔数
            for (var i = 0; i < Math.min(16, dataView.length); i++) {
                sample.magicBytes.push(dataView[i]);
                var hex = dataView[i].toString(16).toUpperCase();
                if (hex.length === 1) hex = '0' + hex;
                sample.hexHeader += hex + " ";
            }
            
            // 提取可读签名
            for (var j = 0; j < Math.min(64, dataView.length); j++) {
                if (dataView[j] >= 32 && dataView[j] < 127) {
                    sample.signature += String.fromCharCode(dataView[j]);
                } else if (dataView[j] === 0) {
                    sample.signature += "\\0";
                } else {
                    sample.signature += ".";
                }
            }
            
            // 格式识别（基于成功脚本的模式）
            if (sample.signature.indexOf('.C.U') !== -1) {
                sample.format = "Compressed_Type1_Data";
                sample.isCompressed = true;
            } else if (sample.signature.indexOf('. .f') !== -1) {
                sample.format = "Compressed_Type2_Data";
                sample.isCompressed = true;
            } else if (sample.signature.indexOf('.i..') !== -1) {
                sample.format = "Uncompressed_Vector_Data";
            } else if (sample.signature.indexOf('DICE') !== -1) {
                sample.format = "DICE_Block";
            } else if (dataView[0] === 0x08) {
                sample.format = "Compressed_Data";
                sample.isCompressed = true;
            }
            
            // 尝试提取经纬度坐标
            sample.coordinates = extractCoordinates(dataView);
            
            // 尝试提取中文文本
            sample.chineseText = extractChineseText(rawData);
            
            // 只在找到有意义数据时输出详细信息
            var hasData = (sample.coordinates.length > 0 || sample.chineseText.length > 0 || 
                          sample.signature.indexOf('.C.U') !== -1 || sample.signature.indexOf('. .f') !== -1);
            
            if (hasData) {
                console.log("=== 找到有效解压后数据！ ===");
                console.log(" 来源: " + sample.context);
                console.log(" 大小: " + sample.fullSize + " 字节");
                console.log(" 头部: " + sample.hexHeader);
                console.log(" 签名: '" + sample.signature.substring(0, 40) + "'");
                console.log(" 格式: " + sample.format);
                
                if (sample.coordinates.length > 0) {
                    console.log(" 坐标数据: " + sample.coordinates.length + " 个");
                    for (var k = 0; k < Math.min(3, sample.coordinates.length); k++) {
                        var coord = sample.coordinates[k];
                        console.log("  坐标" + k + ": (" + coord.x.toFixed(6) + ", " + coord.y.toFixed(6) + ") ← 真实经纬度！");
                    }
                }
                
                if (sample.chineseText.length > 0) {
                    console.log(" 中文文本: " + sample.chineseText.length + " 个");
                    for (var l = 0; l < Math.min(5, sample.chineseText.length); l++) {
                        console.log("  文本" + l + ": " + sample.chineseText[l]);
                    }
                }
                
                console.log("==============================");
            } else {
                // 简化输出无效数据
                console.log(" " + sampleName + " (" + context + "): " + sample.signature.substring(0, 20) + " - 无地图数据");
            }
            
            extractorStats.extractedSamples.push(sample);
            extractorStats.successfulExtractions++;
            
            return sample;
            
        } catch (e) {
            console.log("[提取错误] " + sampleName + ": " + e);
            return null;
        }
    }
    
    // === 坐标提取 ===
    function extractCoordinates(dataView) {
        var coordinates = [];
        try {
            var view = new DataView(dataView.buffer, dataView.byteOffset, dataView.byteLength);
            
            for (var i = 0; i < dataView.length - 8 && coordinates.length < 5; i += 4) {
                try {
                    var x = view.getFloat32(i, true);
                    var y = view.getFloat32(i + 4, true);
                    
                    // 检查是否为有效的中国境内坐标
                    if ((70 <= x && x <= 140 && 15 <= y && y <= 60) || 
                        (70 <= y && y <= 140 && 15 <= x && x <= 60)) {
                        coordinates.push({x: x, y: y, offset: i});
                    }
                } catch (e) {
                    continue;
                }
            }
        } catch (e) {
            // 忽略提取错误
        }
        return coordinates;
    }
    
    // === 中文文本提取 ===
    function extractChineseText(rawData) {
        var texts = [];
        try {
            var text = new TextDecoder('utf-8').decode(rawData);
            var currentText = "";
            
            for (var i = 0; i < text.length; i++) {
                var char = text[i];
                if (char >= '\u4e00' && char <= '\u9fff') {
                    currentText += char;
                } else {
                    if (currentText.length >= 2) {
                        texts.push(currentText);
                        if (texts.length >= 10) break;
                    }
                    currentText = "";
                }
            }
            
            if (currentText.length >= 2) {
                texts.push(currentText);
            }
        } catch (e) {
            // 忽略解码错误
        }
        return texts;
    }
    
    // === Hook sub_10F88 (DICE-AM数据解析器) ===
    function setupDiceDataHook(libBase) {
        var sub_10F88 = libBase.add(0x10F88);
        
        console.log("[DICE Hook] 设置DICE-AM数据解析器Hook...");
        
        Interceptor.attach(sub_10F88, {
            onEnter: function(args) {
                this.startTime = Date.now();
                this.inputArgs = [args[0], args[1], args[2]];
                
                analysisResults.totalCalls++;
                
                // 分析参数模式
                var paramPattern = analyzeParameterPattern(args);
                if (!analysisResults.parameterPatterns[paramPattern.patternKey]) {
                    analysisResults.parameterPatterns[paramPattern.patternKey] = 0;
                }
                analysisResults.parameterPatterns[paramPattern.patternKey]++;
                
                // 分析输入数据格式
                var dataAnalysis = analyzeDataFormat(args[0], 64, "DICE输入数据");
                this.inputAnalysis = dataAnalysis;
                
                if (dataAnalysis) {
                    console.log("[DICE输入] 类型: " + dataAnalysis.dataType + 
                               ", 头部: '" + dataAnalysis.headerPattern + 
                               "', 压缩: " + (dataAnalysis.hasCompression ? "是" : "否"));
                    
                    // 记录输入数据用于对比
                    this.shouldExtractInput = (extractorStats.successfulExtractions < CONFIG.MAX_SAMPLES);
                }
                
                console.log("[参数模式] " + paramPattern.patternKey);
            },
            
            onLeave: function(retval) {
                var duration = Date.now() - this.startTime;
                var returnCode = retval.toInt32();
                
                console.log("[DICE完成] 耗时: " + duration + "ms, 返回: " + returnCode);
                
                // 如果处理成功且还需要样本，提取输入的原始数据
                if (returnCode === 0 && this.shouldExtractInput && this.inputArgs[0]) {
                    console.log("[开始提取] DICE处理的真实输入数据...");
                    var sampleName = "dice_real_input_" + extractorStats.successfulExtractions;
                    // 尝试读取更大的数据块以获取完整内容
                    extractDecompressedData(this.inputArgs[0], 2048, sampleName, "DICE-AM真实输入");
                }
                
                // 尝试从返回值或其他位置获取处理后的数据
                if (returnCode === 0 && this.inputArgs[1] && extractorStats.successfulExtractions < CONFIG.MAX_SAMPLES) {
                    try {
                        // 检查第二个参数是否指向输出缓冲区
                        var outputPtr = this.inputArgs[1];
                        if (outputPtr && !outputPtr.isNull()) {
                            console.log("[尝试提取] DICE处理后输出数据...");
                            var outputSample = "dice_output_" + extractorStats.successfulExtractions;
                            extractDecompressedData(outputPtr, 1024, outputSample, "DICE-AM处理输出");
                        }
                    } catch (e) {
                        console.log("[DICE输出提取失败] " + e);
                    }
                }
            }
        });
        
        console.log("[DICE Hook] DICE-AM数据解析器Hook已设置");
    }
    
    // === Hook girf_sqlite3_bind_blob (SQLite数据绑定) ===
    function setupSQLiteBlobHook(libBase) {
        console.log("[SQLite Hook] 设置SQLite BLOB Hook for girf_sqlite3_bind_blob...");
        
        try {
            var sqliteBlobFunc = libBase.add(0x15000);
            
            Interceptor.attach(sqliteBlobFunc, {
                onEnter: function(args) {
                    extractorStats.totalCalls++;
                    
                    // 采样机制
                    if (extractorStats.totalCalls % CONFIG.SAMPLE_RATE !== 0) {
                        return;
                    }
                    
                    this.shouldAnalyze = true;
                    this.startTime = Date.now();
                    
                    // 分析SQLite BLOB参数
                    this.sqliteStmt = args[0];      // SQLite语句句柄
                    this.paramIndex = args[1];      // 参数索引
                    this.blobData = args[2];        // BLOB数据指针
                    this.blobSize = args[3];        // BLOB数据长度
                    
                    var sampleNum = Math.floor(extractorStats.totalCalls / CONFIG.SAMPLE_RATE);
                    
                    try {
                        var blobSizeInt = this.blobSize.toInt32();
                        var paramIndexInt = this.paramIndex.toInt32();
                        
                        console.log("[SQLite样本 " + sampleNum + "] 第" + extractorStats.totalCalls + "次调用, 大小: " + blobSizeInt + " 字节");
                        
                        // 检查BLOB大小是否在合理范围内
                        if (blobSizeInt >= CONFIG.MIN_DATA_SIZE && blobSizeInt <= CONFIG.MAX_DATA_SIZE) {
                            
                            this.validBlob = true;
                            this.blobSizeInt = blobSizeInt;
                            
                            // 立即分析并提取BLOB数据（在数据还有效时）
                            if (!this.blobData.isNull() && extractorStats.successfulExtractions < CONFIG.MAX_SAMPLES) {
                                console.log("[立即提取] SQLite BLOB数据 (大小: " + blobSizeInt + " 字节)");
                                var sampleName = "sqlite_immediate_" + extractorStats.successfulExtractions;
                                
                                // 立即提取数据，不等到onLeave
                                var sample = extractDecompressedData(this.blobData, blobSizeInt, sampleName, "SQLite立即提取");
                                
                                if (sample && (sample.coordinates.length > 0 || sample.chineseText.length > 0)) {
                                    console.log("[成功] 找到有效数据: 坐标" + sample.coordinates.length + "个, 中文" + sample.chineseText.length + "个");
                                    this.hasValidData = true;
                                }
                            }
                        } else {
                            console.log("[跳过] BLOB大小超出范围: " + blobSizeInt + " 字节");
                        }
                        
                    } catch (e) {
                        console.log("[SQLite错误] " + e);
                    }
                },
                
                onLeave: function(retval) {
                    if (!this.shouldAnalyze) {
                        return;
                    }
                    
                    var duration = Date.now() - this.startTime;
                    var returnCode = retval.toInt32();
                    
                    if (this.hasValidData) {
                        console.log("[SQLite完成] 找到有效数据, 耗时: " + duration + "ms, 返回: " + returnCode);
                    } else {
                        console.log("[SQLite完成] 返回: " + returnCode);
                    }
                }
            });
            
            console.log("[SQLite Hook] Hook已设置");
            
        } catch (e) {
            console.log("[SQLite错误] " + e);
        }
    }
    
    // === Hook文件读取操作（基于成功脚本模式）===
    function setupFileReadHook() {
        console.log("[文件Hook] 设置文件读取Hook...");
        
        try {
            // Hook libc.so的read函数
            var readPtr = Module.findExportByName("libc.so", "read");
            if (!readPtr) {
                console.log("[错误] 无法找到read函数");
                return;
            }
            
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    this.fd = args[0].toInt32();
                    this.buffer = args[1];
                    this.size = args[2].toInt32();
                    this.startTime = Date.now();
                },
                
                onLeave: function(retval) {
                    var bytesRead = retval.toInt32();
                    if (bytesRead <= 0 || this.size < 100) return;
                    
                    try {
                        // 读取数据进行分析
                        var safeSize = Math.min(bytesRead, 1024);
                        var data = this.buffer.readByteArray(safeSize);
                        var view = new Uint8Array(data);
                        
                        // 构建可读签名
                        var signature = "";
                        for (var i = 0; i < Math.min(32, view.length); i++) {
                            if (view[i] >= 32 && view[i] < 127) {
                                signature += String.fromCharCode(view[i]);
                            } else if (view[i] === 0) {
                                signature += "\\0";
                            } else {
                                signature += ".";
                            }
                        }
                        
                        // 检查是否包含地图数据特征（基于成功脚本观察到的模式）
                        var hasMapData = (
                            signature.indexOf('.C.U') !== -1 ||  // compressed1 模式
                            signature.indexOf('. .f') !== -1 ||  // compressed2 模式
                            signature.indexOf('.i..') !== -1 ||  // uncompressed 模式
                            view[0] === 0x08 ||                  // 压缩标识
                            signature.indexOf('DICE') !== -1 ||  // DICE块
                            signature.indexOf('ANS') !== -1      // ANS文件
                        );
                        
                        if (hasMapData && extractorStats.successfulExtractions < CONFIG.MAX_SAMPLES) {
                            console.log("[文件数据] 发现地图数据特征: " + signature.substring(0, 20));
                            
                            var sampleName = "file_read_sample_" + extractorStats.successfulExtractions;
                            var sample = extractDecompressedData(this.buffer, bytesRead, sampleName, "文件读取");
                            
                            if (sample && (sample.coordinates.length > 0 || sample.chineseText.length > 0)) {
                                console.log("[成功] 从文件读取中找到真实地图数据！");
                            }
                        }
                        
                    } catch (e) {
                        // 忽略读取错误
                    }
                }
            });
            
            console.log("[文件Hook] 文件读取Hook已设置");
            
        } catch (e) {
            console.log("[文件Hook错误] " + e);
        }
    }
    
    // === Hook zlib解压（基于成功脚本模式）===
    function setupZlibHook() {
        console.log("[zlib Hook] 设置zlib解压Hook...");
        
        try {
            // Hook libz.so的uncompress函数
            var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
            if (!uncompressPtr) {
                console.log("[错误] 无法找到uncompress函数");
                return;
            }
            
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    this.destBuffer = args[0];     // 解压后数据缓冲区
                    this.destLenPtr = args[1];     // 解压后数据长度指针
                    this.sourceBuffer = args[2];   // 压缩数据缓冲区
                    this.sourceLen = args[3].toInt32(); // 压缩数据长度
                },
                
                onLeave: function(retval) {
                    var result = retval.toInt32();
                    if (result !== 0) return; // Z_OK = 0
                    
                    try {
                        // 读取解压后的数据长度
                        var decompressedLen = this.destLenPtr.readU32();
                        if (decompressedLen <= 0 || decompressedLen > 1024 * 1024) return;
                        
                        if (extractorStats.successfulExtractions < CONFIG.MAX_SAMPLES) {
                            console.log("[zlib解压] 成功解压 " + this.sourceLen + " → " + decompressedLen + " 字节");
                            
                            var sampleName = "zlib_decompressed_" + extractorStats.successfulExtractions;
                            var sample = extractDecompressedData(this.destBuffer, decompressedLen, sampleName, "zlib解压");
                            
                            if (sample && (sample.coordinates.length > 0 || sample.chineseText.length > 0)) {
                                console.log("[成功] 从zlib解压中找到真实地图数据！");
                            }
                        }
                        
                    } catch (e) {
                        console.log("[zlib提取错误] " + e);
                    }
                }
            });
            
            console.log("[zlib Hook] zlib解压Hook已设置");
            
        } catch (e) {
            console.log("[zlib Hook错误] " + e);
        }
    }
    
    // === 生成提取报告 ===
    function generateExtractionReport() {
        console.log("\n=== 解压后数据提取报告 ===");
        console.log("DICE调用次数: " + analysisResults.totalCalls);
        console.log("SQLite调用次数: " + extractorStats.totalCalls);
        console.log("成功提取: " + extractorStats.successfulExtractions + " 个样本");
        
        if (extractorStats.extractedSamples.length > 0) {
            console.log("\n数据样本统计:");
            var formatStats = {};
            for (var i = 0; i < extractorStats.extractedSamples.length; i++) {
                var sample = extractorStats.extractedSamples[i];
                if (!formatStats[sample.format]) {
                    formatStats[sample.format] = 0;
                }
                formatStats[sample.format]++;
            }
            
            for (var format in formatStats) {
                console.log("  " + format + ": " + formatStats[format] + " 个");
            }
            
            console.log("\n最新样本详情:");
            var latest = extractorStats.extractedSamples[extractorStats.extractedSamples.length - 1];
            console.log("  名称: " + latest.name);
            console.log("  来源: " + latest.context);
            console.log("  格式: " + latest.format);  
            console.log("  大小: " + latest.fullSize + " 字节");
            console.log("  坐标数: " + latest.coordinates.length + " 个");
            console.log("  中文文本数: " + latest.chineseText.length + " 个");
            console.log("  签名: " + latest.signature.substring(0, 30));
        }
        
        console.log("==================================\n");
    }
    
    // === 库等待函数 ===
    function waitForLibrary(name, callback) {
        var attempts = 0;
        function check() {
            try {
                var lib = Module.findBaseAddress(name);
                if (lib) {
                    console.log("[库加载] " + name + " 基址: " + lib);
                    callback(lib);
                    return;
                }
            } catch (e) {}
            
            if (++attempts < 30) {
                setTimeout(check, 1000);
            } else {
                console.log("[错误] " + name + " 加载超时");
            }
        }
        check();
    }
    
    // === 主函数 ===
    function main() {
        console.log("[主程序] 初始化真实解压后数据提取器...");
        
        setTimeout(function() {
            try {
                // 使用成功脚本的策略：Hook文件I/O和zlib解压
                setupFileReadHook();          // Hook文件读取操作
                setupZlibHook();              // Hook zlib解压操作
                
                // 保留SQLite Hook作为辅助
                waitForLibrary("libamapnsq.so", function(libBase) {
                    setupSQLiteBlobHook(libBase);    // Hook SQLite数据绑定
                });
                
                setInterval(generateReport, 20000);
                
                console.log("[真实数据提取器] 已启动!");
                console.log(" 策略: 文件读取 + zlib解压 + SQLite数据库");
                console.log(" 现在移动地图，观察真实的解压后数据提取!");
                
            } catch (e) {
                console.log("[错误] Hook设置失败: " + e);
            }
            
        }, CONFIG.INIT_DELAY);
    }
    
    // === 简化报告 ===
    function generateReport() {
        if (extractorStats.successfulExtractions === 0) return;
        
        console.log("\n=== 真实解压后数据提取报告 ===");
        console.log("成功提取: " + extractorStats.successfulExtractions + " 个样本");
        
        var totalCoords = 0;
        var totalTexts = 0;
        var validSamples = 0;
        
        for (var i = 0; i < extractorStats.extractedSamples.length; i++) {
            var sample = extractorStats.extractedSamples[i];
            totalCoords += sample.coordinates.length;
            totalTexts += sample.chineseText.length;
            if (sample.coordinates.length > 0 || sample.chineseText.length > 0) {
                validSamples++;
            }
        }
        
        console.log("数据统计:");
        console.log("   总坐标数: " + totalCoords + " 个");
        console.log("   总中文文本: " + totalTexts + " 个");
        console.log("   有效样本: " + validSamples + "/" + extractorStats.successfulExtractions);
        console.log("   数据来源: 文件读取 + zlib解压 + SQLite数据库");
        console.log("======================================\n");
    }
    
    // === 启动 ===
    try {
        Java.perform(function() {
            console.log("[Java] Java环境已准备就绪");
            main();
        });
    } catch (e) {
        console.log("[错误] Java环境初始化失败: " + e);
        main();
    }
    
})(); 