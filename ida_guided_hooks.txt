     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[启动] IDA引导版 Hook 脚本 已启动
[就绪] 请在地图中平移缩放触发解析
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [模块] 发现 libamapnsq.so，安装钩子
[模块] libamapnsq.so base=0x7f586fe000 安装钩子偏移= 0x20120, 0x228C4, 0x5C060
[成功] Hook sub_228C4 at 0x7f587208c4
[跳过] sub_20120 未启用
[跳过] sub_5C060 未启用
[sub_228C4 enter] cur=0x7f4a25f808 end=0x7f4a3ed040 预览=05 00 00 00 57 1e 17 00 00 00 00 6d 1f fb 1f f6 1f a1 1f f1 1f ec 1f e7 1f e2 1f dd 1f d8 1f d3
[sub_228C4 leave] cnt=1 v1=0 v2(qw)=0x0 nextPtr=0x0 w20=0 w22=0 w24=0
[sub_228C4 enter] cur=0x0 end=0x0 预览=<预览不可用>
[sub_228C4 leave] cnt=2 v1=0 v2(qw)=0x0 nextPtr=0x0 w20=0 w22=0 w24=0
[sub_228C4 enter] cur=0x7f4a25f808 end=0x7f4a3ed040 预览=05 00 00 00 57 1e 17 00 00 00 00 6d 1f fb 1f f6 1f a1 1f f1 1f ec 1f e7 1f e2 1f dd 1f d8 1f d3
[sub_228C4 leave] cnt=3 v1=0 v2(qw)=0x0 nextPtr=0x0 w20=0 w22=0 w24=0
[sub_228C4 leave] cnt=4 v1=0 v2(qw)=0x0 nextPtr=0x0 w20=0 w22=0 w24=0
[sub_228C4 leave] cnt=5 v1=0 v2(qw)=0x0 nextPtr=0x0 w20=0 w22=0 w24=0
[sub_228C4 leave] cnt=6 v1=0 v2(qw)=0xd906030000000d nextPtr=0xab08c817500fd906 w20=0 w22=0 w24=0
[sub_228C4 leave] cnt=7 v1=0 v2(qw)=0x0 nextPtr=0x0 w20=0 w22=0 w24=0
[sub_228C4 leave] cnt=8 v1=0 v2(qw)=0x0 nextPtr=0x0 w20=0 w22=0 w24=0
[sub_228C4 leave] cnt=9 v1=0 v2(qw)=0x0 nextPtr=0x0 w20=0 w22=0 w24=0
[sub_228C4 leave] cnt=10 v1=0 v2(qw)=0x0 nextPtr=0x0 w20=0 w22=0 w24=0
Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=000001E9C39F8980)

Thread 0x0000324c (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x00006184 (most recent call first):
<no Python frame>
