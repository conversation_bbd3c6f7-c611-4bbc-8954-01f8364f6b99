     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[验证脚本] 开始验证高德地图离线数据处理流程...
[\u2717] libamapnsq.so 未找到
[\u2713] libz.so 已加载，基址: 0x7f8cab1000
[\u2713] libc.so 已加载，基址: 0x7f8dc9d000
[验证] uncompress 函数地址: 0x7f8cac667c
[验证脚本] 验证脚本已启动，等待地图操作...
[提示] 请在地图上进行移动操作以触发数据加载流程
[IDA Pro验证] 关键函数地址已通过IDA Pro静态分析验证
  - girf_sqlite3_bind_blob: 0x15000
  - 数据处理函数: 0x5c060
  - AES解密函数: 0xb55c
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [Hook] 检测到可能的.ans文件读取
  文件描述符: 41
  读取字节数: 4096
  数据头部: 08 04 05 06 04 04 05 04
[Hook] 检测到可能的.ans文件读取
  文件描述符: 41
  读取字节数: 4096
  数据头部: 08 07 05 03 04 05 07 08
[Hook] 检测到可能的.ans文件读取
  文件描述符: 41
  读取字节数: 4096
  数据头部: 08 03 06 47 36 41 07 03
[Hook] 检测到可能的.ans文件读取
  文件描述符: 41
  读取字节数: 4096
  数据头部: 08 23 64 15 06 06 0a 02
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xb61
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 4700
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 58 12 00 00 58 12 00 00  x.......X...X...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x60c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 2684
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 78 0a 00 00 78 0a 00 00  x.......x...x...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x5e6
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 10408
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 a4 28 00 00 a4 28 00 00  x........(...(..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xc2d
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 5300
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 b0 14 00 00 b0 14 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x6f2
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 3096
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 14 0c 00 00 14 0c 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x86a
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 11712
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 bc 2d 00 00 bc 2d 00 00  x........-...-..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xc35
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 5304
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 b4 14 00 00 b4 14 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x77f
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 3336
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 04 0d 00 00 04 0d 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x883
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 11760
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 ec 2d 00 00 ec 2d 00 00  x........-...-..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xc35
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 5304
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 b4 14 00 00 b4 14 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x874
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 3712
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 7c 0e 00 00 7c 0e 00 00  x.......|...|...
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 12024
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 f4 2e 00 00 f4 2e 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b8
    at frida/runtime/core.js:144
    at /verification_script.js:100
Error: access violation accessing 0xdc9
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 6304
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 9c 18 00 00 9c 18 00 00  x...............
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 3612
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 18 0e 00 00 18 0e 00 00  x...............
Error: access violation accessing 0x800
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 13288
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 e4 33 00 00 e4 33 00 00  x........3...3..
Error: access violation accessing 0x9af
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xd9c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 5924
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 20 17 00 00 20 17 00 00  x....... ... ...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x979
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 4372
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 10 11 00 00 10 11 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x910
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 13144
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 54 33 00 00 54 33 00 00  x.......T3..T3..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xc2d
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 5300
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 b0 14 00 00 b0 14 00 00  x...............
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 3096
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 14 0c 00 00 14 0c 00 00  x...............
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 11712
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
Error: access violation accessing 0x6f2
    at frida/runtime/core.js:144
00000010  78 00 13 00 00 03 01 00 bc 2d 00 00 bc 2d 00 00  x........-...-..
    at /verification_script.js:100
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x86a
    at frida/runtime/core.js:144
    at /verification_script.js:100
Error: access violation accessing 0xb61
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 4700
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 58 12 00 00 58 12 00 00  x.......X...X...
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 2684
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 78 0a 00 00 78 0a 00 00  x.......x...x...
Error: access violation accessing 0x60c
    at frida/runtime/core.js:144
[Hook] zlib uncompress 被调用
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 10408
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 a4 28 00 00 a4 28 00 00  x........(...(..
Error: access violation accessing 0x5e6
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] 检测到可能的.ans文件读取
  文件描述符: 115
  读取字节数: 531
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x213
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2e d7 00 00 00 00 fe fe 00 00 2e d7  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 115
  读取字节数: 531
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x213
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 8192
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2e d7 00 00 00 00 fe fe 00 00 2e d7  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 115
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 115
  读取字节数: 531
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x213
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2e d7 00 00 00 00 fe fe 00 00 2e d7  ................
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x9e6
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 4572
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 d8 11 00 00 d8 11 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x950
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 3920
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 4c 0f 00 00 4c 0f 00 00  x.......L...L...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x64f
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 12136
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 64 2f 00 00 64 2f 00 00  x.......d/..d/..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x134e
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8700
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 f8 21 00 00 f8 21 00 00  x........!...!..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xcb6
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 6356
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 d0 18 00 00 d0 18 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xba7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 15376
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 0c 3c 00 00 0c 3c 00 00  x........<...<..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x869
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 3436
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 68 0d 00 00 68 0d 00 00  x.......h...h...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x707
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 2972
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 98 0b 00 00 98 0b 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x4d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 9136
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 ac 23 00 00 ac 23 00 00  x........#...#..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1747
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 10664
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 a4 29 00 00 a4 29 00 00  x........)...)..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xcc1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 6340
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 c0 18 00 00 c0 18 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xe8f
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 17480
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 44 44 00 00 44 44 00 00  x.......DD..DD..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x145d
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 10200
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 d4 27 00 00 d4 27 00 00  x........'...'..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xe81
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 6748
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 58 1a 00 00 58 1a 00 00  x.......X...X...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xc80
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 17664
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 fc 44 00 00 fc 44 00 00  x........D...D..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 10
  读取字节数: 16
  数据头部: 08 51 7a 34 6a 65 5f 50
[Hook] 检测到可能的.ans文件读取
  文件描述符: 151
  读取字节数: 8192
  数据头部: 08 cf 0d 3c 63 fa 59 bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 146
  读取字节数: 4096
  数据头部: 08 cf 0d 3c 63 fa 59 bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 166
  读取字节数: 8192
  数据头部: 08 00 00 00 5b 00 00 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 167
  读取字节数: 4096
  数据头部: 08 fb c4 a8 2e 7b e8 b8
[Hook] 检测到可能的.ans文件读取
  文件描述符: 179
  读取字节数: 8192
  数据头部: 08 00 00 00 1b 00 00 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 162
  读取字节数: 4096
  数据头部: 08 00 00 00 1b 00 00 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 162
  读取字节数: 4096
  数据头部: 08 70 0d 3b cb 67 4b bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 169
  读取字节数: 4096
  数据头部: 08 c3 db 4a 34 a4 04 ee
[Hook] 检测到可能的.ans文件读取
  文件描述符: 162
  读取字节数: 4096
  数据头部: 08 cf 0d 3c 63 fa 59 bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 76
  读取字节数: 4096
  数据头部: 08 21 a4 fa 66 d8 9b bf
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xe32
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 6440
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 24 19 00 00 24 19 00 00  x.......$...$...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xaba
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 5332
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 d0 14 00 00 d0 14 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xaa7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 14952
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 64 3a 00 00 64 3a 00 00  x.......d:..d:..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x89c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 4776
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 a4 12 00 00 a4 12 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xa10
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 4380
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 18 11 00 00 18 11 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x766
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 13112
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 34 33 00 00 34 33 00 00  x.......43..43..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xa78
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 5456
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 4c 15 00 00 4c 15 00 00  x.......L...L...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xb37
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 4656
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 2c 12 00 00 2c 12 00 00  x.......,...,...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x929
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 15080
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 e4 3a 00 00 e4 3a 00 00  x........:...:..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 10
  读取字节数: 16
  数据头部: 08 50 e4 28 49 ed 50 b1
[Hook] 检测到可能的.ans文件读取
  文件描述符: 190
  读取字节数: 4096
  数据头部: 08 00 00 00 1b 00 00 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 190
  读取字节数: 4096
  数据头部: 08 70 0d 3b cb 67 4b bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 197
  读取字节数: 4096
  数据头部: 08 00 00 9b 00 e2 5a ce
[Hook] 检测到可能的.ans文件读取
  文件描述符: 197
  读取字节数: 4096
  数据头部: 08 00 00 9b 00 e2 5a ce
[Hook] 检测到可能的.ans文件读取
  文件描述符: 206
  读取字节数: 8192
  数据头部: 08 21 96 3d ac 5b 30 3d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 206
  读取字节数: 8192
  数据头部: 08 df 9e bd 5f d0 22 3e
[Hook] 检测到可能的.ans文件读取
  文件描述符: 197
  读取字节数: 4096
  数据头部: 08 01 72 65 73 2f 6c 61
[Hook] 检测到可能的.ans文件读取
  文件描述符: 197
  读取字节数: 4096
  数据头部: 08 01 72 65 73 2f 6c 61
[Hook] 检测到可能的.ans文件读取
  文件描述符: 206
  读取字节数: 8192
  数据头部: 08 49 16 be f5 49 de bb
[Hook] 检测到可能的.ans文件读取
  文件描述符: 206
  读取字节数: 8192
  数据头部: 08 a8 07 bd df 98 42 3d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 206
  读取字节数: 8192
  数据头部: 08 cf 88 bd 6f d5 da bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 206
  读取字节数: 8192
  数据头部: 08 f9 84 bd 88 97 0a bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 206
  读取字节数: 8192
  数据头部: 08 7d a3 3d b5 d4 89 bd
[统计] 文件读取: 33, zlib解压: 52, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 228
  读取字节数: 8000
  数据头部: 08 a2 5c 57 6a 14 f1 8a
[Hook] 检测到可能的.ans文件读取
  文件描述符: 206
  读取字节数: 8192
  数据头部: 08 50 e8 3d d6 4b d8 be
[Hook] 检测到可能的.ans文件读取
  文件描述符: 169
  读取字节数: 4096
  数据头部: 08 c3 db 4a 34 a4 04 ee
[Hook] 检测到可能的.ans文件读取
  文件描述符: 206
  读取字节数: 8192
  数据头部: 08 35 ea 3d 71 60 5e be
[Hook] 检测到可能的.ans文件读取
  文件描述符: 115
  读取字节数: 531
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x213
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2e d7 00 00 00 00 fe fe 00 00 2e d7  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 146
  读取字节数: 4096
  数据头部: 08 c3 00 15 fe c8 ff 9a
[Hook] 检测到可能的.ans文件读取
  文件描述符: 146
  读取字节数: 4096
  数据头部: 08 bd 05 6a f7 59 07 e7
[Hook] 检测到可能的.ans文件读取
  文件描述符: 229
  读取字节数: 4096
  数据头部: 08 00 00 9b 00 e2 5a ce
[Hook] 检测到可能的.ans文件读取
  文件描述符: 206
  读取字节数: 8192
  数据头部: 08 0a 10 3d f9 02 33 be
[Hook] 检测到可能的.ans文件读取
  文件描述符: 146
  读取字节数: 4096
  数据头部: 08 01 00 00 00 1c 08 01
[Hook] 检测到可能的.ans文件读取
  文件描述符: 146
  读取字节数: 4096
  数据头部: 08 02 54 08 02 00 00 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 229
  读取字节数: 4096
  数据头部: 08 01 72 65 73 2f 6c 61
[Hook] 检测到可能的.ans文件读取
  文件描述符: 222
  读取字节数: 8000
  数据头部: 08 c0 cb 18 c0 8b 4a 95
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 00 00 00 66 73 6d 6e
[Hook] 检测到可能的.ans文件读取
  文件描述符: 222
  读取字节数: 8000
  数据头部: 08 cc 47 4e bd bd e3 a8
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 21 96 3d ac 5b 30 3d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 90 94 3d 7a 2c a2 3c
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 e9 d2 3c 2e 12 d9 3d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 222
  读取字节数: 8000
  数据头部: 08 48 bb e1 38 43 d1 71
[Hook] 检测到可能的.ans文件读取
  文件描述符: 222
  读取字节数: 8000
  数据头部: 08 25 ba c8 07 32 e9 72
[Hook] 检测到可能的.ans文件读取
  文件描述符: 222
  读取字节数: 8000
  数据头部: 08 3f 88 0b 22 a3 fd 19
[Hook] 检测到可能的.ans文件读取
  文件描述符: 222
  读取字节数: 8000
  数据头部: 08 68 34 86 19 aa b7 d9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 222
  读取字节数: 8000
  数据头部: 08 9c 6b 65 0a 63 30 60
[Hook] 检测到可能的.ans文件读取
  文件描述符: 250
  读取字节数: 8000
  数据头部: 08 17 52 68 a7 0b 5d 01
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 df 9e bd 5f d0 22 3e
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 50 5a 98 0a e7 5a 0f
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 46 0e a4 c1 ba de 99
[Hook] 检测到可能的.ans文件读取
  文件描述符: 169
  读取字节数: 4096
  数据头部: 08 c3 db 4a 34 a4 04 ee
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 62 92 cd ce b0 42 b6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 ac c8 26 78 fc b3 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 d7 c2 bd a7 ba ae bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 34 dc 66 0d 5f 2f 28
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 36 3b 8e e5 eb f3 1e
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 e7 2a 69 e1 a8 50 48
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 c9 77 10 bd 56 9e 37
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 49 16 be f5 49 de bb
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 cf be bc f7 35 bf 3d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 a8 07 bd df 98 42 3d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 50 f5 3b e0 04 14 bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 ea f1 77 54 c6 db f9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 36 1b bc c4 37 68 bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 53 55 3e be 79 40 be
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 cf 88 bd 6f d5 da bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 cb e5 d0 36 11 35 40
[Hook] 检测到可能的.ans文件读取
  文件描述符: 274
  读取字节数: 987
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x3db
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 7b 05 00 00 00 00 fe fe 00 00 7b 05  ....{.........{.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 274
  读取字节数: 987
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x3db
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 7b 05 00 00 00 00 fe fe 00 00 7b 05  ....{.........{.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 90 db 67 84 dd eb 25
[Hook] 检测到可能的.ans文件读取
  文件描述符: 274
  读取字节数: 2813
  数据头部: 78 9c ed 57 6f 6c 1c c5
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0xafd
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 02 09 92 00 0d 85 09 92 00 00 00 00  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 20 3f b6 5a bb 69 e7
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 b6 05 7a 40 ca 26 3b
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 db 53 43 c5 0c 4b b1
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 ca 8a a5 68 b4 bc bb
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 f9 84 bd 88 97 0a bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 49 9a 3e e9 48 ae 3d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 02 02 02 03 06 20 99
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 9c 24 4d 97 d2 b2 4a
[Hook] 检测到可能的.ans文件读取
  文件描述符: 225
  读取字节数: 8000
  数据头部: 08 68 5b 51 92 54 c8 3b
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 d8 f9 b4 27 72 8d fa
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 7d a3 3d b5 d4 89 bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 03 00 00 04 0e 05 7f
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 00 01 12 14 03 03 05
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 7b b7 03 dc ca cf 95
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 04 08 03 07 0b 03 03
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 86 a8 3e 25 bb 28 be
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 88 e0 9b 5f b5 ad 05
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 88 ac 7a d1 4e ea 11
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 03 2d 95 44 f0 70 9d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 50 e8 3d d6 4b d8 be
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 f5 e7 3d db 40 8a be
[Hook] 检测到可能的.ans文件读取
  文件描述符: 169
  读取字节数: 4096
  数据头部: 08 ab b5 a8 ff 01 85 e5
[Hook] 检测到可能的.ans文件读取
  文件描述符: 271
  读取字节数: 4096
  数据头部: 08 02 02 02 03 06 20 99
[Hook] 检测到可能的.ans文件读取
  文件描述符: 271
  读取字节数: 4096
  数据头部: 08 03 00 00 04 0e 05 7f
[Hook] 检测到可能的.ans文件读取
  文件描述符: 271
  读取字节数: 4096
  数据头部: 08 00 01 12 14 03 03 05
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 dc 29 00 cc 29 df 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 ba be 11 be 58 91 e0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 271
  读取字节数: 4096
  数据头部: 08 04 08 03 07 0b 03 03
[Hook] 检测到可能的.ans文件读取
  文件描述符: 169
  读取字节数: 4096
  数据头部: 08 ab b5 a8 ff 01 85 e5
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 f9 2f e6 1b 8c 5f 7f
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 10 d0 3c b3 79 16 3e
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 31 80 52 e8 7f bf a9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 35 ea 3d 71 60 5e be
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 09 40 92 48 01 08 8b
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 f9 7f d3 e2 03 17 aa
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 08 80 bb ed 42 be 3d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 41 00 91 08 00 00 f9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 09 40 f9 00 01 3f d6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 e1 24 91 88 86 00 f8
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 93 40 4f 3c 23 1a d6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 b5 94 1a 69 12 01 b9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 0b a6 14 ae 8d bc 63
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 01 40 f9 08 21 40 79
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 00 40 f9 08 09 40 f9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 00 00 90 e0 23 00 91
[Hook] 检测到可能的.ans文件读取
  文件描述符: 169
  读取字节数: 4096
  数据头部: 08 c3 db 4a 34 a4 04 ee
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 2e ed d5 ae 4e 8b 75
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 81 34 bb 9d 09 2f a3
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 bd c0 cd 5f 68 18 48
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 81 40 f9 00 01 3f d6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 00 40 f9 08 11 40 f9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 0a 10 3d f9 02 33 be
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 39 d6 c6 cb f4 01 60
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 5d 40 f9 00 01 3f d6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 8a 00 3a 50 96 93 4f
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 6a 8e f9 a0 fa de e1
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xd16
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 5588
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 d0 15 00 00 d0 15 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8d7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 3980
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 88 0f 00 00 88 0f 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x828
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 12536
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 f4 30 00 00 f4 30 00 00  x........0...0..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 297
  读取字节数: 1024
  数据头部: 08 f3 5e bb 3b a2 94 5a
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 01 00 34 01 05 00 51
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xe9e
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 6580
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 b0 19 00 00 b0 19 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xb5a
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 5428
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 30 15 00 00 30 15 00 00  x.......0...0...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xa3e
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 15288
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 b4 3b 00 00 b4 3b 00 00  x........;...;..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xf71
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 7196
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 18 1c 00 00 18 1c 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xce0
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 61 43 f9 01 01 40 f9
[Hook] zlib 解压成功，解压后大小: 6452
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 30 19 00 00 30 19 00 00  x.......0...0...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xbb8
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 16752
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 6c 41 00 00 6c 41 00 00  x.......lA..lA..
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xdb4
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 6096
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 cc 17 00 00 cc 17 00 00  x...............
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 4868
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 00 13 00 00 00 13 00 00  x...............
Error: access violation accessing 0xab0
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xa1b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 f8 56 5a 1e 8f ed 8c
[Hook] zlib 解压成功，解压后大小: 13856
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 1c 36 00 00 1c 36 00 00  x........6...6..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 0c 40 f9 08 09 40 f9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 3b 5b 5f 7b 26 98 04
[统计] 文件读取: 147, zlib解压: 71, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xdb4
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 6096
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 cc 17 00 00 cc 17 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xa38
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 4544
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 bc 11 00 00 bc 11 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xa00
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 13640
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 44 35 00 00 44 35 00 00  x.......D5..D5..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 04 40 f9 f3 03 00 aa
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 51 18 91 f4 03 00 aa
[Hook] 检测到可能的.ans文件读取
  文件描述符: 288
  读取字节数: 131072
  数据头部: 08 09 40 f9 34 39 40 f9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 e2 8a b9 87 f3 d3 87
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 fa a3 b1 88 e5 1f 54
[Hook] 检测到可能的.ans文件读取
  文件描述符: 298
  读取字节数: 131072
  数据头部: 08 20 00 91 09 fd 5f c8
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 4f 9d 7e 81 9a 83 03
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 62 f6 3d 00 f1 88 bd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 169
  读取字节数: 4096
  数据头部: 08 ab b5 a8 ff 01 85 e5
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 00 00 9b 00 e2 5a ce
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 00 00 9b 00 e2 5a ce
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 20 d3 97 08 52 33 b9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 c9 c7 d9 c5 41 43 cc
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 0b 99 21 44 12 c0 25
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 fa a3 b1 88 e5 1f 54
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 5e e9 72 8e e3 d1 63
[Hook] 检测到可能的.ans文件读取
  文件描述符: 267
  读取字节数: 8000
  数据头部: 08 27 ff 7d 53 d0 85 b3
[Hook] 检测到可能的.ans文件读取
  文件描述符: 334
  读取字节数: 8192
  数据头部: 08 ed 02 3e 8b f6 b2 3d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 71
  读取字节数: 4096
  数据头部: 08 38 c6 7b 4b d1 34 09
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 01 72 65 73 2f 6c 61
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 01 72 65 73 2f 6c 61
[Hook] 检测到可能的.ans文件读取
  文件描述符: 334
  读取字节数: 8192
  数据头部: 08 00 00 00 0b 00 00 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 20 b0 f5 26 9b 5c 4f
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 ed 02 3e 8b f6 b2 3d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 8000
  数据头部: 08 f8 2b a0 55 cf 63 2a
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 8000
  数据头部: 08 8a 5d 93 14 62 a8 58
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 4096
  数据头部: 08 00 00 00 0b 00 00 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 163
  读取字节数: 4096
  数据头部: 08 94 43 10 2b f4 48 1e
[Hook] 检测到可能的.ans文件读取
  文件描述符: 163
  读取字节数: 4096
  数据头部: 08 57 74 c9 58 88 8e 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 8000
  数据头部: 08 83 ea cd 10 08 12 9b
[Hook] 检测到可能的.ans文件读取
  文件描述符: 163
  读取字节数: 4096
  数据头部: 08 a3 59 25 84 22 bd f7
[Hook] 检测到可能的.ans文件读取
  文件描述符: 280
  读取字节数: 8000
  数据头部: 08 db 30 87 49 0a fc a6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 02 f6 df c8 05 96 dd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 8e 59 56 4d 8c c7 30
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 a8 60 a5 f3 a1 08 2c
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 ca f7 12 12 f6 fa da
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 79 93 5a 04 7e b6 2f
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 1e f6 91 e2 cb bc 89
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 cc d2 2a 6e 62 74 aa
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 1d 52 54 ce 1d 8c 64
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 3b bc 25 f9 ff 46 05
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 d4 73 e8 6a c1 7e 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 49 90 41 5e 83 5a bf
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 c1 6a be a5 43 1a f0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 5c 60 2c 69 58 76 dc
[Hook] 检测到可能的.ans文件读取
  文件描述符: 71
  读取字节数: 4096
  数据头部: 08 38 c6 7b 4b d1 34 09
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 37 00 7b 95 87 29 97
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 1f 2d f5 f5 32 0f 76
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 f4 7b 48 21 19 0f d3
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 d3 c1 a5 13 bc 51 0f
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 66 7b 86 ad 68 58 b3
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 58 61 e9 80 b6 0f 4c
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 e9 35 85 f1 f6 48 05
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 a9 2b 33 1b f7 83 4c
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 fd 96 38 4d aa ce ec
[Hook] 检测到可能的.ans文件读取
  文件描述符: 71
  读取字节数: 4096
  数据头部: 08 f1 23 cd 01 38 0b 50
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 8e b4 43 2d 6e 2b 6b
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 1e e4 86 d0 a7 2d de
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 88 05 2d 63 2d 22 4e
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 a4 37 9c 97 4f ae bf
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 5b bf a7 0a 77 c3 69
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 2e 12 13 20 aa 28 5d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 0d 14 2f 9c e3 28 0c
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 16 18 7a fc 08 5f e4
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 5f e4 4f c5 72 ad a7
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 58 9e b4 61 de f5 c6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 6e 11 2a b4 82 7f a4
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 70 f8 48 50 85 ca dd
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 42 9c 89 3c e3 d3 b1
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 46 c2 28 78 bc 5d 6a
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 6d d4 40 30 d5 6e 0d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 13 ac fd f9 f5 cc 70
[Hook] 检测到可能的.ans文件读取
  文件描述符: 242
  读取字节数: 8000
  数据头部: 08 88 b6 5c 46 05 82 28
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 d0 64 e5 72 10 1a b6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 71
  读取字节数: 4096
  数据头部: 08 5f e4 4f c5 72 ad a7
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 a5 41 6a 53 56 4c 29
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 1e 4c 95 3d 6a 39 06
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 16 04 8c d2 36 8f de
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 10 79 59 21 a9 73 67
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 71 32 6b e4 93 1e 8a
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 5e cc b8 3d 8b f5 8c
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 13 07 28 2a 6a be fc
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 24 f5 6a f0 48 3c fb
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 f0 57 7e 5d ed 0f 87
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 e4 fe 6a 9f a3 9f 4f
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 c0 ab e5 5b 9b 64 11
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 c5 3d 21 7d de d2 0c
[统计] 文件读取: 234, zlib解压: 74, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 c6 7f 2b dc ae d9 a6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 f2 f8 f5 5b d9 6b d0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 36 bf b4 c7 84 6d 92
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 b9 0b 17 d2 da 5b 75
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 83 e6 d3 93 59 0e c0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 d7 b2 0f cc d8 65 c4
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 4f 9b b6 df 29 6c 73
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 ff f8 1d 1b de bf 93
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 58 76 d5 cd 5f 5e 6d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 31 41 24 8e 97 3d eb
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 b7 38 54 03 1d dd df
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 8c 74 83 ec 4f 70 fa
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 cd c3 6e f4 20 79 b9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 9d 27 c4 23 77 2f 47
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 ab 5b 26 12 36 99 96
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 58 dd 6d c3 a6 04 a4
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 eb 69 de 22 a3 6e 9f
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 17 34 f2 3b cc cd 70
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 10 44 63 34 56 37 f4
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 93 27 dd 7f 0a 28 44
[Hook] 检测到可能的.ans文件读取
  文件描述符: 276
  读取字节数: 1024
  数据头部: 08 a2 da 5e 5c bd dd 1c
[统计] 文件读取: 255, zlib解压: 74, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 296
  读取字节数: 8192
  数据头部: 08 4d 03 91 00 01 1f d6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 290
  读取字节数: 8192
  数据头部: 08 49 02 91 00 01 1f d6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 290
  读取字节数: 8192
  数据头部: 08 24 40 f9 00 01 3f d6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 290
  读取字节数: 8192
  数据头部: 08 65 00 d1 41 01 80 b9
[Hook] 检测到可能的.ans文件读取
  文件描述符: 296
  读取字节数: 8192
  数据头部: 08 00 00 14 f3 00 00 b4
[Hook] 检测到可能的.ans文件读取
  文件描述符: 290
  读取字节数: 8192
  数据头部: 08 05 40 b9 1f 01 09 6b
[Hook] 检测到可能的.ans文件读取
  文件描述符: 296
  读取字节数: 8192
  数据头部: 08 91 03 91 00 01 1f d6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 290
  读取字节数: 8192
  数据头部: 08 31 40 b9 1f 05 00 71
[Hook] 检测到可能的.ans文件读取
  文件描述符: 290
  读取字节数: 8192
  数据头部: 08 55 40 f9 00 01 3f d6
[Hook] 检测到可能的.ans文件读取
  文件描述符: 290
  读取字节数: 8192
  数据头部: 08 35 40 f9 00 01 3f d6
[统计] 文件读取: 265, zlib解压: 74, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 265, zlib解压: 74, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 265, zlib解压: 74, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 265, zlib解压: 74, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 265, zlib解压: 74, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 265, zlib解压: 74, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
    at /verification_script.js:100
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3966
  数据头部: 78 9c 3d 98 7f 54 64 e5
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xf7e
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  05 00 00 00 04 1f d0 00 00 00 28 98 1f f4 1f e8  ..........(.....
00000010  1f d0 1f dc 1f c4 1f a0 1f 94 1f b8 1f 88 1f 64  ...............d
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3452
  数据头部: 78 9c dd d8 7f 4c a3 f5
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xd7c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  05 00 00 02 08 07 a0 00 00 00 20 2d 08 0c 08 18  .......... -....
00000010  08 24 08 30 08 3c 08 48 08 54 08 60 08 6c 08 78  .$.0.<.H.T.`.l.x
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 2972
  数据头部: 78 9c ed 98 79 54 93 57
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0xb9c
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 02 08 f7 00 08 f7 18 6f 18 0d 1c 07  ...........o....
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4316
  数据头部: 78 9c 5d 99 79 40 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x10dc
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 ee ce ce 13 78 00 02 18 40 00 80 5a 42  ..%....<EMAIL>
00000010  f8 03 01 00 b4 02 57 69 c7 8e ea 88 00 02 18 40  ......Wi.......@
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4580
  数据头部: 78 9c 65 98 77 58 53 e7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11e4
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 ef 00 02 10 c0 00 80 fc 71 45 03 01 00  ..%........qE...
00000010  b4 1b 3a 30 90 4e ed 88 17 61 1b c4 f5 1e 00 68  ..:0.N...a.....h
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4775
  数据头部: 78 9c dd 58 77 5c 54 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12a7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f0 80 fc 71 45 03 01 00 b4 03 00 11 4b  ..%...qE.......K
00000010  50 03 b8 fb 00 00 04 18 40 00 80 fc 71 45 03 01  <EMAIL>..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4316
  数据头部: 78 9c 5d 99 79 40 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x10dc
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 ee ce ce 13 78 00 02 18 40 00 80 5a 42  ..%....<EMAIL>
00000010  f8 03 01 00 b4 02 57 69 c7 8e ea 88 00 02 18 40  ......Wi.......@
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4580
  数据头部: 78 9c 65 98 77 58 53 e7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x11e4
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 ef 00 02 10 c0 00 80 fc 71 45 03 01 00  ..%........qE...
00000010  b4 1b 3a 30 90 4e ed 88 17 61 1b c4 f5 1e 00 68  ..:0.N...a.....h
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4775
  数据头部: 78 9c dd 58 77 5c 54 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12a7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f0 80 fc 71 45 03 01 00 b4 03 00 11 4b  ..%...qE.......K
00000010  50 03 b8 fb 00 00 04 18 40 00 80 fc 71 45 03 01  <EMAIL>..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4256
  数据头部: 78 9c ad 59 09 58 54 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x10a0
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f1 cf 7a 26 f9 03 00 01 4a 1d b8 a6 07  ..%..z&....J....
00000010  01 00 b4 66 5d 52 7c 00 00 f0 1f 37 00 0c 01 03  ...f]R|....7....
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4377
  数据头部: 78 9c a5 59 0b 58 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1119
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f2 bd 84 26 f9 03 00 01 f3 2c 7c 5f 03  ..%...&.....,|_.
00000010  01 00 b4 66 5a 13 4f 00 58 5d cb 84 26 f9 03 00  ...fZ.O.X]..&...
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5249
  数据头部: 78 9c 95 59 79 40 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1481
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f3 26 f9 03 00 01 52 11 41 62 03 01 00  ..%.&....R.Ab...
00000010  b4 43 34 92 22 00 00 f0 1f 32 00 0c 0b 01 31 2e  .C4."....2....1.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5963
  数据头部: 78 9c 85 59 7b 5c cc d9
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x174b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f4 f9 03 00 01 23 9e 5f 9e 8a 26 f9 03  ..%.....#._..&..
00000010  00 01 04 e1 e0 70 7e 80 f9 03 00 01 11 93 e6 dd  .....p~.........
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5529
  数据头部: 78 9c 4d 59 79 5c 4d 5b
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1599
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f5 03 01 5f 02 2f 02 57 b8 88 e0 01 08  ..%..._./.W.....
00000010  57 b1 08 0e dd e8 3f 19 e1 47 13 cd be 41 f8 34  W.....?..G...A.4
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5587
  数据头部: 78 9c 9d 58 69 58 54 47
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x15d3
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 00 00 93 14 4c 56 51 c5 ab 6c f0 01 04 5c  ......LVQ..l...\
00000010  73 d3 50 08 ec 2e 11 3d c5 00 f0 01 04 00 02 4f  s.P....=.......O
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4316
  数据头部: 78 9c 5d 99 79 40 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x10dc
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 ee ce ce 13 78 00 02 18 40 00 80 5a 42  ..%....<EMAIL>
00000010  f8 03 01 00 b4 02 57 69 c7 8e ea 88 00 02 18 40  ......Wi.......@
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4580
  数据头部: 78 9c 65 98 77 58 53 e7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11e4
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 ef 00 02 10 c0 00 80 fc 71 45 03 01 00  ..%........qE...
00000010  b4 1b 3a 30 90 4e ed 88 17 61 1b c4 f5 1e 00 68  ..:0.N...a.....h
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4775
  数据头部: 78 9c dd 58 77 5c 54 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12a7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f0 80 fc 71 45 03 01 00 b4 03 00 11 4b  ..%...qE.......K
00000010  50 03 b8 fb 00 00 04 18 40 00 80 fc 71 45 03 01  <EMAIL>..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4256
  数据头部: 78 9c ad 59 09 58 54 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x10a0
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f1 cf 7a 26 f9 03 00 01 4a 1d b8 a6 07  ..%..z&....J....
00000010  01 00 b4 66 5d 52 7c 00 00 f0 1f 37 00 0c 01 03  ...f]R|....7....
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4377
  数据头部: 78 9c a5 59 0b 58 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1119
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f2 bd 84 26 f9 03 00 01 f3 2c 7c 5f 03  ..%...&.....,|_.
00000010  01 00 b4 66 5a 13 4f 00 58 5d cb 84 26 f9 03 00  ...fZ.O.X]..&...
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5249
  数据头部: 78 9c 95 59 79 40 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1481
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f3 26 f9 03 00 01 52 11 41 62 03 01 00  ..%.&....R.Ab...
00000010  b4 43 34 92 22 00 00 f0 1f 32 00 0c 0b 01 31 2e  .C4."....2....1.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5963
  数据头部: 78 9c 85 59 7b 5c cc d9
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x174b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f4 f9 03 00 01 23 9e 5f 9e 8a 26 f9 03  ..%.....#._..&..
00000010  00 01 04 e1 e0 70 7e 80 f9 03 00 01 11 93 e6 dd  .....p~.........
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4316
  数据头部: 78 9c 5d 99 79 40 53 57
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x10dc
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 ee ce ce 13 78 00 02 18 40 00 80 5a 42  ..%....<EMAIL>
00000010  f8 03 01 00 b4 02 57 69 c7 8e ea 88 00 02 18 40  ......Wi.......@
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4580
  数据头部: 78 9c 65 98 77 58 53 e7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11e4
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 ef 00 02 10 c0 00 80 fc 71 45 03 01 00  ..%........qE...
00000010  b4 1b 3a 30 90 4e ed 88 17 61 1b c4 f5 1e 00 68  ..:0.N...a.....h
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4775
  数据头部: 78 9c dd 58 77 5c 54 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12a7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f0 80 fc 71 45 03 01 00 b4 03 00 11 4b  ..%...qE.......K
00000010  50 03 b8 fb 00 00 04 18 40 00 80 fc 71 45 03 01  <EMAIL>..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4316
  数据头部: 78 9c 5d 99 79 40 53 57
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x10dc
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 ee ce ce 13 78 00 02 18 40 00 80 5a 42  ..%....<EMAIL>
00000010  f8 03 01 00 b4 02 57 69 c7 8e ea 88 00 02 18 40  ......Wi.......@
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4580
  数据头部: 78 9c 65 98 77 58 53 e7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11e4
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 ef 00 02 10 c0 00 80 fc 71 45 03 01 00  ..%........qE...
00000010  b4 1b 3a 30 90 4e ed 88 17 61 1b c4 f5 1e 00 68  ..:0.N...a.....h
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4775
  数据头部: 78 9c dd 58 77 5c 54 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12a7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f0 80 fc 71 45 03 01 00 b4 03 00 11 4b  ..%...qE.......K
00000010  50 03 b8 fb 00 00 04 18 40 00 80 fc 71 45 03 01  <EMAIL>..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4256
  数据头部: 78 9c ad 59 09 58 54 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x10a0
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f1 cf 7a 26 f9 03 00 01 4a 1d b8 a6 07  ..%..z&....J....
00000010  01 00 b4 66 5d 52 7c 00 00 f0 1f 37 00 0c 01 03  ...f]R|....7....
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4377
  数据头部: 78 9c a5 59 0b 58 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1119
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f2 bd 84 26 f9 03 00 01 f3 2c 7c 5f 03  ..%...&.....,|_.
00000010  01 00 b4 66 5a 13 4f 00 58 5d cb 84 26 f9 03 00  ...fZ.O.X]..&...
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5249
  数据头部: 78 9c 95 59 79 40 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1481
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f3 26 f9 03 00 01 52 11 41 62 03 01 00  ..%.&....R.Ab...
00000010  b4 43 34 92 22 00 00 f0 1f 32 00 0c 0b 01 31 2e  .C4."....2....1.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4775
  数据头部: 78 9c dd 58 77 5c 54 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12a7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f0 80 fc 71 45 03 01 00 b4 03 00 11 4b  ..%...qE.......K
00000010  50 03 b8 fb 00 00 04 18 40 00 80 fc 71 45 03 01  <EMAIL>..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4256
  数据头部: 78 9c ad 59 09 58 54 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x10a0
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f1 cf 7a 26 f9 03 00 01 4a 1d b8 a6 07  ..%..z&....J....
00000010  01 00 b4 66 5d 52 7c 00 00 f0 1f 37 00 0c 01 03  ...f]R|....7....
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4377
  数据头部: 78 9c a5 59 0b 58 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1119
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f2 bd 84 26 f9 03 00 01 f3 2c 7c 5f 03  ..%...&.....,|_.
00000010  01 00 b4 66 5a 13 4f 00 58 5d cb 84 26 f9 03 00  ...fZ.O.X]..&...
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5249
  数据头部: 78 9c 95 59 79 40 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1481
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f3 26 f9 03 00 01 52 11 41 62 03 01 00  ..%.&....R.Ab...
00000010  b4 43 34 92 22 00 00 f0 1f 32 00 0c 0b 01 31 2e  .C4."....2....1.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5587
  数据头部: 78 9c 9d 58 69 58 54 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x15d3
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 00 00 93 14 4c 56 51 c5 ab 6c f0 01 04 5c  ......LVQ..l...\
00000010  73 d3 50 08 ec 2e 11 3d c5 00 f0 01 04 00 02 4f  s.P....=.......O
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5249
  数据头部: 78 9c 95 59 79 40 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1481
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f3 26 f9 03 00 01 52 11 41 62 03 01 00  ..%.&....R.Ab...
00000010  b4 43 34 92 22 00 00 f0 1f 32 00 0c 0b 01 31 2e  .C4."....2....1.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5963
  数据头部: 78 9c 85 59 7b 5c cc d9
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x174b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f4 f9 03 00 01 23 9e 5f 9e 8a 26 f9 03  ..%.....#._..&..
00000010  00 01 04 e1 e0 70 7e 80 f9 03 00 01 11 93 e6 dd  .....p~.........
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4775
  数据头部: 78 9c dd 58 77 5c 54 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12a7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f0 80 fc 71 45 03 01 00 b4 03 00 11 4b  ..%...qE.......K
00000010  50 03 b8 fb 00 00 04 18 40 00 80 fc 71 45 03 01  <EMAIL>..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4256
  数据头部: 78 9c ad 59 09 58 54 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x10a0
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f1 cf 7a 26 f9 03 00 01 4a 1d b8 a6 07  ..%..z&....J....
00000010  01 00 b4 66 5d 52 7c 00 00 f0 1f 37 00 0c 01 03  ...f]R|....7....
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4377
  数据头部: 78 9c a5 59 0b 58 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1119
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f2 bd 84 26 f9 03 00 01 f3 2c 7c 5f 03  ..%...&.....,|_.
00000010  01 00 b4 66 5a 13 4f 00 58 5d cb 84 26 f9 03 00  ...fZ.O.X]..&...
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5249
  数据头部: 78 9c 95 59 79 40 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1481
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f3 26 f9 03 00 01 52 11 41 62 03 01 00  ..%.&....R.Ab...
00000010  b4 43 34 92 22 00 00 f0 1f 32 00 0c 0b 01 31 2e  .C4."....2....1.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4775
  数据头部: 78 9c dd 58 77 5c 54 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12a7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f0 80 fc 71 45 03 01 00 b4 03 00 11 4b  ..%...qE.......K
00000010  50 03 b8 fb 00 00 04 18 40 00 80 fc 71 45 03 01  <EMAIL>..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4256
  数据头部: 78 9c ad 59 09 58 54 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x10a0
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f1 cf 7a 26 f9 03 00 01 4a 1d b8 a6 07  ..%..z&....J....
00000010  01 00 b4 66 5d 52 7c 00 00 f0 1f 37 00 0c 01 03  ...f]R|....7....
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4377
  数据头部: 78 9c a5 59 0b 58 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1119
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f2 bd 84 26 f9 03 00 01 f3 2c 7c 5f 03  ..%...&.....,|_.
00000010  01 00 b4 66 5a 13 4f 00 58 5d cb 84 26 f9 03 00  ...fZ.O.X]..&...
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5249
  数据头部: 78 9c 95 59 79 40 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1481
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 25 f3 26 f9 03 00 01 52 11 41 62 03 01 00  ..%.&....R.Ab...
00000010  b4 43 34 92 22 00 00 f0 1f 32 00 0c 0b 01 31 2e  .C4."....2....1.
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xc35
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 5304
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 b4 14 00 00 b4 14 00 00  x...............
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x865
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 3704
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 74 0e 00 00 74 0e 00 00  x.......t...t...
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8aa
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 12024
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 f4 2e 00 00 f4 2e 00 00  x...............
[统计] 文件读取: 325, zlib解压: 137, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 325, zlib解压: 137, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 325, zlib解压: 137, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 325, zlib解压: 137, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 325, zlib解压: 137, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 325, zlib解压: 137, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 325, zlib解压: 137, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 325, zlib解压: 137, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 325, zlib解压: 137, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3676
  数据头部: 78 9c ed 98 fb 57 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xe5c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0c ea 00 0c ea 1b 3d 00 00 00 00  ...........=....
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4839
  数据头部: 78 9c d5 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12e7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bf f8 03 01 00 b4 0c 47 c0 90 d0 08 e3  ..&.......G.....
00000010  09 e1 0a da 0b db 09 de 0a d9 0a dd 09 de 0a dc  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5120
  数据头部: 78 9c bd 59 79 5c 13 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1400
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c1 00 b4 1e 06 70 87 34 6d a4 14 32 00  ..&.....p.4m..2.
00000010  0c 01 01 cd c8 6d 8f 75 f9 03 00 01 f2 30 e6 4d  .....m.u.....0.M
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3057
  数据头部: 78 9c ed 57 f9 5b 93 57
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0xbf1
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0a 8a 00 0a 8a 18 0d 1c 07 00 00  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5013
  数据头部: 78 9c bd 58 09 54 93 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1395
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 b9 81 3d d2 00 82 03 21 c0 10 43 fc 0b  ..&..=....!..C..
00000010  f4 0d bd 62 cf 48 b3 74 82 f4 0b be 21 de a1 53  ...b.H.t....!..S
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4473
  数据头部: 78 9c 95 59 79 40 14 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1179
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ba 00 ff 30 b3 0b 01 00 ff 3c b4 0b 01  ..&...0.....<...
00000010  00 ff 3c b5 0b 01 00 ff 3c b6 0b 01 00 ff 30 b7  ..<.....<.....0.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5643
  数据头部: 78 9c ad 58 09 58 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x160b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bb 01 00 3c 2c 3e 31 40 54 41 5d 43 83  ..&...<,>1@TA]C.
00000010  01 45 89 01 02 04 01 15 02 00 00 00 11 27 00 00  .E...........'..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3676
  数据头部: 78 9c ed 98 fb 57 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xe5c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0c ea 00 0c ea 1b 3d 00 00 00 00  ...........=....
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4839
  数据头部: 78 9c d5 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12e7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bf f8 03 01 00 b4 0c 47 c0 90 d0 08 e3  ..&.......G.....
00000010  09 e1 0a da 0b db 09 de 0a d9 0a dd 09 de 0a dc  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5120
  数据头部: 78 9c bd 59 79 5c 13 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1400
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c1 00 b4 1e 06 70 87 34 6d a4 14 32 00  ..&.....p.4m..2.
00000010  0c 01 01 cd c8 6d 8f 75 f9 03 00 01 f2 30 e6 4d  .....m.u.....0.M
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3057
  数据头部: 78 9c ed 57 f9 5b 93 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xbf1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0a 8a 00 0a 8a 18 0d 1c 07 00 00  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5013
  数据头部: 78 9c bd 58 09 54 93 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1395
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 b9 81 3d d2 00 82 03 21 c0 10 43 fc 0b  ..&..=....!..C..
00000010  f4 0d bd 62 cf 48 b3 74 82 f4 0b be 21 de a1 53  ...b.H.t....!..S
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4473
  数据头部: 78 9c 95 59 79 40 14 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1179
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ba 00 ff 30 b3 0b 01 00 ff 3c b4 0b 01  ..&...0.....<...
00000010  00 ff 3c b5 0b 01 00 ff 3c b6 0b 01 00 ff 30 b7  ..<.....<.....0.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5643
  数据头部: 78 9c ad 58 09 58 53 d7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x160b
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bb 01 00 3c 2c 3e 31 40 54 41 5d 43 83  ..&...<,>1@TA]C.
00000010  01 45 89 01 02 04 01 15 02 00 00 00 11 27 00 00  .E...........'..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3676
  数据头部: 78 9c ed 98 fb 57 53 d7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0xe5c
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0c ea 00 0c ea 1b 3d 00 00 00 00  ...........=....
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4839
  数据头部: 78 9c d5 98 79 58 53 47
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x12e7
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bf f8 03 01 00 b4 0c 47 c0 90 d0 08 e3  ..&.......G.....
00000010  09 e1 0a da 0b db 09 de 0a d9 0a dd 09 de 0a dc  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5120
  数据头部: 78 9c bd 59 79 5c 13 d7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x1400
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c1 00 b4 1e 06 70 87 34 6d a4 14 32 00  ..&.....p.4m..2.
00000010  0c 01 01 cd c8 6d 8f 75 f9 03 00 01 f2 30 e6 4d  .....m.u.....0.M
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3057
  数据头部: 78 9c ed 57 f9 5b 93 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xbf1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0a 8a 00 0a 8a 18 0d 1c 07 00 00  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5013
  数据头部: 78 9c bd 58 09 54 93 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1395
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 b9 81 3d d2 00 82 03 21 c0 10 43 fc 0b  ..&..=....!..C..
00000010  f4 0d bd 62 cf 48 b3 74 82 f4 0b be 21 de a1 53  ...b.H.t....!..S
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4473
  数据头部: 78 9c 95 59 79 40 14 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1179
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ba 00 ff 30 b3 0b 01 00 ff 3c b4 0b 01  ..&...0.....<...
00000010  00 ff 3c b5 0b 01 00 ff 3c b6 0b 01 00 ff 30 b7  ..<.....<.....0.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5643
  数据头部: 78 9c ad 58 09 58 53 d7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x160b
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bb 01 00 3c 2c 3e 31 40 54 41 5d 43 83  ..&...<,>1@TA]C.
00000010  01 45 89 01 02 04 01 15 02 00 00 00 11 27 00 00  .E...........'..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3676
  数据头部: 78 9c ed 98 fb 57 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xe5c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0c ea 00 0c ea 1b 3d 00 00 00 00  ...........=....
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4839
  数据头部: 78 9c d5 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12e7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bf f8 03 01 00 b4 0c 47 c0 90 d0 08 e3  ..&.......G.....
00000010  09 e1 0a da 0b db 09 de 0a d9 0a dd 09 de 0a dc  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5120
  数据头部: 78 9c bd 59 79 5c 13 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1400
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c1 00 b4 1e 06 70 87 34 6d a4 14 32 00  ..&.....p.4m..2.
00000010  0c 01 01 cd c8 6d 8f 75 f9 03 00 01 f2 30 e6 4d  .....m.u.....0.M
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4839
  数据头部: 78 9c d5 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12e7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bf f8 03 01 00 b4 0c 47 c0 90 d0 08 e3  ..&.......G.....
00000010  09 e1 0a da 0b db 09 de 0a d9 0a dd 09 de 0a dc  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5120
  数据头部: 78 9c bd 59 79 5c 13 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1400
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c1 00 b4 1e 06 70 87 34 6d a4 14 32 00  ..&.....p.4m..2.
00000010  0c 01 01 cd c8 6d 8f 75 f9 03 00 01 f2 30 e6 4d  .....m.u.....0.M
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6682
  数据头部: 78 9c 45 58 79 40 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1a1a
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c2 a7 30 9b fa 48 26 49 c1 2e 12 1b ce  ..&..0..H&I.....
00000010  00 6f 38 1c 04 7f 00 6f f8 11 f3 19 bd c4 b7 f0  .o8....o........
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4839
  数据头部: 78 9c d5 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12e7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bf f8 03 01 00 b4 0c 47 c0 90 d0 08 e3  ..&.......G.....
00000010  09 e1 0a da 0b db 09 de 0a d9 0a dd 09 de 0a dc  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3057
  数据头部: 78 9c ed 57 f9 5b 93 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xbf1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0a 8a 00 0a 8a 18 0d 1c 07 00 00  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5013
  数据头部: 78 9c bd 58 09 54 93 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1395
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 b9 81 3d d2 00 82 03 21 c0 10 43 fc 0b  ..&..=....!..C..
00000010  f4 0d bd 62 cf 48 b3 74 82 f4 0b be 21 de a1 53  ...b.H.t....!..S
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4473
  数据头部: 78 9c 95 59 79 40 14 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1179
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ba 00 ff 30 b3 0b 01 00 ff 3c b4 0b 01  ..&...0.....<...
00000010  00 ff 3c b5 0b 01 00 ff 3c b6 0b 01 00 ff 30 b7  ..<.....<.....0.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5643
  数据头部: 78 9c ad 58 09 58 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x160b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bb 01 00 3c 2c 3e 31 40 54 41 5d 43 83  ..&...<,>1@TA]C.
00000010  01 45 89 01 02 04 01 15 02 00 00 00 11 27 00 00  .E...........'..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6076
  数据头部: 78 9c 4d 58 77 40 54 c7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x17bc
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bc c0 24 00 f2 80 bf 70 6b b0 1d ea c4  ..&..$....pk....
00000010  ec 20 41 c2 01 3e af ff f5 fe fe 30 bc 57 c3 f0  . A..>.....0.W..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5013
  数据头部: 78 9c bd 58 09 54 93 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1395
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 b9 81 3d d2 00 82 03 21 c0 10 43 fc 0b  ..&..=....!..C..
00000010  f4 0d bd 62 cf 48 b3 74 82 f4 0b be 21 de a1 53  ...b.H.t....!..S
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4473
  数据头部: 78 9c 95 59 79 40 14 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1179
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ba 00 ff 30 b3 0b 01 00 ff 3c b4 0b 01  ..&...0.....<...
00000010  00 ff 3c b5 0b 01 00 ff 3c b6 0b 01 00 ff 30 b7  ..<.....<.....0.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5643
  数据头部: 78 9c ad 58 09 58 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x160b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bb 01 00 3c 2c 3e 31 40 54 41 5d 43 83  ..&...<,>1@TA]C.
00000010  01 45 89 01 02 04 01 15 02 00 00 00 11 27 00 00  .E...........'..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5013
  数据头部: 78 9c bd 58 09 54 93 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1395
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 b9 81 3d d2 00 82 03 21 c0 10 43 fc 0b  ..&..=....!..C..
00000010  f4 0d bd 62 cf 48 b3 74 82 f4 0b be 21 de a1 53  ...b.H.t....!..S
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4473
  数据头部: 78 9c 95 59 79 40 14 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1179
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ba 00 ff 30 b3 0b 01 00 ff 3c b4 0b 01  ..&...0.....<...
00000010  00 ff 3c b5 0b 01 00 ff 3c b6 0b 01 00 ff 30 b7  ..<.....<.....0.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 2958
  数据头部: 78 9c ed d8 7f 50 a4 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xb8e
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  05 00 00 01 bf 0b 0c 00 00 00 0a b4 0b 78 0b 84  .............x..
00000010  0b 90 0b 9c 0b a8 0b b4 0b c0 0b d8 0b 48 0b cc  .............H..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4133
  数据头部: 78 9c ed 98 e9 5f 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1025
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 10 38 00 10 38 1c 06 00 00 00 00  ......8..8......
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4815
  数据头部: 78 9c bd 57 79 54 13 67
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12cf
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c7 0e d2 7e f1 2c fa 12 ef 39 05 06 1c  ..&...~.,...9...
00000010  07 03 02 02 0b e9 4c 06 08 37 16 00 00 18 c0 00  ......L..7......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5142
  数据头部: 78 9c b5 58 09 58 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1416
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c8 00 01 30 00 1c 36 e5 8f b7 e6 a5 bc  ..&...0..6......
00000010  00 01 30 00 24 31 32 38 e5 8f b7 e6 a5 bc 00 01  ..0.$128........
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6331
  数据头部: 78 9c 55 59 79 5c 13 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x18bb
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c9 06 03 60 b0 84 26 0f 83 43 00 68 2e  ..&...`..&..C.h.
00000010  0c 0d 82 6f 98 28 13 5f 62 0e 12 4e 17 1f 32 4b  ...o.(._b..N..2K
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4815
  数据头部: 78 9c bd 57 79 54 13 67
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12cf
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c7 0e d2 7e f1 2c fa 12 ef 39 05 06 1c  ..&...~.,...9...
00000010  07 03 02 02 0b e9 4c 06 08 37 16 00 00 18 c0 00  ......L..7......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3676
  数据头部: 78 9c ed 98 fb 57 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xe5c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0c ea 00 0c ea 1b 3d 00 00 00 00  ...........=....
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5013
  数据头部: 78 9c bd 58 09 54 93 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1395
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 b9 81 3d d2 00 82 03 21 c0 10 43 fc 0b  ..&..=....!..C..
00000010  f4 0d bd 62 cf 48 b3 74 82 f4 0b be 21 de a1 53  ...b.H.t....!..S
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4473
  数据头部: 78 9c 95 59 79 40 14 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1179
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ba 00 ff 30 b3 0b 01 00 ff 3c b4 0b 01  ..&...0.....<...
00000010  00 ff 3c b5 0b 01 00 ff 3c b6 0b 01 00 ff 30 b7  ..<.....<.....0.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5643
  数据头部: 78 9c ad 58 09 58 53 d7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bb 01 00 3c 2c 3e 31 40 54 41 5d 43 83  ..&...<,>1@TA]C.
Error: access violation accessing 0x160b
    at frida/runtime/core.js:144
00000010  01 45 89 01 02 04 01 15 02 00 00 00 11 27 00 00  .E...........'..
    at /verification_script.js:100
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6076
  数据头部: 78 9c 4d 58 77 40 54 c7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x17bc
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bc c0 24 00 f2 80 bf 70 6b b0 1d ea c4  ..&..$....pk....
00000010  ec 20 41 c2 01 3e af ff f5 fe fe 30 bc 57 c3 f0  . A..>.....0.W..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5431
  数据头部: 78 9c bd 59 79 5c 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1537
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 00 00 2a 37 00 00 0e 0a f3 51 1c 60 00 00  ....*7.....Q.`..
00000010  d0 49 09 00 0e 0a f3 51 1d 0c 00 00 03 37 00 00  .I.....Q.....7..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3676
  数据头部: 78 9c ed 98 fb 57 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xe5c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0c ea 00 0c ea 1b 3d 00 00 00 00  ...........=....
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4839
  数据头部: 78 9c d5 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12e7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bf f8 03 01 00 b4 0c 47 c0 90 d0 08 e3  ..&.......G.....
00000010  09 e1 0a da 0b db 09 de 0a d9 0a dd 09 de 0a dc  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5120
  数据头部: 78 9c bd 59 79 5c 13 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1400
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c1 00 b4 1e 06 70 87 34 6d a4 14 32 00  ..&.....p.4m..2.
00000010  0c 01 01 cd c8 6d 8f 75 f9 03 00 01 f2 30 e6 4d  .....m.u.....0.M
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6682
  数据头部: 78 9c 45 58 79 40 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1a1a
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c2 a7 30 9b fa 48 26 49 c1 2e 12 1b ce  ..&..0..H&I.....
00000010  00 6f 38 1c 04 7f 00 6f f8 11 f3 19 bd c4 b7 f0  .o8....o........
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4839
  数据头部: 78 9c d5 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12e7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bf f8 03 01 00 b4 0c 47 c0 90 d0 08 e3  ..&.......G.....
00000010  09 e1 0a da 0b db 09 de 0a d9 0a dd 09 de 0a dc  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5013
  数据头部: 78 9c bd 58 09 54 93 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1395
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 b9 81 3d d2 00 82 03 21 c0 10 43 fc 0b  ..&..=....!..C..
00000010  f4 0d bd 62 cf 48 b3 74 82 f4 0b be 21 de a1 53  ...b.H.t....!..S
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4473
  数据头部: 78 9c 95 59 79 40 14 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1179
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ba 00 ff 30 b3 0b 01 00 ff 3c b4 0b 01  ..&...0.....<...
00000010  00 ff 3c b5 0b 01 00 ff 3c b6 0b 01 00 ff 30 b7  ..<.....<.....0.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5643
  数据头部: 78 9c ad 58 09 58 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x160b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bb 01 00 3c 2c 3e 31 40 54 41 5d 43 83  ..&...<,>1@TA]C.
00000010  01 45 89 01 02 04 01 15 02 00 00 00 11 27 00 00  .E...........'..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6076
  数据头部: 78 9c 4d 58 77 40 54 c7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x17bc
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bc c0 24 00 f2 80 bf 70 6b b0 1d ea c4  ..&..$....pk....
00000010  ec 20 41 c2 01 3e af ff f5 fe fe 30 bc 57 c3 f0  . A..>.....0.W..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 2958
  数据头部: 78 9c ed d8 7f 50 a4 75
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xb8e
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  05 00 00 01 bf 0b 0c 00 00 00 0a b4 0b 78 0b 84  .............x..
00000010  0b 90 0b 9c 0b a8 0b b4 0b c0 0b d8 0b 48 0b cc  .............H..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4133
  数据头部: 78 9c ed 98 e9 5f 53 47
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x1025
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 10 38 00 10 38 1c 06 00 00 00 00  ......8..8......
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4815
  数据头部: 78 9c bd 57 79 54 13 67
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12cf
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 8192
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c7 0e d2 7e f1 2c fa 12 ef 39 05 06 1c  ..&...~.,...9...
00000010  07 03 02 02 0b e9 4c 06 08 37 16 00 00 18 c0 00  ......L..7......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5142
  数据头部: 78 9c b5 58 09 58 53 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1416
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 8192
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c8 00 01 30 00 1c 36 e5 8f b7 e6 a5 bc  ..&...0..6......
00000010  00 01 30 00 24 31 32 38 e5 8f b7 e6 a5 bc 00 01  ..0.$128........
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6331
  数据头部: 78 9c 55 59 79 5c 13 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x18bb
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c9 06 03 60 b0 84 26 0f 83 43 00 68 2e  ..&...`..&..C.h.
00000010  0c 0d 82 6f 98 28 13 5f 62 0e 12 4e 17 1f 32 4b  ...o.(._b..N..2K
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5013
  数据头部: 78 9c bd 58 09 54 93 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1395
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 8192
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 b9 81 3d d2 00 82 03 21 c0 10 43 fc 0b  ..&..=....!..C..
00000010  f4 0d bd 62 cf 48 b3 74 82 f4 0b be 21 de a1 53  ...b.H.t....!..S
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4473
  数据头部: 78 9c 95 59 79 40 14 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1179
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ba 00 ff 30 b3 0b 01 00 ff 3c b4 0b 01  ..&...0.....<...
00000010  00 ff 3c b5 0b 01 00 ff 3c b6 0b 01 00 ff 30 b7  ..<.....<.....0.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5643
  数据头部: 78 9c ad 58 09 58 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x160b
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 8192
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bb 01 00 3c 2c 3e 31 40 54 41 5d 43 83  ..&...<,>1@TA]C.
00000010  01 45 89 01 02 04 01 15 02 00 00 00 11 27 00 00  .E...........'..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6076
  数据头部: 78 9c 4d 58 77 40 54 c7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x17bc
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bc c0 24 00 f2 80 bf 70 6b b0 1d ea c4  ..&..$....pk....
00000010  ec 20 41 c2 01 3e af ff f5 fe fe 30 bc 57 c3 f0  . A..>.....0.W..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5431
  数据头部: 78 9c bd 59 79 5c 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1537
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 00 00 2a 37 00 00 0e 0a f3 51 1c 60 00 00  ....*7.....Q.`..
00000010  d0 49 09 00 0e 0a f3 51 1d 0c 00 00 03 37 00 00  .I.....Q.....7..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4815
  数据头部: 78 9c bd 57 79 54 13 67
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12cf
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c7 0e d2 7e f1 2c fa 12 ef 39 05 06 1c  ..&...~.,...9...
00000010  07 03 02 02 0b e9 4c 06 08 37 16 00 00 18 c0 00  ......L..7......
[统计] 文件读取: 421, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 421, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 421, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 421, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 421, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 421, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[统计] 文件读取: 423, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 423, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 423, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 423, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 423, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 423, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 423, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 423, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 424, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[统计] 文件读取: 426, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 426, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 426, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 426, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 426, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 426, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 426, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 426, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 426, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[统计] 文件读取: 427, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 427, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 427, zlib解压: 233, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4228
  数据头部: 78 9c ed 58 69 58 53 d7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x1084
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 02 04 8c 00 04 8c 0f 89 00 00 00 00  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4633
  数据头部: 78 9c bd 58 09 50 54 c7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1219
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 cd 5e 4f 08 ab ee 05 00 00 18 c0 00 80  ..&.^O..........
00000010  5a 42 f8 03 01 00 b4 02 36 30 a8 cc b7 90 00 00  ZB......60......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6485
  数据头部: 78 9c 95 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1955
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ce e5 ae 85 36 32 2d 32 e4 b8 b4 00 01  ..&....62-2.....
00000010  30 00 30 e5 85 ac e5 85 b1 e5 8e 95 e6 89 80 00  0.0.............
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 8192
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4633
  数据头部: 78 9c bd 58 09 50 54 c7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1219
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 8192
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 cd 5e 4f 08 ab ee 05 00 00 18 c0 00 80  ..&.^O..........
00000010  5a 42 f8 03 01 00 b4 02 36 30 a8 cc b7 90 00 00  ZB......60......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6485
  数据头部: 78 9c 95 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1955
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ce e5 ae 85 36 32 2d 32 e4 b8 b4 00 01  ..&....62-2.....
00000010  30 00 30 e5 85 ac e5 85 b1 e5 8e 95 e6 89 80 00  0.0.............
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5414
  数据头部: 78 9c 9d 59 0b 54 53 d7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 00 00 10 fe 07 0b 02 bf 05 1d 3f 60 2f 55  ............?`/U
00000010  ff 88 13 bc ff 01 04 7a 81 1d 4e 03 c0 b0 13 f9  .......z..N.....
Error: access violation accessing 0x1526
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4633
  数据头部: 78 9c bd 58 09 50 54 c7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x1219
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 cd 5e 4f 08 ab ee 05 00 00 18 c0 00 80  ..&.^O..........
00000010  5a 42 f8 03 01 00 b4 02 36 30 a8 cc b7 90 00 00  ZB......60......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3676
  数据头部: 78 9c ed 98 fb 57 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xe5c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0c ea 00 0c ea 1b 3d 00 00 00 00  ...........=....
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4839
  数据头部: 78 9c d5 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12e7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bf f8 03 01 00 b4 0c 47 c0 90 d0 08 e3  ..&.......G.....
00000010  09 e1 0a da 0b db 09 de 0a d9 0a dd 09 de 0a dc  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5120
  数据头部: 78 9c bd 59 79 5c 13 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1400
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 8192
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c1 00 b4 1e 06 70 87 34 6d a4 14 32 00  ..&.....p.4m..2.
00000010  0c 01 01 cd c8 6d 8f 75 f9 03 00 01 f2 30 e6 4d  .....m.u.....0.M
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6485
  数据头部: 78 9c 95 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1955
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ce e5 ae 85 36 32 2d 32 e4 b8 b4 00 01  ..&....62-2.....
00000010  30 00 30 e5 85 ac e5 85 b1 e5 8e 95 e6 89 80 00  0.0.............
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5120
  数据头部: 78 9c bd 59 79 5c 13 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1400
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c1 00 b4 1e 06 70 87 34 6d a4 14 32 00  ..&.....p.4m..2.
00000010  0c 01 01 cd c8 6d 8f 75 f9 03 00 01 f2 30 e6 4d  .....m.u.....0.M
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5414
  数据头部: 78 9c 9d 59 0b 54 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1526
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 00 00 10 fe 07 0b 02 bf 05 1d 3f 60 2f 55  ............?`/U
00000010  ff 88 13 bc ff 01 04 7a 81 1d 4e 03 c0 b0 13 f9  .......z..N.....
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3057
  数据头部: 78 9c ed 57 f9 5b 93 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xbf1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0a 8a 00 0a 8a 18 0d 1c 07 00 00  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5013
  数据头部: 78 9c bd 58 09 54 93 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1395
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 b9 81 3d d2 00 82 03 21 c0 10 43 fc 0b  ..&..=....!..C..
00000010  f4 0d bd 62 cf 48 b3 74 82 f4 0b be 21 de a1 53  ...b.H.t....!..S
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4473
  数据头部: 78 9c 95 59 79 40 14 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1179
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ba 00 ff 30 b3 0b 01 00 ff 3c b4 0b 01  ..&...0.....<...
00000010  00 ff 3c b5 0b 01 00 ff 3c b6 0b 01 00 ff 30 b7  ..<.....<.....0.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5643
  数据头部: 78 9c ad 58 09 58 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x160b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bb 01 00 3c 2c 3e 31 40 54 41 5d 43 83  ..&...<,>1@TA]C.
00000010  01 45 89 01 02 04 01 15 02 00 00 00 11 27 00 00  .E...........'..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4133
  数据头部: 78 9c ed 98 e9 5f 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1025
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 10 38 00 10 38 1c 06 00 00 00 00  ......8..8......
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4815
  数据头部: 78 9c bd 57 79 54 13 67
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c7 0e d2 7e f1 2c fa 12 ef 39 05 06 1c  ..&...~.,...9...
00000010  07 03 02 02 0b e9 4c 06 08 37 16 00 00 18 c0 00  ......L..7......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5142
Error: access violation accessing 0x12cf
    at frida/runtime/core.js:144
    at /verification_script.js:100
  数据头部: 78 9c b5 58 09 58 53 57
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c8 00 01 30 00 1c 36 e5 8f b7 e6 a5 bc  ..&...0..6......
00000010  00 01 30 00 24 31 32 38 e5 8f b7 e6 a5 bc 00 01  ..0.$128........
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
Error: access violation accessing 0x1416
    at frida/runtime/core.js:144
  数据头部: 78 9c ed ca 31 0a c2 30
    at /verification_script.js:100
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4633
  数据头部: 78 9c bd 58 09 50 54 c7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 cd 5e 4f 08 ab ee 05 00 00 18 c0 00 80  ..&.^O..........
00000010  5a 42 f8 03 01 00 b4 02 36 30 a8 cc b7 90 00 00  ZB......60......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4815
  数据头部: 78 9c bd 57 79 54 13 67
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
Error: access violation accessing 0x1219
    at frida/runtime/core.js:144
    at /verification_script.js:100
Error: access violation accessing 0x12cf
    at frida/runtime/core.js:144
    at /verification_script.js:100
Error: access violation accessing 0x1416
    at frida/runtime/core.js:144
    at /verification_script.js:100
Error: access violation accessing 0x1219
    at frida/runtime/core.js:144
    at /verification_script.js:100
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c7 0e d2 7e f1 2c fa 12 ef 39 05 06 1c  ..&...~.,...9...
00000010  07 03 02 02 0b e9 4c 06 08 37 16 00 00 18 c0 00  ......L..7......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5142
  数据头部: 78 9c b5 58 09 58 53 57
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c8 00 01 30 00 1c 36 e5 8f b7 e6 a5 bc  ..&...0..6......
00000010  00 01 30 00 24 31 32 38 e5 8f b7 e6 a5 bc 00 01  ..0.$128........
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4633
  数据头部: 78 9c bd 58 09 50 54 c7
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 cd 5e 4f 08 ab ee 05 00 00 18 c0 00 80  ..&.^O..........
00000010  5a 42 f8 03 01 00 b4 02 36 30 a8 cc b7 90 00 00  ZB......60......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6485
  数据头部: 78 9c 95 98 79 58 53 47
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ce e5 ae 85 36 32 2d 32 e4 b8 b4 00 01  ..&....62-2.....
00000010  30 00 30 e5 85 ac e5 85 b1 e5 8e 95 e6 89 80 00  0.0.............
Error: access violation accessing 0x1955
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3452
  数据头部: 78 9c dd d8 7f 4c a3 f5
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xd7c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  05 00 00 02 08 07 a0 00 00 00 20 2d 08 0c 08 18  .......... -....
00000010  08 24 08 30 08 3c 08 48 08 54 08 60 08 6c 08 78  .$.0.<.H.T.`.l.x
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 3676
  数据头部: 78 9c ed 98 fb 57 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xe5c
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0c ea 00 0c ea 1b 3d 00 00 00 00  ...........=....
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4839
  数据头部: 78 9c d5 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12e7
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bf f8 03 01 00 b4 0c 47 c0 90 d0 08 e3  ..&.......G.....
00000010  09 e1 0a da 0b db 09 de 0a d9 0a dd 09 de 0a dc  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5120
  数据头部: 78 9c bd 59 79 5c 13 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1400
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c1 00 b4 1e 06 70 87 34 6d a4 14 32 00  ..&.....p.4m..2.
00000010  0c 01 01 cd c8 6d 8f 75 f9 03 00 01 f2 30 e6 4d  .....m.u.....0.M
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4561
  数据头部: 78 9c 8d 39 09 40 54 55
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x11d1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c0 06 e4 06 e4 06 ee 06 ee 06 f0 06 f0  ..&.............
00000010  06 f2 06 fa 06 fd 06 fe 06 85 07 86 07 8a 07 8a  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5120
  数据头部: 78 9c bd 59 79 5c 13 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1400
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c1 00 b4 1e 06 70 87 34 6d a4 14 32 00  ..&.....p.4m..2.
00000010  0c 01 01 cd c8 6d 8f 75 f9 03 00 01 f2 30 e6 4d  .....m.u.....0.M
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
  文件描述符: 232
  读取字节数: 3057
  数据头部: 78 9c ed 57 f9 5b 93 57
[Hook] zlib uncompress 被调用
Error: access violation accessing 0xbf1
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 0a 8a 00 0a 8a 18 0d 1c 07 00 00  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5013
  数据头部: 78 9c bd 58 09 54 93 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1395
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 b9 81 3d d2 00 82 03 21 c0 10 43 fc 0b  ..&..=....!..C..
00000010  f4 0d bd 62 cf 48 b3 74 82 f4 0b be 21 de a1 53  ...b.H.t....!..S
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4473
  数据头部: 78 9c 95 59 79 40 14 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1179
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ba 00 ff 30 b3 0b 01 00 ff 3c b4 0b 01  ..&...0.....<...
00000010  00 ff 3c b5 0b 01 00 ff 3c b6 0b 01 00 ff 30 b7  ..<.....<.....0.
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5643
  数据头部: 78 9c ad 58 09 58 53 d7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x160b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 bb 01 00 3c 2c 3e 31 40 54 41 5d 43 83  ..&...<,>1@TA]C.
00000010  01 45 89 01 02 04 01 15 02 00 00 00 11 27 00 00  .E...........'..
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4133
  数据头部: 78 9c ed 98 e9 5f 53 47
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 01 10 38 00 10 38 1c 06 00 00 00 00  ......8..8......
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
Error: access violation accessing 0x1025
    at frida/runtime/core.js:144
    at /verification_script.js:100
  文件描述符: 232
  读取字节数: 4815
  数据头部: 78 9c bd 57 79 54 13 67
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x12cf
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c7 0e d2 7e f1 2c fa 12 ef 39 05 06 1c  ..&...~.,...9...
00000010  07 03 02 02 0b e9 4c 06 08 37 16 00 00 18 c0 00  ......L..7......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 5142
  数据头部: 78 9c b5 58 09 58 53 57
[Hook] zlib uncompress 被调用
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 c8 00 01 30 00 1c 36 e5 8f b7 e6 a5 bc  ..&...0..6......
00000010  00 01 30 00 24 31 32 38 e5 8f b7 e6 a5 bc 00 01  ..0.$128........
[Hook] 检测到可能的.ans文件读取
Error: access violation accessing 0x1416
    at frida/runtime/core.js:144
    at /verification_script.js:100
  文件描述符: 232
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 4633
  数据头部: 78 9c bd 58 09 50 54 c7
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1219
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 cd 5e 4f 08 ab ee 05 00 00 18 c0 00 80  ..&.^O..........
00000010  5a 42 f8 03 01 00 b4 02 36 30 a8 cc b7 90 00 00  ZB......60......
[Hook] 检测到可能的.ans文件读取
  文件描述符: 232
  读取字节数: 6485
  数据头部: 78 9c 95 98 79 58 53 47
[Hook] zlib uncompress 被调用
Error: access violation accessing 0x1955
    at frida/runtime/core.js:144
    at /verification_script.js:100
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  00 00 26 ce e5 ae 85 36 32 2d 32 e4 b8 b4 00 01  ..&....62-2.....
00000010  30 00 30 e5 85 ac e5 85 b1 e5 8e 95 e6 89 80 00  0.0.............
[统计] 文件读取: 480, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 480, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 480, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 480, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 480, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 480, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 480, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 480, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[统计] 文件读取: 481, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 481, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 481, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 481, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 481, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 481, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 481, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 481, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 481, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[统计] 文件读取: 482, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[统计] 文件读取: 483, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 483, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 483, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 483, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[统计] 文件读取: 484, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 484, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 484, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[统计] 文件读取: 486, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 486, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取
  文件描述符: 53
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[统计] 文件读取: 487, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 487, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 487, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 487, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 487, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 487, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 487, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 487, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 487, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 487, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[统计] 文件读取: 487, zlib解压: 286, SQLite绑定: 0, AES解密: 0, 数据分发: 0

[Remote::com.autonavi.minimap]-> exit

Thank you for using Frida!
