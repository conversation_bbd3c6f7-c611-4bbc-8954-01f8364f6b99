/*
 * 高德地图目标函数监控脚本
 * 基于IDA Pro精确地址，只监控调用不读取内存
 * 稳定性优先，获取调用信息和参数
 * 版本: Frida 12.9.7 兼容
 */

console.log("[Targeted Hook] 启动目标函数监控脚本...");

var callCount = 0;
var maxCalls = 10;

function setupTargetedHooks() {
    setTimeout(function() {
        console.log("[Setup] 设置目标函数监控...");
        
        try {
            var lib = Module.findBaseAddress("libamapnsq.so");
            if (!lib) {
                console.log("[Error] 未找到libamapnsq.so");
                return;
            }
            
            console.log("[Library] 库基址: " + lib);
            
            // Hook 1: girf_sqlite3_bind_blob (0x15000)
            try {
                var sqlite_addr = lib.add(0x15000);
                Interceptor.attach(sqlite_addr, {
                    onEnter: function(args) {
                        if (callCount < maxCalls) {
                            callCount++;
                            console.log(" [SQLite #" + callCount + "] girf_sqlite3_bind_blob 被调用");
                            console.log("  参数0: " + args[0]);
                            console.log("  参数1: " + args[1]);
                            console.log("  参数2 (数据指针): " + args[2]);
                            console.log("  参数3 (数据大小): " + args[3]);
                            console.log("  参数4: " + args[4]);
                            
                            // 只记录大小，不读取内容
                            try {
                                var size = args[3].toInt32();
                                console.log("数据大小: " + size + " 字节");
                                if (size > 100 && size < 5000) {
                                    console.log("这可能是地图数据！");
                                }
                            } catch (e) {
                                console.log("无法读取大小");
                            }
                        }
                    },
                    onLeave: function(retval) {
                        if (callCount <= maxCalls) {
                            console.log("返回值: " + retval);
                        }
                    }
                });
                console.log("Hook 1: girf_sqlite3_bind_blob 设置成功");
            } catch (e) {
                console.log(" Hook 1 失败: " + e.message);
            }
            
            // Hook 2: sub_13B24 (数据处理函数)
            try {
                var process_addr = lib.add(0x13B24);
                Interceptor.attach(process_addr, {
                    onEnter: function(args) {
                        if (callCount < maxCalls) {
                            console.log(" [Process] sub_13B24 数据处理函数被调用");
                            console.log("  参数0: " + args[0]);
                            console.log("  参数1 (数据指针): " + args[1]);
                            console.log("  参数2 (数据大小): " + args[2]);
                            console.log("  参数3: " + args[3]);
                            console.log("  参数4: " + args[4]);
                            
                            try {
                                var size = args[2].toInt32();
                                console.log("处理数据大小: " + size + " 字节");
                            } catch (e) {
                                console.log("无法读取大小");
                            }
                        }
                    },
                    onLeave: function(retval) {
                        if (callCount <= maxCalls) {
                            console.log("  ← 处理结果: " + retval);
                        }
                    }
                });
                console.log(" Hook 2: sub_13B24 设置成功");
            } catch (e) {
                console.log("Hook 2 失败: " + e.message);
            }
            
            // Hook 3: 监控文件读取但不读取内容
            try {
                var readPtr = Module.findExportByName("libc.so", "read");
                if (readPtr) {
                    Interceptor.attach(readPtr, {
                        onEnter: function(args) {
                            this.fd = args[0].toInt32();
                            this.size = args[2].toInt32();
                            this.isInteresting = (this.size > 1000 && this.size < 100000);
                        },
                        onLeave: function(retval) {
                            if (this.isInteresting && callCount < maxCalls) {
                                var bytesRead = retval.toInt32();
                                if (bytesRead > 0) {
                                    console.log(" [File] 文件读取: fd=" + this.fd + ", 大小=" + bytesRead + " 字节");
                                }
                            }
                        }
                    });
                    console.log(" Hook 3: 文件读取监控设置成功");
                }
            } catch (e) {
                console.log(" Hook 3 失败: " + e.message);
            }
            
            console.log("[Ready] 目标函数监控准备就绪");
            console.log("请移动地图触发函数调用");
            
        } catch (e) {
            console.log("[Setup Error] " + e.message);
        }
    }, 2000);
}

function generateMonitorReport() {
    console.log("\n=== 目标函数监控报告 ===");
    console.log("监控到的函数调用: " + callCount + "/" + maxCalls);
    
    if (callCount >= maxCalls) {
        console.log(" 监控完成！");
        console.log(" 成功监控到函数调用模式");
    } else if (callCount > 0) {
        console.log(" 检测到活动，继续监控...");
    } else {
        console.log(" 请移动地图触发函数调用");
    }
    console.log("===============================\n");
}

// 启动监控
setupTargetedHooks();
setInterval(generateMonitorReport, 20000);

console.log("[Targeted Hook] 目标函数监控脚本已加载"); 