     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[.ans文件分析] 开始专门分析高德地图.ans文件...
[重大发现] 确认.ans文件确实存在于以下路径:
  - /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a*/m*.ans
  - /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
  - /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_*/*.ans
[1] 跟踪.ans文件打开...
[\u2713] .ans文件打开Hook设置成功
[\u2713] .ans文件读取Hook设置成功
[.ans文件分析] 专门分析脚本已启动...
[目标] 专门分析.ans文件的内容和结构
[提示] 请移动地图以触发.ans文件读取
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/geo_fence_global_v2.ans
  fd=55, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/geo_fence_global_v2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/geo_fence_global_v2.ans
  fd=55, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/geo_fence_global_v2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  fd=60, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/bundle_list.ans
  fd=63, 类型=CLOUD_RESOURCE
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  fd=60, 类型=RENDER_CONFIG
[.ans文件数据] CLOUD_RESOURCE - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/bundle_list.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/bundle_list.ans
  fd=63, 类型=CLOUD_RESOURCE
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] CLOUD_RESOURCE - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/bundle_list.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/bundle_list.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 1f 67 00 1f ce 1f 9a 1f 67 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x1f (31)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  fd=64, 类型=CLOUD_RESOURCE
[.ans文件数据] CLOUD_RESOURCE - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  fd=64, 类型=CLOUD_RESOURCE
[.ans文件数据] RENDER_CONFIG - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 0d 00 00 00 08 0a 72 00 1e 0e 1d 94 1d 36 1b 36
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x8 (8)
    textFlags: 0xa (10)
[.ans文件数据] CLOUD_RESOURCE - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  fd=60, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  fd=60, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 06 47 00 06 a1 06 47 1c 0a 17 ef
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x6 (6)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
  fd=65, 类型=CLOUD_RESOURCE
[.ans文件数据] RENDER_CONFIG - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 0d 00 00 00 08 0a 72 00 1e 0e 1d 94 1d 36 1b 36
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x8 (8)
    textFlags: 0xa (10)
[.ans文件数据] CLOUD_RESOURCE - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  fd=60, 类型=RENDER_CONFIG
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
  fd=65, 类型=CLOUD_RESOURCE
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] CLOUD_RESOURCE - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  fd=60, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/static_bundle_15.18.0.117.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 0f ad 00 0f ad 1f a4 0f da 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0xf (15)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/earth_bundle_15.12.0.49.ans
  fd=66, 类型=CLOUD_RESOURCE
[.ans文件数据] RENDER_CONFIG - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 0d 00 00 00 08 0a 72 00 1e 0e 1d 94 1d 36 1b 36
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x8 (8)
    textFlags: 0xa (10)
[.ans文件数据] CLOUD_RESOURCE - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/earth_bundle_15.12.0.49.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  fd=60, 类型=RENDER_CONFIG
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/earth_bundle_15.12.0.49.ans
  fd=66, 类型=CLOUD_RESOURCE
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  fd=60, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] RENDER_CONFIG - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 0d 00 00 00 08 0a 72 00 1e 0e 1d 94 1d 36 1b 36
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x8 (8)
    textFlags: 0xa (10)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  fd=60, 类型=RENDER_CONFIG
[.ans文件数据] CLOUD_RESOURCE - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/earth_bundle_15.12.0.49.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  fd=60, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/earth_bundle_15.12.0.49.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 1b 81 00 1b 81 1f a5 1b ae 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x1b (27)
[.ans文件数据] RENDER_CONFIG - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/monitor_data.ans
  大小: 8192 字节
  头部: 0d 00 00 00 08 0a 72 00 1e 0e 1d 94 1d 36 1b 36
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x8 (8)
    textFlags: 0xa (10)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/map_renderer_string.ans
  fd=112, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/map_renderer_string.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/map_renderer_string.ans
  fd=112, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/map_renderer_string.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] RENDER_CONFIG - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/map_renderer_string.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 1f e5 00 1f e5 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x1f (31)
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 0e a6 00 0e a6 16 01 16 ee 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0xe (14)
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 15 5e 00 15 5e 17 4a 1c 09 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x15 (21)
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 0a 51 00 0a 51 0c eb 1a e3 16 70
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0xa (10)
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 0e 39 00 0e 39 0f 41 14 36 1b 81
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0xe (14)
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 01 2e 00 01 2e 0b 94 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x1 (1)
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 0d 00 00 00 06 09 64 00 09 64 13 e1 17 d7 1a 2f
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x6 (6)
    textFlags: 0x9 (9)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a0/m1.ans
  fd=115, 类型=NAVI_MAIN_1
[.ans文件数据] NAVI_MAIN_1 - ZLIB (置信度: 80%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a0/m1.ans
  大小: 531 字节
  头部: 78 9c 73 f1 74 76 d5 75 f4 65 58 c5 d0 d9 7b be
  原因: 标准zlib头部
[.ans文件数据] NAVI_MAIN_1 - ZLIB (置信度: 80%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a0/m1.ans
  大小: 531 字节
  头部: 78 9c 73 f1 74 76 d5 75 f4 65 58 c5 d0 d9 7b be
  原因: 标准zlib头部
[.ans文件数据] NAVI_MAIN_1 - ZLIB (置信度: 80%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a0/m1.ans
  大小: 139 字节
  头部: 78 9c ed ca 31 0a c2 30 18 86 e1 68 3a 2a 2e 0a
  原因: 标准zlib头部
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 0d 00 00 00 06 01 90 00 0b 8c 11 c1 01 90 13 a9
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x6 (6)
    textFlags: 0x1 (1)
[.ans可疑数据] CLOUD_RESOURCE - TEXT (置信度: 50%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  警告: 
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 ae 00 07 ae 12 57 16 39 05 6d
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] CLOUD_RESOURCE - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0a bb 00 0a bb 0c e9 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xa (10)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=120, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=120, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=125, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=120, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] NAVI_MAIN_1 - ZLIB (置信度: 80%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a0/m1.ans
  大小: 531 字节
  头部: 78 9c 73 f1 74 76 d5 75 f4 65 58 c5 d0 d9 7b be
  原因: 标准zlib头部
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=124, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=124, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=124, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=124, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=120, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=120, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
  fd=124, 类型=POI_AOI
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  fd=120, 类型=SD_MAP
[.ans文件数据] SD_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 b8
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] POI_AOI - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  fd=120, 类型=SD_MAP
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
  fd=124, 类型=POI_AOI
[.ans文件数据] SD_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 b8
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] POI_AOI - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 00 30 00 1c 06 06 b8 00 30 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x0 (0)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 11 fb 00 11 fb 18 0d 1c 06 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x11 (17)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 01 38 00 01 38 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x1 (1)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 04 a9 00 04 a9 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x4 (4)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d ee 00 0d ee 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0c 34 00 0c 34 1c 07 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xc (12)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0b 41 00 10 25 0b 41 19 ef 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0e 56 00 0e 56 1c 07 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xe (14)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 08 b3 00 08 b3 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x8 (8)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 02 69 00 02 69 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x2 (2)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0c 34 00 0c 34 1c 07 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xc (12)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 04 96 00 04 96 1c 06 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x4 (4)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0f df 00 0f df 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xf (15)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 05 c3 00 05 c3 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x5 (5)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 00 30 00 1c 06 06 b8 00 30 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x0 (0)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 03 34 00 03 34 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x3 (3)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 03 64 00 03 64 07 8b 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x3 (3)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 03 7a 00 03 7a 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x3 (3)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=134, 类型=RENDER_CONFIG
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans可疑数据] SD_MAP - TEXT (置信度: 50%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  警告: 
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 11 fb 00 11 fb 18 0d 1c 06 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x11 (17)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 03 c2 00 03 c2 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x3 (3)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=132, 类型=RENDER_CONFIG
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=134, 类型=RENDER_CONFIG
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0a 2d 00 0a 2d 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xa (10)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  fd=132, 类型=OTHER_ANS
[.ans文件数据] OTHER_ANS - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  fd=132, 类型=OTHER_ANS
[.ans文件数据] OTHER_ANS - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=134, 类型=RENDER_CONFIG
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 0d 07 03 00 0b 9c 0d e5 0f 64 0a 63
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0xd (13)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 05 7e 00 05 7e 1c 06 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x5 (5)
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 04 55 00 04 55 04 6e 15 c7 1c ec
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x4 (4)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 cb 00 14 cb 0f 87 1c 06 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans可疑数据] CLOUD_RESOURCE - TEXT (置信度: 50%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_cloudres_v1/f_standard/main_bundle_15.19.3.1.ans
  警告: 
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0f 86 00 0f 86 1c 06 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xf (15)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 03 a3 00 18 8e 15 06 0e f1 03 a3
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x3 (3)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0f 86 00 0f 86 1c 06 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xf (15)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d fa 00 0d fa 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=57, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=57, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=140, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=140, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 05 8a 00 05 8a 0b 18 0e 9f 14 b3
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x5 (5)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
  fd=144, 类型=POI_AOI
[.ans文件数据] POI_AOI - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=133, 类型=SMART_MAP
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
  fd=144, 类型=POI_AOI
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=133, 类型=SMART_MAP
[.ans文件数据] POI_AOI - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans可疑数据] SMART_MAP - AM-ZLIB (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  警告: 
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=146, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=146, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=146, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0c 34 00 0c 34 1c 07 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xc (12)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0e 56 00 0e 56 1c 07 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xe (14)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0c 34 00 0c 34 1c 07 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xc (12)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 03 7a 00 03 7a 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x3 (3)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d ee 00 0d ee 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 0d 07 03 00 0b 9c 0d e5 0f 64 0a 63
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0xd (13)
    textFlags: 0x7 (7)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=146, 类型=SMART_MAP
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=145, 类型=RENDER_CONFIG
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=146, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  fd=155, 类型=RENDER_CONFIG
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 05 c3 00 05 c3 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x5 (5)
[.ans文件数据] RENDER_CONFIG - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_config/bmdversion.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans可疑数据] SD_MAP - TEXT (置信度: 50%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  警告: 
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=146, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=146, 类型=SMART_MAP
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 00 30 00 1c 06 06 b8 00 30 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x0 (0)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 11 fb 00 11 fb 18 0d 1c 06 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x11 (17)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 01 38 00 01 38 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x1 (1)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 04 a9 00 04 a9 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x4 (4)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=146, 类型=SMART_MAP
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0b 41 00 10 25 0b 41 19 ef 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xb (11)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 08 b3 00 08 b3 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x8 (8)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 04 96 00 04 96 1c 06 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x4 (4)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 00 30 00 1c 06 06 b8 00 30 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x0 (0)
[.ans可疑数据] SD_MAP - TEXT (置信度: 50%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  警告: 
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 11 fb 00 11 fb 18 0d 1c 06 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x11 (17)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=146, 类型=SMART_MAP
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0a 2d 00 0a 2d 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xa (10)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 08 30 00 08 30 0a b3 1c 06 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x8 (8)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0b 41 00 10 25 0b 41 19 ef 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 10 87 00 10 87 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x10 (16)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 12 ae 00 12 ae 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x12 (18)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans可疑数据] SD_MAP - TEXT (置信度: 50%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  警告: 
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 03 a3 00 18 8e 15 06 0e f1 03 a3
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x3 (3)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans可疑数据] SMART_MAP - AM-ZLIB (置信度: 60%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  警告: 
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=143, 类型=SMART_MAP
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=162, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=145, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=143, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 06 24 00 19 9b 10 1c 09 38 06 24
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x6 (6)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 05 00 7e 00 16 81 10 1d 09 f8 06 e4
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x5 (5)
    textFlags: 0x0 (0)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 07 ae 00 19 9e 11 66 0a c2 07 ae
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x7 (7)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 04 55 00 04 55 04 6e 15 c7 1c ec
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x4 (4)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 05 04 19 00 19 9e 13 f8 0d d3 07 2b
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x5 (5)
    textFlags: 0x4 (4)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 05 02 80 00 1b 9b 18 87 0f 8a 08 a1
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x5 (5)
    textFlags: 0x2 (2)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 03 0f 00 19 99 10 1a 09 f7 03 0f
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x3 (3)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_idx_33652181813.ans
  fd=161, 类型=FONT_DATA
[.ans文件数据] FONT_DATA - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_idx_33652181813.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 17
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_idx_33652181813.ans
  fd=161, 类型=FONT_DATA
[.ans文件数据] FONT_DATA - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_idx_33652181813.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 17
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_idx_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 36 00 07 36 07 4f 1f d2 1f bb
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=171, 类型=SMART_MAP
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=159, 类型=SMART_MAP
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=159, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=143, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 07 92 00 07 92 0b f9 10 9d 17 05
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x7 (7)
[.ans可疑数据] OTHER_ANS - TEXT (置信度: 50%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  警告: 
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 05 9b 00 05 9b 0b ff 13 f2 1c ed
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x5 (5)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 05 02 14 00 02 14 08 fc 0f e4 12 f7
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x5 (5)
    textFlags: 0x2 (2)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 03 0f 00 19 99 10 1a 09 f7 03 0f
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x3 (3)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 07 92 00 07 92 0b f9 10 9d 17 05
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x7 (7)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 03 0f 00 19 99 10 1a 09 f7 03 0f
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x3 (3)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 05 02 14 00 02 14 08 fc 0f e4 12 f7
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x5 (5)
    textFlags: 0x2 (2)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 03 0f 00 19 99 10 1a 09 f7 03 0f
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x3 (3)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 07 92 00 07 92 0b f9 10 9d 17 05
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x7 (7)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 05 02 14 00 02 14 08 fc 0f e4 12 f7
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x5 (5)
    textFlags: 0x2 (2)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 05 9b 00 05 9b 0b ff 13 f2 1c ed
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x5 (5)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=172, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=172, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 05 02 14 00 02 14 08 fc 0f e4 12 f7
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x5 (5)
    textFlags: 0x2 (2)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=143, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  fd=174, 类型=SMART_MAP
[.ans文件数据] SMART_MAP - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_smart/smart_clickv2.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 07 92 00 07 92 0b f9 10 9d 17 05
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 05 9b 00 05 9b 0b ff 13 f2 1c ed
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x5 (5)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_smart/smartmap_file_cache_7000001_aos.ans
  fd=180, 类型=SMART_DOWNLOAD
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  fd=182, 类型=FONT_DATA
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_smart/smartmap_file_cache_7000001_aos.ans
  fd=177, 类型=SMART_DOWNLOAD
[.ans文件数据] FONT_DATA - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 01 56
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件打开] /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  fd=180, 类型=FONT_DATA
[.ans文件数据] FONT_DATA - DICE-AM (置信度: 100%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 01 56
  原因: DICE魔数匹配, 完整DICE-AM魔数匹配, 版本170合理, 几何类型合理: 0x89
  详细信息:
    version: 0xaa (170)
    flags: 0x0 (0)
    geometryType: 0x89 (137)
    geometryFlags: 0x8d (141)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 36 00 07 36 07 4f 0e 13 03 80
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 06 d9 00 06 d9 0f 50 17 c8 08 ab
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x6 (6)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] OTHER_ANS - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_poi/poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 01 41 00 18 da 11 26 01 41 0b 05
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x1 (1)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 18 00 13 f0 08 18 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 01 e0 00 13 b8 07 a8 01 e0 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x1 (1)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 18 00 13 f0 08 18 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 87 00 14 27 08 87 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 b8 00 15 00 08 b8 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 02 f4 00 14 cc 08 bc 02 f4 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x2 (2)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 04 4a 00 13 b8 0f 4b 04 4a 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x4 (4)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 5e 00 15 00 09 5e 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 a8 00 13 f0 07 a8 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 88 00 14 28 08 88 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 50 00 14 28 08 50 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 bc 00 14 95 08 bc 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 07 d8 00 19 a5 13 77 07 d8 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 f6 00 14 98 08 f6 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0a cf 00 14 ff 0a cf 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xa (10)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 05 00 37 00 19 25 13 8c 10 95 04 be
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x5 (5)
    textFlags: 0x0 (0)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 5e 00 15 00 09 5e 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 8c 00 15 9c 09 8c 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 00 10 00 00 10 07 e2 13 f1 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x0 (0)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 02 50 00 13 f0 0e 28 02 50 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x2 (2)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 a8 00 13 f0 07 a8 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 02 39 00 16 3a 0e 49 02 39 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x2 (2)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 18 00 14 60 08 18 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 c0 00 14 60 08 c0 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 a8 00 13 f0 07 a8 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 05 26 00 1b 7d 0f a6 08 e5 05 26
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x5 (5)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 2a 00 15 3a 09 2a 00 00 00 9a
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 98 00 15 3a 09 98 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 a8 00 13 b8 07 a8 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 04 f8 00 14 29 09 32 04 f8 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x4 (4)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 02 b4 00 14 96 0d e9 02 b4 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x2 (2)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 4d 00 13 b8 08 4d 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 03 32 00 15 00 0f 0a 03 32 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x3 (3)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 e0 00 13 b8 07 e0 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 59 00 15 69 09 59 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 a8 00 13 b8 07 a8 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0a d6 00 13 f0 0a d6 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xa (10)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 01 e0 00 13 b8 07 a8 01 e0 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x1 (1)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0a 00 00 15 d8 0a 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xa (10)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 a8 00 13 b8 07 a8 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 00 62 00 13 b8 0a 1e 00 62 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x0 (0)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 02 c6 00 14 cc 0e d6 02 c6 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x2 (2)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 24 00 15 6c 09 24 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 a8 00 13 f0 07 a8 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 bd 00 14 28 08 bd 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 c6 00 14 5e 09 c6 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0a fb 00 14 95 0a fb 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xa (10)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 88 00 14 d0 08 88 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 88 00 14 98 08 88 00 00 00 b1
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 8a 00 13 f2 08 8a 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 18 00 13 f0 08 18 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 17 00 13 f0 08 17 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 50 00 13 b8 08 50 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 18 00 13 f0 08 18 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 84 00 13 f0 08 84 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 c0 00 13 f0 08 c0 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 85 00 14 95 08 85 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0a d6 00 13 f0 0a d6 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xa (10)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 a8 00 13 b8 07 a8 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 fb 00 15 d3 09 fb 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 c0 00 14 60 08 c0 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 50 00 13 b8 08 50 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 18 00 13 b8 08 18 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 07 7c 00 14 94 08 84 07 7c 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 ba 00 13 f0 08 ba 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 be 00 14 60 08 be 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 18 00 14 60 08 18 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 00 c5 00 13 f0 07 a8 00 c5 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x0 (0)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 bc 00 13 f0 08 bc 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 28 00 14 94 09 28 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 03 b3 00 13 f1 09 5a 03 b3 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x3 (3)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 e0 00 13 f0 07 e0 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 0a a1 00 15 03 0a a1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0xa (10)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 03 00 c5 00 13 f0 07 a8 00 c5 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x3 (3)
    textFlags: 0x0 (0)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 98 00 13 f0 09 98 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 50 00 13 f0 08 50 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 50 00 14 28 08 50 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 97 00 14 98 09 97 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 f8 00 15 08 08 f8 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 9e 00 07 9e 0b d1 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 e0 00 14 28 07 e0 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 28 00 14 ca 09 28 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x9 (9)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0b 66 00 0b 66 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xb (11)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 07 b7 00 07 b7 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 07 e0 00 13 b8 07 e0 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x7 (7)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 04 04 c4 00 1b 59 14 1d 08 7c 04 c4
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x4 (4)
    textFlags: 0x4 (4)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 0d a1 00 0d a1 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0xd (13)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 08 bd 00 14 28 08 bd 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x2 (2)
    textFlags: 0x8 (8)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 14 88 00 14 88 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x14 (20)
[.ans文件数据] SD_MAP - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/dl_sql_sdmap_v1/gb_v6.ans
  大小: 8192 字节
  头部: 0d 00 00 00 01 06 21 00 06 21 00 00 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符
  详细信息:
    textType: 0x1 (1)
    textFlags: 0x6 (6)
[.ans文件数据] FONT_DATA - TEXT (置信度: 70%)
  文件: /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_font/normal_am_i18n_glyph_buffer_33652181813.ans
  大小: 8192 字节
  头部: 0d 00 00 00 02 09 92 00 15 34 09 92 00 00 00 00
  原因: TEXT魔数匹配, 包含文本字符

[Remote::com.autonavi.minimap]-> 
[Remote::com.autonavi.minimap]-> exit

Thank you for using Frida!
