/*
 * 高德 离线数据 导出器（按应用代码路径触发导出）
 *
 * 逻辑：
 *  1) Hook libz.so:uncompress，拿到解压后的缓冲区；
 *  2) 对该内存启用 MemoryAccessMonitor，仅当首个读取者为 libamapnsq.so（应用解析路径）时，才解析并导出；
 *  3) 导出内容：
 *     - 0x25xx 文本/属性页：提取中文分组与上下文，写 JSONL
 *     - 0x05 00 00 02 偏移表页：解析连续上升的 u16 表，写 JSONL
 *     - 0x0d 头的元数据页：导出 u16 头字段与前若干 u16，写 JSONL
 *
 * 说明：导出路径优先 /sdcard/gaode_dump，不可写则回落到
 *      /sdcard/Android/data/<pkg>/files/gaode_dump，再不行则 /data/data/<pkg>/files/gaode_dump。
 * 语法：ES5（Frida 12.9.7）
 */

console.log("[启动] 离线数据导出器 已启动");

var monitoring = false;
var curBuf = null;
var curSize = 0;
var monitorTimer = null;
var outDir = "/sdcard/gaode_dump"; // 首选
var chosenDir = null;
var DUMP_GEOM_BIN = true; // 是否落盘几何块原始数据
var GEOM_DUMP_LIMIT = 100; // 最多落盘次数，避免刷屏
var geomDumped = 0;

function tryWriteProbe(dirPath) {
	try {
		var f = new File(dirPath + "/.probe", "w");
		f.write(""); f.flush(); f.close();
		return true;
	} catch (e) { return false; }
}

function mkdirsByJava(dirPath) {
	try {
		Java.perform(function(){
			var File = Java.use('java.io.File');
			var d = File.$new(dirPath);
			if (!d.exists()) d.mkdirs();
		});
	} catch (e) { }
}

// ===== 使用 libc 的 mkdir 递归创建目录，避免早期调用 Java =====
var libc_mkdir_ptr = Module.findExportByName("libc.so", "mkdir");
var libc_mkdir = libc_mkdir_ptr ? new NativeFunction(libc_mkdir_ptr, 'int', ['pointer', 'int']) : null;
function mkdirsByLibc(dirPath) {
	if (!libc_mkdir || !dirPath) return;
	var parts = dirPath.split('/');
	var cur = "";
	for (var i = 0; i < parts.length; i++) {
		var part = parts[i];
		if (!part || part.length === 0) continue;
		cur = cur + "/" + part;
		try {
			var cstr = Memory.allocUtf8String(cur);
			// 0755 = 0o755 = 493
			libc_mkdir(cstr, 493);
		} catch (_) {}
	}
}

function pickWritableDir() {
	if (chosenDir) return chosenDir;
	// 1) 尝试 /sdcard/gaode_dump（仅用 libc mkdir，避免 Java 早期调用）
	mkdirsByLibc(outDir);
	if (tryWriteProbe(outDir)) {
		chosenDir = outDir;
		console.log("[目录] 使用目录=" + chosenDir); return chosenDir;
	}
	// 2) 尝试 app 外部文件目录（静态推断包名，避免 Java）
	var pkg = "com.autonavi.minimap";
	var p1 = "/sdcard/Android/data/" + pkg + "/files/gaode_dump";
	mkdirsByLibc(p1);
	if (tryWriteProbe(p1)) { chosenDir = p1; console.log("[目录] 使用目录=" + chosenDir); return chosenDir; }
	// 3) 尝试 app 内部 files 目录（静态推断包名）
	var p2 = "/data/data/" + pkg + "/files/gaode_dump";
	mkdirsByLibc(p2);
	if (tryWriteProbe(p2)) { chosenDir = p2; console.log("[目录] 使用目录=" + chosenDir); return chosenDir; }
	// 4) Java 方式仅作为最后兜底（避免在早期触发）
	try {
		Java.perform(function(){
			var ActivityThread = Java.use('android.app.ActivityThread');
			var app = ActivityThread.currentApplication();
			if (app) {
				var ctx = app.getApplicationContext();
				var ext = ctx.getExternalFilesDir(null);
				if (ext) {
					var p = ext.getAbsolutePath() + "/gaode_dump";
					mkdirsByJava(p);
					if (tryWriteProbe(p)) { chosenDir = p; console.log("[目录] 使用目录=" + chosenDir); return; }
				}
				var files = ctx.getFilesDir();
				if (files) {
					var p2j = files.getAbsolutePath() + "/gaode_dump";
					mkdirsByJava(p2j);
					if (tryWriteProbe(p2j)) { chosenDir = p2j; console.log("[目录] 使用目录=" + chosenDir); return; }
				}
			}
		});
	} catch (e) { }
	if (!chosenDir) console.log("[提示] 未能创建可写目录，请手动创建 /sdcard/gaode_dump 并重试");
	return chosenDir;
}

function nowTs() {
	return Date.now().toString();
}

function writeJsonLine(base, obj) {
	var dir = pickWritableDir();
	if (!dir) { console.log("[写入失败] 未选定可写目录"); return; }
	try {
		var path = dir + "/" + base + ".jsonl";
		var f = new File(path, "a");
		f.write(JSON.stringify(obj) + "\n");
		f.flush(); f.close();
	} catch (e) {
		console.log("[写入失败] " + e);
	}
}

// ===== 安全读取与辅助 =====
function getReadableLimit(ptr, desiredLen) {
	try {
		var r = Process.findRangeByAddress(ptr);
		if (!r) return 0;
		var offsetInRange = ptr.sub(r.base).toInt32();
		var remain = r.size - offsetInRange;
		if (remain <= 0) return 0;
		return Math.max(0, Math.min(remain, desiredLen));
	} catch (e) { return 0; }
}

function safeReadByteArray(ptr, desiredLen) {
	var len = getReadableLimit(ptr, desiredLen);
	if (len <= 0) return null;
	try { return ptr.readByteArray(len); } catch (e) { return null; }
}

function readU8(p, o) { return p.add(o).readU8(); }
function readU16(p, o) { return p.add(o).readU16(); }
function readU32(p, o) { return p.add(o).readU32(); }

function hexPreview(ptr, size, limit) {
	var lim = Math.min(size, limit || 64);
	var arr = safeReadByteArray(ptr, lim);
	if (!arr) return "<预览不可用>";
	var bytes = new Uint8Array(arr);
	var hex = "";
	for (var i = 0; i < bytes.length; i++) {
		var b = bytes[i].toString(16);
		if (b.length === 1) b = "0" + b;
		hex += b + (i + 1 < bytes.length ? " " : "");
	}
	return hex;
}

// 新增：从块内任意偏移提取十六进制窗口
function hexWindow(basePtr, totalSize, off, pre, post) {
	if (off < 0) off = 0;
	if (off > totalSize) off = totalSize;
	var start = Math.max(0, off - pre);
	var end = Math.min(totalSize, off + post);
	if (end <= start) return "";
	var arr = safeReadByteArray(basePtr.add(start), end - start);
	if (!arr) return "<预览不可用>";
	var bytes = new Uint8Array(arr);
	var hex = "";
	for (var i = 0; i < bytes.length; i++) {
		var b = bytes[i].toString(16);
		if (b.length === 1) b = "0" + b;
		hex += b + (i + 1 < bytes.length ? " " : "");
	}
	return hex;
}

// ===== 文本提取（中文分组） =====
function extractChineseGroups(basePtr, totalSize, scanLimitBytes) {
	var lim = Math.min(totalSize, scanLimitBytes || totalSize);
	var arr = safeReadByteArray(basePtr, lim);
	if (!arr) return [];
	var bytes = new Uint8Array(arr);
	var groups = [];
	var i = 0;
	while (i < bytes.length) {
		if ((bytes[i] & 0xf0) === 0xe0 && i + 2 < bytes.length && (bytes[i+1] & 0xc0) === 0x80 && (bytes[i+2] & 0xc0) === 0x80) {
			var start = i;
			var s = "";
			while (i + 2 < bytes.length) {
				var b = bytes[i], b1 = bytes[i+1], b2 = bytes[i+2];
				if ((b & 0xf0) === 0xe0 && (b1 & 0xc0) === 0x80 && (b2 & 0xc0) === 0x80) {
					s += String.fromCharCode(((b & 0x0f) << 12) | ((b1 & 0x3f) << 6) | (b2 & 0x3f));
					i += 3;
				} else { break; }
			}
			// 合并相邻中文小间隙
			var mergeEnd = i; var j = i;
			while (j < bytes.length) {
				var gap = 0; var ok = false;
				while (gap < 6 && j < bytes.length) {
					if ((bytes[j] & 0xf0) === 0xe0 && j + 2 < bytes.length && (bytes[j+1] & 0xc0) === 0x80 && (bytes[j+2] & 0xc0) === 0x80) { ok = true; break; }
					j++; gap++;
				}
				if (!ok) break;
				while (j + 2 < bytes.length && (bytes[j] & 0xf0) === 0xe0 && (bytes[j+1] & 0xc0) === 0x80 && (bytes[j+2] & 0xc0) === 0x80) {
					s += String.fromCharCode(((bytes[j] & 0x0f) << 12) | ((bytes[j+1] & 0x3f) << 6) | (bytes[j+2] & 0x3f));
					j += 3;
				}
				mergeEnd = j; i = j;
			}
			if (s.length >= 3) groups.push({ off: start, end: mergeEnd, text: s.substr(0, 256) });
		} else { i++; }
	}
	return groups;
}

// ===== 0x05 偏移表解析 =====
function parseAscendingU16Table(ptr, totalSize, startOff, maxCount) {
	var table = []; var off = startOff; var last = -1;
	for (var i = 0; i < maxCount; i++) {
		if (off + 2 > totalSize) break;
		var v = 0; try { v = readU16(ptr, off); } catch (e) { break; }
		if (v === 0) break; if (last >= 0 && v < last) break;
		table.push({ off: off, val: v }); last = v; off += 2;
	}
	return table;
}

function analyzeAndExport(basePtr, totalSize) {
	if (!basePtr || totalSize <= 0) return;
	var b0 = readU8(basePtr, 0), b1 = readU8(basePtr, 1), b2 = readU8(basePtr, 2), b3 = readU8(basePtr, 3);

	// 0x25xx 文本/属性
	if (b0 === 0x00 && b1 === 0x00 && b2 === 0x25) {
		var typeId = readU32(basePtr, 0) >>> 0;
		var groups = extractChineseGroups(basePtr, totalSize, Math.min(totalSize, 12*1024));
		var details = [];
		for (var gi = 0; gi < Math.min(groups.length, 8); gi++) {
			var g = groups[gi];
			details.push({
				off: g.off,
				end: g.end,
				text: g.text,
				ctx_pre_hex: hexPreview(basePtr, totalSize, 16),
				ctx_post_hex: hexPreview(basePtr.add(g.end), totalSize - g.end, 16)
			});
		}
		console.log("[导出] 25xx_text");
		writeJsonLine("25xx_text", {
			ts: nowTs(), type_id_hex: "0x" + typeId.toString(16), size: totalSize,
			groups_count: groups.length,
			groups: details
		});
		return;
	}

	// 0x05：偏移表类
	if (b0 === 0x05 && b1 === 0x00) {
		var cand = [12, 8, 4, 16, 20, 24, 32];
		var best = null;
		for (var i = 0; i < cand.length; i++) {
			var t = parseAscendingU16Table(basePtr, totalSize, cand[i], 1024);
			if (t.length >= 8) { if (!best || t.length > best.length) best = t; }
		}
		var segPrev = [];
		if (best) {
			for (var k = 0; k < Math.min(best.length, 8); k++) {
				var offb = best[k].val;
				var u16v = 0, u32v = 0;
				try { u16v = readU16(basePtr, offb); } catch (_) {}
				try { u32v = readU32(basePtr, offb) >>> 0; } catch (_) {}
				segPrev.push({ off: offb, u16: u16v, u32: u32v, hex32: hexPreview(basePtr.add(offb), totalSize - offb, 32) });
			}
		}
		console.log("[导出] 05_index");
		writeJsonLine("05_index", {
			ts: nowTs(), size: totalSize,
			found: !!best,
			start_off: best ? best[0].off : null,
			count: best ? best.length : 0,
			first_vals: best ? best.slice(0, 32).map(function(x){return x.val;}) : [],
			segments_preview: segPrev
		});
		return;
	}

	// 0x0d：元数据/范围类
	if (b0 === 0x0d && b1 === 0x00 && b2 === 0x00 && b3 === 0x00) {
		var f1 = readU16(basePtr, 4), f2 = readU16(basePtr, 6), f3 = readU16(basePtr, 8), f4 = readU16(basePtr, 10);
		var u16s = [];
		for (var i2 = 0; i2 < Math.min(64, Math.floor(totalSize/2)); i2++) { u16s.push(readU16(basePtr, i2*2)); }
		var u32s = [];
		for (var j2 = 0; j2 < Math.min(16, Math.floor(totalSize/4)); j2++) { u32s.push(readU32(basePtr, j2*4) >>> 0); }
		console.log("[导出] 0d_meta");
		writeJsonLine("0d_meta", { ts: nowTs(), size: totalSize, head_u16_4: [f1,f2,f3,f4], u16_64: u16s, u32_16: u32s, head_hex: hexPreview(basePtr, totalSize, 32) });
		return;
	}

	// 几何相关：bc bc bc bc / ce ca 0b b1 / DICE-AM
	var isBC = (b0 === 0xbc && b1 === 0xbc && b2 === 0xbc && b3 === 0xbc);
	var isCE = (b0 === 0xce && b1 === 0xca && b2 === 0x0b && b3 === 0xb1);
	var isDICE = (b0 === 0x44 && b1 === 0x49 && b2 === 0x43 && b3 === 0x45);
	if (isBC || isCE || isDICE) {
		var dwords = [];
		for (var dw = 0; dw < Math.min(8, Math.floor(totalSize/4)); dw++) { dwords.push(readU32(basePtr, dw*4) >>> 0); }
		var magic = isBC ? "bc" : (isCE ? "ce" : "DICE-AM");
		writeJsonLine("geom_meta", { ts: nowTs(), size: totalSize, magic: magic, head_hex16: hexPreview(basePtr, totalSize, 16), dwords_first8: dwords });
		return;
	}
}

// 仅几何快速导出：不依赖首读者
function exportGeomQuick(basePtr, totalSize) {
	if (!basePtr || totalSize <= 0) return;
	var arr = safeReadByteArray(basePtr, Math.min(totalSize, 1024));
	if (!arr) return;
	var v = new Uint8Array(arr);
	if (v.length < 4) return;
	var b0 = v[0], b1 = v[1], b2 = v[2], b3 = v[3];
	var isBC = (b0 === 0xbc && b1 === 0xbc && b2 === 0xbc && b3 === 0xbc);
	var isCE = (b0 === 0xce && b1 === 0xca && b2 === 0x0b && b3 === 0xb1);
	var isDICE = (b0 === 0x44 && b1 === 0x49 && b2 === 0x43 && b3 === 0x45);
	if (!(isBC || isCE || isDICE)) return;
	// 写 geom_meta
	try {
		var magic = isBC ? "bc" : (isCE ? "ce" : "DICE-AM");
		var headHex = hexPreview(basePtr, totalSize, 16);
		writeJsonLine("geom_meta", { ts: nowTs(), size: totalSize, magic: magic, head_hex16: headHex });
	} catch (_) {}
	// 可选落盘原始块
	if (DUMP_GEOM_BIN && geomDumped < GEOM_DUMP_LIMIT) {
		var dir = pickWritableDir(); if (!dir) return;
		var binDir = dir + "/geom_bin"; mkdirsByLibc(binDir);
		try {
			var bytes = safeReadByteArray(basePtr, totalSize);
			if (bytes) {
				var fname = binDir + "/" + nowTs() + "_" + (isBC?"bc":(isCE?"ce":"dice")) + "_" + totalSize + ".bin";
				var f = new File(fname, "w");
				f.write(bytes); f.flush(); f.close();
				geomDumped++;
			}
		} catch (_) {}
	}
}

function hook_uncompress_and_monitor() {
	var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
	if (!uncompressPtr) { console.log("[错误] 未找到 uncompress"); return; }
	console.log("[成功] Hook uncompress=" + uncompressPtr);

	Interceptor.attach(uncompressPtr, {
		onEnter: function(args) {
			this.dest = args[0];
			this.destLen = args[1];
		},
		onLeave: function(retval) {
			if (retval.toInt32() !== 0) return;
			if (monitoring) return;
			var size = 0; try { size = this.destLen.readU32(); } catch (e) { return; }
			if (size <= 0 || size > 64*1024*1024) return;
			curBuf = this.dest; curSize = size; monitoring = true;
			console.log("[解压] 尺寸=" + size + " 预览=" + hexPreview(curBuf, curSize, 32));
			// 无条件几何快速导出
			exportGeomQuick(curBuf, curSize);
			// 无条件导出 25xx/05/0d 等（若符合类型会写入 JSONL）
			try { analyzeAndExport(curBuf, curSize); } catch (_) {}

			MemoryAccessMonitor.enable({ base: curBuf, size: curSize }, {
				onAccess: function(d) {
					var sym = DebugSymbol.fromAddress(d.from);
					var mod = sym && sym.moduleName ? sym.moduleName : "<未知>";
					if (mod === "libamapnsq.so") {
						analyzeAndExport(curBuf, curSize);
					} else {
						// 非应用解析路径，跳过
					}
					try { MemoryAccessMonitor.disable(); } catch (_) {}
					monitoring = false; curBuf = null; curSize = 0;
					if (monitorTimer) { try { clearTimeout(monitorTimer); } catch (_) {} monitorTimer = null; }
				}
			});

			monitorTimer = setTimeout(function() {
				if (monitoring) { try { MemoryAccessMonitor.disable(); } catch (_) {}
					monitoring = false; curBuf = null; curSize = 0; console.log("[监控] 超时未命中"); }
			}, 5000);
		}
	});
}

setImmediate(function(){
	pickWritableDir();
	hook_uncompress_and_monitor();
	console.log("[就绪] 将导出到可写目录，实际目录见上方 [目录] 日志");
}); 