/*
 * 高德地图真实数据格式分析器
 * 基于实际观察到的数据模式 (.!9h., .C.Q., ...f.)
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[Real Data Format Analyzer] 启动真实数据格式分析器...");
    
    var CONFIG = {
        INIT_DELAY: 3000,
        DEEP_ANALYSIS: true
    };
    
    var dataPatterns = {
        type1: '.!9h.',  // 高频出现
        type2: '.C.Q.',  // 中频出现  
        type3: '...f.'   // 低频出现
    };
    
    var analysisResults = {
        totalCalls: 0,
        dataTypes: {
            type1: 0,
            type2: 0, 
            type3: 0,
            unknown: 0
        },
        parameterPatterns: {},
        executionTimes: [],
        errorCodes: {}
    };
    
    // === 库等待函数 ===
    function waitForLibrary(libraryName, callback) {
        var maxAttempts = 30;
        var attempt = 0;
        
        function checkLibrary() {
            try {
                var lib = Module.findBaseAddress(libraryName);
                if (lib) {
                    console.log("[Library] " + libraryName + " 已加载，基址: " + lib);
                    callback(lib);
                    return;
                }
            } catch (e) {
                // 继续等待
            }
            
            attempt++;
            if (attempt < maxAttempts) {
                setTimeout(checkLibrary, 1000);
            } else {
                console.log("[Error] " + libraryName + " 加载超时");
            }
        }
        
        checkLibrary();
    }
    
    // === 数据格式分析器 ===
    function analyzeDataFormat(dataPtr, size, context) {
        try {
            if (!dataPtr || dataPtr.isNull()) {
                return null;
            }
            
            // 读取数据头部
            var headerData = dataPtr.readByteArray(Math.min(32, size || 32));
            var headerView = new Uint8Array(headerData);
            
            var analysis = {
                firstByte: headerView[0],
                headerPattern: "",
                dataType: "unknown",
                hasCompression: false,
                estimatedFormat: "custom"
            };
            
            // 构建可读的头部字符串
            for (var i = 0; i < Math.min(8, headerView.length); i++) {
                if (headerView[i] >= 32 && headerView[i] < 127) {
                    analysis.headerPattern += String.fromCharCode(headerView[i]);
                } else if (headerView[i] === 0) {
                    analysis.headerPattern += "\\0";
                } else {
                    analysis.headerPattern += ".";
                }
            }
            
            // 识别数据类型
            if (analysis.headerPattern.indexOf('!9h') !== -1) {
                analysis.dataType = "type1";
                analysis.estimatedFormat = "map_vector_data";
            } else if (analysis.headerPattern.indexOf('C.Q') !== -1) {
                analysis.dataType = "type2"; 
                analysis.estimatedFormat = "poi_annotation_data";
            } else if (analysis.headerPattern.indexOf('..f') !== -1) {
                analysis.dataType = "type3";
                analysis.estimatedFormat = "special_feature_data";
            }
            
            // 检查是否为压缩数据
            analysis.hasCompression = (headerView[0] === 0x8);
            
            // 分析数据结构
            if (headerView.length >= 16) {
                analysis.structureHints = {
                    possibleSize: (headerView[8] | (headerView[9] << 8) | (headerView[10] << 16) | (headerView[11] << 24)),
                    possibleFlags: (headerView[12] | (headerView[13] << 8)),
                    possibleVersion: headerView[14],
                    possibleChecksum: headerView[15]
                };
            }
            
            return analysis;
            
        } catch (e) {
            console.log("[Error] 数据格式分析失败: " + e);
            return null;
        }
    }
    
    // === 参数模式分析 ===
    function analyzeParameterPattern(args) {
        var pattern = {
            arg0: args[0] ? args[0].toString() : "null",
            arg1: args[1] ? args[1].toInt32() : 0,
            arg2: args[2] ? args[2].toInt32() : 0,
            patternKey: ""
        };
        
        // 创建参数模式键
        pattern.patternKey = "arg1:" + pattern.arg1 + "_arg2:0x" + pattern.arg2.toString(16);
        
        return pattern;
    }
    
    // === 主要函数分析 ===
    function setupRealDataAnalysis(libBase) {
        var sub_10F88 = libBase.add(0x10F88);
        
        console.log("[Real Data] 设置真实数据格式分析器...");
        
        Interceptor.attach(sub_10F88, {
            onEnter: function(args) {
                this.startTime = Date.now();
                this.args = [args[0], args[1], args[2]];
                
                analysisResults.totalCalls++;
                
                // 分析参数模式
                var paramPattern = analyzeParameterPattern(args);
                if (!analysisResults.parameterPatterns[paramPattern.patternKey]) {
                    analysisResults.parameterPatterns[paramPattern.patternKey] = 0;
                }
                analysisResults.parameterPatterns[paramPattern.patternKey]++;
                
                // 分析数据格式
                var dataAnalysis = analyzeDataFormat(args[0], 32, "sub_10F88");
                this.dataAnalysis = dataAnalysis;
                
                if (dataAnalysis) {
                    console.log("[Data Format] 检测到数据类型: " + dataAnalysis.dataType);
                    console.log("  - 头部模式: '" + dataAnalysis.headerPattern + "'");
                    console.log("  - 首字节: 0x" + dataAnalysis.firstByte.toString(16));
                    console.log("  - 估计格式: " + dataAnalysis.estimatedFormat);
                    console.log("  - 压缩标识: " + (dataAnalysis.hasCompression ? "是" : "否"));
                    
                    if (dataAnalysis.structureHints) {
                        console.log("  - 可能的大小: " + dataAnalysis.structureHints.possibleSize);
                        console.log("  - 可能的标志: 0x" + dataAnalysis.structureHints.possibleFlags.toString(16));
                        console.log("  - 可能的版本: " + dataAnalysis.structureHints.possibleVersion);
                    }
                    
                    // 统计数据类型
                    if (analysisResults.dataTypes[dataAnalysis.dataType] !== undefined) {
                        analysisResults.dataTypes[dataAnalysis.dataType]++;
                    } else {
                        analysisResults.dataTypes.unknown++;
                    }
                }
                
                console.log("[Param Pattern] " + paramPattern.patternKey);
            },
            
            onLeave: function(retval) {
                var duration = Date.now() - this.startTime;
                var returnCode = retval.toInt32();
                
                analysisResults.executionTimes.push(duration);
                
                if (!analysisResults.errorCodes[returnCode]) {
                    analysisResults.errorCodes[returnCode] = 0;
                }
                analysisResults.errorCodes[returnCode]++;
                
                console.log("[Execution] 耗时: " + duration + "ms, 返回码: " + returnCode);
                
                if (this.dataAnalysis && this.dataAnalysis.dataType !== "unknown") {
                    console.log("[Success] " + this.dataAnalysis.dataType + " 数据处理完成");
                }
            }
        });
        
        console.log("[Real Data] 真实数据格式分析器已设置");
    }
    
    // === 分析报告生成 ===
    function generateAnalysisReport() {
        console.log("\n=== 真实数据格式分析报告 ===");
        
        console.log("总调用次数: " + analysisResults.totalCalls);
        
        console.log("\n数据类型分布:");
        for (var type in analysisResults.dataTypes) {
            var count = analysisResults.dataTypes[type];
            var percentage = ((count / analysisResults.totalCalls) * 100).toFixed(1);
            console.log("  " + type + ": " + count + " 次 (" + percentage + "%)");
        }
        
        console.log("\n参数模式频率:");
        var sortedPatterns = [];
        for (var pattern in analysisResults.parameterPatterns) {
            sortedPatterns.push({
                pattern: pattern,
                count: analysisResults.parameterPatterns[pattern]
            });
        }
        sortedPatterns.sort(function(a, b) { return b.count - a.count; });
        
        for (var i = 0; i < Math.min(5, sortedPatterns.length); i++) {
            var item = sortedPatterns[i];
            console.log("  " + item.pattern + ": " + item.count + " 次");
        }
        
        if (analysisResults.executionTimes.length > 0) {
            var avgTime = analysisResults.executionTimes.reduce(function(a, b) { return a + b; }, 0) / analysisResults.executionTimes.length;
            var maxTime = Math.max.apply(null, analysisResults.executionTimes);
            console.log("\n执行时间统计:");
            console.log("  平均耗时: " + avgTime.toFixed(2) + "ms");
            console.log("  最大耗时: " + maxTime + "ms");
        }
        
        console.log("\n返回码分布:");
        for (var code in analysisResults.errorCodes) {
            console.log("  返回码 " + code + ": " + analysisResults.errorCodes[code] + " 次");
        }
        
        console.log("==========================\n");
    }
    
    // === 定期报告 ===
    function setupPeriodicReporting() {
        setInterval(function() {
            if (analysisResults.totalCalls > 0) {
                generateAnalysisReport();
            }
        }, 25000); // 每25秒生成一次报告
    }
    
    // === 主入口 ===
    function main() {
        console.log("[Main] 等待应用初始化完成...");
        
        setTimeout(function() {
            console.log("[Main] 开始初始化真实数据格式分析器...");
            
            try {
                waitForLibrary("libamapnsq.so", function(libBase) {
                    try {
                        setupRealDataAnalysis(libBase);
                        setupPeriodicReporting();
                        
                        console.log("[Real Data Format Analyzer] 真实数据格式分析器已启动!");
                        console.log("现在移动地图以触发数据处理，观察真实数据格式...");
                        
                    } catch (e) {
                        console.log("[Error] 分析器初始化失败: " + e);
                    }
                });
                
            } catch (e) {
                console.log("[Error] 主初始化失败: " + e);
            }
        }, CONFIG.INIT_DELAY);
    }
    
    // === 启动分析器 ===
    try {
        Java.perform(function() {
            console.log("[Java] Java环境已准备就绪");
            main();
        });
    } catch (e) {
        console.log("[Error] Java环境初始化失败: " + e);
        main();
    }
    
})(); 