(function() {
  console.log("[ANS文件分析器-精简版] 启动");
  
  // 全局变量
  var m1_ans_fd = -1;
  var m1_ans_address = null;
  var m1_ans_size = 0;
  var ansFiles = {};
  var maxDataSize = 64;
  
  // 工具函数：打印十六进制字节数据
  function hexdump(data, offset, length) {
    if (!data || !length) return "";
    
    var maxLen = Math.min(length, maxDataSize);
    var result = "";
    for (var i = offset; i < offset + maxLen; i++) {
      var byteVal = data[i] & 0xFF;
      var hex = byteVal.toString(16).toUpperCase();
      if (hex.length == 1) hex = "0" + hex;
      result += hex + " ";
    }
    
    return result;
  }
  
  // 防止应用崩溃 - 拦截exit函数
  var exit_ptr = Module.findExportByName("libc.so", "exit");
  if (exit_ptr) {
    Interceptor.replace(exit_ptr, new NativeCallback(function() {
      console.log("[+] 拦截exit()调用");
      return 0;
    }, 'void', ['int']));
    console.log("[+] 拦截exit()函数");
  }
  
  // 监控库加载 - 只观察不干预
  Interceptor.attach(Module.findExportByName(null, "dlopen"), {
    onEnter: function(args) {
      try {
        this.path = args[0].readUtf8String();
      } catch(e) {}
    },
    onLeave: function(retval) {
      if (this.path && this.path.indexOf("libamaploc.so") !== -1) {
        console.log("[库加载] " + this.path);
      }
    }
  });
  
  // 监控文件打开
  Interceptor.attach(Module.findExportByName("libc.so", "open"), {
    onEnter: function(args) {
      try {
        var path = args[0].readUtf8String();
        if (path && path.indexOf(".ans") !== -1) {
          this.path = path;
          this.isAnsFile = true;
          
          // 特别关注m1.ans文件
          if (path.indexOf("m1.ans") !== -1) {
            this.isM1Ans = true;
            console.log("[重要] m1.ans文件打开: " + path);
          }
        }
      } catch(e) {}
    },
    onLeave: function(retval) {
      if (!this.isAnsFile) return;
      
      var fd = retval.toInt32();
      if (fd > 0) {
        ansFiles[fd] = this.path;
        
        if (this.isM1Ans) {
          m1_ans_fd = fd;
          console.log("[+] m1.ans文件描述符: " + fd);
        }
        
        console.log("[ANS文件] 打开: " + this.path + " -> fd:" + fd);
      }
    }
  });
  
  // 监控mmap内存映射
  Interceptor.attach(Module.findExportByName("libc.so", "mmap"), {
    onEnter: function(args) {
      this.fd = args[4].toInt32();
      this.length = args[1].toInt32();
      this.offset = args[5].toInt32();
      
      if (ansFiles[this.fd]) {
        this.isAnsFile = true;
        this.path = ansFiles[this.fd];
        this.isM1Ans = (this.fd === m1_ans_fd);
      }
    },
    onLeave: function(retval) {
      if (!this.isAnsFile) return;
      
      var mapped_addr = retval;
      if (!mapped_addr.isNull()) {
        if (this.isM1Ans) {
          m1_ans_address = mapped_addr;
          m1_ans_size = this.length;
          
          console.log("[M1.ANS映射] 地址: " + mapped_addr + 
                     ", 大小: " + this.length + " 字节" + 
                     ", 偏移: " + this.offset);
          
          // 读取头部数据
          try {
            var header = Memory.readByteArray(mapped_addr, Math.min(32, this.length));
            console.log("[M1.ANS头部] " + hexdump(new Uint8Array(header), 0, header.byteLength));
          } catch(e) {}
        }
      }
    }
  });
  
  // 延迟10秒后安全地尝试一次性Java层hook
  setTimeout(function() {
    try {
      Java.perform(function() {
        console.log("[Java层] 简单安全检查");
        // 检测主要资源加载类是否存在但不hook
        try {
          var testClass = Java.use("com.autonavi.ae.gmap.AMapController");
          console.log("[Java层] 检测到AMapController类");
        } catch(e) {
          console.log("[Java层] 未找到AMapController类");
        }
      });
    } catch(e) {
      console.log("[错误] Java检查失败: " + e);
    }
  }, 10000);
})();
