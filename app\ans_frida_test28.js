
(function() {
  console.log("[ANS追踪] 开始监控m1.ans和m3.ans文件...");
  
  // 全局变量，用于追踪文件操作
  var ansFileDescriptors = {};
  var lastGestureTime = 0;
  
  // 监控文件打开
  var openPtr = Module.findExportByName("libc.so", "open");
  if (openPtr) {
    Interceptor.attach(openPtr, {
      onEnter: function(args) {
        try {
          this.path = args[0].readUtf8String();
          if (this.path && (this.path.indexOf("m1.ans") !== -1 || this.path.indexOf("m3.ans") !== -1)) {
            console.log("[ANS追踪] 打开ANS文件: " + this.path);
          }
        } catch(e) {}
      },
      onLeave: function(result) {
        if (this.path && (this.path.indexOf("m1.ans") !== -1 || this.path.indexOf("m3.ans") !== -1)) {
          var fd = result.toInt32();
          if (fd > 0) {
            console.log("[ANS追踪] 文件打开成功: fd=" + fd);
            ansFileDescriptors[fd] = this.path;
          }
        }
      }
    });
  }
  
  // 监控文件读取
  var readPtr = Module.findExportByName("libc.so", "read");
  if (readPtr) {
    Interceptor.attach(readPtr, {
      onEnter: function(args) {
        var fd = args[0].toInt32();
        if (ansFileDescriptors[fd]) {
          this.fd = fd;
          this.buffer = args[1];
          this.size = args[2].toInt32();
          this.ansFilePath = ansFileDescriptors[fd];
        }
      },
      onLeave: function(result) {
        if (this.fd && result.toInt32() > 0) {
          var bytesRead = result.toInt32();
          console.log("[ANS追踪] 读取文件: " + this.ansFilePath + 
                     " (" + bytesRead + "/" + this.size + " 字节)");
          
          // 只打印小数据块的头部
          if (bytesRead <= 32) {
            console.log(hexdump(this.buffer, {length: Math.min(bytesRead, 32)}));
          }
        }
      }
    });
  }
  
  // Java层监控
  setTimeout(function() {
    Java.perform(function() {
      try {
        // 监控手势处理
        var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
        if (GLMapEngine.addGestureMessage) {
          GLMapEngine.addGestureMessage.implementation = function(engineId, gestureMessage) {
            var type = gestureMessage.getType();
            if (type == 0) { // 移动手势
              try {
                var moveMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.MoveGestureMapMessage"));
                var dx = moveMsg.mTouchDeltaX.value;
                var dy = moveMsg.mTouchDeltaY.value;
                console.log("[ANS追踪] 地图手势: 移动 dx=" + dx + ", dy=" + dy);
                lastGestureTime = Date.now();
              } catch(e) {}
            }
            return this.addGestureMessage(engineId, gestureMessage);
          };
          console.log("[ANS追踪] 成功Hook手势处理方法");
        }
      } catch(e) {
        console.log("[ANS追踪] 手势监控错误: " + e);
      }
    });
  }, 1000);
  
  console.log("[ANS追踪] 监控设置完成");
})();

