(function() {
  console.log("[ANS-Frida测试] 脚本启动...");
  
  // 全局跟踪变量
  var ans_fd = {};
  var ans_paths = {};
  var ans_mmaps = {};
  
  // 防止应用崩溃
  var exit_ptr = Module.findExportByName("libc.so", "exit");
  if (exit_ptr) {
    Interceptor.replace(exit_ptr, new NativeCallback(function(code) {
      console.log("[ANS-Frida测试] 拦截exit()调用: " + code);
    }, 'void', ['int']));
  }
  
  // 监控文件打开
  var open_ptr = Module.findExportByName("libc.so", "open");
  if (open_ptr) {
    Interceptor.attach(open_ptr, {
      onEnter: function(args) {
        var path = args[0].readUtf8String();
        if (path && (path.includes("m1.ans") || path.includes("m3.ans"))) {
          this.path = path;
          console.log("[ANS-Frida测试] 打开文件: " + path);
        }
      },
      onLeave: function(retval) {
        if (this.path) {
          var fd = retval.toInt32();
          if (fd > 0) {
            ans_fd[fd] = this.path;
            ans_paths[this.path] = fd;
            console.log("[ANS-Frida测试] 文件描述符: " + fd);
          }
        }
      }
    });
  }

  // 监控读取操作 - 简化版本，不再获取调用栈
  var read_ptr = Module.findExportByName("libc.so", "read");
  if (read_ptr) {
    Interceptor.attach(read_ptr, {
      onEnter: function(args) {
        this.fd = args[0].toInt32();
        this.buffer = args[1];
        this.size = args[2].toInt32();

        if (ans_fd[this.fd]) {
          this.ansFile = true;
          this.path = ans_fd[this.fd];
        }
      },
      onLeave: function(retval) {
        if (!this.ansFile) return;

        var size = retval.toInt32();
        if (size <= 0) return;

        // 仅记录读取大小，避免过多输出
        console.log("[ANS-Frida测试] 读取" + this.path + ", 大小: " + size + "字节");

        // 只打印非常小的块数据
        if (size <= 16) {
          try {
            console.log(hexdump(this.buffer, {length: size}));
          } catch(e) {}
        }
      }
    });
  }

  // 监控mmap调用 - 简化版本
  var mmap_ptr = Module.findExportByName("libc.so", "mmap");
  if (mmap_ptr) {
    Interceptor.attach(mmap_ptr, {
      onEnter: function(args) {
        this.fd = args[4].toInt32();
        this.size = args[1].toInt32();
        if (ans_fd[this.fd]) {
          this.ansFile = true;
          this.path = ans_fd[this.fd];
        }
      },
      onLeave: function(retval) {
        if (!this.ansFile) return;

        console.log("[ANS-Frida测试] 映射" + this.path + ", 地址: " + retval);
      }
    });
  }

  // 减少Java层Hook，只监控关键类
  setTimeout(function() {
    Java.perform(function() {
      try {
        console.log("[ANS-Frida测试] Java环境准备就绪");

        // 尝试监控特定方法，而不是所有方法
        try {
          var AmapCompat = Java.use("com.autonavi.minimap.offline.nativesupport.AmapCompat");

          // 只记录类中的方法数量，不进行Hook
          var methods = AmapCompat.class.getDeclaredMethods();
          console.log("[ANS-Frida测试] AmapCompat类方法数量: " + methods.length);

          // 仅列出方法名，不进行Hook
          for (var i = 0; i < methods.length; i++) {
            console.log("[ANS-Frida测试] 方法: " + methods[i].getName());
          }
        } catch(e) {
          console.log("[ANS-Frida测试] AmapCompat分析失败: " + e);
        }
      } catch(e) {
        console.log("[ANS-Frida测试] Java分析失败: " + e);
      }
    });
  }, 2000);

  console.log("[ANS-Frida测试] 脚本设置完成");
})();
