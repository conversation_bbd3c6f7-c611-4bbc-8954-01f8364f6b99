
"""
基于APP真实代码逻辑的数据解析器
严格按照IDA Pro反汇编的sub_5C060和sub_10F88逻辑实现
"""

import os
import zlib
import struct

class AppLogicParser:
    """基于APP真实逻辑的解析器"""
    
    def __init__(self):
        self.data_blocks = []
        self.structured_results = []
        
    def parse_ans_file(self, filepath):
        """严格按照APP逻辑解析.ans文件"""
        print("🎯 基于APP真实代码逻辑的数据解析")
        print("参考: IDA Pro sub_5C060 + sub_10F88 反汇编逻辑")
        print("=" * 80)
        
        if not os.path.exists(filepath):
            print(f"❌ 文件不存在: {filepath}")
            return
            
        with open(filepath, 'rb') as f:
            raw_data = f.read()
            
        print(f"📁 文件: {filepath}")
        print(f"📏 大小: {len(raw_data):,} 字节")
        
        # 步骤1: 按照APP逻辑查找AM-zlib容器
        self.parse_am_zlib_container(raw_data)
        
        # 步骤2: 按照sub_5C060逻辑处理数据块
        self.process_data_blocks()
        
        # 步骤3: 按照sub_10F88逻辑解析具体内容
        self.parse_structured_content()
        
        # 输出最终结果
        self.output_final_results()
    
    def parse_am_zlib_container(self, raw_data):
        """解析AM-zlib容器 - 基于APP文件读取逻辑"""
        print("\n🗂️ 步骤1: AM-zlib容器解析")
        print("-" * 50)
        
        # 检查AM-zlib头部（APP逻辑：检查文件魔数）
        if raw_data.startswith(b'AM-zlib\x00'):
            print("✅ 发现AM-zlib容器格式")
            header_size = 8
        else:
            print("⚠️ 非标准AM-zlib格式，尝试直接解析")
            header_size = 0
            
        # 扫描zlib数据块（模拟APP的解压循环）
        offset = header_size
        block_count = 0
        
        while offset < len(raw_data) - 10:
            # 查找zlib魔数 0x78 0x9C（APP逻辑中的compress检查）
            zlib_start = raw_data.find(b'\x78\x9c', offset)
            if zlib_start == -1:
                break
                
            # APP会尝试不同的块大小进行解压
            for block_size in [4096, 8192, 16384, 32768]:
                if zlib_start + block_size <= len(raw_data):
                    try:
                        compressed_data = raw_data[zlib_start:zlib_start + block_size]
                        decompressed_data = zlib.decompress(compressed_data)
                        
                        print(f"📦 数据块 #{block_count}: 压缩{len(compressed_data)} → 解压{len(decompressed_data)} 字节")
                        
                        # 存储数据块信息
                        self.data_blocks.append({
                            'id': block_count,
                            'offset': zlib_start,
                            'compressed_size': len(compressed_data),
                            'decompressed_size': len(decompressed_data),
                            'compressed_data': compressed_data,
                            'decompressed_data': decompressed_data
                        })
                        
                        offset = zlib_start + block_size
                        block_count += 1
                        break
                    except:
                        continue
            else:
                offset = zlib_start + 1
                
            # 限制分析数量，避免过多输出
            if block_count >= 15:
                break
                
        print(f"📊 共解析 {len(self.data_blocks)} 个数据块")
    
    def process_data_blocks(self):
        """处理数据块 - 基于sub_5C060逻辑"""
        print("\n🏗️ 步骤2: 数据块处理 (基于sub_5C060逻辑)")
        print("-" * 50)
        
        for block in self.data_blocks:
            data = block['decompressed_data']
            
            print(f"\n📋 处理数据块 #{block['id']}:")
            
            # 基于APP代码逻辑进行数据分类（sub_5C060中的条件判断）
            data_type, structured_data = self.classify_and_process_data(data, block['id'])
            
            self.structured_results.append({
                'block_id': block['id'],
                'data_type': data_type,
                'original_size': len(data),
                'structured_data': structured_data
            })
    
    def classify_and_process_data(self, data, block_id):
        """数据分类和处理 - 基于APP内部逻辑"""
        
        # 显示数据头部（用于调试和验证）
        header_hex = ' '.join(f'{b:02x}' for b in data[:16])
        print(f"   头部: {header_hex}")
        
        # 1. 检查DICE-AM格式（APP中的魔数检查）
        if data.startswith(b'DICE-AM'):
            print("   🎯 类型: DICE-AM矢量数据")
            return 'DICE_AM', self.parse_dice_am_structure(data)
            
        # 2. 检查是否是二进制索引数据（基于数据模式判断）
        elif self.is_binary_index_data(data):
            print("   🎯 类型: 二进制索引数据")
            return 'BINARY_INDEX', self.parse_binary_index(data)
            
        # 3. 检查中文文本数据（UTF-8模式检测）
        elif self.contains_chinese_text(data):
            print("   🎯 类型: UTF-8中文文本")
            return 'CHINESE_TEXT', self.parse_chinese_text_data(data)
            
        # 4. 检查JSON/XML配置（文本模式检测）
        elif self.contains_config_data(data):
            print("   🎯 类型: 配置数据")
            return 'CONFIG', self.parse_config_data(data)
            
        else:
            print("   🎯 类型: 未知二进制数据")
            return 'UNKNOWN', {'raw_data_preview': data[:64].hex()}
    
    def parse_dice_am_structure(self, data):
        """解析DICE-AM结构 - 基于APP的真实解析逻辑"""
        try:
            # DICE-AM头部结构（从IDA分析得出）
            magic = data[:7].decode('ascii')
            version = data[7]
            
            # 关键：APP中版本验证逻辑 (version ^ 0xAB)
            version_check = version ^ 0xAB
            
            data_length = struct.unpack('<I', data[8:12])[0]
            point_count = struct.unpack('<I', data[12:16])[0]
            
            print(f"      魔数: {magic}")
            print(f"      版本: {version} (验证: {version_check})")
            print(f"      数据长度: {data_length}")
            print(f"      坐标点数: {point_count}")
            
            # 解析坐标点（APP的坐标读取逻辑）
            coordinates = []
            offset = 16
            
            for i in range(min(point_count, 5)):  # 限制输出数量
                if offset + 8 <= len(data):
                    try:
                        x = struct.unpack('<f', data[offset:offset+4])[0]
                        y = struct.unpack('<f', data[offset+4:offset+8])[0]
                        
                        # 检查坐标有效性（中国境内坐标范围）
                        if self.is_valid_chinese_coordinate(x, y):
                            coordinates.append((x, y))
                            print(f"      🌍 坐标{i}: ({x:.6f}, {y:.6f}) ← 真实经纬度")
                        else:
                            print(f"      📐 坐标{i}: ({x:.6f}, {y:.6f}) (投影坐标)")
                            
                        offset += 12  # APP中每个点的步长
                    except:
                        break
            
            return {
                'format': 'DICE-AM',
                'version': version,
                'version_check': version_check,
                'point_count': point_count,
                'coordinates': coordinates,
                'valid_coordinates': len(coordinates)
            }
            
        except Exception as e:
            print(f"      ❌ DICE-AM解析失败: {e}")
            return {'error': str(e)}
    
    def parse_binary_index(self, data):
        """解析二进制索引数据"""
        try:
            # 分析二进制模式
            header_int = struct.unpack('<I', data[:4])[0] if len(data) >= 4 else 0
            
            print(f"      索引头: 0x{header_int:08x}")
            
            # 查找可能的索引模式
            index_count = 0
            for i in range(4, len(data) - 4, 4):
                try:
                    value = struct.unpack('<I', data[i:i+4])[0]
                    if 0x0F00 <= value <= 0x0FFF:  # 基于观察到的模式
                        index_count += 1
                except:
                    break
                    
            print(f"      索引项数: {index_count}")
            
            return {
                'format': 'BINARY_INDEX',
                'header': header_int,
                'index_count': index_count
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def parse_chinese_text_data(self, data):
        """解析中文文本数据 - 基于APP的UTF-8处理逻辑"""
        try:
            # UTF-8解码
            text = data.decode('utf-8', errors='ignore')
            
            # 提取中文地名
            placenames = []
            current_name = ""
            
            for char in text:
                if '\u4e00' <= char <= '\u9fff':  # 中文字符
                    current_name += char
                elif current_name and len(current_name) >= 2:
                    placenames.append(current_name)
                    current_name = ""
                else:
                    current_name = ""
                    
            if current_name and len(current_name) >= 2:
                placenames.append(current_name)
            
            # 分类地名
            roads = [name for name in placenames if any(suffix in name for suffix in ['路', '街', '道', '巷'])]
            areas = [name for name in placenames if any(suffix in name for suffix in ['市', '区', '县', '镇', '村'])]
            pois = [name for name in placenames if any(suffix in name for suffix in ['公园', '广场', '商场', '中心'])]
            
            print(f"      地名总数: {len(placenames)}")
            print(f"      道路: {len(roads)} 个")
            print(f"      行政区: {len(areas)} 个")
            print(f"      POI: {len(pois)} 个")
            
            # 显示前几个地名
            if placenames:
                print(f"      示例地名: {', '.join(placenames[:5])}")
            
            return {
                'format': 'CHINESE_TEXT',
                'total_placenames': len(placenames),
                'roads': roads[:10],  # 限制数量
                'areas': areas[:10],
                'pois': pois[:10],
                'sample_names': placenames[:10]
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def parse_config_data(self, data):
        """解析配置数据"""
        try:
            # 尝试找到可读文本
            text = data.decode('utf-8', errors='ignore')
            
            # 查找JSON片段
            json_fragments = []
            start = 0
            while True:
                json_start = text.find('{', start)
                if json_start == -1:
                    break
                json_end = text.find('}', json_start)
                if json_end > json_start:
                    fragment = text[json_start:json_end+1]
                    json_fragments.append(fragment)
                    start = json_end + 1
                else:
                    break
            
            print(f"      配置片段: {len(json_fragments)} 个")
            if json_fragments:
                print(f"      示例: {json_fragments[0][:50]}...")
                
            return {
                'format': 'CONFIG',
                'json_fragments': json_fragments,
                'contains_style': any('style' in f.lower() for f in json_fragments),
                'contains_color': any('color' in f.lower() for f in json_fragments)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def is_binary_index_data(self, data):
        """检查是否是二进制索引数据"""
        if len(data) < 8:
            return False
            
        # 检查开头是否符合索引数据模式
        try:
            header = struct.unpack('<I', data[:4])[0]
            return header < 0x100 and len(data) % 4 == 0
        except:
            return False
    
    def contains_chinese_text(self, data):
        """检查是否包含中文文本"""
        try:
            text = data.decode('utf-8', errors='ignore')
            return any('\u4e00' <= char <= '\u9fff' for char in text[:200])
        except:
            return False
    
    def contains_config_data(self, data):
        """检查是否包含配置数据"""
        try:
            text = data.decode('utf-8', errors='ignore')
            return '{' in text or 'style' in text.lower() or 'color' in text.lower()
        except:
            return False
    
    def is_valid_chinese_coordinate(self, x, y):
        """检查是否为有效的中国境内坐标"""
        # 中国大陆经纬度范围
        return (70 <= x <= 140 and 15 <= y <= 60) or (70 <= y <= 140 and 15 <= x <= 60)
    
    def parse_structured_content(self):
        """解析结构化内容 - 基于sub_10F88逻辑"""
        print("\n📊 步骤3: 结构化内容解析 (基于sub_10F88逻辑)")
        print("-" * 50)
        
        # 统计各类型数据
        type_counts = {}
        coordinates_found = 0
        placenames_found = 0
        
        for result in self.structured_results:
            data_type = result['data_type']
            structured = result['structured_data']
            
            type_counts[data_type] = type_counts.get(data_type, 0) + 1
            
            # 统计坐标和地名
            if data_type == 'DICE_AM' and 'coordinates' in structured:
                coordinates_found += len(structured['coordinates'])
            elif data_type == 'CHINESE_TEXT' and 'total_placenames' in structured:
                placenames_found += structured['total_placenames']
        
        print(f"📈 数据类型统计:")
        for dtype, count in type_counts.items():
            print(f"   {dtype}: {count} 个数据块")
            
        print(f"\n🎯 解析结果统计:")
        print(f"   🌍 有效坐标: {coordinates_found} 个")
        print(f"   🏷️ 地名文本: {placenames_found} 个")
    
    def output_final_results(self):
        """输出最终结果"""
        print("\n" + "=" * 80)
        print("📋 最终解析结果 - 基于APP真实代码逻辑")
        print("=" * 80)
        
        # 汇总所有坐标
        all_coordinates = []
        all_placenames = []
        
        for result in self.structured_results:
            structured = result['structured_data']
            
            if result['data_type'] == 'DICE_AM' and 'coordinates' in structured:
                all_coordinates.extend(structured['coordinates'])
                
            elif result['data_type'] == 'CHINESE_TEXT' and 'sample_names' in structured:
                all_placenames.extend(structured['sample_names'])
        
        print(f"🌍 解压后发现的经纬度坐标:")
        for i, (x, y) in enumerate(all_coordinates[:10]):  # 显示前10个
            print(f"   {i+1}. ({x:.6f}, {y:.6f})")
        if len(all_coordinates) > 10:
            print(f"   ... 还有 {len(all_coordinates)-10} 个坐标")
            
        print(f"\n🏷️ 解压后发现的地名文本:")
        for i, name in enumerate(all_placenames[:20]):  # 显示前20个
            print(f"   {i+1}. {name}")
        if len(all_placenames) > 20:
            print(f"   ... 还有 {len(all_placenames)-20} 个地名")
            
        print(f"\n💡 解析总结:")
        print(f"   📊 解压数据块: {len(self.data_blocks)} 个")
        print(f"   🌍 真实坐标: {len(all_coordinates)} 个")
        print(f"   🏷️ 地名标注: {len(all_placenames)} 个")
        print(f"   ✅ 解析方式: 严格按照APP代码逻辑 (sub_5C060 + sub_10F88)")

def main():
    """主函数"""
    parser = AppLogicParser()
    
    # 查找ans文件
    test_files = ['file/m1.ans', 'file/m3.ans']
    target_file = None
    
    for f in test_files:
        if os.path.exists(f):
            target_file = f
            break
    
    if target_file:
        parser.parse_ans_file(target_file)
    else:
        print("❌ 未找到.ans文件")
        print("💡 请确保file/目录下有m1.ans或m3.ans文件")

if __name__ == "__main__":
    main() 