// .ans文件专门分析脚本 - 基于深度验证发现的真实.ans文件路径
// 目标: 专门分析.ans文件的内容和结构

console.log("[.ans文件分析] 开始专门分析高德地图.ans文件...");
console.log("[重大发现] 确认.ans文件确实存在于以下路径:");
console.log("  - /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a*/m*.ans");
console.log("  - /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/PosAoi.ans");
console.log("  - /storage/emulated/0/Android/data/com.autonavi.minimap/files/render/cache_sql_*/*.ans");

// .ans文件分析状态
var ansAnalysis = {
    ansFiles: {},           // .ans文件映射
    ansDataBlocks: [],      // .ans文件数据块
    ansFileTypes: {},       // .ans文件类型统计
    realMapData: [],        // 确认的地图数据
    startTime: Date.now()
};

// 安全的字节数组处理
function safeByteArrayToHex(byteArray, maxLen) {
    var hexBytes = [];
    var len = Math.min(maxLen || 16, byteArray.length);
    for (var i = 0; i < len; i++) {
        hexBytes.push(('0' + byteArray[i].toString(16)).slice(-2));
    }
    return hexBytes.join(' ');
}

// .ans文件类型识别
function identifyAnsFileType(filename) {
    if (filename.indexOf('/navi/compile_v3/') !== -1) {
        if (filename.indexOf('/m1.ans') !== -1) return "NAVI_MAIN_1";
        if (filename.indexOf('/m2.ans') !== -1) return "NAVI_MAIN_2";
        return "NAVI_COMPILE";
    }
    if (filename.indexOf('PosAoi.ans') !== -1) return "POI_AOI";
    if (filename.indexOf('/cache_sql_config/') !== -1) return "RENDER_CONFIG";
    if (filename.indexOf('/cache_sql_smart/') !== -1) return "SMART_MAP";
    if (filename.indexOf('/cache_sql_font/') !== -1) return "FONT_DATA";
    if (filename.indexOf('/dl_sql_cloudres_v1/') !== -1) return "CLOUD_RESOURCE";
    if (filename.indexOf('/dl_sql_smart/') !== -1) return "SMART_DOWNLOAD";
    if (filename.indexOf('/dl_sql_sdmap_v1/') !== -1) return "SD_MAP";
    return "OTHER_ANS";
}

// 数据类型严格验证
function strictDataTypeCheck(headerArray, size, source) {
    var analysis = {
        type: "UNKNOWN",
        confidence: 0,
        reasons: [],
        warnings: [],
        details: {}
    };
    
    if (headerArray.length < 8) {
        analysis.warnings.push("数据太短，无法可靠识别");
        return analysis;
    }
    
    // DICE-AM严格验证
    if (headerArray[0] === 0x44 && headerArray[1] === 0x49 && 
        headerArray[2] === 0x43 && headerArray[3] === 0x45) {
        analysis.type = "DICE-AM";
        analysis.confidence = 60;
        analysis.reasons.push("DICE魔数匹配");
        
        // 验证完整魔数 "DICE-AM\0"
        if (headerArray[4] === 0x2d && headerArray[5] === 0x41 && 
            headerArray[6] === 0x4d && headerArray[7] === 0x00) {
            analysis.confidence += 30;
            analysis.reasons.push("完整DICE-AM魔数匹配");
            
            // 详细分析DICE-AM结构
            if (headerArray.length >= 16) {
                analysis.details.version = headerArray[8];
                analysis.details.flags = headerArray[9];
                analysis.details.geometryType = headerArray[10];
                analysis.details.geometryFlags = headerArray[11];
                
                if (headerArray[8] === 0xaa) {
                    analysis.confidence += 5;
                    analysis.reasons.push("版本170合理");
                }
                
                var geoType = headerArray[10];
                if (geoType === 0x89 || geoType === 0x8d || geoType === 0xcf) {
                    analysis.confidence += 5;
                    analysis.reasons.push("几何类型合理: 0x" + geoType.toString(16));
                }
            }
        } else {
            analysis.warnings.push("DICE后缀不匹配");
        }
    }
    // TEXT严格验证
    else if (headerArray[0] === 0x0d && headerArray[1] === 0x00 && 
             headerArray[2] === 0x00 && headerArray[3] === 0x00) {
        analysis.type = "TEXT";
        analysis.confidence = 50;
        analysis.reasons.push("TEXT魔数匹配");
        
        // 验证后续数据
        var hasTextLike = false;
        for (var i = 8; i < Math.min(headerArray.length, 16); i++) {
            var byte = headerArray[i];
            if ((byte >= 0x20 && byte <= 0x7e) || byte >= 0x80) {
                hasTextLike = true;
                break;
            }
        }
        
        if (hasTextLike) {
            analysis.confidence += 20;
            analysis.reasons.push("包含文本字符");
        }
        
        if (headerArray.length >= 8) {
            analysis.details.textType = headerArray[4];
            analysis.details.textFlags = headerArray[5];
        }
    }
    // CONFIG严格验证
    else if (headerArray[0] === 0xbc && headerArray[1] === 0xbc && 
             headerArray[2] === 0xbc && headerArray[3] === 0xbc) {
        analysis.type = "CONFIG";
        analysis.confidence = 70;
        analysis.reasons.push("CONFIG魔数匹配");
        
        if (size >= 20 && size <= 100000) {
            analysis.confidence += 10;
            analysis.reasons.push("大小合理");
        }
        
        if (headerArray.length >= 12) {
            analysis.details.configType = headerArray[8];
            analysis.details.configFlags = headerArray[9];
        }
    }
    // zlib压缩数据
    else if (headerArray[0] === 0x78 && headerArray[1] === 0x9c) {
        analysis.type = "ZLIB";
        analysis.confidence = 80;
        analysis.reasons.push("标准zlib头部");
    }
    // AM-zlib容器
    else if (headerArray[0] === 0x08) {
        analysis.type = "AM-ZLIB";
        analysis.confidence = 60;
        analysis.reasons.push("AM-zlib容器头部");
    }
    
    return analysis;
}

// 检查是否是.ans文件
function isAnsFile(filename) {
    return filename && filename.indexOf('.ans') !== -1;
}

// 1. Hook文件打开 - 专门跟踪.ans文件
console.log("[1] 跟踪.ans文件打开...");

try {
    var libc = Process.getModuleByName("libc.so");
    var openPtr = libc.getExportByName("open");
    
    Interceptor.attach(openPtr, {
        onEnter: function(args) {
            this.filename = args[0].readCString();
            this.flags = args[1].toInt32();
        },
        onLeave: function(retval) {
            var fd = retval.toInt32();
            if (fd > 0 && isAnsFile(this.filename)) {
                var ansFileType = identifyAnsFileType(this.filename);
                
                ansAnalysis.ansFiles[fd] = {
                    fd: fd,
                    filename: this.filename,
                    fileType: ansFileType,
                    openTime: Date.now(),
                    totalBytes: 0,
                    dataBlocks: []
                };
                
                console.log("[.ans文件打开] " + this.filename);
                console.log("  fd=" + fd + ", 类型=" + ansFileType);
                
                // 统计文件类型
                ansAnalysis.ansFileTypes[ansFileType] = (ansAnalysis.ansFileTypes[ansFileType] || 0) + 1;
            }
        }
    });
    
    console.log("[✓] .ans文件打开Hook设置成功");
} catch (e) {
    console.log("[✗] .ans文件打开Hook失败: " + e.message);
}

// 2. Hook文件读取 - 专门分析.ans文件内容
try {
    var readPtr = libc.getExportByName("read");
    
    Interceptor.attach(readPtr, {
        onEnter: function(args) {
            this.fd = args[0].toInt32();
            this.buf = args[1];
            this.count = args[2].toInt32();
        },
        onLeave: function(retval) {
            var bytesRead = retval.toInt32();
            if (bytesRead > 16 && ansAnalysis.ansFiles[this.fd]) {
                try {
                    var ansFile = ansAnalysis.ansFiles[this.fd];
                    var data = this.buf.readByteArray(Math.min(64, bytesRead));
                    var header = new Uint8Array(data);
                    
                    // 安全的数组处理
                    var headerArray = [];
                    for (var i = 0; i < Math.min(16, header.length); i++) {
                        headerArray.push(header[i]);
                    }
                    
                    // 严格验证数据类型
                    var analysis = strictDataTypeCheck(headerArray, bytesRead, "ans_file_read");
                    var headerHex = safeByteArrayToHex(headerArray, 16);
                    
                    var dataBlock = {
                        source: "ans_file_read",
                        fd: this.fd,
                        filename: ansFile.filename,
                        fileType: ansFile.fileType,
                        size: bytesRead,
                        type: analysis.type,
                        confidence: analysis.confidence,
                        reasons: analysis.reasons,
                        warnings: analysis.warnings,
                        details: analysis.details,
                        headerHex: headerHex,
                        timestamp: Date.now()
                    };
                    
                    ansAnalysis.ansDataBlocks.push(dataBlock);
                    ansFile.dataBlocks.push(dataBlock);
                    ansFile.totalBytes += bytesRead;
                    
                    if (analysis.confidence >= 70) {
                        ansAnalysis.realMapData.push(dataBlock);
                        
                        console.log("[.ans文件数据] " + ansFile.fileType + " - " + analysis.type + " (置信度: " + analysis.confidence + "%)");
                        console.log("  文件: " + ansFile.filename);
                        console.log("  大小: " + bytesRead + " 字节");
                        console.log("  头部: " + headerHex);
                        console.log("  原因: " + analysis.reasons.join(", "));
                        
                        if (analysis.details && Object.keys(analysis.details).length > 0) {
                            console.log("  详细信息:");
                            for (var key in analysis.details) {
                                var value = analysis.details[key];
                                if (typeof value === 'number') {
                                    console.log("    " + key + ": 0x" + value.toString(16) + " (" + value + ")");
                                } else {
                                    console.log("    " + key + ": " + value);
                                }
                            }
                        }
                        
                        if (analysis.warnings.length > 0) {
                            console.log("  警告: " + analysis.warnings.join(", "));
                        }
                    } else if (analysis.confidence >= 40) {
                        console.log("[.ans可疑数据] " + ansFile.fileType + " - " + analysis.type + " (置信度: " + analysis.confidence + "%)");
                        console.log("  文件: " + ansFile.filename);
                        console.log("  警告: " + analysis.warnings.join(", "));
                    }
                    
                } catch (e) {
                    console.log("[错误] .ans文件数据分析失败: " + e.message);
                }
            }
        }
    });
    
    console.log("[✓] .ans文件读取Hook设置成功");
} catch (e) {
    console.log("[✗] .ans文件读取Hook失败: " + e.message);
}

// 3. 定期输出.ans文件分析报告
setInterval(function() {
    var runtime = Math.floor((Date.now() - ansAnalysis.startTime) / 1000);
    
    console.log("\n[.ans文件分析报告] ==========================================");
    console.log("运行时间: " + runtime + "s");
    console.log("");
    
    console.log(".ans文件统计:");
    var ansFileCount = Object.keys(ansAnalysis.ansFiles).length;
    console.log("  打开的.ans文件数: " + ansFileCount);
    
    console.log("");
    console.log(".ans文件类型分布:");
    for (var fileType in ansAnalysis.ansFileTypes) {
        console.log("  " + fileType + ": " + ansAnalysis.ansFileTypes[fileType] + " 个");
    }
    
    console.log("");
    console.log("数据块分析:");
    console.log("  总数据块: " + ansAnalysis.ansDataBlocks.length);
    console.log("  高置信度数据: " + ansAnalysis.realMapData.length);
    
    // 按数据类型统计
    var dataTypes = {};
    for (var i = 0; i < ansAnalysis.realMapData.length; i++) {
        var data = ansAnalysis.realMapData[i];
        var key = data.fileType + "-" + data.type;
        dataTypes[key] = (dataTypes[key] || 0) + 1;
    }
    
    console.log("");
    console.log("确认的.ans文件数据类型:");
    for (var type in dataTypes) {
        console.log("  " + type + ": " + dataTypes[type] + " 次");
    }
    
    // 计算总体置信度
    var totalConfidence = 0;
    var count = 0;
    for (var i = 0; i < ansAnalysis.realMapData.length; i++) {
        totalConfidence += ansAnalysis.realMapData[i].confidence;
        count++;
    }
    
    if (count > 0) {
        var avgConfidence = (totalConfidence / count).toFixed(1);
        console.log("");
        console.log(".ans文件数据置信度: " + avgConfidence + "%");
        
        if (avgConfidence >= 80) {
            console.log("结论: 高置信度确认.ans文件包含真实地图数据");
        } else if (avgConfidence >= 60) {
            console.log("结论: 中等置信度，.ans文件可能包含地图数据");
        } else {
            console.log("结论: 低置信度，需要进一步验证");
        }
    }
    
    console.log("===============================================\n");
}, 25000);

console.log("[.ans文件分析] 专门分析脚本已启动...");
console.log("[目标] 专门分析.ans文件的内容和结构");
console.log("[提示] 请移动地图以触发.ans文件读取");
