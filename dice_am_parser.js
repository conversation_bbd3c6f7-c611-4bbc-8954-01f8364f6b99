 /*
 * DICE-AM格式专用解析器
 * 基于真实捕获的数据：44 49 43 45 2D 41 4D 00 AA 00 89 8D CF 8D...
 */

(function() {
    'use strict';
    
    console.log("[DICE-AM Parser] 启动DICE-AM专用解析器...");
    
    var CONFIG = {
        INIT_DELAY: 3000,
        MAX_SAMPLES: 8
    };
    
    var parseStats = {
        totalDiceBlocks: 0,
        parsedCoordinates: 0,
        parsedChineseText: 0,
        parsedPOIs: 0
    };
    
    // === DICE-AM格式解析 ===
    function parseDiceAMBlock(data) {
        try {
            var view = new Uint8Array(data);
            var result = {
                header: "",
                version: 0,
                coordinates: [],
                chineseText: [],
                pois: [],
                roads: []
            };
            
            // 验证DICE-AM头部
            if (view.length < 8) return null;
            
            // 提取头部 "DICE-AM"
            for (var i = 0; i < 7; i++) {
                result.header += String.fromCharCode(view[i]);
            }
            
            if (result.header !== "DICE-AM") {
                return null;
            }
            
            result.version = view[7]; // 版本号
            console.log("[DICE-AM] 解析版本 " + result.version + " 的数据块");
            
            // 从固定偏移开始解析数据
            var offset = 8;
            
            // 解析数据头部信息
            if (view.length > offset + 8) {
                var headerInfo = {
                    field1: (view[offset] | (view[offset+1] << 8)),
                    field2: (view[offset+2] | (view[offset+3] << 8) | (view[offset+4] << 16) | (view[offset+5] << 24)),
                    field3: (view[offset+6] | (view[offset+7] << 8))
                };
                console.log("[DICE-AM] 头部信息: " + JSON.stringify(headerInfo));
                offset += 8;
            }
            
            // 查找坐标数据（可能以特定模式存储）
            result.coordinates = searchCoordinatesInDice(view, offset);
            
            // 查找中文文本数据
            result.chineseText = searchChineseInDice(view, offset);
            
            // 查找POI数据
            result.pois = searchPOIsInDice(view, offset);
            
            console.log("[DICE-AM 结果] 坐标: " + result.coordinates.length + 
                       ", 中文: " + result.chineseText.length + 
                       ", POI: " + result.pois.length);
            
            return result;
            
        } catch (e) {
            console.log("[DICE-AM 解析错误] " + e);
            return null;
        }
    }
    
    // === 在DICE-AM中搜索坐标 ===
    function searchCoordinatesInDice(view, startOffset) {
        var coords = [];
        try {
            var dataView = new DataView(view.buffer, view.byteOffset, view.byteLength);
            
            // 尝试不同的坐标编码方式
            for (var i = startOffset; i < view.length - 8; i += 2) {
                try {
                    // 方式1: 标准float32 (little endian)
                    if (i % 4 === 0) {
                        var x1 = dataView.getFloat32(i, true);
                        var y1 = dataView.getFloat32(i + 4, true);
                        if (isValidCoordinate(x1, y1)) {
                            coords.push({
                                x: x1, y: y1, offset: i, 
                                type: "float32_le", 
                                desc: "(" + x1.toFixed(6) + ", " + y1.toFixed(6) + ")"
                            });
                            console.log("[坐标发现] Float32LE: " + coords[coords.length-1].desc + " @" + i);
                        }
                    }
                    
                    // 方式2: 压缩坐标 (可能是int16或int32的缩放版本)
                    var x2 = dataView.getInt32(i, true) / 100000.0; // 常见的坐标压缩比例
                    var y2 = dataView.getInt32(i + 4, true) / 100000.0;
                    if (isValidCoordinate(x2, y2)) {
                        coords.push({
                            x: x2, y: y2, offset: i, 
                            type: "int32_scaled", 
                            desc: "(" + x2.toFixed(6) + ", " + y2.toFixed(6) + ")"
                        });
                        console.log("[坐标发现] Int32缩放: " + coords[coords.length-1].desc + " @" + i);
                    }
                    
                    // 方式3: 16位压缩坐标
                    var x3 = dataView.getInt16(i, true) / 100.0;
                    var y3 = dataView.getInt16(i + 2, true) / 100.0;
                    if (isValidCoordinate(x3, y3)) {
                        coords.push({
                            x: x3, y: y3, offset: i, 
                            type: "int16_scaled", 
                            desc: "(" + x3.toFixed(6) + ", " + y3.toFixed(6) + ")"
                        });
                        console.log("[坐标发现] Int16缩放: " + coords[coords.length-1].desc + " @" + i);
                    }
                    
                    if (coords.length >= 10) break; // 限制输出
                    
                } catch (e) {
                    continue;
                }
            }
        } catch (e) {
            console.log("[坐标搜索错误] " + e);
        }
        return coords;
    }
    
    // === 在DICE-AM中搜索中文文本 ===
    function searchChineseInDice(view, startOffset) {
        var texts = [];
        try {
            // 搜索UTF-8中文字节模式
            for (var i = startOffset; i < view.length - 3; i++) {
                if (view[i] >= 0xE4 && view[i] <= 0xE9 && 
                    view[i+1] >= 0x80 && view[i+1] <= 0xBF &&
                    view[i+2] >= 0x80 && view[i+2] <= 0xBF) {
                    
                    // 找到可能的中文开始位置，尝试解码更长的字符串
                    var maxLen = Math.min(i + 64, view.length);
                    var chineseBytes = view.slice(i, maxLen);
                    
                    try {
                        var text = new TextDecoder('utf-8', {fatal: false}).decode(chineseBytes);
                        var chineseChars = "";
                        
                        for (var j = 0; j < text.length; j++) {
                            var char = text[j];
                            if (char >= '\u4e00' && char <= '\u9fff') {
                                chineseChars += char;
                            } else if (chineseChars.length > 0) {
                                break; // 遇到非中文字符停止
                            }
                        }
                        
                        if (chineseChars.length >= 2) {
                            texts.push({
                                text: chineseChars,
                                offset: i,
                                length: chineseChars.length,
                                desc: chineseChars
                            });
                            console.log("[中文发现] '" + chineseChars + "' @" + i);
                            
                            i += chineseChars.length * 3; // 跳过已处理的字节
                            if (texts.length >= 10) break;
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }
        } catch (e) {
            console.log("[中文搜索错误] " + e);
        }
        return texts;
    }
    
    // === 在DICE-AM中搜索POI数据 ===
    function searchPOIsInDice(view, startOffset) {
        var pois = [];
        try {
            // POI数据可能包含坐标+名称的组合
            // 寻找特定的数据模式
            for (var i = startOffset; i < view.length - 16; i++) {
                // 寻找可能的POI结构标识
                if (view[i] === 0x01 || view[i] === 0x02 || view[i] === 0x03) {
                    // 检查后面是否跟着坐标数据
                    try {
                        var dataView = new DataView(view.buffer, view.byteOffset + i + 1, 8);
                        var px = dataView.getFloat32(0, true);
                        var py = dataView.getFloat32(4, true);
                        
                        if (isValidCoordinate(px, py)) {
                            pois.push({
                                type: view[i],
                                x: px,
                                y: py,
                                offset: i,
                                desc: "POI类型" + view[i] + " (" + px.toFixed(6) + ", " + py.toFixed(6) + ")"
                            });
                            console.log("[POI发现] " + pois[pois.length-1].desc + " @" + i);
                            
                            if (pois.length >= 5) break;
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }
        } catch (e) {
            console.log("[POI搜索错误] " + e);
        }
        return pois;
    }
    
    // === 坐标有效性检查 ===
    function isValidCoordinate(x, y) {
        // 中国境内坐标范围 + 一些常见的投影坐标
        return (
            (x >= 70 && x <= 140 && y >= 15 && y <= 55) ||      // 标准经纬度
            (x >= 15 && x <= 55 && y >= 70 && y <= 140) ||      // 坐标可能颠倒
            (x >= 10000000 && x <= 20000000 && y >= 2000000 && y <= 6000000) || // 可能的投影坐标
            (x >= 2000000 && x <= 6000000 && y >= 10000000 && y <= 20000000)    // 投影坐标颠倒
        );
    }
    
    // === Hook zlib解压专门捕获DICE-AM ===
    function setupDiceAMHook() {
        console.log("[DICE-AM Hook] 设置专门的DICE-AM Hook...");
        
        try {
            var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
            if (uncompressPtr) {
                Interceptor.attach(uncompressPtr, {
                    onEnter: function(args) {
                        this.destBuffer = args[0];
                        this.destLenPtr = args[1];
                    },
                    onLeave: function(retval) {
                        if (retval.toInt32() === 0 && parseStats.totalDiceBlocks < CONFIG.MAX_SAMPLES) {
                            try {
                                var len = this.destLenPtr.readU32();
                                if (len > 8 && len < 1024 * 1024) {
                                    var data = this.destBuffer.readByteArray(len);
                                    var view = new Uint8Array(data);
                                    
                                    // 检查是否为DICE-AM数据
                                    if (view.length >= 7 && 
                                        view[0] === 0x44 && view[1] === 0x49 && view[2] === 0x43 && 
                                        view[3] === 0x45 && view[4] === 0x2D && view[5] === 0x41 && view[6] === 0x4D) {
                                        
                                        parseStats.totalDiceBlocks++;
                                        console.log("\n🎯=== DICE-AM数据块 #" + parseStats.totalDiceBlocks + " ===");
                                        
                                        var result = parseDiceAMBlock(data);
                                        if (result) {
                                            parseStats.parsedCoordinates += result.coordinates.length;
                                            parseStats.parsedChineseText += result.chineseText.length;
                                            parseStats.parsedPOIs += result.pois.length;
                                        }
                                        
                                        console.log("🎯=================================\n");
                                    }
                                }
                            } catch (e) {
                                console.log("[DICE-AM Hook错误] " + e);
                            }
                        }
                    }
                });
                console.log("[DICE-AM Hook] Hook已设置");
            }
        } catch (e) {
            console.log("[DICE-AM Hook错误] " + e);
        }
    }
    
    // === 生成解析报告 ===
    function generateParseReport() {
        if (parseStats.totalDiceBlocks === 0) return;
        
        console.log("\n🎯=== DICE-AM解析报告 ===");
        console.log("DICE-AM数据块: " + parseStats.totalDiceBlocks + " 个");
        console.log("解析出坐标: " + parseStats.parsedCoordinates + " 个");
        console.log("解析出中文: " + parseStats.parsedChineseText + " 个");
        console.log("解析出POI: " + parseStats.parsedPOIs + " 个");
        console.log("🎯==========================\n");
    }
    
    // === 主函数 ===
    function main() {
        console.log("[Main] 初始化DICE-AM专用解析器...");
        
        setTimeout(function() {
            setupDiceAMHook();
            
            setInterval(generateParseReport, 15000);
            
            console.log("[DICE-AM Parser] DICE-AM专用解析器已启动!");
            console.log("现在移动地图，专门解析DICE-AM数据块...");
            
        }, CONFIG.INIT_DELAY);
    }
    
    // === 启动 ===
    try {
        Java.perform(function() {
            console.log("[Java] Java环境已准备就绪");
            main();
        });
    } catch (e) {
        console.log("[Java Error] " + e);
        main();
    }
    
})();