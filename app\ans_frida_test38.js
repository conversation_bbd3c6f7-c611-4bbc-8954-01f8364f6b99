// ANS文件处理流程轻量级跟踪脚本

(function() {
    console.log("[ANS流程分析] 启动...");

    // 全局变量
    var libamapnsq = null;
    var libamapr = null;
    var libamapmain = null;
    var fileOperations = {};
    var lastAnsData = null;

    // 延迟执行，确保应用完全启动
    setTimeout(function() {
        setupHooks();
    }, 3000);

    function setupHooks() {
        try {
            // 1. 查找关键库
            libamapnsq = Process.findModuleByName("libamapnsq.so");
            libamapr = Process.findModuleByName("libamapr.so");
            libamapmain = Process.findModuleByName("libamapmain.so");
            
            if (!libamapnsq || !libamapr || !libamapmain) {
                console.log("[ANS流程分析] 未找到所有关键库，稍后重试");
                setTimeout(setupHooks, 1000);
                return;
            }
            
            console.log("[ANS流程分析] 已找到关键库:");
            console.log("  libamapnsq.so: " + libamapnsq.base);
            console.log("  libamapr.so: " + libamapr.base);
            console.log("  libamapmain.so: " + libamapmain.base);
            
            // 2. 设置文件操作钩子
            setupFileHooks();
            
            // 3. 设置ANS解析钩子
            setupParserHooks();
            
            console.log("[ANS流程分析] 钩子设置完成，等待操作...");
        } catch(e) {
            console.log("[ANS流程分析] 设置钩子失败: " + e);
        }
    }
    
    function setupFileHooks() {
        // 钩住open系统调用
        Interceptor.attach(Module.findExportByName(null, "open"), {
            onEnter: function(args) {
                try {
                    var path = args[0].readUtf8String();
                    if (path && path.indexOf(".ans") !== -1) {
                        this.path = path;
                        this.isAnsFile = true;
                        console.log("[文件操作] 打开ANS文件: " + path);
                    }
                } catch(e) {}
            },
            onLeave: function(retval) {
                if (this.isAnsFile) {
                    var fd = retval.toInt32();
                    if (fd > 0) {
                        fileOperations[fd] = {
                            path: this.path,
                            size: 0
                        };
                        console.log("[文件操作] ANS文件打开成功，文件描述符: " + fd);
                    }
                }
            }
        });
        
        // 钩住read系统调用
        Interceptor.attach(Module.findExportByName(null, "read"), {
            onEnter: function(args) {
                var fd = args[0].toInt32();
                this.fd = fd;
                this.buffer = args[1];
                this.size = args[2].toInt32();
                
                if (fileOperations[fd]) {
                    this.isAnsFile = true;
                }
            },
            onLeave: function(retval) {
                if (this.isAnsFile && retval.toInt32() > 0) {
                    var bytesRead = retval.toInt32();
                    var fileOp = fileOperations[this.fd];
                    
                    fileOp.size += bytesRead;
                    
                    // 只记录前几个读取操作，避免日志过多
                    if (fileOp.size < 32768) {
                        console.log("[文件操作] 读取ANS文件 fd=" + this.fd + ", 大小=" + bytesRead);
                    }
                }
            }
        });
        
        // 钩住close系统调用
        Interceptor.attach(Module.findExportByName(null, "close"), {
            onEnter: function(args) {
                var fd = args[0].toInt32();
                this.fd = fd;
                this.hasFileOp = !!fileOperations[fd];
            },
            onLeave: function(retval) {
                if (this.hasFileOp) {
                    var fileOp = fileOperations[this.fd];
                    console.log("[文件操作] 关闭ANS文件 fd=" + this.fd);
                    console.log("[文件操作] ANS文件 " + fileOp.path + " 已关闭，总共读取 " + fileOp.size + " 字节");
                    
                    // 删除文件操作记录，避免内存泄漏
                    delete fileOperations[this.fd];
                }
            }
        });
    }
    
    function setupParserHooks() {
        // 1. 钩住主解析函数 sub_C654
        var parserFuncOffset = 0xC654;
        var parserFuncAddr = libamapnsq.base.add(parserFuncOffset);
        
        console.log("[ANS流程分析] 设置解析函数钩子: " + parserFuncAddr);
        
        Interceptor.attach(parserFuncAddr, {
            onEnter: function(args) {
                this.srcData = args[0];
                this.destBuffer = args[1];
                this.sizePtr = args[2];
                this.size = args[4].toInt32();
                
                console.log("[ANS解析] 调用解析函数 sub_C654");
                console.log("  源数据大小: " + this.size + " 字节");
            },
            onLeave: function(retval) {
                if (retval.toInt32() === 0) {
                    try {
                        var decompressedSize = Memory.readInt(this.sizePtr);
                        console.log("[ANS解析] 解析成功，解压后大小: " + decompressedSize + " 字节");
                        
                        // 保存解压后的数据引用
                        lastAnsData = {
                            buffer: this.destBuffer,
                            size: decompressedSize,
                            timestamp: Date.now()
                        };
                    } catch(e) {
                        console.log("[ANS解析] 读取解析数据失败: " + e);
                    }
                } else {
                    console.log("[ANS解析] 解析失败，返回值: " + retval);
                }
            }
        });
        
        // 2. 钩住uncompress函数
        var uncompressAddr = Module.findExportByName("libz.so", "uncompress");
        
        if (uncompressAddr) {
            console.log("[ANS流程分析] 设置uncompress函数钩子: " + uncompressAddr);
            
            Interceptor.attach(uncompressAddr, {
                onEnter: function(args) {
                    this.srcLen = args[3].toInt32();
                    
                    console.log("[ANS解压] 调用uncompress函数");
                    console.log("  源大小: " + this.srcLen + " 字节");
                },
                onLeave: function(retval) {
                    console.log("[ANS解压] uncompress返回: " + retval.toInt32());
                }
            });
        }
    }
    
    console.log("[ANS流程分析] 脚本设置完成，等待操作...");
})();