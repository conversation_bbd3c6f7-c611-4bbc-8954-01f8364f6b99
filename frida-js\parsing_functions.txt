          {
            v13 = sub_64EB40(v10, 0);
            v14 = sub_64EB40(v10, 1);
            v15 = sub_64EB40(v10, 2);
            v16 = v13[5];
            v17 = v14[5];
            v18 = v15;
            v19 = v26;
            if ( v16 >= v17 )
              v20 = v14[5];
            else
              v20 = v13[5];
            if ( v16 >= v17 )
              v21 = v16;
            else
              v21 = v14[5];
            while ( v19 != v27 )
            {
              if ( *((_DWORD *)v19 + 8) >= v20 && *((_DWORD *)v19 + 7) <= v21 )
                goto LABEL_28;
              v19 = sub_B3970((__int64)v19);
            }
            v22 = v18[5];
            v24[0] = v20;
            v24[1] = v21;
            *(_DWORD *)sub_AF164(&v26, v24) = v22;
          }
        }
        else if ( v9 == 64 && sub_64EB70((__int64)v8, "default") )
        {
          sub_B78AC();
          if ( v12 )
            v25 = *(_QWORD *)(v11 + 40);
        }
      }
      ++v3;
    }
    sub_11B1C8(v4, (__int64 *)&v26);
    sub_11B1D0(v4, &v25);
  }
  else if ( sub_537F00() )
  {
    v23 = (void (__fastcall ***)(_QWORD, __int64, _QWORD, __int64, const char *, const char *, const char *, __int64, const char *))sub_537F00();
    (**v23)(
      v23,
      32,
      0,
      64,
      "pos",
      "Lane-LanePosEngine",
      "void lane_pos::LanePosEngine::parseLinkScoreThreshold(const cJSON *)",
      1074,
      "linkscore_threshold data illegal");
  }
LABEL_28:
  sub_B45E0((__int64)&v26);
}
// AEE4C: variable 'v3' is possibly undefined
// AEE4C: variable 'v2' is possibly undefined
// AEE88: variable 'v12' is possibly undefined
// AEE8C: variable 'v11' is possibly undefined

//----- (00000000000AEFDC) ----------------------------------------------------
void sub_AEFDC()
{
  __int64 v0; // x20
  __int64 v1; // x0
  char *v2; // x1
  __int64 v3; // x19
  char v4; // zf
  int v5; // w0
  _QWORD *v6; // x0
  char v7; // w21
  __int64 v8; // x1
  __int64 v9; // x2
  __int64 v10; // x3
  __int64 v11; // x4
  __int64 v12; // x5
  __int64 v13; // x6
  __int64 v14; // x7
  __int64 v15; // x0
  __int64 v16; // x2
  __int64 v17; // x3
  __int64 v18; // x4
  __int64 v19; // x5
  __int64 v20; // x6
  __int64 v21; // x7
  int v22; // w0
  _QWORD *v23; // x0
  char v24; // w21
  __int64 v25; // x1
  __int64 v26; // x2
  __int64 v27; // x3
  __int64 v28; // x4
  __int64 v29; // x5
  __int64 v30; // x6
  __int64 v31; // x7
  __int64 v32; // x0
  __int64 v33; // x2
  __int64 v34; // x3
  __int64 v35; // x4
  __int64 v36; // x5
  __int64 v37; // x6
  __int64 v38; // x7
  int v39; // w0
  _QWORD *v40; // x0
  char v41; // w20
  __int64 v42; // x1
  __int64 v43; // x2
  __int64 v44; // x3
  __int64 v45; // x4
  __int64 v46; // x5
  __int64 v47; // x6
  __int64 v48; // x7
  __int64 v49; // [xsp+0h] [xbp-60h]
  __int64 v50; // [xsp+0h] [xbp-60h]
  __int64 v51; // [xsp+0h] [xbp-60h]
  __int64 v52; // [xsp+0h] [xbp-60h]
  __int64 v53; // [xsp+0h] [xbp-60h]
  unsigned int v54; // [xsp+8h] [xbp-58h]
  __int64 v55; // [xsp+8h] [xbp-58h]
  __int64 v56; // [xsp+8h] [xbp-58h]
  __int64 v57; // [xsp+8h] [xbp-58h]
  __int64 v58; // [xsp+8h] [xbp-58h]
  int v59; // [xsp+Ch] [xbp-54h]
  __int64 v60; // [xsp+10h] [xbp-50h]
  __int64 v61; // [xsp+10h] [xbp-50h]
  __int64 v62; // [xsp+10h] [xbp-50h]
  __int64 v63; // [xsp+10h] [xbp-50h]
  __int64 v64; // [xsp+18h] [xbp-48h]
  __int64 v65; // [xsp+18h] [xbp-48h]
  __int64 v66; // [xsp+18h] [xbp-48h]
  __int64 v67; // [xsp+18h] [xbp-48h]
  __int64 v68; // [xsp+18h] [xbp-48h]
  unsigned __int64 v69[4]; // [xsp+20h] [xbp-40h] BYREF

  sub_B7A00();
  if ( v2 )
  {
    v3 = v1;
    if ( sub_B77C4(v1, v2) )
    {
      sub_B79B4();
      if ( v4 )
      {
        BYTE4(v64) = 1;
        v5 = (unsigned int)sub_AEB54(v69, "is_open");
        v6 = sub_AECFC(v5, v0, (__int64)v69);
        v7 = (char)v6;
        sub_B790C((__int64)v6, v8, v9, v10, v11, v12, v13, v14, v49, v54, 0, v64, v69[0]);
        if ( (v7 & 1) != 0 )
--
    *((_DWORD *)a4 + 2) = v22 + 1;
    memcpy((void *)(v23 + 88 * v22), v17, 0x58u);
    goto LABEL_6;
  }
  v24 = 1011;
LABEL_9:
  j__pthread_rwlock_unlock_0(rwlock);
  return v24;
}

//----- (00000000002B2B58) ----------------------------------------------------
__int64 __fastcall sub_2B2B58(__int64 a1, __int64 a2)
{
  pthread_rwlock_t *v2; // x19
  unsigned int v5; // w23
  const char *v7; // x2
  const char *v8; // x2
  const char *v9; // x2
  void *ptr; // [xsp+8h] [xbp-B8h] BYREF
  __int64 v11; // [xsp+10h] [xbp-B0h]
  int v12[12]; // [xsp+18h] [xbp-A8h] BYREF
  int v13[12]; // [xsp+48h] [xbp-78h] BYREF
  __int64 v14; // [xsp+78h] [xbp-48h]

  v2 = (pthread_rwlock_t *)(a1 + 864);
  v14 = *(_QWORD *)(_ReadStatusReg(TPIDR_EL0) + 40);
  j__pthread_rwlock_wrlock((pthread_rwlock_t *)(a1 + 864));
  if ( sub_B2FEC((unsigned __int8 *)a2, (unsigned __int8 *)(a1 + 920))
    || (sub_2B2780(a1), sub_AF3C4((unsigned __int8 *)a2, "")) )
  {
    v5 = 1000;
  }
  else
  {
    sub_2B2D78((__int64)v13);
    sub_2B2D78((__int64)v12);
    v13[0] = 1;
    v12[0] = 2;
    v5 = sub_29F4F0();
    if ( (*(_BYTE *)a2 & 1) != 0 )
      v7 = *(const char **)(a2 + 16);
    else
      v7 = (const char *)(a2 + 1);
    sub_29DC58(1u, "get parking: %s, code: %d", v7, v5);
    if ( v5 == 1000 )
    {
      v5 = sub_2B2E0C(a1, (__int64)v13, (__int64)v12);
      sub_29E650(v13);
      sub_29E650(v12);
      v8 = (*(_BYTE *)a2 & 1) != 0 ? *(const char **)(a2 + 16) : (const char *)(a2 + 1);
      sub_29DC58(1u, "parse parking: %s, lines: %d, code: %d", v8, *(_DWORD *)(a1 + 952), v5);
      if ( v5 == 1000 )
      {
        ptr = 0;
        v11 = 0x100000000LL;
        v5 = sub_29FC64(qword_919790, (unsigned __int8 *)a2, &ptr);
        if ( v5 == 1000 )
        {
          sub_2B2EF0(a1, (__int64)&ptr);
          if ( (*(_BYTE *)a2 & 1) != 0 )
            v9 = *(const char **)(a2 + 16);
          else
            v9 = (const char *)(a2 + 1);
          sub_29DC58(1u, "parse spots: %s, spots: %d, code: %d", v9, *(_DWORD *)(a1 + 968), 1000);
          if ( ptr )
          {
            free(ptr);
            ptr = 0;
          }
          LODWORD(v11) = 0;
        }
      }
    }
    sub_29D160(v12);
    sub_29D160(v13);
    if ( v5 == 1000 )
      sub_B294C((unsigned __int8 *)(a1 + 920), (unsigned __int8 *)a2);
    else
      sub_2B2780(a1);
  }
  j__pthread_rwlock_unlock(v2);
  return v5;
}
// 919790: using guessed type __int64 qword_919790;

//----- (00000000002B2D78) ----------------------------------------------------
void __fastcall sub_2B2D78(__int64 a1)
{
  unsigned __int8 *v1; // x20
  unsigned __int64 v3[4]; // [xsp+0h] [xbp-40h] BYREF

  v1 = (unsigned __int8 *)(a1 + 24);
  v3[3] = *(_QWORD *)(_ReadStatusReg(TPIDR_EL0) + 40);
  sub_B27E8(a1 + 24);
  *(_DWORD *)a1 = 0;
  sub_AEB54(v3, "");
  *(_DWORD *)(a1 + 4) = 0;
  *(_WORD *)(a1 + 8) = 0;
  *(_BYTE *)(a1 + 10) = 0;
  *(_DWORD *)(a1 + 12) = 0;
  *(_QWORD *)(a1 + 16) = 0;
  sub_B294C(v1, (unsigned __int8 *)v3);
  sub_AD4D0((__int64)v3);
}

//----- (00000000002B2E0C) ----------------------------------------------------
__int64 __fastcall sub_2B2E0C(__int64 a1, __int64 a2, __int64 a3)
{
  unsigned int v6; // w0
  __int64 v7; // x21
  unsigned __int64 v8; // x22
  __int64 v9; // x0
  __int64 v10; // x8
  __int64 v11; // x9
  __int64 result; // x0
  int v13; // [xsp+0h] [xbp-40h] BYREF
  unsigned __int16 v14; // [xsp+4h] [xbp-3Ch] BYREF
  __int64 v15; // [xsp+8h] [xbp-38h]

  v15 = *(_QWORD *)(_ReadStatusReg(TPIDR_EL0) + 40);
  v6 = sub_29A578(a1 + 280, *(_QWORD *)(a3 + 16));
  if ( !v6 )
    return 1251;
  v7 = *(_QWORD *)(a2 + 16);
  v8 = 88LL * v6;
  v9 = operator new[](v8);
  v10 = 0;
  do
  {
    v11 = v9 + v10;
    v10 += 88;
    *(_DWORD *)(v11 + 48) = 0;
    *(_QWORD *)(v11 + 24) = 0;
    *(_QWORD *)(v11 + 32) = 0;
    *(_DWORD *)(v11 + 40) = 0;
    *(_BYTE *)(v11 + 44) = 0;
  }
  while ( v8 != v10 );
  *(_QWORD *)(a1 + 944) = v9;
  v14 = 0;
  v13 = 0;
  result = sub_2996D0(a1, a3, v7, 3, 0, v9, &v14, &v13);
  *(_DWORD *)(a1 + 952) = v14;
  return result;
}

//----- (00000000002B2EF0) ----------------------------------------------------
__int64 __fastcall sub_2B2EF0(__int64 a1, __int64 a2)
{
  __int64 *v4; // x0
  __int64 v5; // x8
  unsigned __int8 v6; // w8
  unsigned __int64 v7; // x11
  __int64 v8; // x20
  unsigned __int64 v9; // x26
  unsigned int *v10; // x27
  unsigned int *v11; // x21
  _QWORD *v12; // x22
  __int64 v13; // x20
  unsigned __int64 i; // x24
  int32x2_t v15; // d0
  float64x2_t v16; // q0
  float64x2_t v17; // q1
  unsigned __int64 v18; // x10
--
          {
            v13 = sub_64EB40(v10, 0);
            v14 = sub_64EB40(v10, 1);
            v15 = sub_64EB40(v10, 2);
            v16 = v13[5];
            v17 = v14[5];
            v18 = v15;
            v19 = v26;
            if ( v16 >= v17 )
              v20 = v14[5];
            else
              v20 = v13[5];
            if ( v16 >= v17 )
              v21 = v16;
            else
              v21 = v14[5];
            while ( v19 != v27 )
            {
              if ( *((_DWORD *)v19 + 8) >= v20 && *((_DWORD *)v19 + 7) <= v21 )
                goto LABEL_28;
              v19 = sub_B3970((__int64)v19);
            }
            v22 = v18[5];
            v24[0] = v20;
            v24[1] = v21;
            *(_DWORD *)sub_AF164(&v26, v24) = v22;
          }
        }
        else if ( v9 == 64 && sub_64EB70((__int64)v8, "default") )
        {
          sub_382DA0();
          if ( v12 )
            v25 = *(_QWORD *)(v11 + 40);
        }
      }
      ++v3;
    }
    sub_11B1C8(v4, (__int64 *)&v26);
    sub_11B1D0(v4, &v25);
  }
  else if ( sub_537F00() )
  {
    v23 = (void (__fastcall ***)(_QWORD, __int64, _QWORD, __int64, const char *, const char *, const char *, __int64, const char *))sub_537F00();
    (**v23)(
      v23,
      32,
      0,
      64,
      "pos",
      "Lane-Next_LanePosEngine",
      "void next_lane_pos::LanePosEngine::parseLinkScoreThreshold(const cJSON *)",
      804,
      "linkscore_threshold data illegal");
  }
LABEL_28:
  sub_B45E0((__int64)&v26);
}
// 37EC28: variable 'v3' is possibly undefined
// 37EC28: variable 'v2' is possibly undefined
// 37EC64: variable 'v12' is possibly undefined
// 37EC68: variable 'v11' is possibly undefined

//----- (000000000037EDB8) ----------------------------------------------------
void sub_37EDB8()
{
  __int64 v0; // x20
  __int64 v1; // x0
  const char *v2; // x1
  __int64 v3; // x19
  char v4; // zf
  int v5; // w0
  _QWORD *v6; // x0
  char v7; // w21
  __int64 v8; // x1
  __int64 v9; // x2
  __int64 v10; // x3
  __int64 v11; // x4
  __int64 v12; // x5
  __int64 v13; // x6
  __int64 v14; // x7
  __int64 v15; // x0
  __int64 v16; // x2
  __int64 v17; // x3
  __int64 v18; // x4
  __int64 v19; // x5
  __int64 v20; // x6
  __int64 v21; // x7
  int v22; // w0
  _QWORD *v23; // x0
  char v24; // w21
  __int64 v25; // x1
  __int64 v26; // x2
  __int64 v27; // x3
  __int64 v28; // x4
  __int64 v29; // x5
  __int64 v30; // x6
  __int64 v31; // x7
  __int64 v32; // x0
  __int64 v33; // x2
  __int64 v34; // x3
  __int64 v35; // x4
  __int64 v36; // x5
  __int64 v37; // x6
  __int64 v38; // x7
  int v39; // w0
  _QWORD *v40; // x0
  char v41; // w20
  __int64 v42; // x1
  __int64 v43; // x2
  __int64 v44; // x3
  __int64 v45; // x4
  __int64 v46; // x5
  __int64 v47; // x6
  __int64 v48; // x7
  __int64 v49; // [xsp+0h] [xbp-60h]
  __int64 v50; // [xsp+0h] [xbp-60h]
  __int64 v51; // [xsp+0h] [xbp-60h]
  __int64 v52; // [xsp+0h] [xbp-60h]
  __int64 v53; // [xsp+0h] [xbp-60h]
  unsigned int v54; // [xsp+8h] [xbp-58h]
  __int64 v55; // [xsp+8h] [xbp-58h]
  __int64 v56; // [xsp+8h] [xbp-58h]
  __int64 v57; // [xsp+8h] [xbp-58h]
  __int64 v58; // [xsp+8h] [xbp-58h]
  int v59; // [xsp+Ch] [xbp-54h]
  __int64 v60; // [xsp+10h] [xbp-50h]
  __int64 v61; // [xsp+10h] [xbp-50h]
  __int64 v62; // [xsp+10h] [xbp-50h]
  __int64 v63; // [xsp+10h] [xbp-50h]
  __int64 v64; // [xsp+18h] [xbp-48h]
  __int64 v65; // [xsp+18h] [xbp-48h]
  __int64 v66; // [xsp+18h] [xbp-48h]
  __int64 v67; // [xsp+18h] [xbp-48h]
  __int64 v68; // [xsp+18h] [xbp-48h]
  unsigned __int64 v69[4]; // [xsp+20h] [xbp-40h] BYREF

  sub_382ED8();
  if ( v2 )
  {
    v3 = v1;
    if ( sub_382CFC(v1, v2) )
    {
      sub_382E80();
      if ( v4 )
      {
        BYTE4(v64) = 1;
        v5 = (unsigned int)sub_AEB54(v69, "is_open");
        v6 = sub_37EACC(v5, v0, (__int64)v69);
        v7 = (char)v6;
        sub_382E24((__int64)v6, v8, v9, v10, v11, v12, v13, v14, v49, v54, 0, v64, v69[0]);
        if ( (v7 & 1) != 0 )
