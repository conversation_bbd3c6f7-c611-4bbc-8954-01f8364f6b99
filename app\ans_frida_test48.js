// 高德地图 nativeAddMapGestureMsg 下游函数执行流程分析脚本
// 适用于 Frida 12.9.7，使用 ES5 语法
// 使用Java层钩子和Stalker跟踪Native层函数

(function() {
    'use strict';

    // 全局配置
    var config = {
        // 关键函数偏移量 (相对于 libamapr.so 基地址)
        functionOffsets: {
            // 主函数 - 通过IDA Pro确认的nativeAddMapGestureMsg
            nativeAddMapGestureMsg: 0x6ee70c
        },
        // 安全配置
        enableStalker: false,      // 是否启用Stalker跟踪（可能导致性能问题）
        stalkerMaxEvents: 1000,    // Stalker最多记录的事件数
        sampleRate: 0.5,           // 只记录50%的函数调用，减少干扰
        maxExceptions: 3,          // 每个函数最多记录的异常数
        disableAllLogging: false   // 紧急情况下可设为true，完全禁用日志
    };

    // 全局变量
    var gModuleMap = {};          // 模块映射
    var gExceptionCount = {};     // 每个函数的异常计数
    var gStalkerEvents = [];      // Stalker事件记录

    // 工具函数 - 日志输出
    function log(message) {
        if (!config.disableAllLogging) {
            console.log("[+] " + message);
        }
    }

    function logError(message) {
        if (!config.disableAllLogging) {
            console.log("[-] " + message);
        }
    }

    // 工具函数 - 格式化地址
    function formatAddress(address) {
        if (!address) return "0x0";
        try {
            return "0x" + address.toString(16);
        } catch (e) {
            return "0x???";
        }
    }

    // 工具函数 - 获取手势类型名称
    function getGestureTypeName(type) {
        var gestureTypes = {
            0: "未知手势",
            1: "单指按下",
            2: "单指移动",
            3: "单指抬起",
            4: "双指按下",
            5: "双指移动",
            6: "双指抬起",
            7: "放大",
            8: "缩小",
            9: "旋转",
            10: "长按",
            11: "双击",
            12: "倾斜"
        };
        return gestureTypes[type] || "未知手势(" + type + ")";
    }

    // 1. 枚举所有已加载模块
    function enumerateLoadedModules() {
        log("开始枚举关键模块...");
        
        try {
            var modules = Process.enumerateModules();
            
            // 存储所有模块信息
            for (var i = 0; i < modules.length; i++) {
                var module = modules[i];
                gModuleMap[module.name] = {
                    base: module.base,
                    size: module.size,
                    path: module.path
                };
                
                // 只输出关键模块信息
                if (module.name === "libamapr.so" || module.name === "libamapnsq.so") {
                    log("加载模块: " + module.name + " @ " + formatAddress(module.base));
                }
            }
            
            log("模块枚举完成，共 " + modules.length + " 个模块");
        } catch (e) {
            logError("枚举模块失败: " + e);
        }
    }

    // 2. 钩住 Java 层的 nativeAddMapGestureMsg 方法
    function hookJavaGestureMethod() {
        log("开始监控 Java 层 nativeAddMapGestureMsg 方法...");
        
        Java.perform(function() {
            try {
                // 尝试两种可能的类名
                var GLMapEngine = null;
                
                try {
                    GLMapEngine = Java.use("com.autonavi.amap.mapcore.map.GLMapEngine");
                    log("找到类: com.autonavi.amap.mapcore.map.GLMapEngine");
                } catch (e) {
                    try {
                        GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                        log("找到类: com.autonavi.jni.ae.gmap.GLMapEngine");
                    } catch (e2) {
                        logError("未找到 GLMapEngine 类");
                        return;
                    }
                }
                
                if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                        // 采样记录，减少日志量
                        var doLog = Math.random() <= config.sampleRate;
                        var startTime = new Date().getTime();
                        
                        if (doLog) {
                            // 记录手势类型和参数
                            log("手势事件开始: 类型=" + getGestureTypeName(type));
                            log("  参数: engineId=" + engineId + 
                                ", nativePtr=0x" + nativePtr.toString(16) + 
                                ", param1=" + param1.toFixed(2) + 
                                ", param2=" + param2.toFixed(2) + 
                                ", param3=" + param3.toFixed(2) + 
                                ", param4=" + param4);
                        }
                        
                        // 如果启用了Stalker，开始跟踪
                        if (config.enableStalker) {
                            startStalker();
                        }
                        
                        // 调用原始方法
                        var result = this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                        
                        // 如果启用了Stalker，停止跟踪
                        if (config.enableStalker) {
                            stopStalker();
                        }
                        
                        if (doLog) {
                            // 计算总处理时间
                            var totalTime = new Date().getTime() - startTime;
                            log("手势处理完成，耗时: " + totalTime + "ms");
                        }
                        
                        return result;
                    };
                    log("成功钩住 Java 层 nativeAddMapGestureMsg 方法");
                } else {
                    logError("未找到 nativeAddMapGestureMsg 方法");
                }
            } catch (e) {
                logError("钩住 Java 层方法失败: " + e);
            }
        });
    }
    
    // 3. 使用Stalker跟踪Native函数执行
    var gStalkerStarted = false;
    var gStalkerThreadId = null;
    
    function startStalker() {
        if (gStalkerStarted) return;
        
        try {
            gStalkerEvents = [];
            gStalkerThreadId = Process.getCurrentThreadId();
            
            log("开始跟踪线程: " + gStalkerThreadId);
            
            Stalker.follow(gStalkerThreadId, {
                events: {
                    call: true,      // 记录函数调用
                    ret: true,       // 记录函数返回
                    exec: false      // 不记录每条指令执行
                },
                
                onReceive: function(events) {
                    // 解析并记录事件
                    var parsed = Stalker.parse(events);
                    
                    // 限制记录的事件数量
                    if (gStalkerEvents.length < config.stalkerMaxEvents) {
                        gStalkerEvents = gStalkerEvents.concat(parsed);
                    }
                }
            });
            
            gStalkerStarted = true;
        } catch (e) {
            logError("启动Stalker跟踪失败: " + e);
        }
    }
    
    function stopStalker() {
        if (!gStalkerStarted) return;
        
        try {
            log("停止跟踪线程: " + gStalkerThreadId);
            
            Stalker.unfollow(gStalkerThreadId);
            Stalker.flush();
            
            // 分析并输出跟踪结果
            analyzeStalkerEvents();
            
            gStalkerStarted = false;
        } catch (e) {
            logError("停止Stalker跟踪失败: " + e);
        }
    }
    
    function analyzeStalkerEvents() {
        log("分析Stalker跟踪结果，共 " + gStalkerEvents.length + " 个事件");
        
        // 查找libamapr.so模块
        var libamapr = gModuleMap["libamapr.so"];
        if (!libamapr) {
            logError("未找到libamapr.so模块信息，无法分析跟踪结果");
            return;
        }
        
        // 计算nativeAddMapGestureMsg地址
        var nativeAddMapGestureMsgAddr = libamapr.base.add(config.functionOffsets.nativeAddMapGestureMsg);
        
        // 分析调用链
        var callStack = [];
        var lastCall = null;
        
        for (var i = 0; i < gStalkerEvents.length; i++) {
            var event = gStalkerEvents[i];
            
            if (event.type === 'call') {
                callStack.push(event);
                lastCall = event;
                
                // 检查是否是我们关注的函数
                if (event.target.equals(nativeAddMapGestureMsgAddr)) {
                    log("检测到调用: nativeAddMapGestureMsg @ " + formatAddress(event.target));
                }
                
                // 检查是否是libamapr.so中的函数
                if (event.target.compare(libamapr.base) >= 0 && 
                    event.target.compare(libamapr.base.add(libamapr.size)) < 0) {
                    // 计算偏移量
                    var offset = event.target.sub(libamapr.base);
                    log("检测到libamapr.so内部调用: " + formatAddress(offset) + " @ " + formatAddress(event.target));
                }
            } else if (event.type === 'ret' && lastCall) {
                if (callStack.length > 0) {
                    callStack.pop();
                }
            }
        }
        
        log("Stalker分析完成");
    }

    // 设置异常处理器
    function setupExceptionHandler() {
        Process.setExceptionHandler(function(exception) {
            // 简单记录异常并继续
            logError("捕获异常: " + exception.type + " @ " + formatAddress(exception.address));
            
            // 返回 true 表示已处理异常，脚本将继续运行
            return true;
        });
    }

    // 主函数
    function main() {
        log("高德地图 nativeAddMapGestureMsg 下游函数执行流程分析脚本");
        log("适用于 Frida 12.9.7，使用 ES5 语法");
        log("Stalker跟踪: " + (config.enableStalker ? "开启" : "关闭"));
        
        // 设置异常处理
        setupExceptionHandler();
        
        try {
            // 1. 枚举所有已加载模块
            enumerateLoadedModules();
            
            // 延迟执行其他操作，确保模块信息已获取
            setTimeout(function() {
                try {
                    // 2. 钩住 Java 层的 nativeAddMapGestureMsg 方法
                    hookJavaGestureMethod();
                    
                    log("脚本设置完成，等待手势事件...");
                } catch (e) {
                    logError("设置钩子失败: " + e);
                }
            }, 1000);
        } catch (e) {
            logError("脚本初始化失败: " + e);
        }
    }
    
    // 启动脚本
    main();
})(); 