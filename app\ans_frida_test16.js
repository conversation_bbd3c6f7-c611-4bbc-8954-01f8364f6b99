setTimeout(function() {
    console.log("[极简方案] 启动");
    
    // 拦截exit
    var exit_ptr = Module.findExportByName(null, "exit");
    if (exit_ptr) {
      Interceptor.replace(exit_ptr, new NativeCallback(function() {
        return 0;
      }, 'void', ['int']));
    }
    
    // 更健壮的Java Hook - 多次尝试
    var hookAttempts = 0;
    var maxAttempts = 5;
    
    function attemptHook() {
      hookAttempts++;
      console.log("[*] 尝试Hook Java层 (" + hookAttempts + "/" + maxAttempts + ")");
      
      Java.perform(function() {
        try {
          // 先检查类是否存在
          Java.enumerateLoadedClasses({
            onMatch: function(name) {
              if (name.includes("autonavi") && name.includes("MapActivity")) {
                console.log("[+] 发现可能的目标类: " + name);
              }
            },
            onComplete: function() {}
          });
          
          // Hook NewMapActivity类
          var NewMapActivity = Java.use("com.autonavi.map.activity.NewMapActivity");
          console.log("[+] 成功加载NewMapActivity类");
          
          // 列出类中的方法
          var methods = NewMapActivity.class.getDeclaredMethods();
          console.log("[+] 类中的方法:");
          for (var i = 0; i < methods.length; i++) {
            var method = methods[i];
            if (method.toString().includes("J()") || method.toString().length <= 3) {
              console.log("    - " + method.toString() + " [可能是目标]");
            }
          }
          
          // 尝试Hook J方法
          if (NewMapActivity.J) {
            NewMapActivity.J.implementation = function() {
              console.log("[Hook成功] NewMapActivity.J()方法被调用");
              var result = this.J();
              console.log("[Hook成功] NewMapActivity.J()方法返回");
              return result;
            };
            console.log("[+] J()方法Hook成功安装");
          } else {
            console.log("[-] 未找到J()方法");
          }
  
          // 增加更多方法监控
          if (NewMapActivity.loadMainMap) {
            NewMapActivity.loadMainMap.implementation = function(z) {
              console.log("[Hook成功] loadMainMap调用");
              return this.loadMainMap(z);
            };
          }
  
          // 监控Activity生命周期方法
          NewMapActivity.onResume.implementation = function() {
            console.log("[Hook成功] onResume被调用");
            return this.onResume();
          };
  
        } catch(e) {
          console.log("[-] Java Hook错误: " + e);
          
          // 如果还有尝试次数，继续
          if (hookAttempts < maxAttempts) {
            setTimeout(attemptHook, 3000);
          }
        }
      });
    }
    
    // 第一次尝试更早
    setTimeout(attemptHook, 500);
    
  }, 1000);
  