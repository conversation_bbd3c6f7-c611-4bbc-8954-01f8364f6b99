// 高德地图JNI钩子脚本 - 修复版
// 专注于稳定性和兼容性

(function() {
    // 全局变量
    var lastGestureTime = 0;
    var foundNativeFunc = false;
    var nativeAddr = null;

    // 初始化
    setTimeout(function() {
        Java.perform(function() {
            console.log("[+] 高德地图JNI钩子脚本 - 修复版已启动");
            
            // 1. 钩住Java层JNI方法
            hookJavaJNI();
            
            // 2. 钩住JNI_OnLoad函数
            setTimeout(function() {
                hookJNIOnLoad();
            }, 1000);
            
            // 3. 使用特征码扫描
            setTimeout(function() {
                scanFunctionSignature();
            }, 2000);
        });
    }, 1000);

    // 钩住Java层JNI方法
    function hookJavaJNI() {
        try {
            var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
            if (GLMapEngine.nativeAddMapGestureMsg) {
                var original = GLMapEngine.nativeAddMapGestureMsg;
                
                GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                    lastGestureTime = new Date().getTime();
                    console.log("\n[JNI] nativeAddMapGestureMsg被调用");
                    console.log("  参数: engineId=" + engineId + 
                               ", nativePtr=0x" + nativePtr.toString(16) + 
                               ", type=" + type + 
                               ", param1=" + param1.toFixed(2) + 
                               ", param2=" + param2.toFixed(2) + 
                               ", param3=" + param3.toFixed(2) + 
                               ", param4=" + param4);
                    
                    // 获取当前线程ID
                    var threadId = Process.getCurrentThreadId();
                    console.log("  线程ID: " + threadId);
                    
                    // 获取Java调用栈
                    var stack = Java.use("android.util.Log")
                        .getStackTraceString(Java.use("java.lang.Exception").$new())
                        .split("\n");
                    console.log("  Java调用栈:\n" + stack.slice(1, 10).join("\n"));
                    
                    // 如果找到了Native函数地址但还没钩住
                    if (nativeAddr && !foundNativeFunc) {
                        tryHookAddress(nativeAddr);
                    }
                    
                    // 调用原始方法
                    var result = original.call(this, engineId, nativePtr, type, param1, param2, param3, param4);
                    console.log("[JNI] nativeAddMapGestureMsg返回");
                    return result;
                };
                console.log("[+] 已钩住Java层JNI方法: nativeAddMapGestureMsg");
            }
        } catch (e) {
            console.log("[-] 钩住Java层JNI方法失败: " + e);
        }
    }
    
    // 钩住JNI_OnLoad函数
    function hookJNIOnLoad() {
        try {
            var libamapr = Process.findModuleByName("libamapr.so");
            if (!libamapr) {
                console.log("[-] 未找到libamapr.so模块");
                return;
            }
            
            console.log("[+] 找到libamapr.so模块: " + libamapr.base);
            
            // 1. 尝试直接找到JNI_OnLoad符号
            var jniOnLoadAddr = Module.findExportByName("libamapr.so", "JNI_OnLoad");
            if (jniOnLoadAddr) {
                console.log("[+] 找到JNI_OnLoad函数: " + jniOnLoadAddr);
                
                // 钩住JNI_OnLoad函数
                Interceptor.attach(jniOnLoadAddr, {
                    onEnter: function(args) {
                        console.log("[+] JNI_OnLoad被调用");
                        console.log("  JNIEnv: " + args[0]);
                        console.log("  JavaVM: " + args[1]);
                        
                        // 保存参数
                        this.env = args[0];
                        
                        // 钩住RegisterNatives函数
                        var envAddr = ptr(args[0]);
                        var envVtable = Memory.readPointer(envAddr);
                        
                        // RegisterNatives在JNIEnv函数表中的偏移量通常是215
                        var registerNativesOffset = 215 * Process.pointerSize;
                        var registerNativesPtr = Memory.readPointer(envVtable.add(registerNativesOffset));
                        
                        console.log("  RegisterNatives函数: " + registerNativesPtr);
                        
                        // 钩住RegisterNatives
                        Interceptor.attach(registerNativesPtr, {
                            onEnter: function(args) {
                                var jclass = args[1];
                                var methods = args[2];
                                var nMethods = args[3].toInt32();
                                
                                console.log("[+] RegisterNatives被调用");
                                console.log("  类: " + jclass);
                                console.log("  方法表: " + methods);
                                console.log("  方法数量: " + nMethods);
                                
                                // 遍历方法表
                                for (var i = 0; i < nMethods; i++) {
                                    var methodsStructSize = 3 * Process.pointerSize;
                                    var methodNamePtr = Memory.readPointer(methods.add(i * methodsStructSize));
                                    var methodSigPtr = Memory.readPointer(methods.add(i * methodsStructSize + Process.pointerSize));
                                    var methodFnPtr = Memory.readPointer(methods.add(i * methodsStructSize + 2 * Process.pointerSize));
                                    
                                    try {
                                        var methodName = Memory.readUtf8String(methodNamePtr);
                                        var methodSig = Memory.readUtf8String(methodSigPtr);
                                        
                                        console.log("  方法 #" + i + ": " + methodName + " " + methodSig + " @ " + methodFnPtr);
                                        
                                        // 如果找到nativeAddMapGestureMsg方法
                                        if (methodName === "nativeAddMapGestureMsg") {
                                            console.log("[!] 找到nativeAddMapGestureMsg函数: " + methodFnPtr);
                                            nativeAddr = methodFnPtr;
                                            
                                            // 尝试钩住这个函数
                                            if (!foundNativeFunc) {
                                                tryHookAddress(methodFnPtr);
                                            }
                                        }
                                    } catch (e) {
                                        console.log("  读取方法 #" + i + " 信息失败: " + e);
                                    }
                                }
                            }
                        });
                    },
                    onLeave: function(retval) {
                        console.log("[+] JNI_OnLoad返回: " + retval);
                    }
                });
            } else {
                console.log("[-] 未找到JNI_OnLoad函数");
            }
            
            // 2. 尝试钩住已知的JNI注册函数
            var jniRegisterOffset = 0x6e56bc; // 基于IDA Pro分析
            var jniRegisterAddr = libamapr.base.add(jniRegisterOffset);
            
            try {
                Interceptor.attach(jniRegisterAddr, {
                    onEnter: function(args) {
                        console.log("[+] JNI注册函数被调用");
                        console.log("  参数1: " + args[0]);
                        console.log("  参数2: " + args[1]);
                        
                        // 尝试读取参数2指向的数据（可能是JNINativeMethod结构）
                        try {
                            var methodsPtr = args[1];
                            var methodNamePtr = Memory.readPointer(methodsPtr);
                            var methodSigPtr = Memory.readPointer(methodsPtr.add(Process.pointerSize));
                            var methodFnPtr = Memory.readPointer(methodsPtr.add(2 * Process.pointerSize));
                            
                            try {
                                var methodName = Memory.readUtf8String(methodNamePtr);
                                var methodSig = Memory.readUtf8String(methodSigPtr);
                                
                                console.log("  方法名: " + methodName);
                                console.log("  方法签名: " + methodSig);
                                console.log("  函数指针: " + methodFnPtr);
                                
                                if (methodName === "nativeAddMapGestureMsg") {
                                    console.log("[!] 找到nativeAddMapGestureMsg函数: " + methodFnPtr);
                                    nativeAddr = methodFnPtr;
                                    
                                    // 尝试钩住这个函数
                                    if (!foundNativeFunc) {
                                        tryHookAddress(methodFnPtr);
                                    }
                                }
                            } catch (e) {
                                console.log("  读取方法名或签名失败: " + e);
                            }
                        } catch (e) {
                            console.log("  读取方法结构失败: " + e);
                        }
                    }
                });
                console.log("[+] 成功钩住JNI注册函数");
            } catch (e) {
                console.log("[-] 钩住JNI注册函数失败: " + e);
            }
        } catch (e) {
            console.log("[-] 钩住JNI_OnLoad函数失败: " + e);
        }
    }
    
    // 使用特征码扫描查找函数
    function scanFunctionSignature() {
        try {
            var libamapr = Process.findModuleByName("libamapr.so");
            if (!libamapr) {
                console.log("[-] 未找到libamapr.so模块");
                return;
            }
            
            // 1. 尝试直接扫描字符串
            var pattern = "6e 61 74 69 76 65 41 64 64 4d 61 70 47 65 73 74 75 72 65 4d 73 67 00"; // "nativeAddMapGestureMsg\0"
            
            console.log("[*] 扫描字符串: nativeAddMapGestureMsg");
            Memory.scan(libamapr.base, libamapr.size, pattern, {
                onMatch: function(address, size) {
                    console.log("[+] 找到字符串: " + address);
                    
                    // 搜索引用这个字符串的地址
                    searchStringReference(libamapr, address);
                    
                    return 'stop';
                },
                onError: function(reason) {
                    console.log("[-] 字符串扫描错误: " + reason);
                },
                onComplete: function() {
                    console.log("[*] 字符串扫描完成");
                }
            });
            
            // 2. 尝试直接钩住已知偏移量的函数
            var knownOffset = 0x6ee70c;
            var targetAddr = libamapr.base.add(knownOffset);
            
            console.log("[*] 尝试直接钩住已知偏移量函数: 0x" + knownOffset.toString(16));
            tryHookAddress(targetAddr);
            
            // 3. 尝试钩住附近的地址
            var offsets = [
                knownOffset - 16,
                knownOffset - 8,
                knownOffset - 4,
                knownOffset + 4,
                knownOffset + 8,
                knownOffset + 16
            ];
            
            for (var i = 0; i < offsets.length; i++) {
                var offset = offsets[i];
                var addr = libamapr.base.add(offset);
                
                console.log("[*] 尝试钩住偏移量: 0x" + offset.toString(16));
                tryHookAddress(addr);
            }
        } catch (e) {
            console.log("[-] 扫描函数特征码失败: " + e);
        }
    }
    
    // 搜索字符串引用
    function searchStringReference(module, stringAddr) {
        try {
            console.log("[*] 搜索对字符串的引用...");
            
            // 读取字符串
            var str = Memory.readUtf8String(stringAddr);
            console.log("  字符串内容: " + str);
            
            // 在.data段搜索引用
            var dataSegmentOffset = 0x1BCAEA0; // 基于IDA Pro分析
            var dataSegmentAddr = module.base.add(dataSegmentOffset);
            
            console.log("  检查.data段引用: " + dataSegmentAddr);
            
            try {
                // 读取JNI注册表结构
                var methodNamePtr = Memory.readPointer(dataSegmentAddr);
                var methodSigPtr = Memory.readPointer(dataSegmentAddr.add(Process.pointerSize));
                var methodFnPtr = Memory.readPointer(dataSegmentAddr.add(2 * Process.pointerSize));
                
                console.log("  方法名指针: " + methodNamePtr);
                console.log("  方法签名指针: " + methodSigPtr);
                console.log("  函数指针: " + methodFnPtr);
                
                try {
                    var methodName = Memory.readUtf8String(methodNamePtr);
                    var methodSig = Memory.readUtf8String(methodSigPtr);
                    
                    console.log("  方法名: " + methodName);
                    console.log("  方法签名: " + methodSig);
                    
                    if (methodName === "nativeAddMapGestureMsg") {
                        console.log("[!] 找到nativeAddMapGestureMsg函数: " + methodFnPtr);
                        nativeAddr = methodFnPtr;
                        
                        // 尝试钩住这个函数
                        if (!foundNativeFunc) {
                            tryHookAddress(methodFnPtr);
                        }
                    }
                } catch (e) {
                    console.log("  读取方法名或签名失败: " + e);
                }
            } catch (e) {
                console.log("  读取JNI注册表结构失败: " + e);
            }
        } catch (e) {
            console.log("[-] 搜索字符串引用失败: " + e);
        }
    }
    
    // 尝试钩住指定地址
    function tryHookAddress(address) {
        try {
            console.log("[*] 尝试钩住地址: " + address);
            
            // 1. 使用Interceptor.attach
            try {
                Interceptor.attach(address, {
                    onEnter: function(args) {
                        console.log("\n[Native] nativeAddMapGestureMsg被调用 (地址: " + address + ")");
                        
                        // 尝试读取参数
                        try {
                            console.log("  JNIEnv*: " + args[0]);
                            console.log("  jclass: " + args[1]);
                            console.log("  engineId: " + args[2].toInt32());
                            console.log("  nativePtr: " + args[3]);
                            console.log("  type: " + args[4].toInt32());
                            console.log("  param1: " + args[5].readFloat().toFixed(2));
                            console.log("  param2: " + args[6].readFloat().toFixed(2));
                            console.log("  param3: " + args[7].readFloat().toFixed(2));
                            console.log("  param4: " + args[8].toInt32());
                        } catch (e) {
                            console.log("  读取参数失败: " + e);
                        }
                        
                        // 打印调用栈
                        try {
                            console.log("  调用栈:\n" + Thread.backtrace(this.context, Backtracer.ACCURATE)
                                .map(DebugSymbol.fromAddress).join("\n"));
                        } catch (e) {
                            console.log("  获取调用栈失败: " + e);
                        }
                        
                        this.startTime = new Date().getTime();
                    },
                    onLeave: function(retval) {
                        console.log("[Native] nativeAddMapGestureMsg返回");
                        console.log("  执行时间: " + (new Date().getTime() - this.startTime) + "ms");
                    }
                });
                console.log("[+] 成功钩住地址: " + address);
                foundNativeFunc = true;
                return true;
            } catch (e) {
                console.log("[-] 钩住地址失败: " + e);
                return false;
            }
        } catch (e) {
            console.log("[-] 尝试钩住地址时出错: " + e);
            return false;
        }
    }
})();
