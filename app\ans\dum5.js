// 高德地图GPS模拟器积分破解脚本 v8
// 功能：最简实现，直接返回有效值
Java.perform(function() {
    console.log("\n[+] 积分破解脚本已启动...");

    try {
        var GpsUserModel = Java.use("tgo.ngo.mockgps.model.app.GpsUserModel");
        console.log("[+] 已找到用户模型类: GpsUserModel");
        
        // Integer类型
        var Integer = Java.use('java.lang.Integer');
        
        // 1. 简单直接地修改积分值
        GpsUserModel.getCount.implementation = function() {
            console.log("[+] 返回积分: 999");
            return Integer.valueOf(999);
        };
        
        // 2. 修改积分可用状态
        if (GpsUserModel.getCanUse) {
            GpsUserModel.getCanUse.implementation = function() {
                console.log("[+] 返回可用状态: 1 (可用)");
                return Integer.valueOf(1);
            };
        }
        
        // 3. 清空积分不足提示
        GpsUserModel.getNotCanUseMag.implementation = function() {
            console.log("[+] 清空积分不足提示");
            return "";
        };
        
        console.log("[+] 积分破解完成，请尝试启动应用");

    } catch(e) {
        console.log("[!] 脚本执行出错: " + e);
    }
    
    // 额外尝试修复闪退问题 - 主动捕获可能的异常
    try {
        // 全局异常处理器
        Java.use('java.lang.Thread').setDefaultUncaughtExceptionHandler({
            uncaughtException: function(thread, error) {
                console.log("[!] 捕获到应用异常: " + error);
                // 允许应用继续运行，不崩溃
            }
        });
    } catch(e) {
        console.log("[!] 设置异常处理器失败: " + e);
    }
});
