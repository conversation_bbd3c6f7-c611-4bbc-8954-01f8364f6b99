#!/usr/bin/env python3
"""
数据解析流程演示器
直接展示：解压后数据是什么，结构化数据是什么
"""

import os
import zlib

def demonstrate_data_flow():
    """演示真正的数据流程"""
    print("🎯 高德地图数据解析流程演示")
    print("=" * 60)
    
    # 检查ans文件
    ans_files = ['file/m1.ans', 'file/m3.ans']
    target_file = None
    
    for f in ans_files:
        if os.path.exists(f):
            target_file = f
            break
    
    if not target_file:
        print("❌ 未找到.ans文件")
        demonstrate_theoretical_flow()
        return
    
    print(f"📁 分析文件: {target_file}")
    
    with open(target_file, 'rb') as f:
        raw_data = f.read()
    
    print(f"原始文件大小: {len(raw_data)} 字节")
    
    # 查找并解压zlib数据
    decompress_and_analyze(raw_data)
    
def decompress_and_analyze(raw_data):
    """解压并分析数据"""
    print("\n🗜️ 【解压阶段】模拟 libz.so:uncompress()")
    print("-" * 50)
    
    # 查找zlib标识
    zlib_pos = raw_data.find(b'\x78\x9c')
    if zlib_pos == -1:
        print("❌ 未找到zlib压缩数据")
        return
    
    print(f"✅ 找到zlib数据，位置: {zlib_pos}")
    
    # 尝试解压
    try:
        compressed_chunk = raw_data[zlib_pos:zlib_pos + 8192]
        decompressed = zlib.decompress(compressed_chunk)
        
        print(f"压缩前: {len(compressed_chunk)} 字节")
        print(f"解压后: {len(decompressed)} 字节")
        
        # 分析解压后的数据
        analyze_decompressed_data(decompressed)
        
        # 模拟结构化
        simulate_structuring(decompressed)
        
    except Exception as e:
        print(f"❌ 解压失败: {e}")

def analyze_decompressed_data(data):
    """分析解压后的数据 - 这就是解压解析的数据"""
    print("\n📊 【解压后数据分析】")
    print("💡 这就是你问的'解压解析的数据是什么'")
    print("-" * 50)
    
    # 检查数据头部
    header = data[:16]
    header_text = ''.join(chr(b) if 32 <= b < 127 else '.' for b in header)
    print(f"数据头部: {header_text}")
    print(f"数据大小: {len(data)} 字节")
    
    # 判断数据类型
    if data.startswith(b'DICE-AM'):
        print("✅ DICE-AM矢量地图数据")
        print("内容: 经纬度坐标、道路轮廓、建筑边界")
        print("格式: 魔数 + 版本 + 点数 + 坐标数组")
        
    elif b'{' in data[:50]:
        print("✅ JSON配置数据")
        print("内容: 地图样式、颜色设置、图标配置")
        print("格式: 标准JSON字符串")
        
    else:
        print("✅ 其他格式数据")
        print("可能包含: 中文地名、纹理数据等")
    
    print("\n💡 关键点:")
    print("• 这是原始地理信息，还不能直接用于渲染")
    print("• 需要APP进一步处理才能给GPU使用")

def simulate_structuring(raw_data):
    """模拟结构化过程"""
    print("\n🏗️ 【结构化数据处理】")
    print("💡 这就是你问的'结构化数据是什么'")
    print("-" * 50)
    
    print("模拟APP处理逻辑 (sub_5C394 + sub_10F88):")
    
    if raw_data.startswith(b'DICE-AM'):
        print("🎯 矢量数据结构化:")
        print("  输入: 经纬度坐标 (116.123456, 39.654321)")
        print("  处理: 坐标系转换")
        print("  输出: 屏幕坐标 (340.5, 180.2) + 颜色 + 类型")
        print("  格式: GPU顶点缓冲区")
        
    elif b'{' in raw_data[:50]:
        print("🎯 配置数据结构化:")
        print("  输入: JSON {'road_color': '#333333'}")
        print("  处理: 解析并转换")
        print("  输出: GPU参数 (0.2, 0.2, 0.2, 1.0)")
        print("  格式: OpenGL uniform变量")
        
    print("\n💡 关键点:")
    print("• 结构化数据是GPU可直接使用的格式")
    print("• 包含屏幕坐标、颜色值、渲染参数等")
    print("• 经过APP逻辑处理，不是原始数据")
    
    simulate_gpu_rendering()

def simulate_gpu_rendering():
    """模拟GPU渲染"""
    print("\n🎮 【GPU渲染阶段】")
    print("-" * 50)
    
    print("结构化数据 → GPU指令:")
    print("• 顶点数据 → glDrawArrays(GL_LINES, ...)")
    print("• 文字数据 → glDrawTexture(...)")  
    print("• 样式参数 → glUniform4f(...)")
    
    print("\nGPU处理:")
    print("• 顶点着色器: 处理坐标变换")
    print("• 片段着色器: 处理颜色和纹理")
    print("• 最终输出: 屏幕上的地图画面")

def demonstrate_theoretical_flow():
    """演示理论流程"""
    print("\n📚 理论流程演示")
    print("=" * 60)
    
    print("🔄 完整数据转换流程:")
    print("1. 📁 .ans文件 (压缩)")
    print("   ↓ APP读取")
    print("2. 🗜️ 解压后数据 (原始地理信息)")
    print("   • DICE-AM: 经纬度坐标")
    print("   • JSON: 样式配置") 
    print("   • 文本: 中文地名")
    print("   ↓ APP处理")
    print("3. 🏗️ 结构化数据 (GPU就绪)")
    print("   • 顶点缓冲区: 屏幕坐标+颜色")
    print("   • 渲染参数: 线宽+颜色+透明度")
    print("   • 文字纹理: 字体+位置+大小")
    print("   ↓ GPU渲染")
    print("4. 🖼️ 屏幕地图画面")

if __name__ == "__main__":
    demonstrate_data_flow() 