#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高德地图.ans文件数据提取器 - 基于Frida验证结果
转换为OpenStreetMap格式
"""

import struct
import zlib
import json
import xml.etree.ElementTree as ET
from typing import Dict, List, Tuple, Optional
import os
from dataclasses import dataclass
from enum import Enum

print("[重大发现] 高德地图数据主要存储在.ans文件中，而非APK内")
print("[分析目标] .ans文件: 基于Frida验证的真实路径")
print("开始提取高德地图.ans文件数据...")

class DataType(Enum):
    """数据类型枚举"""
    DICE_AM = "DICE-AM"
    CONFIG = "CONFIG" 
    TEXT = "TEXT"
    ZLIB = "ZLIB"
    AM_ZLIB = "AM-ZLIB"
    UNKNOWN = "UNKNOWN"

class AnsFileType(Enum):
    """ANS文件类型枚举"""
    NAVI_MAIN = "NAVI_MAIN"      # 主要导航数据
    POI_AOI = "POI_AOI"          # 兴趣点和区域数据
    RENDER_CONFIG = "RENDER_CONFIG"  # 渲染配置
    CLOUD_RESOURCE = "CLOUD_RESOURCE"  # 云端资源
    SMART_MAP = "SMART_MAP"      # 智能地图
    SD_MAP = "SD_MAP"            # SD地图
    FONT_DATA = "FONT_DATA"      # 字体数据
    OTHER_ANS = "OTHER_ANS"      # 其他ANS文件

@dataclass
class DiceAMData:
    """DICE-AM矢量数据结构"""
    magic: bytes
    version: int
    flags: int
    geometry_type: int
    geometry_flags: int
    data_size: int
    raw_data: bytes

@dataclass
class TextData:
    """TEXT文本数据结构"""
    magic: bytes
    text_type: int
    text_flags: int
    data_size: int
    raw_data: bytes

@dataclass
class ZlibData:
    """ZLIB压缩数据结构"""
    header: bytes
    compressed_size: int
    decompressed_data: Optional[bytes]

@dataclass
class AMZlibData:
    """AM-zlib压缩数据结构"""
    magic: bytes
    version: int
    flags: int
    geometry_type: int
    geometry_flags: int
    compressed_size: int
    decompressed_data: Optional[bytes]

def identify_data_type(data: bytes) -> DataType:
    """识别数据类型 (基于Frida分析结果)"""
    if len(data) >= 8:
        # AM-zlib格式: 41 4d 2d 7a 6c 69 62 00 ("AM-zlib\0")
        if data[:8] == b'\x41\x4d\x2d\x7a\x6c\x69\x62\x00':
            return DataType.AM_ZLIB

        # DICE-AM格式: 44 49 43 45 2d 41 4d 00
        if data[:8] == b'\x44\x49\x43\x45\x2d\x41\x4d\x00':
            return DataType.DICE_AM

        # TEXT格式: 0d 00 00 00
        if data[:4] == b'\x0d\x00\x00\x00':
            return DataType.TEXT

        # CONFIG格式: bc bc bc bc
        if data[:4] == b'\xbc\xbc\xbc\xbc':
            return DataType.CONFIG

        # 标准zlib格式: 78 9c
        if data[:2] == b'\x78\x9c':
            return DataType.ZLIB

    return DataType.UNKNOWN

def identify_ans_file_type(filepath: str) -> AnsFileType:
    """根据文件路径识别.ans文件类型"""
    # 使用反斜杠和正斜杠兼容的路径匹配
    filepath_normalized = filepath.replace('\\', '/')

    if '/navi/compile_v3/' in filepath_normalized or '\\navi\\compile_v3\\' in filepath:
        return AnsFileType.NAVI_MAIN
    elif 'PosAoi.ans' in filepath:
        return AnsFileType.POI_AOI
    elif '/cache_sql_config/' in filepath_normalized or '\\cache_sql_config\\' in filepath:
        return AnsFileType.RENDER_CONFIG
    elif '/cache_sql_smart/' in filepath_normalized or '\\cache_sql_smart\\' in filepath:
        return AnsFileType.SMART_MAP
    elif '/cache_sql_font/' in filepath_normalized or '\\cache_sql_font\\' in filepath:
        return AnsFileType.FONT_DATA
    elif '/dl_sql_cloudres_v1/' in filepath_normalized or '\\dl_sql_cloudres_v1\\' in filepath:
        return AnsFileType.CLOUD_RESOURCE
    elif '/dl_sql_sdmap_v1/' in filepath_normalized or '\\dl_sql_sdmap_v1\\' in filepath:
        return AnsFileType.SD_MAP
    elif '/cache_sql_poi/' in filepath_normalized or '\\cache_sql_poi\\' in filepath:
        return AnsFileType.POI_AOI
    else:
        return AnsFileType.OTHER_ANS

def parse_dice_am_data(data: bytes) -> Optional[DiceAMData]:
    """解析DICE-AM数据"""
    try:
        if len(data) < 16:
            return None
        
        # 解析头部
        magic = data[:8]  # DICE-AM\0
        version = data[8]  # 版本号 (170 = 0xAA)
        flags = data[9]    # 标志
        geometry_type = data[10]  # 几何类型
        geometry_flags = data[11] # 几何标志
        
        return DiceAMData(
            magic=magic,
            version=version,
            flags=flags,
            geometry_type=geometry_type,
            geometry_flags=geometry_flags,
            data_size=len(data),
            raw_data=data
        )
    except Exception as e:
        print(f"[错误] 解析DICE-AM数据失败: {e}")
        return None

def parse_text_data(data: bytes) -> Optional[TextData]:
    """解析TEXT数据"""
    try:
        if len(data) < 8:
            return None
        
        # 解析头部
        magic = data[:4]      # 0d 00 00 00
        text_type = data[4]   # 文本类型
        text_flags = data[5]  # 文本标志
        
        return TextData(
            magic=magic,
            text_type=text_type,
            text_flags=text_flags,
            data_size=len(data),
            raw_data=data
        )
    except Exception as e:
        print(f"[错误] 解析TEXT数据失败: {e}")
        return None

def parse_zlib_data(data: bytes) -> Optional[ZlibData]:
    """解析ZLIB压缩数据"""
    try:
        # 尝试解压
        decompressed = zlib.decompress(data)

        return ZlibData(
            header=data[:2],
            compressed_size=len(data),
            decompressed_data=decompressed
        )
    except Exception as e:
        print(f"[错误] 解压ZLIB数据失败: {e}")
        return ZlibData(
            header=data[:2],
            compressed_size=len(data),
            decompressed_data=None
        )

def parse_am_zlib_data(data: bytes) -> Optional[AMZlibData]:
    """解析AM-zlib压缩数据"""
    try:
        if len(data) < 16:
            return None

        # 解析头部
        magic = data[:8]      # AM-zlib\0
        version = data[8]     # 版本号
        flags = data[9]       # 标志
        geometry_type = data[10]  # 几何类型
        geometry_flags = data[11] # 几何标志

        # 尝试解压数据 (跳过头部)
        compressed_data = data[16:]  # 跳过16字节头部
        decompressed = None

        try:
            # 尝试标准zlib解压
            decompressed = zlib.decompress(compressed_data)
        except:
            try:
                # 尝试其他解压方法
                import gzip
                decompressed = gzip.decompress(compressed_data)
            except:
                print(f"[警告] AM-zlib数据解压失败，可能需要特殊解压算法")

        return AMZlibData(
            magic=magic,
            version=version,
            flags=flags,
            geometry_type=geometry_type,
            geometry_flags=geometry_flags,
            compressed_size=len(data),
            decompressed_data=decompressed
        )
    except Exception as e:
        print(f"[错误] 解析AM-zlib数据失败: {e}")
        return None

def parse_ans_file_data(file_handle, filepath: str, file_type: AnsFileType, data_type: DataType):
    """解析.ans文件数据"""
    try:
        # 重置文件指针
        file_handle.seek(0)
        
        # 读取整个文件
        file_data = file_handle.read()
        
        parsed_data = {
            "filepath": filepath,
            "file_type": file_type.value,
            "data_type": data_type.value,
            "file_size": len(file_data),
            "data_blocks": []
        }
        
        # 根据数据类型解析
        if data_type == DataType.DICE_AM:
            dice_data = parse_dice_am_data(file_data)
            if dice_data:
                parsed_data["data_blocks"].append({
                    "type": "DICE-AM",
                    "version": dice_data.version,
                    "geometry_type": dice_data.geometry_type,
                    "geometry_flags": dice_data.geometry_flags,
                    "size": dice_data.data_size
                })

        elif data_type == DataType.AM_ZLIB:
            am_zlib_data = parse_am_zlib_data(file_data)
            if am_zlib_data:
                parsed_data["data_blocks"].append({
                    "type": "AM-ZLIB",
                    "version": am_zlib_data.version,
                    "geometry_type": am_zlib_data.geometry_type,
                    "geometry_flags": am_zlib_data.geometry_flags,
                    "compressed_size": am_zlib_data.compressed_size,
                    "decompressed_size": len(am_zlib_data.decompressed_data) if am_zlib_data.decompressed_data else 0,
                    "decompressed": am_zlib_data.decompressed_data is not None
                })

                # 如果解压成功，分析解压后的数据
                if am_zlib_data.decompressed_data:
                    decompressed_type = identify_data_type(am_zlib_data.decompressed_data)
                    if decompressed_type != DataType.UNKNOWN:
                        parsed_data["data_blocks"].append({
                            "type": f"DECOMPRESSED_{decompressed_type.value}",
                            "size": len(am_zlib_data.decompressed_data)
                        })

        elif data_type == DataType.TEXT:
            text_data = parse_text_data(file_data)
            if text_data:
                parsed_data["data_blocks"].append({
                    "type": "TEXT",
                    "text_type": text_data.text_type,
                    "text_flags": text_data.text_flags,
                    "size": text_data.data_size
                })

        elif data_type == DataType.ZLIB:
            zlib_data = parse_zlib_data(file_data)
            if zlib_data:
                parsed_data["data_blocks"].append({
                    "type": "ZLIB",
                    "compressed_size": zlib_data.compressed_size,
                    "decompressed_size": len(zlib_data.decompressed_data) if zlib_data.decompressed_data else 0,
                    "decompressed": zlib_data.decompressed_data is not None
                })

                # 如果解压成功，分析解压后的数据
                if zlib_data.decompressed_data:
                    decompressed_type = identify_data_type(zlib_data.decompressed_data)
                    if decompressed_type != DataType.UNKNOWN:
                        parsed_data["data_blocks"].append({
                            "type": f"DECOMPRESSED_{decompressed_type.value}",
                            "size": len(zlib_data.decompressed_data)
                        })
        
        return parsed_data
        
    except Exception as e:
        print(f"[错误] 解析.ans文件数据失败: {e}")
        return None

def extract_gaode_ans_data():
    """提取高德地图.ans文件数据"""
    
    # 基于Frida验证的真实.ans文件路径
    ans_file_paths = [
        # 核心导航数据
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\autonavi\\data\\navi\\compile_v3\\chn\\a0\\m1.ans",
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\autonavi\\data\\navi\\compile_v3\\chn\\a3\\m1.ans",
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\autonavi\\data\\navi\\compile_v3\\chn\\a3\\m2.ans",
        
        # POI和AOI数据
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\autonavi\\data\\PosAoi.ans",
        
        # 渲染配置数据
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\render\\cache_sql_config\\geo_fence_global_v2.ans",
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\render\\cache_sql_config\\monitor_data.ans",
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\render\\cache_sql_config\\bmdversion.ans",
        
        # 云端资源数据
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\render\\dl_sql_cloudres_v1\\f_standard\\bundle_list.ans",
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\render\\dl_sql_cloudres_v1\\f_standard\\main_bundle_15.19.3.1.ans",
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\render\\dl_sql_cloudres_v1\\f_standard\\static_bundle_15.18.0.117.ans",
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\render\\dl_sql_cloudres_v1\\f_standard\\earth_bundle_15.12.0.49.ans",
        
        # 智能地图数据
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\render\\cache_sql_smart\\smart_clickv2.ans",
        
        # SD地图数据
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\render\\dl_sql_sdmap_v1\\gb_v6.ans",
        
        # POI布局数据
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\render\\cache_sql_poi\\poilayoutpers_20250521zh-Hans9E368DC55A9DC242E5DDFBCAA709D3C53.ans",
        
        # 字体数据
        "F:\\baidu\\decode\\gaodeApp\\app\\file\\render\\cache_sql_font\\normal_am_i18n_glyph_buffer_33652181813.ans"
    ]
    
    print(f"[开始提取] 分析 {len(ans_file_paths)} 个.ans文件")
    
    extracted_data = {
        "NAVI_MAIN": [],      # 主要导航数据
        "POI_AOI": [],        # 兴趣点和区域数据
        "RENDER_CONFIG": [],  # 渲染配置
        "CLOUD_RESOURCE": [], # 云端资源
        "SMART_MAP": [],      # 智能地图
        "SD_MAP": [],         # SD地图
        "FONT_DATA": []       # 字体数据
    }
    
    for ans_path in ans_file_paths:
        try:
            # 检查文件是否存在
            if not os.path.exists(ans_path):
                print(f"[跳过] 文件不存在: {ans_path}")
                continue
            
            print(f"\n[分析文件] {ans_path}")
            file_size = os.path.getsize(ans_path)
            print(f"  大小: {file_size} 字节")
            
            # 确定文件类型
            file_type = identify_ans_file_type(ans_path)
            print(f"  类型: {file_type.value}")
            
            # 读取文件头部进行格式识别
            with open(ans_path, 'rb') as f:
                header = f.read(64)
                if header:
                    hex_header = ' '.join(f'{b:02x}' for b in header[:16])
                    print(f"  头部: {hex_header}")
                    
                    # 检查数据格式
                    data_type = identify_data_type(header)
                    print(f"  格式: {data_type.value}")
                    
                    if data_type != DataType.UNKNOWN:
                        # 解析地图数据
                        parsed_data = parse_ans_file_data(f, ans_path, file_type, data_type)
                        if parsed_data:
                            extracted_data[file_type.value].append(parsed_data)
                            print(f"  [成功] 提取了 {len(parsed_data.get('data_blocks', []))} 个数据块")
        
        except Exception as e:
            print(f"  [错误] 分析文件失败: {e}")
    
    return extracted_data

if __name__ == "__main__":
    try:
        # 提取.ans文件数据
        result = extract_gaode_ans_data()
        
        # 输出统计信息
        print("\n" + "="*50)
        print("提取结果统计:")
        for file_type, data_list in result.items():
            print(f"  {file_type}: {len(data_list)} 个文件")
        
        # 保存结果到JSON文件
        output_file = "gaode_ans_extracted_data.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n[完成] 提取结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"数据提取失败: {e}")
