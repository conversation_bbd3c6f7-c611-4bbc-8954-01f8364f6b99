#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于IDA Pro反汇编代码的.ans文件解析器
严格按照sub_5C394和sub_10F88等函数的实际逻辑
"""

import os
import zlib
import struct
import ctypes

class IDABasedParser:
    """严格基于IDA Pro反汇编代码的解析器"""
    
    def __init__(self, filename):
        self.filename = filename
        self.file_size = os.path.getsize(filename)
        self.file_data = None
        
    def parse_ans_file(self):
        """解析.ans文件 - 严格按照IDA Pro分析的代码逻辑"""
        print(f"🎯 基于IDA Pro代码逻辑解析: {self.filename}")
        print("=" * 80)
        
        # 读取整个文件到内存
        with open(self.filename, 'rb') as f:
            self.file_data = f.read()
        
        # 1. 验证AM-zlib文件头 (按照真实APP逻辑)
        if not self.validate_am_zlib_header():
            print("❌ AM-zlib头部验证失败")
            return False
        
        # 2. 解析数据块索引表 (按照真实APP的索引读取逻辑)
        block_index = self.parse_block_index()
        if not block_index:
            print("❌ 数据块索引解析失败")
            return False
        
        # 3. 按照sub_5C394的调度逻辑处理每个数据块
        self.process_blocks_via_sub5c394_logic(block_index)
        
        return True
    
    def validate_am_zlib_header(self):
        """验证AM-zlib文件头 - 按照真实APP的头部检查"""
        print("\n📋 AM-zlib头部验证 (基于真实APP逻辑)")
        print("-" * 50)
        
        if len(self.file_data) < 64:
            print("❌ 文件太小，无法包含完整头部")
            return False
        
        # 检查魔数
        magic = self.file_data[0:8]
        if magic != b'AM-zlib\x00':
            print(f"❌ 魔数不匹配: {magic}")
            return False
        
        print("✅ AM-zlib魔数验证通过")
        
        # 解析头部字段 (按照真实APP结构)
        try:
            version_info = struct.unpack('<Q', self.file_data[8:16])[0]
            main_data_size = struct.unpack('<I', self.file_data[16:20])[0]
            reserved1 = struct.unpack('<I', self.file_data[20:24])[0]
            reserved2 = struct.unpack('<Q', self.file_data[24:32])[0]
            
            print(f"📦 版本信息: 0x{version_info:016X}")
            print(f"📦 主数据大小: {main_data_size} 字节")
            print(f"📦 保留字段1: 0x{reserved1:08X}")
            print(f"📦 保留字段2: 0x{reserved2:016X}")
            
            # 验证数据大小一致性 (真实APP会检查这个)
            if main_data_size > self.file_size:
                print(f"⚠️  数据大小异常: {main_data_size} > {self.file_size}")
                return False
            
            return True
            
        except struct.error as e:
            print(f"❌ 头部解析错误: {e}")
            return False
    
    def parse_block_index(self):
        """解析数据块索引表 - 按照真实APP的索引读取逻辑"""
        print("\n📋 数据块索引解析 (基于真实APP逻辑)")
        print("-" * 50)
        
        # 真实APP在64字节后开始读取索引
        index_start = 64
        
        if len(self.file_data) < index_start + 16:
            print("❌ 索引数据不足")
            return None
        
        try:
            # 解析索引头部 (按照真实APP格式)
            index_header = self.file_data[index_start:index_start+16]
            
            index_count = struct.unpack('<I', index_header[0:4])[0]
            index_table_size = struct.unpack('<I', index_header[4:8])[0]
            index_flags = struct.unpack('<I', index_header[8:12])[0]
            index_checksum = struct.unpack('<I', index_header[12:16])[0]
            
            print(f"📁 索引项数量: {index_count}")
            print(f"📁 索引表大小: {index_table_size}")
            print(f"📁 索引标志: 0x{index_flags:08X}")
            print(f"📁 索引校验和: 0x{index_checksum:08X}")
            
            # 验证索引项数量的合理性 (真实APP的安全检查)
            if index_count == 0 or index_count > 50000:
                print(f"⚠️  索引项数量异常，使用备用扫描策略")
                return self.fallback_block_scan()
            
            # 读取索引表项 (每项20字节，按照真实APP格式)
            index_entries = []
            entry_size = 20  # 真实APP的索引项大小
            table_start = index_start + 16
            
            for i in range(min(index_count, 10000)):  # 安全限制
                entry_offset = table_start + i * entry_size
                
                if entry_offset + entry_size > len(self.file_data):
                    break
                
                entry_data = self.file_data[entry_offset:entry_offset+entry_size]
                
                # 解析索引项字段 (按照真实APP结构)
                data_offset = struct.unpack('<I', entry_data[0:4])[0]
                data_size = struct.unpack('<I', entry_data[4:8])[0]
                data_type = struct.unpack('<I', entry_data[8:12])[0]
                data_flags = struct.unpack('<I', entry_data[12:16])[0]
                data_checksum = struct.unpack('<I', entry_data[16:20])[0]
                
                # 验证数据块的有效性 (真实APP的边界检查)
                if (data_offset + data_size <= self.file_size and 
                    data_size > 0 and data_offset >= table_start + index_count * entry_size):
                    
                    index_entries.append({
                        'index': i,
                        'offset': data_offset,
                        'size': data_size,
                        'type': data_type,
                        'flags': data_flags,
                        'checksum': data_checksum
                    })
                    
                    print(f"📋 索引项 #{i}: 偏移=0x{data_offset:08X}, "
                          f"大小={data_size}, 类型=0x{data_type:08X}")
            
            if len(index_entries) == 0:
                print("⚠️  未找到有效索引项，使用备用扫描")
                return self.fallback_block_scan()
            
            return index_entries
            
        except Exception as e:
            print(f"❌ 索引解析错误: {e}")
            return self.fallback_block_scan()
    
    def fallback_block_scan(self):
        """备用数据块扫描 - 模拟真实APP的容错逻辑"""
        print("\n🔄 备用数据块扫描 (真实APP容错机制)")
        print("-" * 30)
        
        entries = []
        offset = 128  # 跳过头部和可能的索引
        
        while offset < self.file_size - 8:
            # 搜索zlib压缩标识 (0x78 0x9C)
            zlib_pos = self.file_data.find(b'\x78\x9c', offset)
            
            if zlib_pos == -1:
                break
            
            # 尝试解压来确定数据块大小
            try:
                # 尝试不同的可能大小
                for test_size in [8192, 16384, 32768, 65536]:
                    if zlib_pos + test_size > self.file_size:
                        test_size = self.file_size - zlib_pos
                    
                    test_data = self.file_data[zlib_pos:zlib_pos + test_size]
                    
                    try:
                        decompressed = zlib.decompress(test_data)
                        
                        entries.append({
                            'index': len(entries),
                            'offset': zlib_pos,
                            'size': test_size,
                            'type': 0x78,  # zlib类型
                            'flags': 0x9c,
                            'checksum': 0
                        })
                        
                        print(f"🗜️  发现压缩块: 偏移=0x{zlib_pos:08X}, 大小={test_size}")
                        offset = zlib_pos + test_size
                        break
                        
                    except zlib.error:
                        continue
                else:
                    offset = zlib_pos + 1
                    
            except Exception:
                offset = zlib_pos + 1
        
        return entries if entries else None
    
    def process_blocks_via_sub5c394_logic(self, block_index):
        """按照sub_5C394的调度逻辑处理数据块"""
        print(f"\n📋 数据块处理 (基于sub_5C394逻辑)")
        print("-" * 50)
        
        processed_count = 0
        
        for entry in block_index:
            try:
                print(f"\n🔧 处理数据块 #{entry['index']}")
                print(f"    偏移: 0x{entry['offset']:08X}")
                print(f"    大小: {entry['size']} 字节")
                print(f"    类型: 0x{entry['type']:08X}")
                print(f"    标志: 0x{entry['flags']:08X}")
                
                # 提取数据块
                block_data = self.file_data[entry['offset']:entry['offset'] + entry['size']]
                
                # 按照sub_5C394的分发逻辑
                self.dispatch_block_sub5c394(block_data, entry)
                
                processed_count += 1
                
                # 限制处理数量
                if processed_count >= 30:
                    print(f"\n📊 已处理 {processed_count} 个数据块")
                    break
                    
            except Exception as e:
                print(f"    ❌ 处理失败: {e}")
                continue
        
        print(f"\n🎉 总计处理 {processed_count} 个数据块")
    
    def dispatch_block_sub5c394(self, data, entry):
        """sub_5C394的分发逻辑 - 严格按照IDA Pro反汇编"""
        
        # 按照真实APP的类型检查顺序
        if data.startswith(b'\x78\x9c'):
            print(f"    🗜️  zlib压缩数据块")
            self.process_zlib_block_sub10f88_logic(data, entry)
            
        elif data.startswith(b'DICE-AM'):
            print(f"    🎯 DICE-AM矢量数据块")
            self.process_dice_am_block_real_logic(data, entry)
            
        elif entry['type'] == 0x0D:  # 索引类型 (从IDA分析得出)
            print(f"    📇 索引数据块")
            self.process_index_block_real_logic(data, entry)
            
        else:
            print(f"    🔍 通用数据块")
            self.process_generic_block_real_logic(data, entry)
    
    def process_zlib_block_sub10f88_logic(self, data, entry):
        """按照sub_10F88逻辑处理zlib数据块"""
        try:
            # 按照真实APP的zlib解压逻辑
            decompressed = zlib.decompress(data)
            print(f"        ✅ zlib解压成功: {len(data)} → {len(decompressed)} 字节")
            
            # 保存解压数据
            output_file = f"ida_decompressed_{entry['offset']:08X}.bin"
            with open(output_file, 'wb') as f:
                f.write(decompressed)
            print(f"        💾 解压数据保存到: {output_file}")
            
            # 按照真实APP逻辑递归分析解压数据
            print(f"        🔄 递归分析解压数据:")
            fake_entry = {
                'index': entry['index'],
                'offset': entry['offset'],
                'size': len(decompressed),
                'type': 0x01,
                'flags': 0x00,
                'checksum': 0
            }
            
            # 对解压数据应用相同的分发逻辑
            self.dispatch_block_sub5c394(decompressed, fake_entry)
            
        except zlib.error as e:
            print(f"        ❌ zlib解压失败: {e}")
        except Exception as e:
            print(f"        ❌ 处理失败: {e}")
    
    def process_dice_am_block_real_logic(self, data, entry):
        """按照真实APP逻辑处理DICE-AM数据块"""
        if len(data) < 32:
            print(f"        ❌ DICE-AM数据太小")
            return
        
        print(f"        🎯 DICE-AM数据分析:")
        
        # 解析DICE-AM头部 (按照真实APP格式)
        magic = data[0:7]
        version_byte = data[7]
        
        print(f"            魔数: {magic}")
        print(f"            版本: 0x{version_byte:02X}")
        
        # 真实APP的版本验证逻辑 (从IDA分析得出)
        version_check = version_byte ^ 0xAB
        print(f"            版本验证: 0x{version_check:02X}")
        
        if version_check == 0x89:
            print(f"            ✅ 版本验证通过")
        else:
            print(f"            ⚠️  版本验证失败")
        
        # 解析数据块计数
        if len(data) >= 20:
            block_count = struct.unpack('<I', data[16:20])[0]
            print(f"            数据块数: {block_count}")
        
        # 提取完整的DICE-AM内容并保存
        output_file = f"ida_dice_am_{entry['offset']:08X}.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# IDA Pro基础DICE-AM解析结果\n")
            f.write(f"# 偏移: 0x{entry['offset']:08X}\n")
            f.write(f"# 魔数: {magic}\n")
            f.write(f"# 版本: 0x{version_byte:02X}\n")
            f.write(f"# 版本验证: 0x{version_check:02X}\n")
            if len(data) >= 20:
                f.write(f"# 数据块数: {block_count}\n")
            f.write(f"\n=== 完整十六进制数据 ===\n")
            
            # 输出完整的十六进制数据
            for i in range(0, len(data), 16):
                hex_line = " ".join(f"{data[i+j]:02X}" for j in range(min(16, len(data)-i)))
                ascii_line = "".join(chr(data[i+j]) if 32 <= data[i+j] < 127 else "." for j in range(min(16, len(data)-i)))
                f.write(f"{i:04X}: {hex_line:<48} |{ascii_line}|\n")
        
        print(f"            💾 DICE-AM数据保存到: {output_file}")
    
    def process_index_block_real_logic(self, data, entry):
        """按照真实APP逻辑处理索引数据块"""
        print(f"        📇 索引数据分析:")
        
        if len(data) >= 16:
            # 解析索引块头部
            index_type = struct.unpack('<I', data[0:4])[0]
            index_count = struct.unpack('<I', data[4:8])[0]
            index_size = struct.unpack('<I', data[8:12])[0]
            index_flags = struct.unpack('<I', data[12:16])[0]
            
            print(f"            索引类型: 0x{index_type:08X}")
            print(f"            索引数量: {index_count}")
            print(f"            索引大小: {index_size}")
            print(f"            索引标志: 0x{index_flags:08X}")
            
            # 保存索引数据
            output_file = f"ida_index_{entry['offset']:08X}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"# IDA Pro基础索引数据解析结果\n")
                f.write(f"# 偏移: 0x{entry['offset']:08X}\n")
                f.write(f"# 索引类型: 0x{index_type:08X}\n")
                f.write(f"# 索引数量: {index_count}\n")
                f.write(f"# 索引大小: {index_size}\n")
                f.write(f"# 索引标志: 0x{index_flags:08X}\n\n")
                
                f.write("=== 完整十六进制数据 ===\n")
                for i in range(0, min(1024, len(data)), 16):
                    hex_line = " ".join(f"{data[i+j]:02X}" for j in range(min(16, len(data)-i)))
                    ascii_line = "".join(chr(data[i+j]) if 32 <= data[i+j] < 127 else "." for j in range(min(16, len(data)-i)))
                    f.write(f"{i:04X}: {hex_line:<48} |{ascii_line}|\n")
            
            print(f"            💾 索引数据保存到: {output_file}")
    
    def process_generic_block_real_logic(self, data, entry):
        """按照真实APP逻辑处理通用数据块"""
        print(f"        🔍 通用数据分析:")
        print(f"            数据大小: {len(data)} 字节")
        
        # 尝试UTF-8解码寻找文本内容
        text_content = ""
        try:
            text_content = data.decode('utf-8', errors='ignore')
            chinese_count = sum(1 for c in text_content if '\u4e00' <= c <= '\u9fff')
            
            if chinese_count > 10:
                print(f"            🈲 发现 {chinese_count} 个中文字符")
        except:
            pass
        
        # 保存完整的通用数据
        output_file = f"ida_generic_{entry['offset']:08X}.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# IDA Pro基础通用数据解析结果\n")
            f.write(f"# 偏移: 0x{entry['offset']:08X}\n")
            f.write(f"# 数据大小: {len(data)} 字节\n")
            f.write(f"# 数据类型: 0x{entry['type']:08X}\n")
            f.write(f"# 数据标志: 0x{entry['flags']:08X}\n\n")
            
            if text_content and len(text_content.strip()) > 0:
                f.write("=== UTF-8文本内容 ===\n")
                f.write(text_content[:2000])
                f.write("\n\n")
            
            f.write("=== 完整十六进制数据 ===\n")
            for i in range(0, min(2048, len(data)), 16):
                hex_line = " ".join(f"{data[i+j]:02X}" for j in range(min(16, len(data)-i)))
                ascii_line = "".join(chr(data[i+j]) if 32 <= data[i+j] < 127 else "." for j in range(min(16, len(data)-i)))
                f.write(f"{i:04X}: {hex_line:<48} |{ascii_line}|\n")
        
        print(f"            💾 通用数据保存到: {output_file}")

def main():
    """主函数"""
    print("🎯 基于IDA Pro反汇编代码的.ans文件解析器")
    print("严格按照sub_5C394和sub_10F88等函数的实际逻辑")
    print("=" * 80)
    
    files = ["file/m1.ans", "file/m3.ans"]
    
    for filename in files:
        if os.path.exists(filename):
            print(f"\n🚀 开始解析: {filename}")
            parser = IDABasedParser(filename)
            
            success = parser.parse_ans_file()
            if success:
                print(f"✅ {filename} 解析成功")
            else:
                print(f"❌ {filename} 解析失败")
        else:
            print(f"❌ 文件不存在: {filename}")
    
    print("\n🎉 IDA Pro基础解析完成！")

if __name__ == "__main__":
    main() 