#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据解析与渲染流程分析器
深度分析raw_data_*.bin文件的本质、解析逻辑和渲染流程
"""

import os
import zlib
import struct
import json

class DataParsingRenderingAnalyzer:
    """数据解析与渲染流程分析器"""
    
    def __init__(self):
        self.analysis_results = {
            'raw_data_nature': {},
            'parsing_pipeline': {},
            'rendering_logic': {},
            'data_flow': {}
        }
    
    def analyze_complete_pipeline(self):
        """分析完整的数据处理管道"""
        print("🎯 数据解析与渲染流程完整分析")
        print("=" * 80)
        
        # 1. 分析raw_data的本质
        self.analyze_raw_data_nature()
        
        # 2. 分析解析管道
        self.analyze_parsing_pipeline()
        
        # 3. 分析渲染逻辑
        self.analyze_rendering_logic()
        
        # 4. 生成完整流程图
        self.generate_complete_flow_diagram()
        
        return True
    
    def analyze_raw_data_nature(self):
        """分析raw_data_*.bin文件的本质"""
        print("\n📋 raw_data_*.bin文件本质分析")
        print("-" * 50)
        
        print("🎯 raw_data_*.bin是什么：")
        print("   📁 raw_data_file_read_*.bin     - 从磁盘.ans文件读取的原始字节")
        print("   🗜️ raw_data_zlib_compressed_*.bin - zlib压缩格式的原始数据")
        print("   💾 raw_data_sqlite_blob_*.bin   - 准备存入SQLite的原始数据")
        
        print("\n🔍 数据格式详解：")
        print("   • AM-zlib格式 - 高德地图专有的容器格式")
        print("   • zlib压缩块 - 标准zlib压缩的地图数据块")
        print("   • DICE-AM块 - 高德地图的矢量数据格式")
        print("   • JSON配置 - 地图样式和配置信息")
        print("   • 中文文本 - UTF-8编码的地名、道路名等")
        
        self.analysis_results['raw_data_nature'] = {
            'file_types': ['AM-zlib', 'zlib压缩', 'DICE-AM', 'JSON', '中文文本'],
            'sources': ['磁盘文件', 'zlib压缩', 'SQLite绑定'],
            'format': '二进制原始字节',
            'modification': '零修改，与APP处理的数据完全一致'
        }
    
    def analyze_parsing_pipeline(self):
        """分析解析管道"""
        print("\n📋 APP数据解析管道分析")
        print("-" * 50)
        
        print("🔄 完整解析流程：")
        print("   1️⃣ 文件读取阶段 (libc.so:read)")
        print("      └─ 从磁盘读取.ans文件 → raw_data_file_read_*.bin")
        print("   ")
        print("   2️⃣ 数据解压阶段 (libz.so:uncompress)")
        print("      ├─ 输入：zlib压缩数据 → raw_data_zlib_compressed_*.bin")
        print("      └─ 输出：8192字节解压数据块")
        print("   ")
        print("   3️⃣ 数据分发阶段 (sub_5C394)")
        print("      ├─ 根据数据头部类型分发")
        print("      ├─ DICE-AM → 矢量数据处理")
        print("      ├─ JSON → 配置数据处理")
        print("      └─ 中文文本 → 标注数据处理")
        print("   ")
        print("   4️⃣ 结构化存储 (girf_sqlite3_bind_blob)")
        print("      ├─ 输入：解析后的结构化数据 → raw_data_sqlite_blob_*.bin")
        print("      └─ 输出：SQLite数据库存储")
        
        self.analysis_results['parsing_pipeline'] = {
            'stages': ['文件读取', '数据解压', '数据分发', '结构化存储'],
            'functions': ['libc:read', 'libz:uncompress', 'sub_5C394', 'girf_sqlite3_bind_blob'],
            'data_transformations': ['原始文件 → 压缩块 → 解压数据 → 结构化数据 → 数据库']
        }
    
    def analyze_rendering_logic(self):
        """分析渲染逻辑"""
        print("\n📋 地图渲染逻辑分析")
        print("-" * 50)
        
        print("🎨 渲染管道流程：")
        print("   ")
        print("   📊 数据类型 → 渲染方式")
        print("   ├─ 矢量坐标 → OpenGL矢量渲染")
        print("   │  ├─ 道路：线条渲染 (GL_LINES)")
        print("   │  ├─ 建筑：多边形渲染 (GL_TRIANGLES)")
        print("   │  └─ 水域：填充渲染 (GL_TRIANGLE_FAN)")
        print("   │")
        print("   ├─ 文本标注 → 文字叠加渲染")
        print("   │  ├─ 道路名：沿路径渲染")
        print("   │  ├─ 地名：点位标注")
        print("   │  └─ POI：图标+文字")
        print("   │")
        print("   └─ 栅格瓦片 → 纹理渲染")
        print("      ├─ 卫星图：GL_TEXTURE_2D")
        print("      └─ 背景：基础地图纹理")
        
        print("\n🖼️ 渲染着色器逻辑：")
        print("   • 顶点着色器：处理坐标变换和投影")
        print("   • 片段着色器：处理颜色、纹理和光照")
        print("   • 几何着色器：处理线条宽度和端点样式")
        
        self.analysis_results['rendering_logic'] = {
            'vector_rendering': ['道路线条', '建筑多边形', '水域填充'],
            'text_rendering': ['道路名沿线', '地名点标', 'POI图标'],
            'raster_rendering': ['卫星纹理', '背景地图'],
            'shaders': ['顶点着色器', '片段着色器', '几何着色器']
        }
    
    def generate_complete_flow_diagram(self):
        """生成完整的数据流程图"""
        print("\n📊 完整数据流程图")
        print("=" * 80)
        
        print("""
🗺️ 高德地图数据处理与渲染完整流程图

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   磁盘.ans文件   │───▶│   Frida捕获      │───▶│ raw_data_file_  │
│   (AM-zlib格式) │    │   libc:read     │    │   read_*.bin    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   zlib解压       │◀───│   数据解压       │◀───│   APP读取处理   │
│   (8192字节块)  │    │   libz:uncompress│    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │
         ▼                        ▼
┌─────────────────┐    ┌─────────────────┐
│ raw_data_zlib_  │    │   数据分发       │
│ compressed_*.bin│    │   sub_5C394     │
└─────────────────┘    └─────────────────┘
                                │
                                ▼
         ┌──────────────────────┼──────────────────────┐
         │                      │                      │
         ▼                      ▼                      ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ DICE-AM数据  │    │  JSON配置   │    │  中文文本    │
│  (矢量坐标)  │    │  (样式)     │    │  (地名)     │
└─────────────┘    └─────────────┘    └─────────────┘
         │                      │                      │
         └──────────────────────┼──────────────────────┘
                                ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SQLite存储     │◀───│   数据绑定       │───▶│ raw_data_sqlite_│
│   (结构化数据)   │    │bind_blob:0x15000│    │   blob_*.bin    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   渲染引擎       │    │   OpenGL渲染     │    │   屏幕显示       │
│   (GPU处理)     │───▶│   (着色器)      │───▶│   (最终效果)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘

🔍 关键点：
• raw_data_*.bin = APP实际处理的原始字节，零修改
• 解析 = 从二进制数据中提取结构化信息 
• 渲染 = 将结构化数据转换为GPU可绘制的几何和纹理
• 显示 = GPU执行渲染指令，在屏幕上绘制地图
        """)
    
    def analyze_specific_data_example(self):
        """分析具体的数据示例"""
        print("\n📋 具体数据示例分析")
        print("-" * 50)
        
        print("🔍 DICE-AM矢量数据解析：")
        print("   原始字节: 44 49 43 45 2d 41 4d 00 ...")
        print("   ├─ 'DICE-AM' (魔数标识)")
        print("   ├─ 版本字节 (0x00)")
        print("   ├─ 坐标点数量 (4字节整数)")
        print("   └─ 坐标数据 (float32数组)")
        print("   ")
        print("   渲染处理:")
        print("   ├─ 转换为OpenGL顶点缓冲区")
        print("   ├─ 应用投影变换矩阵")
        print("   └─ GPU绘制为线条或多边形")
        
        print("\n🔍 中文文本数据解析：")
        print("   原始字节: e4 b8 ad e5 9b bd ... (UTF-8)")
        print("   ├─ UTF-8解码 → '中国'")
        print("   ├─ 字体渲染引擎处理")
        print("   └─ 生成文字纹理")
        print("   ")
        print("   渲染处理:")
        print("   ├─ 字体光栅化")
        print("   ├─ 文字纹理贴图")
        print("   └─ 叠加到地图层")
        
        print("\n🔍 JSON配置数据解析：")
        print("   原始字节: 7b 22 72 65 73 5f ... ('{\"res_...')")
        print("   ├─ JSON解析 → 对象结构")
        print("   ├─ 样式参数提取")
        print("   └─ 渲染参数设置")
        print("   ")
        print("   渲染处理:")
        print("   ├─ 设置颜色主题")
        print("   ├─ 配置线条样式")
        print("   └─ 调整渲染参数")
    
    def save_analysis_report(self):
        """保存分析报告"""
        report = {
            'analysis_time': 'December 2024',
            'purpose': '分析raw_data_*.bin文件的本质和完整处理流程',
            'results': self.analysis_results
        }
        
        with open('data_parsing_rendering_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 分析报告保存到: data_parsing_rendering_analysis.json")

def main():
    """主函数"""
    print("🎯 数据解析与渲染流程分析器")
    print("深度分析raw_data_*.bin文件的本质和处理流程")
    print("=" * 80)
    
    analyzer = DataParsingRenderingAnalyzer()
    
    # 执行完整分析
    analyzer.analyze_complete_pipeline()
    
    # 分析具体示例
    analyzer.analyze_specific_data_example()
    
    # 保存报告
    analyzer.save_analysis_report()
    
    print("\n🏆 核心结论：")
    print("=" * 50)
    print("1. raw_data_*.bin = APP处理的原始二进制数据，未经修改")
    print("2. 解析 = 从二进制中提取矢量、文本、配置等结构化数据")
    print("3. 渲染 = 将结构化数据转换为GPU绘制指令和纹理")
    print("4. 显示 = GPU执行OpenGL指令，在屏幕绘制最终地图")
    print("\n💡 这就是从raw_data到屏幕显示的完整流程！")

if __name__ == "__main__":
    main() 