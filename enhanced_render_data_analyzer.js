/**
 * 高德地图渲染数据深度分析器 - 增强版
 * 基于 full_execution_flow_analysis.md 的IDA Pro分析结果
 * 
 * 功能：
 * 1. 深度解析 DICE-AM 数据块结构
 * 2. 提取矢量坐标、POI、纹理信息
 * 3. 可视化渲染数据流
 * 4. 导出解析结果
 */
(function() {
    console.log("🚀 [Enhanced Render Data Analyzer] 增强版渲染数据分析器启动...");

    // === 配置区域 ===
    const CONFIG = {
        ENABLE_DEEP_PARSING: true,      // 深度解析
        ENABLE_DATA_EXPORT: false,      // 数据导出（可能影响性能）
        ENABLE_VISUALIZATION: true,     // 可视化输出
        ENABLE_VECTOR_ANALYSIS: true,   // 矢量数据分析
        ENABLE_POI_ANALYSIS: true,      // POI数据分析
        MAX_PARSED_ITEMS: 100,          // 最大解析项目数
        VERBOSE_HEX_DUMP: false         // 详细十六进制转储
    };

    // === 数据结构定义 ===
    var parsedVectorData = [];     // 矢量数据
    var parsedPOIData = [];        // POI数据  
    var parsedTextureData = [];    // 纹理数据
    var renderingPipeline = [];    // 渲染管道数据
    var dataStatistics = {         // 数据统计
        totalDataBlocks: 0,
        totalBytes: 0,
        diceAmBlocks: 0,
        vectorElements: 0,
        poiElements: 0,
        textureElements: 0
    };

    // === 反调试和稳定性保护 ===
    function setupAntiDebugProtection() {
        try {
            // 拦截 exit 和 abort
            ["exit", "abort", "_exit"].forEach(function(funcName) {
                var funcPtr = Module.findExportByName("libc.so", funcName);
                if (funcPtr) {
                    Interceptor.replace(funcPtr, new NativeCallback(function(code) {
                        console.log("🛡️ [Protection] 拦截 " + funcName + "() 调用，代码: " + code);
                        return 0;
                    }, 'void', ['int']));
                }
            });

            // Java层保护
            setTimeout(function() {
                Java.perform(function() {
                    try {
                        // 防止调试检测
                        var Debug = Java.use("android.os.Debug");
                        Debug.isDebuggerConnected.implementation = function() {
                            return false;
                        };

                        // 防止进程终止
                        var Process = Java.use("android.os.Process");
                        Process.killProcess.implementation = function(pid) {
                            console.log("🛡️ [Protection] 阻止进程终止，PID: " + pid);
                        };

                        console.log("🛡️ [Protection] Java层保护已启用");
                    } catch (e) {
                        console.log("⚠️ [Protection] Java层保护失败: " + e);
                    }
                });
            }, 500);

            console.log("✅ [Protection] 反调试保护已启用");
        } catch (e) {
            console.log("❌ [Protection] 保护机制初始化失败: " + e);
        }
    }

    // === 高级数据解析函数 ===
    function analyzeDiceAmDataBlock(buffer, size) {
        try {
            var analysis = {
                header: "",
                version: 0,
                dataType: "unknown",
                vectorCount: 0,
                poiCount: 0,
                textureCount: 0,
                coordinates: [],
                pois: [],
                textures: [],
                rawData: null
            };

            if (size < 16) return analysis;

            // 读取头部信息
            analysis.header = buffer.readUtf8String(16);
            
            if (!analysis.header.includes("DICE-AM")) {
                return analysis;
            }

            // 解析版本信息（基于IDA分析的版本检查逻辑）
            if (size >= 9) {
                var versionByte = buffer.add(8).readU8();
                analysis.version = versionByte ^ 0xAB;  // 文档中的版本检查算法
            }

            // 解析数据类型和计数（基于推测的数据结构）
            if (size >= 32) {
                try {
                    // 尝试读取数据块计数信息（偏移可能需要调整）
                    var dataTypeMarker = buffer.add(16).readU32();
                    var elementCount = buffer.add(20).readU32();
                    
                    // 基于数据类型标记判断内容类型
                    if (dataTypeMarker === 0x56454354) {  // "VECT" - 矢量数据
                        analysis.dataType = "vector";
                        analysis.vectorCount = elementCount;
                        console.log("🎯 [DICE-AM] 检测到矢量数据块，元素数量: " + elementCount);
                    } else if (dataTypeMarker === 0x504F4949) {  // "POII" - POI数据
                        analysis.dataType = "poi";
                        analysis.poiCount = elementCount;
                        console.log("📍 [DICE-AM] 检测到POI数据块，元素数量: " + elementCount);
                    } else if (dataTypeMarker === 0x54455854) {  // "TEXT" - 纹理数据
                        analysis.dataType = "texture";
                        analysis.textureCount = elementCount;
                        console.log("🖼️ [DICE-AM] 检测到纹理数据块，元素数量: " + elementCount);
                    }

                    // 尝试解析坐标数据（矢量数据的情况）
                    if (analysis.dataType === "vector" && analysis.vectorCount > 0 && analysis.vectorCount < 1000) {
                        analysis.coordinates = parseVectorCoordinates(buffer, size, analysis.vectorCount);
                    }

                    // 尝试解析POI数据
                    if (analysis.dataType === "poi" && analysis.poiCount > 0 && analysis.poiCount < 100) {
                        analysis.pois = parsePOIData(buffer, size, analysis.poiCount);
                    }

                } catch (e) {
                    console.log("⚠️ [DICE-AM] 详细解析失败: " + e);
                }
            }

            // 存储原始数据用于进一步分析
            if (CONFIG.ENABLE_DATA_EXPORT && size < 4096) {
                analysis.rawData = buffer.readByteArray(size);
            }

            return analysis;

        } catch (e) {
            console.log("❌ [DICE-AM] 分析失败: " + e);
            return {error: e.toString(), size: size};
        }
    }

    // === 矢量坐标解析 ===
    function parseVectorCoordinates(buffer, size, count) {
        var coordinates = [];
        try {
            // 假设坐标数据从偏移32开始，每个坐标8字节（经度+纬度）
            var coordOffset = 32;
            var maxCoords = Math.min(count, (size - coordOffset) / 8);
            
            for (var i = 0; i < maxCoords; i++) {
                var offset = coordOffset + (i * 8);
                if (offset + 8 <= size) {
                    var longitude = buffer.add(offset).readFloat();
                    var latitude = buffer.add(offset + 4).readFloat();
                    
                    // 验证坐标合理性（中国境内大概范围）
                    if (longitude >= 70 && longitude <= 140 && latitude >= 10 && latitude <= 60) {
                        coordinates.push({
                            index: i,
                            longitude: longitude,
                            latitude: latitude
                        });
                    }
                }
            }
            
            if (coordinates.length > 0) {
                console.log("📐 [Vector] 解析到 " + coordinates.length + " 个有效坐标点");
                console.log("   - 首个坐标: (" + coordinates[0].longitude + ", " + coordinates[0].latitude + ")");
                if (coordinates.length > 1) {
                    var lastIdx = coordinates.length - 1;
                    console.log("   - 末尾坐标: (" + coordinates[lastIdx].longitude + ", " + coordinates[lastIdx].latitude + ")");
                }
            }
            
        } catch (e) {
            console.log("⚠️ [Vector] 坐标解析失败: " + e);
        }
        
        return coordinates;
    }

    // === POI数据解析 ===
    function parsePOIData(buffer, size, count) {
        var pois = [];
        try {
            // POI数据可能包含：坐标、名称、类型等
            var poiOffset = 32;  // 假设POI数据从偏移32开始
            
            for (var i = 0; i < Math.min(count, 20); i++) {  // 限制解析数量避免性能问题
                var offset = poiOffset + (i * 64);  // 假设每个POI条目64字节
                if (offset + 32 <= size) {
                    try {
                        var longitude = buffer.add(offset).readFloat();
                        var latitude = buffer.add(offset + 4).readFloat();
                        var typeId = buffer.add(offset + 8).readU32();
                        
                        // 尝试读取POI名称（可能是UTF-8编码）
                        var nameOffset = offset + 16;
                        var poiName = "";
                        try {
                            poiName = buffer.add(nameOffset).readUtf8String(32).replace(/\0/g, '');
                        } catch (e) {
                            poiName = "未知POI";
                        }
                        
                        if (longitude >= 70 && longitude <= 140 && latitude >= 10 && latitude <= 60) {
                            pois.push({
                                index: i,
                                name: poiName,
                                longitude: longitude,
                                latitude: latitude,
                                typeId: typeId
                            });
                        }
                    } catch (e) {
                        // 单个POI解析失败，继续下一个
                    }
                }
            }
            
            if (pois.length > 0) {
                console.log("📍 [POI] 解析到 " + pois.length + " 个POI点");
                pois.forEach(function(poi, idx) {
                    if (idx < 3) {  // 只显示前3个
                        console.log("   - " + poi.name + " (" + poi.longitude + ", " + poi.latitude + ")");
                    }
                });
            }
            
        } catch (e) {
            console.log("⚠️ [POI] POI解析失败: " + e);
        }
        
        return pois;
    }

    // === 可视化数据输出 ===
    function visualizeRenderData() {
        if (!CONFIG.ENABLE_VISUALIZATION) return;

        console.log("\n🎨 ========== 渲染数据可视化 ==========");
        console.log("📊 数据统计:");
        console.log("   - 总数据块: " + dataStatistics.totalDataBlocks);
        console.log("   - 总字节数: " + dataStatistics.totalBytes.toLocaleString());
        console.log("   - DICE-AM块: " + dataStatistics.diceAmBlocks);
        console.log("   - 矢量元素: " + dataStatistics.vectorElements);
        console.log("   - POI元素: " + dataStatistics.poiElements);

        // 显示最新的矢量数据
        if (parsedVectorData.length > 0) {
            console.log("\n🎯 最新矢量数据:");
            var latest = parsedVectorData[parsedVectorData.length - 1];
            console.log("   - 时间: " + new Date(latest.timestamp).toLocaleTimeString());
            console.log("   - 坐标数量: " + latest.coordinates.length);
            if (latest.coordinates.length > 0) {
                console.log("   - 范围: (" + 
                    Math.min(...latest.coordinates.map(c => c.longitude)).toFixed(4) + ", " +
                    Math.min(...latest.coordinates.map(c => c.latitude)).toFixed(4) + ") 到 (" +
                    Math.max(...latest.coordinates.map(c => c.longitude)).toFixed(4) + ", " +
                    Math.max(...latest.coordinates.map(c => c.latitude)).toFixed(4) + ")");
            }
        }

        // 显示最新的POI数据
        if (parsedPOIData.length > 0) {
            console.log("\n📍 最新POI数据:");
            var latestPOI = parsedPOIData[parsedPOIData.length - 1];
            console.log("   - 时间: " + new Date(latestPOI.timestamp).toLocaleTimeString());
            console.log("   - POI数量: " + latestPOI.pois.length);
            latestPOI.pois.slice(0, 3).forEach(function(poi) {
                console.log("   - " + poi.name + " [" + poi.typeId + "]");
            });
        }

        console.log("======================================\n");
    }

    // === 核心Hook函数 ===
    function setupCoreHooks() {
        // Hook libamapr.so 函数
        setupAmaprHooks();
        
        // Hook libamapnsq.so 函数
        setupAmapnsqHooks();
        
        // Hook zlib 解压
        setupZlibHooks();
        
        // Hook 文件I/O
        setupFileIOHooks();
    }

    function setupAmaprHooks() {
        var libamapr = Process.findModuleByName("libamapr.so");
        if (!libamapr) {
            console.log("⚠️ [Hook] libamapr.so 未找到");
            return;
        }

        console.log("📍 [Hook] libamapr.so 基址: " + libamapr.base);

        try {
            // Hook nativeAddMapGestureMsg - 入口函数
            var nativeAddMapGestureMsg = libamapr.base.add(0x6ee70c);
            Interceptor.attach(nativeAddMapGestureMsg, {
                onEnter: function(args) {
                    console.log("🎯 [Gesture] 手势消息处理开始");
                    renderingPipeline.push({
                        stage: "gesture_start",
                        timestamp: Date.now()
                    });
                },
                onLeave: function(retval) {
                    console.log("✅ [Gesture] 手势消息处理完成");
                    renderingPipeline.push({
                        stage: "gesture_complete",
                        timestamp: Date.now()
                    });
                }
            });

            // Hook triggerRenderUpdate - 渲染触发器
            var triggerRenderUpdate = libamapr.base.add(0x6FBC78);
            Interceptor.attach(triggerRenderUpdate, {
                onEnter: function(args) {
                    console.log("🎨 [Render] 渲染更新触发");
                    renderingPipeline.push({
                        stage: "render_trigger",
                        timestamp: Date.now()
                    });
                }
            });

            console.log("✅ [Hook] libamapr.so 核心函数已挂钩");

        } catch (e) {
            console.log("❌ [Hook] libamapr.so 挂钩失败: " + e);
        }
    }

    function setupAmapnsqHooks() {
        var libamapnsq = Process.findModuleByName("libamapnsq.so");
        if (!libamapnsq) {
            console.log("⚠️ [Hook] libamapnsq.so 未找到");
            return;
        }

        console.log("📍 [Hook] libamapnsq.so 基址: " + libamapnsq.base);

        try {
            // Hook sub_10F88 - 核心数据解析器
            var sub_10F88 = libamapnsq.base.add(0x10F88);
            Interceptor.attach(sub_10F88, {
                onEnter: function(args) {
                    this.parseBuffer = args[0];
                    this.parseSize = args[1] ? args[1].toInt32() : 0;
                    
                    console.log("🔍 [DataParser] 开始解析数据，大小: " + this.parseSize + " 字节");
                    
                    if (this.parseBuffer && this.parseSize > 0) {
                        // 深度分析数据块
                        var analysis = analyzeDiceAmDataBlock(this.parseBuffer, this.parseSize);
                        this.analysis = analysis;
                        
                        if (analysis.header && analysis.header.includes("DICE-AM")) {
                            dataStatistics.diceAmBlocks++;
                            console.log("🎲 [DataParser] DICE-AM数据块详细信息:");
                            console.log("   - 版本: " + analysis.version);
                            console.log("   - 数据类型: " + analysis.dataType);
                            console.log("   - 矢量数量: " + analysis.vectorCount);
                            console.log("   - POI数量: " + analysis.poiCount);
                            
                            // 存储解析结果
                            if (CONFIG.ENABLE_VECTOR_ANALYSIS && analysis.coordinates.length > 0) {
                                parsedVectorData.push({
                                    timestamp: Date.now(),
                                    coordinates: analysis.coordinates,
                                    dataType: analysis.dataType
                                });
                                dataStatistics.vectorElements += analysis.coordinates.length;
                            }
                            
                            if (CONFIG.ENABLE_POI_ANALYSIS && analysis.pois.length > 0) {
                                parsedPOIData.push({
                                    timestamp: Date.now(),
                                    pois: analysis.pois,
                                    dataType: analysis.dataType
                                });
                                dataStatistics.poiElements += analysis.pois.length;
                            }
                        }
                        
                        dataStatistics.totalDataBlocks++;
                        dataStatistics.totalBytes += this.parseSize;
                    }
                },
                onLeave: function(retval) {
                    var returnCode = retval.toInt32();
                    var success = (returnCode === 0);
                    
                    console.log("✅ [DataParser] 解析完成，状态: " + (success ? "成功" : "失败(" + returnCode + ")"));
                    
                    if (success && this.analysis && this.analysis.header.includes("DICE-AM")) {
                        renderingPipeline.push({
                            stage: "data_parsed",
                            timestamp: Date.now(),
                            dataType: this.analysis.dataType,
                            elementCount: this.analysis.vectorCount + this.analysis.poiCount
                        });
                    }
                }
            });

            console.log("✅ [Hook] libamapnsq.so 核心函数已挂钩");

        } catch (e) {
            console.log("❌ [Hook] libamapnsq.so 挂钩失败: " + e);
        }
    }

    function setupZlibHooks() {
        try {
            var uncompress = Module.findExportByName("libz.so", "uncompress");
            if (uncompress) {
                Interceptor.attach(uncompress, {
                    onEnter: function(args) {
                        this.sourceLen = args[3].toInt32();
                        console.log("🗜️ [Zlib] 开始解压，源大小: " + this.sourceLen + " 字节");
                    },
                    onLeave: function(retval) {
                        if (retval.toInt32() === 0) {
                            console.log("✅ [Zlib] 解压成功");
                            renderingPipeline.push({
                                stage: "decompression",
                                timestamp: Date.now()
                            });
                        }
                    }
                });
                console.log("✅ [Hook] zlib 解压监控已启用");
            }
        } catch (e) {
            console.log("❌ [Hook] zlib 监控失败: " + e);
        }
    }

    function setupFileIOHooks() {
        try {
            var ansFiles = {};
            
            // 监控文件打开
            var open_ptr = Module.findExportByName("libc.so", "open");
            if (open_ptr) {
                Interceptor.attach(open_ptr, {
                    onEnter: function(args) {
                        var path = args[0].readUtf8String();
                        if (path && path.includes(".ans")) {
                            this.ansPath = path;
                        }
                    },
                    onLeave: function(retval) {
                        if (this.ansPath) {
                            var fd = retval.toInt32();
                            if (fd > 0) {
                                ansFiles[fd] = this.ansPath;
                                console.log("📁 [FileIO] ANS文件打开: " + this.ansPath);
                            }
                        }
                    }
                });
            }

            // 监控文件读取
            var read_ptr = Module.findExportByName("libc.so", "read");
            if (read_ptr) {
                Interceptor.attach(read_ptr, {
                    onEnter: function(args) {
                        this.fd = args[0].toInt32();
                        this.size = args[2].toInt32();
                        if (ansFiles[this.fd]) {
                            this.isAnsFile = true;
                        }
                    },
                    onLeave: function(retval) {
                        if (this.isAnsFile && retval.toInt32() > 0) {
                            console.log("📖 [FileIO] ANS数据读取: " + retval.toInt32() + " 字节");
                            renderingPipeline.push({
                                stage: "file_read",
                                timestamp: Date.now(),
                                bytes: retval.toInt32()
                            });
                        }
                    }
                });
            }

            console.log("✅ [Hook] 文件I/O监控已启用");
        } catch (e) {
            console.log("❌ [Hook] 文件I/O监控失败: " + e);
        }
    }

    // === 数据清理和管理 ===
    function setupDataManagement() {
        setInterval(function() {
            // 可视化输出
            visualizeRenderData();
            
            // 清理旧数据避免内存溢出
            if (parsedVectorData.length > CONFIG.MAX_PARSED_ITEMS) {
                parsedVectorData = parsedVectorData.slice(-CONFIG.MAX_PARSED_ITEMS / 2);
            }
            if (parsedPOIData.length > CONFIG.MAX_PARSED_ITEMS) {
                parsedPOIData = parsedPOIData.slice(-CONFIG.MAX_PARSED_ITEMS / 2);
            }
            if (renderingPipeline.length > 50) {
                renderingPipeline = renderingPipeline.slice(-25);
            }
            
        }, 20000);  // 每20秒执行一次
    }

    // === 主初始化函数 ===
    function initialize() {
        console.log("🎯 [Init] 开始初始化增强版渲染数据分析器...");

        // 设置保护机制
        setupAntiDebugProtection();

        // 延迟设置Hook以确保模块加载
        setTimeout(function() {
            try {
                setupCoreHooks();
                setupDataManagement();
                
                console.log("✅ [Init] 初始化完成！");
                console.log("🎮 请在高德地图上进行手势操作以触发数据解析...");
                console.log("📊 数据可视化将每20秒更新一次");
                
            } catch (e) {
                console.log("❌ [Init] 初始化失败: " + e);
            }
        }, 3000);
    }

    // 启动分析器
    initialize();

    console.log("🚀 [Enhanced Render Data Analyzer] 脚本加载完成");
})(); 