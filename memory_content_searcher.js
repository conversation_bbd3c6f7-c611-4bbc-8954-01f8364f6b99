/*
 * 高德地图内存内容深度搜索器
 * 基于观察到的数据大小和模式，搜索实际解析后的内容
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[Memory Content Searcher] 启动内存内容深度搜索器...");
    
    var CONFIG = {
        INIT_DELAY: 3000,
        SEARCH_PATTERNS: {
            VECTOR_SIZE: 1705315720,    // 观察到的矢量数据大小
            POI_SIZE: 1317759112,       // 观察到的POI数据大小
            UNKNOWN_SIZE: -1980347640   // 观察到的未知数据大小
        }
    };
    
    var searchResults = {
        foundMemoryRegions: [],
        parsedVectorData: [],
        parsedPoiData: [],
        rawHexDumps: []
    };
    
    // === 内存区域搜索器 ===
    function searchMemoryRegions() {
        console.log("[Memory Search] 开始搜索内存区域...");
        
        try {
            // 获取进程内存映射
            var ranges = Process.enumerateRanges('rw-');
            console.log("[Memory] 找到 " + ranges.length + " 个可读写内存区域");
            
            for (var i = 0; i < ranges.length; i++) {
                var range = ranges[i];
                
                // 跳过太小的区域
                if (range.size < 0x1000) continue;
                
                console.log("[Range] 搜索区域: " + range.base + " - " + range.base.add(range.size) + " (大小: " + range.size + ")");
                
                // 在每个内存区域中搜索特定模式
                searchInRange(range);
            }
            
            console.log("[Memory Search] 内存搜索完成");
            
        } catch (e) {
            console.log("[Error] 内存搜索失败: " + e);
        }
    }
    
    // === 在指定范围内搜索 ===
    function searchInRange(range) {
        try {
            var searchSize = Math.min(range.size, 0x100000); // 最多搜索1MB
            var stepSize = 0x1000; // 每4KB搜索一次
            
            for (var offset = 0; offset < searchSize; offset += stepSize) {
                try {
                    var addr = range.base.add(offset);
                    
                    // 搜索可能的地图数据模式
                    searchMapDataPatterns(addr, Math.min(stepSize, searchSize - offset));
                    
                } catch (e) {
                    // 跳过无法读取的内存
                    continue;
                }
            }
            
        } catch (e) {
            console.log("[Range Error] 范围搜索失败: " + e);
        }
    }
    
    // === 搜索地图数据模式 ===
    function searchMapDataPatterns(addr, size) {
        try {
            // 读取内存块
            var data = addr.readByteArray(Math.min(size, 1024));
            var view = new Uint8Array(data);
            
            // 搜索模式1: 连续的浮点数（可能是坐标）
            searchFloatPatterns(addr, view);
            
            // 搜索模式2: UTF-8中文文本
            searchChineseTextPatterns(addr, view);
            
            // 搜索模式3: 结构化数据头部
            searchStructuredDataHeaders(addr, view);
            
        } catch (e) {
            // 继续搜索
        }
    }
    
    // === 搜索浮点数模式 ===
    function searchFloatPatterns(baseAddr, view) {
        try {
            for (var i = 0; i <= view.length - 16; i += 4) {
                // 读取4个连续的32位浮点数
                var values = [];
                var validCoords = 0;
                
                for (var j = 0; j < 4; j++) {
                    try {
                        var addr = baseAddr.add(i + j * 4);
                        var value = addr.readFloat();
                        values.push(value);
                        
                        // 检查是否为合理的坐标值
                        if (isValidCoordinate(value)) {
                            validCoords++;
                        }
                    } catch (e) {
                        break;
                    }
                }
                
                // 如果有3个或更多有效坐标，记录这个位置
                if (validCoords >= 3) {
                    console.log("[Coordinates] 发现坐标序列 @ " + baseAddr.add(i) + ": " + values.join(", "));
                    
                    searchResults.parsedVectorData.push({
                        address: baseAddr.add(i),
                        type: "coordinate_sequence",
                        coordinates: values,
                        validCount: validCoords
                    });
                    
                    // 提取更多坐标
                    extractExtendedCoordinates(baseAddr.add(i));
                }
            }
        } catch (e) {
            // 继续搜索
        }
    }
    
    // === 提取扩展坐标序列 ===
    function extractExtendedCoordinates(startAddr) {
        try {
            var coordinates = [];
            var maxCoords = 50; // 最多读取50个坐标
            
            for (var i = 0; i < maxCoords; i++) {
                try {
                    var addr = startAddr.add(i * 4);
                    var value = addr.readFloat();
                    
                    if (isValidCoordinate(value)) {
                        coordinates.push(value);
                    } else {
                        break;
                    }
                } catch (e) {
                    break;
                }
            }
            
            if (coordinates.length >= 6) {
                console.log("[Extended Coords] 扩展坐标序列 (" + coordinates.length + " 个): " + 
                           coordinates.slice(0, 6).join(", ") + "...");
                
                // 分析坐标模式
                analyzeCoordinatePattern(coordinates);
            }
            
        } catch (e) {
            console.log("[Coord Error] 扩展坐标提取失败: " + e);
        }
    }
    
    // === 分析坐标模式 ===
    function analyzeCoordinatePattern(coords) {
        try {
            var analysis = {
                count: coords.length,
                minValue: Math.min.apply(null, coords),
                maxValue: Math.max.apply(null, coords),
                avgValue: coords.reduce(function(a, b) { return a + b; }, 0) / coords.length,
                possibleType: "unknown"
            };
            
            // 判断可能的坐标类型
            if (analysis.minValue >= 70 && analysis.maxValue <= 140) {
                analysis.possibleType = "longitude";
            } else if (analysis.minValue >= 10 && analysis.maxValue <= 60) {
                analysis.possibleType = "latitude";
            } else if (analysis.minValue >= 0 && analysis.maxValue <= 5000) {
                analysis.possibleType = "relative_coordinates";
            }
            
            console.log("[Coord Analysis] 类型: " + analysis.possibleType + 
                       ", 数量: " + analysis.count + 
                       ", 范围: " + analysis.minValue.toFixed(2) + " - " + analysis.maxValue.toFixed(2));
            
        } catch (e) {
            console.log("[Analysis Error] 坐标分析失败: " + e);
        }
    }
    
    // === 搜索中文文本模式 ===
    function searchChineseTextPatterns(baseAddr, view) {
        try {
            // 转换为可能的UTF-8字符串
            var text = "";
            for (var i = 0; i < Math.min(view.length, 256); i++) {
                if (view[i] >= 32 && view[i] < 127) {
                    text += String.fromCharCode(view[i]);
                } else if (view[i] === 0) {
                    break;
                } else {
                    text += ".";
                }
            }
            
            // 检查UTF-8中文
            try {
                var addr = baseAddr;
                var utf8Text = addr.readUtf8String(64);
                
                if (utf8Text && containsChinese(utf8Text)) {
                    console.log("[Chinese Text] 发现中文文本 @ " + addr + ": '" + utf8Text.substring(0, 20) + "'");
                    
                    searchResults.parsedPoiData.push({
                        address: addr,
                        type: "chinese_text",
                        text: utf8Text,
                        length: utf8Text.length
                    });
                    
                    // 提取更多文本
                    extractExtendedText(addr);
                }
            } catch (e) {
                // UTF-8解析失败，继续搜索
            }
            
        } catch (e) {
            // 继续搜索
        }
    }
    
    // === 提取扩展文本 ===
    function extractExtendedText(startAddr) {
        try {
            var maxLength = 512;
            var fullText = startAddr.readUtf8String(maxLength);
            
            if (fullText && fullText.length > 20) {
                console.log("[Extended Text] 完整文本: '" + fullText + "'");
                
                // 分析文本内容
                analyzeTextContent(fullText);
            }
            
        } catch (e) {
            console.log("[Text Error] 扩展文本提取失败: " + e);
        }
    }
    
    // === 分析文本内容 ===
    function analyzeTextContent(text) {
        try {
            var analysis = {
                length: text.length,
                chineseCharCount: (text.match(/[\u4e00-\u9fff]/g) || []).length,
                englishCharCount: (text.match(/[a-zA-Z]/g) || []).length,
                numberCount: (text.match(/[0-9]/g) || []).length,
                possibleType: "unknown"
            };
            
            // 判断可能的文本类型
            if (analysis.chineseCharCount > analysis.length * 0.5) {
                analysis.possibleType = "poi_name";
            } else if (analysis.numberCount > analysis.length * 0.3) {
                analysis.possibleType = "coordinates_string";
            } else if (analysis.englishCharCount > analysis.length * 0.5) {
                analysis.possibleType = "metadata";
            }
            
            console.log("[Text Analysis] 类型: " + analysis.possibleType + 
                       ", 长度: " + analysis.length + 
                       ", 中文: " + analysis.chineseCharCount + 
                       ", 数字: " + analysis.numberCount);
            
        } catch (e) {
            console.log("[Text Analysis Error] 文本分析失败: " + e);
        }
    }
    
    // === 搜索结构化数据头部 ===
    function searchStructuredDataHeaders(baseAddr, view) {
        try {
            // 寻找可能的数据结构标识符
            var patterns = [
                [0x08, 0x21, 0x39, 0x68], // .!9h
                [0x08, 0x43, 0x2E, 0x51], // .C.Q
                [0x44, 0x49, 0x43, 0x45], // DICE
                [0x41, 0x4E, 0x53, 0x00]  // ANS\0
            ];
            
            for (var p = 0; p < patterns.length; p++) {
                var pattern = patterns[p];
                
                for (var i = 0; i <= view.length - pattern.length; i++) {
                    var match = true;
                    for (var j = 0; j < pattern.length; j++) {
                        if (view[i + j] !== pattern[j]) {
                            match = false;
                            break;
                        }
                    }
                    
                    if (match) {
                        console.log("[Structure] 发现数据头部模式 @ " + baseAddr.add(i) + ": " + 
                                   pattern.map(function(b) { return "0x" + b.toString(16); }).join(" "));
                        
                        // 提取完整的头部信息
                        extractDataHeader(baseAddr.add(i));
                    }
                }
            }
            
        } catch (e) {
            // 继续搜索
        }
    }
    
    // === 提取数据头部 ===
    function extractDataHeader(headerAddr) {
        try {
            var headerData = headerAddr.readByteArray(32);
            var headerView = new Uint8Array(headerData);
            
            console.log("[Header Dump] 完整头部:");
            console.log(hexdump(headerData, {length: 32, ansi: false}));
            
            searchResults.rawHexDumps.push({
                address: headerAddr,
                type: "data_header",
                data: headerData,
                size: 32
            });
            
        } catch (e) {
            console.log("[Header Error] 头部提取失败: " + e);
        }
    }
    
    // === 辅助函数 ===
    function isValidCoordinate(value) {
        // 扩展的坐标范围检查
        if (isNaN(value) || !isFinite(value)) return false;
        
        // 中国境内坐标
        if ((value >= 70 && value <= 140) || (value >= 10 && value <= 60)) return true;
        
        // 相对坐标或像素坐标
        if (value >= 0 && value <= 10000) return true;
        
        // 墨卡托投影坐标
        if (Math.abs(value) >= 1000000 && Math.abs(value) <= 20000000) return true;
        
        return false;
    }
    
    function containsChinese(text) {
        return /[\u4e00-\u9fff]/.test(text);
    }
    
    function waitForLibrary(libraryName, callback) {
        var maxAttempts = 30;
        var attempt = 0;
        
        function checkLibrary() {
            try {
                var lib = Module.findBaseAddress(libraryName);
                if (lib) {
                    console.log("[Library] " + libraryName + " 已加载，基址: " + lib);
                    callback(lib);
                    return;
                }
            } catch (e) {
                // 继续等待
            }
            
            attempt++;
            if (attempt < maxAttempts) {
                setTimeout(checkLibrary, 1000);
            } else {
                console.log("[Error] " + libraryName + " 加载超时");
            }
        }
        
        checkLibrary();
    }
    
    // === 结果报告 ===
    function generateSearchReport() {
        console.log("\n=== 内存内容搜索报告 ===");
        console.log("发现的内存区域: " + searchResults.foundMemoryRegions.length);
        console.log("解析的矢量数据: " + searchResults.parsedVectorData.length);
        console.log("解析的POI数据: " + searchResults.parsedPoiData.length);
        console.log("原始数据转储: " + searchResults.rawHexDumps.length);
        
        if (searchResults.parsedVectorData.length > 0) {
            console.log("\n矢量数据样本:");
            for (var i = 0; i < Math.min(3, searchResults.parsedVectorData.length); i++) {
                var item = searchResults.parsedVectorData[i];
                console.log("  [" + i + "] " + item.address + " - " + item.type + " (" + item.validCount + " 有效坐标)");
            }
        }
        
        if (searchResults.parsedPoiData.length > 0) {
            console.log("\nPOI数据样本:");
            for (var j = 0; j < Math.min(3, searchResults.parsedPoiData.length); j++) {
                var poi = searchResults.parsedPoiData[j];
                console.log("  [" + j + "] " + poi.address + " - " + poi.type + " (长度: " + poi.length + ")");
            }
        }
        
        console.log("=========================\n");
    }
    
    // === 主入口 ===
    function main() {
        console.log("[Main] 等待应用初始化完成...");
        
        setTimeout(function() {
            console.log("[Main] 开始内存内容搜索...");
            
            try {
                // 立即开始搜索现有内存
                searchMemoryRegions();
                
                // 定期生成报告
                setInterval(function() {
                    generateSearchReport();
                }, 15000); // 每15秒生成报告
                
                console.log("[Memory Content Searcher] 内存内容搜索器已启动!");
                
            } catch (e) {
                console.log("[Error] 搜索器初始化失败: " + e);
            }
        }, CONFIG.INIT_DELAY);
    }
    
    // === 启动搜索器 ===
    try {
        Java.perform(function() {
            console.log("[Java] Java环境已准备就绪");
            main();
        });
    } catch (e) {
        console.log("[Error] Java环境初始化失败: " + e);
        main();
    }
    
})(); 