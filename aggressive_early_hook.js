/*
 * 高德地图激进早期Hook脚本
 * 在应用完全启动前设置所有Hook
 * 延迟启动策略确保Hook完全就绪
 * 版本: Frida 12.9.7 兼容
 */

console.log("[Aggressive Hook] 启动激进早期Hook脚本...");

var dataFound = 0;
var maxData = 3;
var isReady = false;

// 立即设置所有Hook，不等待延迟
function setupImmediateHooks() {
    console.log("[Immediate] 立即设置Hook...");
    
    try {
        // Hook 1: 文件读取 - 最基础最稳定
        var readPtr = Module.findExportByName("libc.so", "read");
        if (readPtr) {
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    this.buffer = args[1];
                    this.size = args[2].toInt32();
                    this.isTarget = (this.size > 500 && this.size < 20000);
                },
                onLeave: function(retval) {
                    if (this.isTarget && dataFound < maxData && isReady) {
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 0) {
                            console.log("[Early Read] 捕获文件读取: " + bytesRead + " 字节");
                            
                            // 尝试安全预览
                            try {
                                var preview = this.buffer.readByteArray(Math.min(64, bytesRead));
                                var bytes = new Uint8Array(preview);
                                var text = "";
                                
                                for (var i = 0; i < Math.min(32, bytes.length); i++) {
                                    if (bytes[i] >= 32 && bytes[i] < 127) {
                                        text += String.fromCharCode(bytes[i]);
                                    }
                                }
                                
                                console.log("[预览] " + text.substring(0, 20));
                                
                                if (text.indexOf('<?xml') >= 0 || 
                                    text.indexOf('{"') >= 0 || 
                                    text.indexOf('DICE') >= 0) {
                                    
                                    dataFound++;
                                    console.log("🎯 [发现 #" + dataFound + "] 目标数据类型!");
                                    
                                    // 尝试读取更多内容
                                    setTimeout(function() {
                                        try {
                                            var fullData = this.buffer.readByteArray(Math.min(512, bytesRead));
                                            var fullBytes = new Uint8Array(fullData);
                                            var fullText = "";
                                            
                                            for (var j = 0; j < fullBytes.length; j++) {
                                                if (fullBytes[j] >= 32 && fullBytes[j] < 127) {
                                                    fullText += String.fromCharCode(fullBytes[j]);
                                                } else if (fullBytes[j] === 10) {
                                                    fullText += "\n";
                                                } else {
                                                    fullText += ".";
                                                }
                                            }
                                            
                                            console.log("\n" + "=".repeat(60));
                                            console.log("📋 真实数据内容 #" + dataFound);
                                            console.log("=".repeat(60));
                                            console.log(fullText);
                                            console.log("=".repeat(60) + "\n");
                                            
                                        } catch (e) {
                                            console.log("[延迟读取失败] " + e.message);
                                        }
                                    }.bind(this), 50);
                                }
                                
                            } catch (e) {
                                console.log("[预览失败] " + e.message);
                            }
                        }
                    }
                }
            });
            console.log("✅ [Immediate] read() Hook设置成功");
        }
        
        // Hook 2: 监控库加载
        var dlopen = Module.findExportByName("libdl.so", "dlopen");
        if (dlopen) {
            Interceptor.attach(dlopen, {
                onEnter: function(args) {
                    if (args[0]) {
                        var libName = args[0].readCString();
                        if (libName && libName.indexOf('amap') >= 0) {
                            console.log("[Library Load] 加载库: " + libName);
                        }
                    }
                },
                onLeave: function(retval) {}
            });
            console.log("✅ [Immediate] dlopen() Hook设置成功");
        }
        
        // Hook 3: 内存分配监控
        var malloc = Module.findExportByName("libc.so", "malloc");
        if (malloc) {
            Interceptor.attach(malloc, {
                onEnter: function(args) {
                    this.size = args[0].toInt32();
                    this.isLarge = (this.size > 1000 && this.size < 10000);
                },
                onLeave: function(retval) {
                    if (this.isLarge && dataFound < maxData && isReady) {
                        console.log("[Memory] 分配大内存块: " + this.size + " 字节, 地址: " + retval);
                    }
                }
            });
            console.log("✅ [Immediate] malloc() Hook设置成功");
        }
        
        console.log("[Immediate] 基础Hook设置完成");
        
    } catch (e) {
        console.log("[Immediate Error] " + e.message);
    }
}

// 延迟设置库特定Hook
function setupLibraryHooks() {
    setTimeout(function() {
        console.log("[Library Hooks] 设置库特定Hook...");
        
        try {
            var lib = Module.findBaseAddress("libamapnsq.so");
            if (lib) {
                console.log("[Library] 找到libamapnsq.so: " + lib);
                
                // Hook SQLite函数
                try {
                    var sqliteAddr = lib.add(0x15000);
                    Interceptor.attach(sqliteAddr, {
                        onEnter: function(args) {
                            if (dataFound < maxData) {
                                console.log("🎯 [SQLite] girf_sqlite3_bind_blob调用");
                                try {
                                    var size = args[3].toInt32();
                                    console.log("  数据大小: " + size + " 字节");
                                    if (size > 100 && size < 5000) {
                                        console.log("  ✅ 可能的地图数据!");
                                    }
                                } catch (e) {}
                            }
                        }
                    });
                    console.log("✅ [Library] SQLite Hook设置成功");
                } catch (e) {
                    console.log("❌ [Library] SQLite Hook失败: " + e.message);
                }
                
            } else {
                console.log("[Library] 库尚未加载，继续等待...");
                // 递归等待
                setupLibraryHooks();
                return;
            }
            
            // 标记为就绪
            isReady = true;
            console.log("🚀 [Ready] 所有Hook就绪，开始监控数据流!");
            
        } catch (e) {
            console.log("[Library Error] " + e.message);
        }
    }, 1000);
}

// 数据流报告
function dataFlowReport() {
    console.log("\n📊 === 数据流监控报告 ===");
    console.log("就绪状态: " + (isReady ? "✅" : "❌"));
    console.log("发现数据: " + dataFound + "/" + maxData);
    
    if (dataFound >= maxData) {
        console.log("🎉 数据捕获完成!");
    } else if (dataFound > 0) {
        console.log("🎯 部分数据已捕获，继续监控...");
    } else if (isReady) {
        console.log("💡 Hook就绪，等待数据流...");
    } else {
        console.log("⏳ Hook准备中...");
    }
    console.log("============================\n");
}

// 立即开始设置Hook
setupImmediateHooks();

// 延迟设置库Hook
setupLibraryHooks();

// 定期报告
setInterval(dataFlowReport, 15000);

console.log("[Aggressive Hook] 激进早期Hook脚本已加载"); 