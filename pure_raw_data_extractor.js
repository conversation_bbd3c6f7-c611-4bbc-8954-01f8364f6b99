/*
 * 纯原生数据提取器 - 提取APP处理的完全未修改的原始数据
 * 基于IDA Pro精确定位Hook点，确保零数据修改
 * 目标：获取渲染前的纯原生数据，一个字节都不改变
 */

console.log("启动纯原生数据提取器");
console.log("目标：提取APP处理的完全未修改的原始数据");

// 原始数据收集器
var rawDataCollector = {
    totalCaptured: 0,
    maxCaptures: 20,  // 限制捕获数量
    capturedData: []
};

// 严格的数据保存函数 - 确保零修改
function saveRawDataToDisk(data, source, index) {
    try {
        // 直接保存原始字节，不进行任何转换
        var filename = "raw_data_" + source + "_" + index.toString().padStart(4, '0') + ".bin";
        
        console.log("保存原始数据:", filename, "大小:", data.byteLength, "字节");
        
        // 只显示前32字节的十六进制预览，不修改原数据
        if (data.byteLength >= 32) {
            var preview = new Uint8Array(data.slice(0, 32));
            var hex = Array.from(preview).map(function(b) { 
                return b.toString(16).padStart(2, '0'); 
            }).join(' ');
            console.log("   预览:", hex);
        }
        
        // 使用Java File API保存文件
        var JavaFile = Java.use("java.io.File");
        var JavaFileOutputStream = Java.use("java.io.FileOutputStream");
        
        Java.perform(function() {
            var file = JavaFile.$new(filename);
            var fos = JavaFileOutputStream.$new(file);
            
            // 将ArrayBuffer转换为Java字节数组
            var byteArray = Java.array('byte', Array.from(new Uint8Array(data)));
            fos.write(byteArray);
            fos.close();
        });
        
        return true;
    } catch (e) {
        console.log(" 保存失败:", e.message);
        
        // 备用保存方法：直接写入到内存中供后续分析
        try {
            rawDataCollector.capturedRawData = rawDataCollector.capturedRawData || [];
            rawDataCollector.capturedRawData.push({
                filename: filename,
                data: data,
                size: data.byteLength
            });
            console.log("备用保存到内存:", filename);
            return true;
        } catch (e2) {
            console.log("备用保存也失败:", e2.message);
        }
    }
    return false;
}

// Hook 1: 文件读取 - 捕获从磁盘读取的原始.ans数据
function hookFileRead() {
    console.log("Hook文件读取操作");
    
    var readPtr = Module.findExportByName("libc.so", "read");
    if (readPtr) {
        Interceptor.attach(readPtr, {
            onEnter: function(args) {
                this.fd = args[0].toInt32();
                this.buffer = args[1];
                this.size = args[2].toInt32();
            },
            onLeave: function(retval) {
                var bytesRead = retval.toInt32();
                
                if (bytesRead > 1000 && this.size > 1000 && rawDataCollector.totalCaptured < rawDataCollector.maxCaptures) {
                    try {
                        // 直接读取原始字节数据，不进行任何处理
                        var rawData = this.buffer.readByteArray(bytesRead);
                        
                        // 检查是否可能是.ans文件数据
                        var bytes = new Uint8Array(rawData);
                        var isAnsData = false;
                        
                        // 检查AM-zlib头
                        if (bytesRead >= 8) {
                            var headerText = "";
                            for (var i = 0; i < 8; i++) {
                                if (bytes[i] >= 32 && bytes[i] < 127) {
                                    headerText += String.fromCharCode(bytes[i]);
                                }
                            }
                            isAnsData = headerText.indexOf("AM-zlib") >= 0;
                        }
                        
                        // 检查zlib压缩数据
                        if (!isAnsData && bytesRead >= 2) {
                            isAnsData = (bytes[0] === 0x78 && bytes[1] === 0x9c);
                        }
                        
                        if (isAnsData) {
                            console.log(" 发现原始.ans数据，文件描述符:", this.fd, "大小:", bytesRead);
                            
                            // 保存完全原始的数据
                            if (saveRawDataToDisk(rawData, "file_read", rawDataCollector.totalCaptured)) {
                                rawDataCollector.capturedData.push({
                                    source: "file_read",
                                    index: rawDataCollector.totalCaptured,
                                    size: bytesRead,
                                    timestamp: Date.now()
                                });
                                rawDataCollector.totalCaptured++;
                            }
                        }
                    } catch (e) {
                        // 静默忽略读取错误
                    }
                }
            }
        });
    }
}

// Hook 2: zlib解压前 - 捕获解压前的原始压缩数据
function hookZlibBeforeDecompress() {
    console.log("Hook zlib解压前数据");
    
    var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
    if (uncompressPtr) {
        Interceptor.attach(uncompressPtr, {
            onEnter: function(args) {
                this.source = args[2];
                this.sourceLen = args[3].toInt32();
                
                if (this.sourceLen > 100 && this.sourceLen < 100000 && rawDataCollector.totalCaptured < rawDataCollector.maxCaptures) {
                    try {
                        // 读取压缩前的原始数据
                        var rawCompressedData = this.source.readByteArray(this.sourceLen);
                        
                        console.log(" 发现zlib压缩原始数据，大小:", this.sourceLen);
                        
                        // 保存压缩状态的原始数据
                        if (saveRawDataToDisk(rawCompressedData, "zlib_compressed", rawDataCollector.totalCaptured)) {
                            rawDataCollector.capturedData.push({
                                source: "zlib_compressed",
                                index: rawDataCollector.totalCaptured,
                                size: this.sourceLen,
                                timestamp: Date.now()
                            });
                            rawDataCollector.totalCaptured++;
                        }
                    } catch (e) {
                        // 静默忽略
                    }
                }
            }
        });
    }
}

// Hook 3: SQLite blob绑定前 - 捕获绑定到数据库前的原始数据
function hookSQLiteBlob() {
    console.log(" Hook SQLite blob绑定");
    
    // 等待libamapnsq.so加载
    var checkLibInterval = setInterval(function() {
        var libamapnsq = Process.findModuleByName("libamapnsq.so");
        if (libamapnsq) {
            clearInterval(checkLibInterval);
            
            try {
                // Hook实际的bind_blob函数
                var bindBlobAddr = libamapnsq.base.add(0x15000);
                console.log(" Hook girf_sqlite3_bind_blob at:", bindBlobAddr);
                
                Interceptor.attach(bindBlobAddr, {
                    onEnter: function(args) {
                        this.data = args[2];
                        this.size = args[3].toInt32();
                        
                        if (this.size > 100 && this.size < 50000 && rawDataCollector.totalCaptured < rawDataCollector.maxCaptures) {
                            try {
                                // 读取绑定前的原始数据
                                var rawData = this.data.readByteArray(this.size);
                                
                                console.log(" 发现SQLite绑定原始数据，大小:", this.size);
                                
                                // 保存绑定前的原始数据
                                if (saveRawDataToDisk(rawData, "sqlite_blob", rawDataCollector.totalCaptured)) {
                                    rawDataCollector.capturedData.push({
                                        source: "sqlite_blob",
                                        index: rawDataCollector.totalCaptured,
                                        size: this.size,
                                        timestamp: Date.now()
                                    });
                                    rawDataCollector.totalCaptured++;
                                }
                            } catch (e) {
                                // 静默忽略
                            }
                        }
                    }
                });
            } catch (e) {
                console.log(" Hook SQLite失败:", e.message);
            }
        }
    }, 100);
}

// 生成原始数据报告
function generateRawDataReport() {
    console.log("\n" + "=".repeat(80));
    console.log(" 纯原生数据提取报告");
    console.log("=".repeat(80));
    
    console.log(" 提取统计:");
    console.log("   总提取数量:", rawDataCollector.totalCaptured);
    
    // 按来源分组
    var bySource = {};
    rawDataCollector.capturedData.forEach(function(item) {
        bySource[item.source] = (bySource[item.source] || 0) + 1;
    });
    
    console.log(" 按来源统计:");
    Object.keys(bySource).forEach(function(source) {
        console.log("   " + source + ":", bySource[source], "个文件");
    });
    
    console.log("\n 重要说明:");
    console.log("    所有数据都是APP处理的原始字节，未经任何修改");
    console.log("    保存为.bin二进制文件，可用十六进制编辑器查看");
    console.log("    数据来源：文件读取、zlib压缩、SQLite绑定");
    console.log("    这些就是APP实际处理的纯原生数据");
}

// 主函数
function main() {
    console.log(" 启动纯原生数据提取");
    console.log(" 目标：零修改的原始数据提取");
    
    // 设置所有Hook点
    hookFileRead();
    hookZlibBeforeDecompress();
    hookSQLiteBlob();
    
    // 定期报告
    setInterval(function() {
        if (rawDataCollector.totalCaptured > 0) {
            generateRawDataReport();
        }
        
        if (rawDataCollector.totalCaptured >= rawDataCollector.maxCaptures) {
            console.log("🏁 已达到最大捕获数量，停止收集");
        }
    }, 15000);
    
    console.log(" 所有Hook已设置，等待原始数据...");
    console.log(" 在地图App中操作以触发数据处理");
    console.log(" 原始数据将保存为 raw_data_*.bin 文件");
}

// 启动
main(); 