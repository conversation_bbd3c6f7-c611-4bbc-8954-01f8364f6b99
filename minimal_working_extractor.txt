     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[Real Data Extractor] 启动解压后数据提取器...
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=000001E6E2988520)

Thread 0x000019f4 (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x00001520 (most recent call first):
<no Python frame>
