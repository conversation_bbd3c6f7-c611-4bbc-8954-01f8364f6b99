// 详细渲染流程分析脚本 - ES5兼容版本
// 追踪从离线数据读取到最终渲染的完整流程

console.log("[详细渲染流程] 开始分析离线地图渲染的详细步骤...");

// 渲染流程统计
var renderFlow = {
    // 数据读取阶段
    fileReads: 0,
    dataTypes: {
        diceAM: 0,      // DICE-AM矢量数据
        config: 0,      // bc bc配置数据  
        text: 0,        // 文本数据
        unknown: 0      // 未知数据
    },
    
    // 数据处理阶段
    decompressions: 0,
    dataProcessing: 0,
    
    // 渲染阶段
    glCalls: {
        drawArrays: 0,
        drawElements: 0,
        useProgram: 0,
        bindTexture: 0,
        uniformMatrix: 0
    },
    
    // 性能统计
    startTime: Date.now(),
    totalDataSize: 0,
    decompressedSize: 0
};

// 数据类型识别函数
function identifyDataType(header) {
    var view = new Uint8Array(header);
    
    if (view.length >= 4) {
        // DICE-AM矢量数据 (地图几何)
        if (view[0] === 0x44 && view[1] === 0x49 && view[2] === 0x43 && view[3] === 0x45) {
            return "DICE-AM";
        }
        // 配置数据 (渲染参数)
        if (view[0] === 0xbc && view[1] === 0xbc && view[2] === 0xbc && view[3] === 0xbc) {
            return "CONFIG";
        }
        // 文本数据 (地名标注)
        if (view[0] === 0x0d && view[1] === 0x00 && view[2] === 0x00 && view[3] === 0x00) {
            return "TEXT";
        }
        // 未知数据类型
        if (view[0] === 0xce && view[1] === 0xca && view[2] === 0x0b && view[3] === 0xb1) {
            return "UNKNOWN";
        }
        // zlib压缩头
        if (view[0] === 0x78 && view[1] === 0x9c) {
            return "ZLIB";
        }
        // AM-zlib容器
        if (view[0] === 0x08) {
            return "AM-ZLIB";
        }
    }
    return "OTHER";
}

// 分析数据内容
function analyzeDataContent(data, dataType, size) {
    console.log("[数据分析] 类型: " + dataType + ", 大小: " + size + " 字节");
    
    if (dataType === "DICE-AM") {
        // 分析DICE-AM矢量数据结构
        var view = new Uint8Array(data);
        if (view.length >= 16) {
            var version = view[8];
            var flags = view[9];
            console.log("  [DICE-AM] 版本: " + version + ", 标志: 0x" + flags.toString(16));
            console.log("  [DICE-AM] 可能包含: 道路/建筑/水域几何数据");
        }
        renderFlow.dataTypes.diceAM++;
        
    } else if (dataType === "CONFIG") {
        // 分析配置数据结构
        var view = new Uint8Array(data);
        if (view.length >= 16) {
            var configType = view[8];
            var configFlags = view[9];
            console.log("  [CONFIG] 配置类型: " + configType + ", 标志: 0x" + configFlags.toString(16));
            console.log("  [CONFIG] 可能包含: 颜色/样式/渲染参数");
        }
        renderFlow.dataTypes.config++;
        
    } else if (dataType === "TEXT") {
        // 分析文本数据
        console.log("  [TEXT] 可能包含: 地名/道路名/POI标注");
        renderFlow.dataTypes.text++;
        
    } else {
        renderFlow.dataTypes.unknown++;
    }
}

// 1. 验证库加载
var libz = null;
var libc = null;
var libGLESv2 = null;

try {
    libz = Process.getModuleByName("libz.so");
    console.log("[✓] libz.so 已加载");
} catch (e) {
    console.log("[✗] libz.so 未找到");
}

try {
    libc = Process.getModuleByName("libc.so");
    console.log("[✓] libc.so 已加载");
} catch (e) {
    console.log("[✗] libc.so 未找到");
}

try {
    libGLESv2 = Process.getModuleByName("libGLESv2.so");
    console.log("[✓] libGLESv2.so 已加载");
} catch (e) {
    console.log("[✗] libGLESv2.so 未找到");
}

// 2. Hook文件读取 - 第一阶段：数据获取
if (libc) {
    var readPtr = libc.getExportByName("read");
    if (readPtr) {
        Interceptor.attach(readPtr, {
            onEnter: function(args) {
                this.fd = args[0].toInt32();
                this.buf = args[1];
                this.count = args[2].toInt32();
            },
            onLeave: function(retval) {
                var bytesRead = retval.toInt32();
                if (bytesRead > 0 && this.count >= 8) {
                    try {
                        var header = this.buf.readByteArray(Math.min(8, bytesRead));
                        if (header) {
                            var dataType = identifyDataType(header);
                            
                            if (dataType === "AM-ZLIB" || dataType === "ZLIB") {
                                renderFlow.fileReads++;
                                renderFlow.totalDataSize += bytesRead;
                                
                                console.log("[阶段1-数据读取] #" + renderFlow.fileReads);
                                console.log("  文件描述符: " + this.fd);
                                console.log("  数据大小: " + bytesRead + " 字节");
                                console.log("  数据类型: " + dataType);
                                
                                // 分析读取模式
                                if (this.count === 4096) {
                                    console.log("  [读取模式] 标准4KB块 - 常规地图数据");
                                } else if (this.count === 8192) {
                                    console.log("  [读取模式] 标准8KB块 - 高密度数据");
                                } else if (this.count === 131072) {
                                    console.log("  [读取模式] 大块128KB - 批量预加载");
                                } else if (this.count < 1024) {
                                    console.log("  [读取模式] 小块压缩 - 精确数据");
                                }
                            }
                        }
                    } catch (e) {
                        // 忽略内存读取错误
                    }
                }
            }
        });
        console.log("[✓] 文件读取Hook设置成功");
    }
}

// 3. Hook zlib解压 - 第二阶段：数据解压和分析
if (libz) {
    var uncompressPtr = libz.getExportByName("uncompress");
    if (uncompressPtr) {
        Interceptor.attach(uncompressPtr, {
            onEnter: function(args) {
                this.dest = args[0];
                this.destLen = args[1];
                this.source = args[2];
                this.sourceLen = args[3];
            },
            onLeave: function(retval) {
                if (retval.toInt32() === 0) {
                    renderFlow.decompressions++;
                    var decompressedSize = this.destLen.readU32();
                    renderFlow.decompressedSize += decompressedSize;
                    
                    console.log("[阶段2-数据解压] #" + renderFlow.decompressions);
                    console.log("  解压后大小: " + decompressedSize + " 字节");
                    
                    // 分析解压后的数据
                    try {
                        var decompressedData = this.dest.readByteArray(Math.min(32, decompressedSize));
                        if (decompressedData) {
                            var dataType = identifyDataType(decompressedData);
                            console.log("  解压数据类型: " + dataType);
                            
                            // 详细分析数据内容
                            analyzeDataContent(decompressedData, dataType, decompressedSize);
                        }
                    } catch (e) {
                        console.log("  [警告] 解压数据分析失败: " + e.message);
                    }
                }
            }
        });
        console.log("[✓] zlib解压Hook设置成功");
    }
}

// 4. Hook OpenGL渲染 - 第三阶段：渲染管道
if (libGLESv2) {
    // Hook glDrawArrays - 顶点数组渲染
    var glDrawArraysPtr = libGLESv2.getExportByName("glDrawArrays");
    if (glDrawArraysPtr) {
        Interceptor.attach(glDrawArraysPtr, {
            onEnter: function(args) {
                renderFlow.glCalls.drawArrays++;
                var mode = args[0].toInt32();
                var first = args[1].toInt32();
                var count = args[2].toInt32();
                
                if (renderFlow.glCalls.drawArrays % 10 === 1) {
                    console.log("[阶段3-渲染] glDrawArrays #" + renderFlow.glCalls.drawArrays);
                    console.log("  渲染模式: 0x" + mode.toString(16));
                    console.log("  起始顶点: " + first);
                    console.log("  顶点数量: " + count);
                    
                    // 分析渲染模式
                    if (mode === 0x0004) {
                        console.log("  [渲染类型] GL_TRIANGLES - 三角形面片 (建筑/水域)");
                    } else if (mode === 0x0005) {
                        console.log("  [渲染类型] GL_TRIANGLE_STRIP - 三角形带 (道路)");
                    } else if (mode === 0x0001) {
                        console.log("  [渲染类型] GL_LINES - 线条 (道路边界)");
                    } else if (mode === 0x0003) {
                        console.log("  [渲染类型] GL_LINE_STRIP - 线条带 (轮廓)");
                    }
                }
            }
        });
    }
    
    // Hook glUseProgram - 着色器程序切换
    var glUseProgramPtr = libGLESv2.getExportByName("glUseProgram");
    if (glUseProgramPtr) {
        Interceptor.attach(glUseProgramPtr, {
            onEnter: function(args) {
                renderFlow.glCalls.useProgram++;
                var program = args[0].toInt32();
                
                if (renderFlow.glCalls.useProgram % 5 === 1) {
                    console.log("[阶段3-着色器] glUseProgram #" + renderFlow.glCalls.useProgram);
                    console.log("  着色器程序ID: " + program);
                    console.log("  [用途] 可能用于: 矢量/文本/纹理渲染");
                }
            }
        });
    }
    
    // Hook glBindTexture - 纹理绑定
    var glBindTexturePtr = libGLESv2.getExportByName("glBindTexture");
    if (glBindTexturePtr) {
        Interceptor.attach(glBindTexturePtr, {
            onEnter: function(args) {
                renderFlow.glCalls.bindTexture++;
                var target = args[0].toInt32();
                var texture = args[1].toInt32();
                
                if (renderFlow.glCalls.bindTexture % 10 === 1) {
                    console.log("[阶段3-纹理] glBindTexture #" + renderFlow.glCalls.bindTexture);
                    console.log("  纹理目标: 0x" + target.toString(16));
                    console.log("  纹理ID: " + texture);
                    console.log("  [用途] 可能用于: 地图瓦片/图标/字体");
                }
            }
        });
    }
    
    console.log("[✓] OpenGL渲染Hook设置成功");
}

// 5. 定期输出详细统计
setInterval(function() {
    var runtime = Math.floor((Date.now() - renderFlow.startTime) / 1000);
    var compressionRatio = renderFlow.totalDataSize > 0 ?
        (renderFlow.decompressedSize / renderFlow.totalDataSize).toFixed(2) : 0;

    console.log("\n[详细渲染流程统计] ==========================================");
    console.log("运行时间: " + runtime + "s");
    console.log("");
    console.log("阶段1 - 数据读取:");
    console.log("  文件读取次数: " + renderFlow.fileReads);
    console.log("  总数据大小: " + Math.floor(renderFlow.totalDataSize / 1024) + " KB");
    console.log("");
    console.log("阶段2 - 数据处理:");
    console.log("  解压操作: " + renderFlow.decompressions);
    console.log("  解压后大小: " + Math.floor(renderFlow.decompressedSize / 1024) + " KB");
    console.log("  解压比率: " + compressionRatio + ":1");
    console.log("  DICE-AM矢量: " + renderFlow.dataTypes.diceAM + " (几何数据)");
    console.log("  配置数据: " + renderFlow.dataTypes.config + " (样式参数)");
    console.log("  文本数据: " + renderFlow.dataTypes.text + " (地名标注)");
    console.log("  未知数据: " + renderFlow.dataTypes.unknown + " (索引/元数据)");
    console.log("");
    console.log("阶段3 - 渲染输出:");
    console.log("  顶点渲染: " + renderFlow.glCalls.drawArrays + " (几何绘制)");
    console.log("  着色器切换: " + renderFlow.glCalls.useProgram + " (渲染状态)");
    console.log("  纹理绑定: " + renderFlow.glCalls.bindTexture + " (材质资源)");
    console.log("");

    // 基于实际数据的性能分析
    if (renderFlow.decompressions > 0 && renderFlow.fileReads > 0) {
        var dataEfficiency = (renderFlow.decompressions / renderFlow.fileReads * 100).toFixed(1);
        console.log("性能分析:");
        console.log("  数据处理效率: " + dataEfficiency + "% (解压/读取比)");

        if (renderFlow.glCalls.useProgram > 0) {
            var renderEfficiency = (renderFlow.glCalls.drawArrays / renderFlow.glCalls.useProgram * 100).toFixed(1);
            console.log("  渲染效率: " + renderEfficiency + "% (绘制/着色器比)");
        }

        // 数据类型分布分析
        var totalDataTypes = renderFlow.dataTypes.diceAM + renderFlow.dataTypes.config +
                           renderFlow.dataTypes.text + renderFlow.dataTypes.unknown;
        if (totalDataTypes > 0) {
            console.log("  数据类型分布:");
            console.log("    几何数据: " + (renderFlow.dataTypes.diceAM / totalDataTypes * 100).toFixed(1) + "%");
            console.log("    配置数据: " + (renderFlow.dataTypes.config / totalDataTypes * 100).toFixed(1) + "%");
            console.log("    文本数据: " + (renderFlow.dataTypes.text / totalDataTypes * 100).toFixed(1) + "%");
            console.log("    其他数据: " + (renderFlow.dataTypes.unknown / totalDataTypes * 100).toFixed(1) + "%");
        }
    }
    console.log("===============================================\n");
}, 15000);

console.log("[详细渲染流程] 分析脚本已启动，等待地图操作...");
console.log("[提示] 请移动地图以触发完整的渲染流程分析");
