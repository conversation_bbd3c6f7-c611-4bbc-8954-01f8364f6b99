// 手势事件追踪脚本 - 从AMapController.addGestureMapMessage开始跟踪完整调用链

(function() {
    console.log("[手势调用链分析] 启动...");
    
    // 全局变量
    var callDepth = 0;
    var callStack = [];
    var libamapnsq = null;
    var libamapr = null;
    var libamapmain = null;
    
    // 延迟执行，确保应用完全启动
    setTimeout(function() {
        try {
            // 查找关键库
            libamapnsq = Process.findModuleByName("libamapnsq.so");
            libamapr = Process.findModuleByName("libamapr.so");
            libamapmain = Process.findModuleByName("libamapmain.so");
            
            if (libamapnsq && libamapr && libamapmain) {
                console.log("[手势调用链分析] 已找到关键库:");
                console.log("  libamapnsq.so: " + libamapnsq.base);
                console.log("  libamapr.so: " + libamapr.base);
                console.log("  libamapmain.so: " + libamapmain.base);
            }
            
            // 设置Java层钩子
            setupJavaHooks();
            
            // 设置JNI钩子
            setTimeout(function() {
                setupJNIHooks();
            }, 2000);
            
            // 设置Native层钩子
            setTimeout(function() {
                setupNativeHooks();
            }, 3000);
            
        } catch(e) {
            console.log("[手势调用链分析] 设置钩子失败: " + e);
        }
    }, 3000);
    
    // 获取缩进
    function getIndent(depth) {
        return "  ".repeat(depth);
    }
    
    // 设置Java层钩子
    function setupJavaHooks() {
        console.log("[手势调用链分析] 设置Java层钩子...");
        
        Java.perform(function() {
            try {
                // 1. 钩住AMapController.addGestureMapMessage方法 - 关键入口点
                var AMapController = Java.use("com.autonavi.ae.gmap.AMapController");
                if (AMapController.addGestureMapMessage) {
                    console.log("[Java] 找到AMapController.addGestureMapMessage方法");
                    
                    AMapController.addGestureMapMessage.implementation = function(engineId, gestureMessage) {
                        callDepth = 0;
                        callStack = [];
                        
                        console.log("\n[Java] ★★★ AMapController.addGestureMapMessage被调用 ★★★");
                        console.log("[Java] 参数: engineId=" + engineId + ", gestureMessage类型=" + gestureMessage.getClass().getName());
                        
                        // 记录调用信息
                        callStack.push({
                            layer: "Java",
                            class: "AMapController",
                            method: "addGestureMapMessage",
                            params: {
                                engineId: engineId,
                                messageType: gestureMessage ? gestureMessage.getType() : "unknown"
                            }
                        });
                        
                        // 分析手势类型
                        try {
                            if (gestureMessage) {
                                var type = gestureMessage.getType();
                                console.log("[Java] 手势类型: " + type);
                                
                                // 移动手势
                                if (type == 0) {
                                    try {
                                        var moveMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.MoveGestureMapMessage"));
                                        var dx = moveMsg.mTouchDeltaX.value;
                                        var dy = moveMsg.mTouchDeltaY.value;
                                        console.log("[Java] 移动手势: dx=" + dx + ", dy=" + dy);
                                    } catch(e) {}
                                }
                                // 缩放手势
                                else if (type == 1) {
                                    try {
                                        var scaleMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.ScaleGestureMapMessage"));
                                        var factor = scaleMsg.mScaleFactor.value;
                                        console.log("[Java] 缩放手势: factor=" + factor);
                                    } catch(e) {}
                                }
                            }
                        } catch(e) {
                            console.log("[Java] 分析手势类型失败: " + e);
                        }
                        
                        // 调用原方法
                        var result = this.addGestureMapMessage(engineId, gestureMessage);
                        
                        // 输出调用栈
                        setTimeout(function() {
                            console.log("\n[手势调用链分析] === 完整调用链 (" + callStack.length + " 个调用) ===");
                            for (var i = 0; i < callStack.length; i++) {
                                var call = callStack[i];
                                var indent = "  ".repeat(i);
                                console.log(indent + i + ": [" + call.layer + "] " + 
                                          (call.class ? call.class + "." : "") + 
                                          (call.method || call.function || ""));
                            }
                            console.log("[手势调用链分析] === 调用链结束 ===\n");
                        }, 500);
                        
                        return result;
                    };
                } else {
                    console.log("[Java] 未找到AMapController.addGestureMapMessage方法");
                }
                
                // 2. 钩住GLMapEngine.addGestureMessage方法
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine.addGestureMessage) {
                    console.log("[Java] 找到GLMapEngine.addGestureMessage方法");
                    
                    GLMapEngine.addGestureMessage.implementation = function(engineId, gestureMessage) {
                        callDepth++;
                        var indent = getIndent(callDepth);
                        
                        console.log(indent + "[Java] GLMapEngine.addGestureMessage被调用");
                        console.log(indent + "[Java] 参数: engineId=" + engineId);
                        
                        // 记录调用信息
                        callStack.push({
                            layer: "Java",
                            class: "GLMapEngine",
                            method: "addGestureMessage",
                            params: {
                                engineId: engineId,
                                messageType: gestureMessage ? gestureMessage.getType() : "unknown"
                            },
                            depth: callDepth
                        });
                        
                        var result = this.addGestureMessage(engineId, gestureMessage);
                        callDepth--;
                        
                        return result;
                    };
                }
                
                // 3. 钩住nativeAddMapGestureMsg静态方法
                if (GLMapEngine.nativeAddMapGestureMsg) {
                    console.log("[Java] 找到GLMapEngine.nativeAddMapGestureMsg方法");
                    
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                        callDepth++;
                        var indent = getIndent(callDepth);
                        
                        console.log(indent + "[JNI] GLMapEngine.nativeAddMapGestureMsg被调用");
                        console.log(indent + "[JNI] 参数: engineId=" + engineId + ", nativePtr=" + nativePtr + 
                                   ", type=" + type + ", param1=" + param1 + ", param2=" + param2 + 
                                   ", param3=" + param3 + ", param4=" + param4);
                        
                        // 记录调用信息
                        callStack.push({
                            layer: "JNI",
                            class: "GLMapEngine",
                            method: "nativeAddMapGestureMsg",
                            params: {
                                engineId: engineId,
                                nativePtr: nativePtr,
                                type: type,
                                param1: param1,
                                param2: param2,
                                param3: param3,
                                param4: param4
                            },
                            depth: callDepth
                        });
                        
                        var result = this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                        callDepth--;
                        
                        return result;
                    };
                }
                
                console.log("[Java] Java层钩子设置完成");
            } catch(e) {
                console.log("[Java] 设置Java钩子失败: " + e);
            }
        });
    }
    
    // 设置JNI钩子
    function setupJNIHooks() {
        console.log("[手势调用链分析] 设置JNI钩子...");
        
        try {
            // 在libamapmain.so中查找nativeAddMapGestureMsg函数
            if (libamapmain) {
                // 尝试查找可能的JNI导出函数
                var jniFunctions = [
                    "Java_com_autonavi_jni_ae_gmap_GLMapEngine_nativeAddMapGestureMsg"
                ];
                
                jniFunctions.forEach(function(funcName) {
                    var funcAddr = Module.findExportByName(libamapmain.name, funcName);
                    if (funcAddr) {
                        console.log("[JNI] 找到JNI函数: " + funcName + " @ " + funcAddr);
                        
                        Interceptor.attach(funcAddr, {
                            onEnter: function(args) {
                                callDepth++;
                                var indent = getIndent(callDepth);
                                
                                // JNI函数的参数: JNIEnv*, jobject, jint engineId, jlong nativePtr, jint type, jfloat param1, jfloat param2, jfloat param3, jint param4
                                console.log(indent + "[Native] " + funcName + "被调用");
                                console.log(indent + "[Native] 参数: engineId=" + args[2].toInt32() + 
                                           ", nativePtr=" + args[3].toString() + 
                                           ", type=" + args[4].toInt32() + 
                                           ", param1=" + args[5].toFloat() + 
                                           ", param2=" + args[6].toFloat() + 
                                           ", param3=" + args[7].toFloat() + 
                                           ", param4=" + args[8].toInt32());
                                
                                // 记录调用信息
                                callStack.push({
                                    layer: "Native",
                                    function: funcName,
                                    params: {
                                        engineId: args[2].toInt32(),
                                        nativePtr: args[3].toString(),
                                        type: args[4].toInt32(),
                                        param1: args[5].toFloat(),
                                        param2: args[6].toFloat(),
                                        param3: args[7].toFloat(),
                                        param4: args[8].toInt32()
                                    },
                                    depth: callDepth
                                });
                                
                                // 记录调用栈
                                var backtrace = Thread.backtrace(this.context, Backtracer.ACCURATE).slice(0, 8);
                                console.log(indent + "[Native] 调用栈:");
                                for (var i = 0; i < backtrace.length; i++) {
                                    var symbol = DebugSymbol.fromAddress(backtrace[i]);
                                    var module = Process.findModuleByAddress(backtrace[i]);
                                    console.log(indent + "  " + i + ": " + (symbol.name || backtrace[i]) + 
                                              (module ? " (" + module.name + ")" : ""));
                                }
                            },
                            onLeave: function(retval) {
                                var indent = getIndent(callDepth);
                                console.log(indent + "[Native] " + funcName + "返回: " + retval);
                                callDepth--;
                            }
                        });
                    }
                });
            }
            
            console.log("[JNI] JNI钩子设置完成");
        } catch(e) {
            console.log("[JNI] 设置JNI钩子失败: " + e);
        }
    }
    
    // 设置Native层钩子
    function setupNativeHooks() {
        console.log("[手势调用链分析] 设置Native层钩子...");
        
        try {
            // 在libamapr.so中查找可能处理手势的函数
            if (libamapr) {
                // 尝试查找可能的渲染相关函数
                var renderFunctions = [
                    "dice::CMapRenderSystem::init",
                    "dice::CAnAmapController::doRenderASync",
                    "dice::doEGLRequireMapRender_"
                ];
                
                renderFunctions.forEach(function(funcName) {
                    var funcAddr = Module.findExportByName(libamapr.name, funcName);
                    if (funcAddr) {
                        console.log("[Native] 找到渲染函数: " + funcName + " @ " + funcAddr);
                        
                        Interceptor.attach(funcAddr, {
                            onEnter: function(args) {
                                callDepth++;
                                var indent = getIndent(callDepth);
                                
                                console.log(indent + "[Native] " + funcName + "被调用");
                                
                                // 记录调用信息
                                callStack.push({
                                    layer: "Native",
                                    function: funcName,
                                    depth: callDepth
                                });
                                
                                // 记录调用栈
                                var backtrace = Thread.backtrace(this.context, Backtracer.ACCURATE).slice(0, 5);
                                console.log(indent + "[Native] 调用栈:");
                                for (var i = 0; i < backtrace.length; i++) {
                                    var symbol = DebugSymbol.fromAddress(backtrace[i]);
                                    var module = Process.findModuleByAddress(backtrace[i]);
                                    console.log(indent + "  " + i + ": " + (symbol.name || backtrace[i]) + 
                                              (module ? " (" + module.name + ")" : ""));
                                }
                            },
                            onLeave: function(retval) {
                                var indent = getIndent(callDepth);
                                console.log(indent + "[Native] " + funcName + "返回: " + retval);
                                callDepth--;
                            }
                        });
                    }
                });
                
                // 尝试在libamapr.so中查找可能的手势处理函数
                if (libamapr.base) {
                    // 在内存中搜索可能的函数特征
                    console.log("[Native] 搜索可能的手势处理函数...");
                    
                    // 这里我们使用一个保守的方法，只搜索一小段内存范围
                    var searchRange = 1024 * 1024; // 搜索前1MB内存
                    
                    // 在libamapr.so中搜索可能处理手势的函数
                    try {
                        Memory.scan(libamapr.base, searchRange, "47 65 73 74 75 72 65", {  // "Gesture"的ASCII码
                            onMatch: function(address, size) {
                                console.log("[Native] 找到可能的手势相关数据: " + address);
                                
                                // 尝试查找附近的函数
                                var startAddr = address.sub(0x100);
                                var endAddr = address.add(0x100);
                                
                                // 尝试在这个范围内找到函数开始
                                try {
                                    for (var addr = startAddr; addr.compare(endAddr) < 0; addr = addr.add(4)) {
                                        // ARM64函数通常以特定指令开始
                                        var value = Memory.readU32(addr);
                                        if ((value & 0xFFC003FF) === 0xD10003FF) { // STP x29, x30, [sp, #imm]
                                            console.log("[Native] 可能的函数入口点: " + addr);
                                            
                                            // 钩住这个可能的函数
                                            Interceptor.attach(addr, {
                                                onEnter: function(args) {
                                                    callDepth++;
                                                    var indent = getIndent(callDepth);
                                                    
                                                    console.log(indent + "[Native] 可能的手势处理函数被调用: " + addr);
                                                    
                                                    // 记录调用信息
                                                    callStack.push({
                                                        layer: "Native",
                                                        function: "可能的手势处理函数@" + addr,
                                                        depth: callDepth
                                                    });
                                                    
                                                    // 记录调用栈
                                                    var backtrace = Thread.backtrace(this.context, Backtracer.ACCURATE).slice(0, 5);
                                                    console.log(indent + "[Native] 调用栈:");
                                                    for (var i = 0; i < backtrace.length; i++) {
                                                        var symbol = DebugSymbol.fromAddress(backtrace[i]);
                                                        var module = Process.findModuleByAddress(backtrace[i]);
                                                        console.log(indent + "  " + i + ": " + (symbol.name || backtrace[i]) + 
                                                                  (module ? " (" + module.name + ")" : ""));
                                                    }
                                                },
                                                onLeave: function(retval) {
                                                    var indent = getIndent(callDepth);
                                                    console.log(indent + "[Native] 可能的手势处理函数返回: " + retval);
                                                    callDepth--;
                                                }
                                            });
                                        }
                                    }
                                } catch(e) {
                                    console.log("[Native] 搜索函数入口点时出错: " + e);
                                }
                                
                                return "continue";
                            },
                            onError: function(reason) {
                                console.log("[Native] 内存扫描错误: " + reason);
                            },
                            onComplete: function() {
                                console.log("[Native] 内存扫描完成");
                            }
                        });
                    } catch(e) {
                        console.log("[Native] 内存扫描失败: " + e);
                    }
                }
            }
            
            // 在libamapnsq.so中钩住ANS文件解析函数
            if (libamapnsq) {
                var parserFuncAddr = libamapnsq.base.add(0xC654);
                console.log("[Native] 钩住ANS文件解析函数: " + parserFuncAddr);
                
                Interceptor.attach(parserFuncAddr, {
                    onEnter: function(args) {
                        callDepth++;
                        var indent = getIndent(callDepth);
                        
                        console.log(indent + "[Native] ANS文件解析函数sub_C654被调用");
                        
                        // 记录调用信息
                        callStack.push({
                            layer: "Native",
                            function: "sub_C654 (ANS文件解析)",
                            depth: callDepth
                        });
                        
                        // 记录调用栈
                        var backtrace = Thread.backtrace(this.context, Backtracer.ACCURATE).slice(0, 5);
                        console.log(indent + "[Native] 调用栈:");
                        for (var i = 0; i < backtrace.length; i++) {
                            var symbol = DebugSymbol.fromAddress(backtrace[i]);
                            var module = Process.findModuleByAddress(backtrace[i]);
                            console.log(indent + "  " + i + ": " + (symbol.name || backtrace[i]) + 
                                      (module ? " (" + module.name + ")" : ""));
                        }
                    },
                    onLeave: function(retval) {
                        var indent = getIndent(callDepth);
                        console.log(indent + "[Native] ANS文件解析函数sub_C654返回: " + retval);
                        callDepth--;
                    }
                });
            }
            
            console.log("[Native] Native层钩子设置完成");
        } catch(e) {
            console.log("[Native] 设置Native钩子失败: " + e);
        }
    }
    
    console.log("[手势调用链分析] 脚本设置完成，等待手势操作...");
})();
