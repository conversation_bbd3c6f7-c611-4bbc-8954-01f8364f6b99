function hookTest8(){
    Java.perform(function(){
        var Demo = Java.use("tgo.ngo.mockgps.ui.MainActivity");
        //getDeclaredMethods枚举所有方法
        var methods =Demo.class.getDeclaredMethods();
        console.log("开始进行hook")
        for(var j=0; j < methods.length; j++){
            var methodName = methods[j].getName();
            console.log(methodName);
            for(var k=0; k<Demo[methodName].overloads.length;k++){
                Demo[methodName].overloads[k].implementation = function(){
                    for(var i=0;i<arguments.length;i++){
                        console.log(arguments[i]);
                    }
                    return this[methodName].apply(this,arguments);
                }
            }
        }
    })
}
function main(){
    Java.perform(function(){
        hookTest8();
    });
}
setImmediate(main);