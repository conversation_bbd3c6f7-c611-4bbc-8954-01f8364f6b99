(function() {
  console.log("[ANS文件分析器-精简版] 启动");
  
  // 全局变量
  var ansFiles = {};
  
  // 只监控文件操作，不干扰程序执行
  var open_ptr = Module.findExportByName("libc.so", "open");
  if (open_ptr) {
    Interceptor.attach(open_ptr, {
      onEnter: function(args) {
        try {
          var path = args[0].readUtf8String();
          if (path && path.indexOf(".ans") !== -1) {
            this.path = path;
            this.isAnsFile = true;
            
            // 特别关注m1.ans
            if (path.indexOf("m1.ans") !== -1) {
              this.isM1Ans = true;
            }
          }
        } catch(e) {}
      },
      onLeave: function(retval) {
        if (!this.isAnsFile) return;
        
        var fd = retval.toInt32();
        if (fd > 0) {
          // 仅记录文件信息
          ansFiles[fd] = {
            path: this.path,
            filename: this.path.split("/").pop()
          };
          
          if (this.isM1Ans) {
            console.log("[重要] m1.ans文件打开: " + this.path + " (fd: " + fd + ")");
          } else {
            console.log("[ANS文件] " + this.path.split("/").pop() + " (fd: " + fd + ")");
          }
        }
      }
    });
  }
  
  // 监控so库加载
  Interceptor.attach(Module.findExportByName(null, "dlopen"), {
    onEnter: function(args) {
      try {
        this.path = args[0].readUtf8String();
      } catch(e) {}
    },
    onLeave: function(retval) {
      if (this.path && (this.path.indexOf("libamaploc.so") !== -1 || 
                        this.path.indexOf("libamapnsq.so") !== -1)) {
        console.log("[库加载] " + this.path);
      }
    }
  });
  
  // 不使用Java层监控，避免干扰应用
  
  console.log("[ANS文件分析器-精简版] 初始化完成，等待文件操作...");
})();
