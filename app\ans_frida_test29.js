(function() {
  console.log("[ANS解析] 开始监控ANS文件解析...");
  
  // 全局变量
  var ansFileDescriptors = {};
  var lastGestureTime = 0;
  var decompressData = {};
  var fileContents = {};
  
  // 监控文件打开
  var openPtr = Module.findExportByName("libc.so", "open");
  if (openPtr) {
    Interceptor.attach(openPtr, {
      onEnter: function(args) {
        try {
          this.path = args[0].readUtf8String();
          if (this.path && (this.path.indexOf("m1.ans") !== -1 || this.path.indexOf("m3.ans") !== -1)) {
            console.log("[ANS解析] 打开文件: " + this.path);
          }
        } catch(e) {}
      },
      onLeave: function(result) {
        if (this.path && (this.path.indexOf("m1.ans") !== -1 || this.path.indexOf("m3.ans") !== -1)) {
          var fd = result.toInt32();
          if (fd > 0) {
            console.log("[ANS解析] 文件打开成功: fd=" + fd);
            ansFileDescriptors[fd] = this.path;
            fileContents[this.path] = {
              blocks: [],
              header: null
            };
          }
        }
      }
    });
  }
  
  // 监控文件读取
  var readPtr = Module.findExportByName("libc.so", "read");
  if (readPtr) {
    Interceptor.attach(readPtr, {
      onEnter: function(args) {
        var fd = args[0].toInt32();
        if (ansFileDescriptors[fd]) {
          this.fd = fd;
          this.buffer = args[1];
          this.size = args[2].toInt32();
          this.ansFilePath = ansFileDescriptors[fd];
        }
      },
      onLeave: function(result) {
        if (this.fd && result.toInt32() > 0) {
          var bytesRead = result.toInt32();
          console.log("[ANS解析] 读取数据: " + this.ansFilePath + 
                     " (" + bytesRead + "/" + this.size + " 字节)");
          
          // 保存文件头部内容
          if (bytesRead == 16 && fileContents[this.ansFilePath] && !fileContents[this.ansFilePath].header) {
            try {
              console.log(hexdump(this.buffer, {length: bytesRead}));
              fileContents[this.ansFilePath].header = new ArrayBuffer(bytesRead);
              var headerData = new Uint8Array(fileContents[this.ansFilePath].header);
              var readData = new Uint8Array(Memory.readByteArray(this.buffer, bytesRead));
              for (var i = 0; i < bytesRead; i++) {
                headerData[i] = readData[i];
              }
              console.log("[ANS解析] 保存文件头部成功");
            } catch(e) {
              console.log("[ANS解析] 保存文件头部失败: " + e);
            }
          }
          
          // 保存重要数据块
          if (bytesRead >= 345 && bytesRead <= 500) {
            try {
              console.log("[ANS解析] 保存重要数据块: " + bytesRead + "字节");
              var blockData = {
                size: bytesRead,
                offset: fileContents[this.ansFilePath].blocks.length,
                data: new ArrayBuffer(bytesRead)
              };
              var destData = new Uint8Array(blockData.data);
              var sourceData = new Uint8Array(Memory.readByteArray(this.buffer, bytesRead));
              for (var i = 0; i < bytesRead; i++) {
                destData[i] = sourceData[i];
              }
              fileContents[this.ansFilePath].blocks.push(blockData);
            } catch(e) {
              console.log("[ANS解析] 保存数据块失败: " + e);
            }
          }
        }
      }
    });
  }
  
  // 监控LZ4解压函数
  var targetModules = ["libamapmain.so", "libamapr.so", "libamapnsq.so"];
  for (var i = 0; i < targetModules.length; i++) {
    var moduleName = targetModules[i];
    var lz4Ptr = Module.findExportByName(moduleName, "LZ4_decompress_safe");
    if (lz4Ptr) {
      Interceptor.attach(lz4Ptr, {
        onEnter: function(args) {
          this.src = args[0];
          this.dest = args[1];
          this.srcSize = args[2].toInt32();
          this.destMaxSize = args[3].toInt32();
          
          // 只记录较大的解压操作
          if (this.srcSize > 1000) {
            this.record = true;
          }
        },
        onLeave: function(result) {
          if (this.record && result.toInt32() > 0) {
            var decompressedSize = result.toInt32();
            console.log("[ANS解析] 解压数据: " + 
                       "压缩=" + this.srcSize + 
                       ", 解压=" + decompressedSize + 
                       ", 比例=" + (decompressedSize/this.srcSize).toFixed(2));
            
            // 保存解压数据摘要
            if (decompressedSize > 10000) {
              try {
                // 显示解压后数据的前32字节
                console.log("[ANS解析] 解压后数据头部:");
                console.log(hexdump(this.dest, {length: 32}));
                
                // 记录解压后数据的特征
                var dataId = "decomp_" + Date.now();
                decompressData[dataId] = {
                  srcSize: this.srcSize,
                  destSize: decompressedSize,
                  timestamp: Date.now(),
                  timeSinceGesture: Date.now() - lastGestureTime
                };
              } catch(e) {}
            }
          }
        }
      });
      console.log("[ANS解析] 成功Hook " + moduleName + "!LZ4_decompress_safe");
    }
  }
  
  // 监控mmap操作
  var mmapPtr = Module.findExportByName("libc.so", "mmap");
  if (mmapPtr) {
    Interceptor.attach(mmapPtr, {
      onEnter: function(args) {
        this.size = args[1].toInt32();
        this.fd = args[4].toInt32();
        if (ansFileDescriptors[this.fd]) {
          this.ansFile = true;
          this.path = ansFileDescriptors[this.fd];
        }
      },
      onLeave: function(result) {
        if (this.ansFile && !result.isNull()) {
          console.log("[ANS解析] 内存映射文件: " + this.path + 
                     ", 大小: " + this.size + 
                     ", 地址: " + result);
          
          // 显示映射区域的前32字节
          if (this.size > 32) {
            console.log(hexdump(result, {length: 32}));
          }
        }
      }
    });
  }
  
  // 监控手势
  setTimeout(function() {
    Java.perform(function() {
      try {
        var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
        if (GLMapEngine.addGestureMessage) {
          GLMapEngine.addGestureMessage.implementation = function(engineId, gestureMessage) {
            var type = gestureMessage.getType();
            if (type == 0) { // 移动手势
              try {
                var moveMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.MoveGestureMapMessage"));
                var dx = moveMsg.mTouchDeltaX.value;
                var dy = moveMsg.mTouchDeltaY.value;
                console.log("[ANS解析] 地图手势: 移动 dx=" + dx + ", dy=" + dy);
                lastGestureTime = Date.now();
              } catch(e) {}
            }
            return this.addGestureMessage(engineId, gestureMessage);
          };
        }
      } catch(e) {
        console.log("[ANS解析] 手势监控错误: " + e);
      }
    });
  }, 2000);
  
  console.log("[ANS解析] 设置完成");
  console.log("[ANS解析] 使用提示: 要查看m3.ans，请启动导航功能");
})();
