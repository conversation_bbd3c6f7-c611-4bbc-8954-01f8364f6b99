/*
 * 高德地图 数据追踪器（IDA + Frida 最终验证）
 *
 * 目的：精准找出“谁”在读取 zlib 解压后的原始数据块，为 IDA 中的函数定位提供确证依据。
 *
 * 方法：内存访问监控（MemoryAccessMonitor）
 *   1. Hook libz.so:uncompress，拿到解压后缓冲区地址与大小。
 *   2. 对该内存范围启用只读监控；首个读取该内存的指令即为真实消费者。
 *   3. 打印模块名、函数名、模块基址与偏移，便于在 IDA 中一键定位。
 *
 * 语法标准：ES5（兼容 Frida 12.9.7）
 */

console.log("[启动] 高德 数据追踪器 已启动（最终验证脚本）");

var isMonitoring = false;
var monitorTimer = null;

function toHexPreview(ptr, size) {
	try {
		var arr = ptr.readByteArray(Math.min(size, 32));
		var bytes = new Uint8Array(arr);
		var hex = "";
		for (var i = 0; i < bytes.length; i++) {
			var b = bytes[i].toString(16);
			if (b.length === 1) b = "0" + b;
			hex += b + (i + 1 < bytes.length ? " " : "");
		}
		return hex;
	} catch (e) {
		return "<预览不可用>";
	}
}

function hook_zlib_uncompress() {
	var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
	if (!uncompressPtr) {
		console.log("[错误] 未在 libz.so 中找到 'uncompress' 符号，脚本退出。");
		return;
	}
	console.log("[成功] 已 Hook libz.so:uncompress 地址=" + uncompressPtr);

	Interceptor.attach(uncompressPtr, {
		onEnter: function(args) {
			// int uncompress(Bytef *dest, uLongf *destLen, const Bytef *source, uLong sourceLen)
			this.destPtr = args[0];
			this.destLenPtr = args[1];
			this.srcPtr = args[2];
			this.srcLenVal = args[3].toUInt32 ? args[3].toUInt32() : args[3].toInt32();
		},
		onLeave: function(retval) {
			// Z_OK == 0
			if (retval.toInt32() !== 0) return;
			if (!this.destPtr || !this.destLenPtr) return;
			if (isMonitoring) return; // 防止重入

			var size = 0;
			try { size = this.destLenPtr.readU32(); } catch (e) { return; }
			if (size <= 0 || size > 64 * 1024 * 1024) return; // 合理性保护

			console.log("\n[解压完成] 数据块大小=" + size + " 字节");
			console.log("   - 目标缓冲区: " + this.destPtr + ", 头部预览(32B Hex): " + toHexPreview(this.destPtr, size));

			try {
				isMonitoring = true;
				console.log("   - [追踪器] 启用 MemoryAccessMonitor（只读监控该数据块）...");

				MemoryAccessMonitor.enable({ base: this.destPtr, size: size }, {
					onAccess: function(details) {
						console.log("\n==================================================");
						console.log("[命中] 发现对解压后数据块的内存读取！");
						var sym = DebugSymbol.fromAddress(details.from);
						var modName = sym && sym.moduleName ? sym.moduleName : "<未知模块>";
						var modBase = Module.findBaseAddress(modName);
						var addrStr = sym && sym.address ? ("" + sym.address) : ("" + details.from);
						var funcName = sym && sym.name ? sym.name : "<无符号>";
						var offsetStr = modBase ? ("0x" + ptr(details.from).sub(modBase).toString(16)) : "<未知偏移>";
						console.log("- 模块: " + modName);
						console.log("- 模块基址: " + (modBase ? modBase : "<未知>") );
						console.log("- 访问地址: " + addrStr + "  (模块内偏移: " + offsetStr + ")");
						console.log("- 符号: " + funcName);
						console.log("- 访问类型: " + details.operation + ", 大小: " + details.size);
						console.log("==================================================");

						// 命中一次即关闭，避免刷屏
						try { MemoryAccessMonitor.disable(); } catch (_) {}
						isMonitoring = false;
						if (monitorTimer) { try { clearTimeout(monitorTimer); } catch (_) {} monitorTimer = null; }
					}
				});

				// 安全超时：5秒无命中则关闭监控
				monitorTimer = setTimeout(function() {
					if (isMonitoring) {
						try { MemoryAccessMonitor.disable(); } catch (_) {}
						isMonitoring = false;
						console.log("[追踪器] 监控超时，已自动关闭。");
					}
				}, 5000);
			} catch (e) {
				console.log("[错误] 启用 MemoryAccessMonitor 失败: " + e.message);
				try { MemoryAccessMonitor.disable(); } catch (_) {}
				isMonitoring = false;
				if (monitorTimer) { try { clearTimeout(monitorTimer); } catch (_) {} monitorTimer = null; }
			}
		}
	});
}

setImmediate(function() {
	hook_zlib_uncompress();
});

console.log("[就绪] 数据追踪器已部署，请在地图中进行缩放/平移以触发数据加载。"); 