// 高德地图 .so 模块分析与 JNI 调用追踪脚本
// 适用于 Frida 12.9.7，使用 ES5 语法
// 分析 nativeAddMapGestureMsg 执行流程

(function() {
    'use strict';

    // 全局配置
    var config = {
        debug: false,                  // 调试模式开关
        logStackTrace: true,           // 是否记录调用栈
        logRegisteredMethods: true,    // 是否记录注册的方法
        hookCrossSoCalls: true,        // 是否钩住跨 so 调用
        hookFileOperations: false,     // 是否钩住文件操作
        targetSoNames: [               // 目标 so 文件名列表
            "libamapr.so",             // 高德地图主要渲染引擎
            "libamapnsq.so",           // ANS 文件解析模块
            "libnative-lib.so",        // 示例目标1
            "libcrypto.so"             // 示例目标2
        ],
        systemSoNames: [               // 系统库名称列表
            "libc.so", 
            "libdl.so", 
            "libm.so", 
            "libart.so",
            "liblog.so"
        ]
    };

    // 全局变量
    var gJNIEnv = null;                // JNIEnv 指针
    var gRegisteredMethods = {};       // 已注册的 JNI 方法 {类名: {方法名: {签名, 地址, so文件}}}
    var gModuleMap = {};               // 模块映射 {模块名: {base, size, path}}
    var gAddressModuleMap = {};        // 地址到模块的映射 {地址范围: 模块名}
    var gHookedFunctions = {};         // 已钩住的函数 {地址: true}
    var gCallGraph = {};               // 调用图 {调用者地址: [被调用者地址]}
    var gLastNativeCall = null;        // 最后一次本地调用的信息

    // 工具函数 - 字符串填充
    function padString(str, length, padChar) {
        str = String(str);
        padChar = padChar || ' ';
        var padding = '';
        for (var i = str.length; i < length; i++) {
            padding += padChar;
        }
        return str + padding;
    }

    // 工具函数 - 格式化输出
    function log(message) {
        console.log("[+] " + message);
    }

    function logError(message) {
        console.log("[-] " + message);
    }

    function logDebug(message) {
        if (config.debug) {
            console.log("[DEBUG] " + message);
        }
    }

    // 工具函数 - 地址格式化
    function formatAddress(address) {
        if (!address) return "0x0";
        return "0x" + address.toString(16);
    }

    // 工具函数 - 获取模块信息
    function getModuleByAddress(address) {
        var module = Process.findModuleByAddress(address);
        if (module) {
            return module;
        }
        return null;
    }

    // 工具函数 - 格式化地址为 模块+偏移 格式
    function formatAddressWithModule(address) {
        if (!address || address.isNull()) return "0x0";
        
        try {
            var module = getModuleByAddress(address);
            if (module) {
                var offset = address.sub(module.base);
                return module.name + "!" + formatAddress(offset);
            }
        } catch (e) {
            logError("格式化地址失败: " + e);
        }
        return formatAddress(address);
    }

    // 工具函数 - 获取调用栈
    function getStackTrace(context) {
        if (!config.logStackTrace) return "";
        if (!context) return "\n无法获取调用栈: 上下文为空";
        
        try {
            var trace = Thread.backtrace(context, Backtracer.ACCURATE)
                .map(function(addr) {
                    if (!addr || addr.isNull()) return "0x0";
                    return formatAddressWithModule(addr);
                });
            return "\n调用栈:\n" + trace.join("\n");
        } catch (e) {
            return "\n获取调用栈失败: " + e;
        }
    }

    // 工具函数 - 判断地址属于哪个 .so
    function findModuleForAddress(address) {
        if (!address || address.isNull()) return null;
        
        try {
            var module = Process.findModuleByAddress(address);
            if (module) {
                return {
                    name: module.name,
                    base: module.base,
                    path: module.path,
                    isSystemLib: config.systemSoNames.indexOf(module.name) !== -1
                };
            }
        } catch (e) {
            logDebug("获取地址模块信息失败: " + e);
        }
        return null;
    }

    // 1. 枚举所有已加载模块
    function enumerateLoadedModules() {
        log("开始枚举所有已加载模块...");
        
        var modules = Process.enumerateModules();
        var moduleTable = new Array(modules.length);
        
        log("共发现 " + modules.length + " 个已加载模块");
        log("┌─────────────────────────┬────────────────────┬───────────────────────────────────────────────┬──────────┐");
        log("│ 模块名称                │ 基地址             │ 文件路径                                      │ 系统库   │");
        log("├─────────────────────────┼────────────────────┼───────────────────────────────────────────────┼──────────┤");
        
        for (var i = 0; i < modules.length; i++) {
            var module = modules[i];
            var isSystemLib = config.systemSoNames.indexOf(module.name) !== -1;
            
            // 存储模块信息
            gModuleMap[module.name] = {
                base: module.base,
                size: module.size,
                path: module.path
            };
            
            // 存储地址范围到模块的映射
            var endAddress = module.base.add(module.size);
            gAddressModuleMap[module.base + "-" + endAddress] = module.name;
            
            // 格式化输出
            var name = module.name;
            if (name.length > 23) {
                name = name.substr(0, 20) + "...";
            } else {
                name = padString(name, 23);
            }
            
            var base = padString(formatAddress(module.base), 20);
            
            var path = module.path;
            if (path.length > 45) {
                path = path.substr(0, 42) + "...";
            } else {
                path = padString(path, 45);
            }
            
            var system = isSystemLib ? padString("是", 10) : padString("否", 10);
            
            log("│ " + name + " │ " + base + " │ " + path + " │ " + system + " │");
        }
        
        log("└─────────────────────────┴────────────────────┴───────────────────────────────────────────────┴──────────┘");
    }

    // 2. 监控 RegisterNatives 调用
    function hookRegisterNatives() {
        log("开始监控 RegisterNatives 调用...");
        
        var RegisterNatives = null;
        
        // 获取 JNIEnv 的 RegisterNatives 函数指针
        var getJNIEnv = function() {
            if (gJNIEnv === null) {
                Java.perform(function() {
                    var env = Java.vm.getEnv();
                    if (env) {
                        gJNIEnv = env.handle;
                        logDebug("获取到 JNIEnv: " + gJNIEnv);
                    }
                });
            }
            return gJNIEnv;
        };
        
        try {
            // 获取 JNIEnv
            var env = getJNIEnv();
            if (!env) {
                logError("无法获取 JNIEnv");
                return;
            }
            
            // 获取 JNIEnv->functions 指针
            var envVtable = Memory.readPointer(env);
            
            // RegisterNatives 在 JNIEnv 函数表中的偏移量通常是 215
            var registerNativesOffset = 215 * Process.pointerSize;
            var registerNativesPtr = Memory.readPointer(envVtable.add(registerNativesOffset));
            
            log("找到 RegisterNatives 函数指针: " + formatAddress(registerNativesPtr));
            
            // 钩住 RegisterNatives 函数
            Interceptor.attach(registerNativesPtr, {
                onEnter: function(args) {
                    this.jclass = args[1];
                    this.methods = args[2];
                    this.nMethods = args[3].toInt32();
                    
                    // 获取类名
                    var className = null;
                    Java.perform(function() {
                        try {
                            var jclassObject = Java.cast(ptr(args[1]), Java.use("java.lang.Class"));
                            className = jclassObject.getName();
                        } catch (e) {
                            logError("获取类名失败: " + e);
                        }
                    });
                    
                    this.className = className || "未知类";
                    
                    log("RegisterNatives 被调用: 类 = " + this.className + ", 方法数 = " + this.nMethods);
                    
                    // 获取调用者模块信息
                    this.callerModule = findModuleForAddress(this.returnAddress);
                    var callerInfo = this.callerModule ? 
                        "来自 " + this.callerModule.name : 
                        "未知来源";
                    
                    log("RegisterNatives 调用者: " + callerInfo);
                    
                    // 遍历方法表
                    for (var i = 0; i < this.nMethods; i++) {
                        var methodsStructSize = 3 * Process.pointerSize;
                        var methodNamePtr = Memory.readPointer(this.methods.add(i * methodsStructSize));
                        var methodSigPtr = Memory.readPointer(this.methods.add(i * methodsStructSize + Process.pointerSize));
                        var methodFnPtr = Memory.readPointer(this.methods.add(i * methodsStructSize + 2 * Process.pointerSize));
                        
                        try {
                            var methodName = Memory.readUtf8String(methodNamePtr);
                            var methodSig = Memory.readUtf8String(methodSigPtr);
                            
                            // 获取函数所在的模块
                            var methodModule = findModuleForAddress(methodFnPtr);
                            var moduleInfo = methodModule ? 
                                methodModule.name : 
                                "未知模块";
                            
                            log("  注册方法 #" + i + ": " + methodName + " " + methodSig + 
                                " @ " + formatAddressWithModule(methodFnPtr) + 
                                " [" + moduleInfo + "]");
                            
                            // 存储注册的方法信息
                            if (!gRegisteredMethods[this.className]) {
                                gRegisteredMethods[this.className] = {};
                            }
                            
                            gRegisteredMethods[this.className][methodName] = {
                                signature: methodSig,
                                address: methodFnPtr,
                                module: moduleInfo
                            };
                            
                            // 如果是目标方法，尝试钩住它
                            if (methodName === "nativeAddMapGestureMsg") {
                                log("找到目标方法 nativeAddMapGestureMsg，准备钩住...");
                                hookNativeMethod(methodFnPtr, methodName, methodSig);
                            }
                        } catch (e) {
                            logError("处理方法 #" + i + " 失败: " + e);
                        }
                    }
                }
            });
            
            log("成功钩住 RegisterNatives 函数");
        } catch (e) {
            logError("钩住 RegisterNatives 失败: " + e);
        }
    }

    // 3. 钩住特定的 Native 方法
    function hookNativeMethod(methodPtr, methodName, methodSig) {
        if (!methodPtr || methodPtr.isNull()) {
            logError("无法钩住方法: " + methodName + " - 无效的函数指针");
            return;
        }
        
        if (gHookedFunctions[methodPtr]) {
            logDebug("方法已经被钩住: " + methodName);
            return;
        }
        
        try {
            Interceptor.attach(methodPtr, {
                onEnter: function(args) {
                    try {
                        log("\n========== Native 方法被调用 ==========");
                        log("方法: " + methodName + " " + methodSig);
                        log("地址: " + formatAddressWithModule(methodPtr));
                        
                        // 解析参数
                        try {
                            var paramTypes = parseJNISignature(methodSig);
                            log("参数类型: " + JSON.stringify(paramTypes));
                            
                            // 前两个参数总是 JNIEnv* 和 jclass/jobject
                            log("JNIEnv*: " + args[0]);
                            log("jclass/jobject: " + args[1]);
                            
                            // 解析其他参数
                            for (var i = 0; i < paramTypes.length; i++) {
                                var paramValue = "";
                                var argIndex = i + 2; // 跳过 JNIEnv 和 jclass/jobject
                                
                                if (args[argIndex] === undefined || args[argIndex] === null || args[argIndex].isNull()) {
                                    paramValue = "null";
                                    continue;
                                }
                                
                                try {
                                    switch (paramTypes[i]) {
                                        case 'I': // int
                                            paramValue = args[argIndex].toInt32();
                                            break;
                                        case 'J': // long
                                            paramValue = args[argIndex].toString();
                                            break;
                                        case 'F': // float
                                            paramValue = args[argIndex].readFloat().toFixed(2);
                                            break;
                                        case 'D': // double
                                            paramValue = args[argIndex].readDouble().toFixed(2);
                                            break;
                                        case 'Z': // boolean
                                            paramValue = args[argIndex].toInt32() !== 0 ? "true" : "false";
                                            break;
                                        case 'B': // byte
                                            paramValue = args[argIndex].toInt32() & 0xFF;
                                            break;
                                        case 'C': // char
                                            paramValue = String.fromCharCode(args[argIndex].toInt32());
                                            break;
                                        case 'S': // short
                                            paramValue = args[argIndex].toInt32() & 0xFFFF;
                                            break;
                                        default:
                                            paramValue = args[argIndex];
                                    }
                                } catch (e) {
                                    paramValue = "无法读取 (" + e.message + ")";
                                }
                                
                                log("参数 " + i + " (" + paramTypes[i] + "): " + paramValue);
                            }
                        } catch (e) {
                            logError("解析参数失败: " + e);
                        }
                        
                        // 记录调用栈
                        if (config.logStackTrace) {
                            try {
                                log(getStackTrace(this.context));
                            } catch (e) {
                                logError("获取调用栈失败: " + e);
                            }
                        }
                        
                        // 记录当前调用信息，用于跟踪跨 so 调用
                        gLastNativeCall = {
                            method: methodName,
                            address: methodPtr,
                            timestamp: new Date().getTime()
                        };
                    } catch (e) {
                        logError("处理 Native 方法调用失败: " + e);
                    }
                },
                onLeave: function(retval) {
                    try {
                        log("方法返回: " + (retval ? retval : "void"));
                        log("========== Native 方法结束 ==========\n");
                    } catch (e) {
                        logError("处理方法返回失败: " + e);
                    }
                }
            });
            
            gHookedFunctions[methodPtr] = true;
            log("成功钩住 Native 方法: " + methodName);
        } catch (e) {
            logError("钩住 Native 方法失败: " + e);
        }
    }

    // 4. 钩住跨 so 调用
    function hookCrossSoCalls() {
        if (!config.hookCrossSoCalls) return;
        
        log("开始监控跨 so 调用...");
        
        // 遍历目标 so 文件
        config.targetSoNames.forEach(function(soName) {
            var module = Process.findModuleByName(soName);
            if (!module) {
                logError("未找到模块: " + soName);
                return;
            }
            
            log("分析模块导出函数: " + soName);
            
            // 遍历模块导出函数
            var exports = module.enumerateExports();
            log("模块 " + soName + " 共有 " + exports.length + " 个导出函数");
            
            exports.forEach(function(exp) {
                if (exp.type === 'function') {
                    try {
                        // 钩住导出函数
                        Interceptor.attach(exp.address, {
                            onEnter: function(args) {
                                try {
                                    // 获取调用者地址
                                    var caller = this.returnAddress;
                                    if (!caller || caller.isNull()) return;
                                    
                                    var callerModule = findModuleForAddress(caller);
                                    
                                    // 如果调用者和被调用者来自不同的 so，则记录
                                    if (callerModule && callerModule.name !== soName) {
                                        log("\n========== 检测到跨 so 调用 ==========");
                                        log("调用者: " + formatAddressWithModule(caller) + " [" + callerModule.name + "]");
                                        log("被调用: " + exp.name + "@" + formatAddressWithModule(exp.address) + " [" + soName + "]");
                                        
                                        // 记录调用图
                                        if (!gCallGraph[caller]) {
                                            gCallGraph[caller] = [];
                                        }
                                        if (gCallGraph[caller].indexOf(exp.address) === -1) {
                                            gCallGraph[caller].push(exp.address);
                                        }
                                        
                                        // 记录调用栈
                                        if (config.logStackTrace) {
                                            log(getStackTrace(this.context));
                                        }
                                        
                                        log("========== 跨 so 调用结束 ==========\n");
                                    }
                                } catch (e) {
                                    logError("处理跨 so 调用失败: " + e);
                                }
                            }
                        });
                    } catch (e) {
                        logDebug("钩住导出函数 " + exp.name + " 失败: " + e);
                    }
                }
            });
        });
    }

    // 5. 钩住 nativeAddMapGestureMsg 的执行流程
    function hookNativeAddMapGestureMsg() {
        log("开始监控 nativeAddMapGestureMsg 执行流程...");
        
        // 钩住 Java 层的 nativeAddMapGestureMsg 方法
        Java.perform(function() {
            try {
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                        try {
                            log("\n========== Java层调用 nativeAddMapGestureMsg ==========");
                            log("参数: engineId=" + engineId + 
                               ", nativePtr=0x" + nativePtr.toString(16) + 
                               ", type=" + type + 
                               ", param1=" + param1.toFixed(2) + 
                               ", param2=" + param2.toFixed(2) + 
                               ", param3=" + param3.toFixed(2) + 
                               ", param4=" + param4);
                            
                            // 获取Java调用栈
                            try {
                                var stack = Java.use("android.util.Log")
                                    .getStackTraceString(Java.use("java.lang.Exception").$new())
                                    .split("\n");
                                log("Java调用栈:\n" + stack.slice(1, 10).join("\n"));
                            } catch (e) {
                                logError("获取Java调用栈失败: " + e);
                            }
                            
                            // 调用原始方法
                            var result;
                            try {
                                result = this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                                log("nativeAddMapGestureMsg 返回: " + result);
                            } catch (e) {
                                logError("调用原始方法失败: " + e);
                                // 重新抛出异常以保持原始行为
                                throw e;
                            }
                            
                            log("========== Java层调用结束 ==========\n");
                            return result;
                        } catch (e) {
                            logError("Java层钩子异常: " + e);
                            // 重新抛出异常以保持原始行为
                            throw e;
                        }
                    };
                    log("成功钩住 Java 层 nativeAddMapGestureMsg 方法");
                } else {
                    logError("未找到 nativeAddMapGestureMsg 方法");
                }
            } catch (e) {
                logError("钩住 Java 层方法失败: " + e);
            }
        });
        
        // 修改直接钩住已知偏移量的函数部分
        try {
            var libamapr = Process.findModuleByName("libamapr.so");
            if (libamapr) {
                var knownOffset = 0x6ee70c;
                try {
                    var targetAddr = libamapr.base.add(knownOffset);
                    if (!targetAddr || targetAddr.isNull()) {
                        logError("计算目标地址失败");
                        return;
                    }
                    
                    log("尝试直接钩住已知偏移量函数: 0x" + knownOffset.toString(16));
                    
                    Interceptor.attach(targetAddr, {
                        onEnter: function(args) {
                            try {
                                log("\n========== Native层 nativeAddMapGestureMsg 被调用 ==========");
                                
                                // 尝试读取参数
                                try {
                                    if (args[0]) log("JNIEnv*: " + args[0]);
                                    if (args[1]) log("jclass: " + args[1]);
                                    if (args[2]) log("engineId: " + args[2].toInt32());
                                    if (args[3]) log("nativePtr: " + args[3]);
                                    if (args[4]) log("type: " + args[4].toInt32());
                                    if (args[5]) log("param1: " + args[5].readFloat().toFixed(2));
                                    if (args[6]) log("param2: " + args[6].readFloat().toFixed(2));
                                    if (args[7]) log("param3: " + args[7].readFloat().toFixed(2));
                                    if (args[8]) log("param4: " + args[8].toInt32());
                                } catch (e) {
                                    logError("读取参数失败: " + e);
                                }
                                
                                // 记录调用栈
                                if (config.logStackTrace) {
                                    try {
                                        log(getStackTrace(this.context));
                                    } catch (e) {
                                        logError("获取调用栈失败: " + e);
                                    }
                                }
                                
                                this.startTime = new Date().getTime();
                            } catch (e) {
                                logError("处理 Native 层函数调用失败: " + e);
                            }
                        },
                        onLeave: function(retval) {
                            try {
                                log("nativeAddMapGestureMsg 返回: " + (retval ? retval : "void"));
                                log("执行时间: " + (new Date().getTime() - this.startTime) + "ms");
                                log("========== Native层 nativeAddMapGestureMsg 结束 ==========\n");
                            } catch (e) {
                                logError("处理函数返回失败: " + e);
                            }
                        }
                    });
                    
                    log("成功钩住 Native 层 nativeAddMapGestureMsg 函数");
                } catch (e) {
                    logError("钩住 Native 层函数失败: " + e + ", 地址: " + formatAddress(libamapr.base.add(knownOffset)));
                }
            } else {
                logError("未找到 libamapr.so 模块");
            }
        } catch (e) {
            logError("处理 libamapr.so 模块失败: " + e);
        }
        
        // 钩住下游函数（基于分析报告）
        try {
            var libamapr = Process.findModuleByName("libamapr.so");
            if (libamapr) {
                // 关键下游函数偏移量列表
                var downstreamFunctions = [
                    { offset: 0x6FB98C, name: "getMapEngineInstance" },
                    { offset: 0x6F3430, name: "validateEngine" },
                    { offset: 0x6FB530, name: "processGestureMessage" },
                    { offset: 0x713690, name: "processGesture_sub" },
                    { offset: 0x6FBC78, name: "triggerRenderUpdate" },
                    { offset: 0x6FB9E0, name: "updateMapView" },
                    { offset: 0x6FB550, name: "finalizeProcessing" }
                ];
                
                downstreamFunctions.forEach(function(func) {
                    try {
                        var funcAddr = libamapr.base.add(func.offset);
                        
                        Interceptor.attach(funcAddr, {
                            onEnter: function(args) {
                                log("\n========== 下游函数 " + func.name + " 被调用 ==========");
                                log("地址: " + formatAddressWithModule(funcAddr));
                                
                                // 记录调用栈
                                if (config.logStackTrace) {
                                    log(getStackTrace(this.context));
                                }
                                
                                this.startTime = new Date().getTime();
                            },
                            onLeave: function(retval) {
                                log(func.name + " 返回: " + retval);
                                log("执行时间: " + (new Date().getTime() - this.startTime) + "ms");
                                log("========== 下游函数 " + func.name + " 结束 ==========\n");
                            }
                        });
                        
                        log("成功钩住下游函数: " + func.name);
                    } catch (e) {
                        logError("钩住下游函数 " + func.name + " 失败: " + e);
                    }
                });
            }
        } catch (e) {
            logError("钩住下游函数失败: " + e);
        }
    }

    // 工具函数 - 解析 JNI 签名
    function parseJNISignature(signature) {
        var result = [];
        var i = 0;
        
        // 跳过开头的 '('
        if (signature.charAt(0) === '(') {
            i = 1;
        }
        
        while (i < signature.length && signature.charAt(i) !== ')') {
            var c = signature.charAt(i);
            
            if (c === 'L') {
                // 对象类型
                var end = signature.indexOf(';', i);
                if (end !== -1) {
                    result.push(signature.substring(i, end + 1));
                    i = end + 1;
                    continue;
                }
            } else if (c === '[') {
                // 数组类型
                var arrayDepth = 0;
                while (i < signature.length && signature.charAt(i) === '[') {
                    arrayDepth++;
                    i++;
                }
                
                if (i < signature.length) {
                    if (signature.charAt(i) === 'L') {
                        var end = signature.indexOf(';', i);
                        if (end !== -1) {
                            var arrayType = signature.substring(i, end + 1);
                            var fullType = '';
                            for (var j = 0; j < arrayDepth; j++) {
                                fullType += '[';
                            }
                            fullType += arrayType;
                            result.push(fullType);
                            i = end + 1;
                            continue;
                        }
                    } else {
                        var baseType = signature.charAt(i);
                        var fullType = '';
                        for (var j = 0; j < arrayDepth; j++) {
                            fullType += '[';
                        }
                        fullType += baseType;
                        result.push(fullType);
                        i++;
                        continue;
                    }
                }
            } else {
                // 基本类型
                result.push(c);
                i++;
            }
        }
        
        return result;
    }

    // 6. 导出映射表为 JSON
    function exportMappingToJSON() {
        try {
            var mapping = {
                registeredMethods: gRegisteredMethods,
                modules: gModuleMap,
                callGraph: gCallGraph
            };
            
            log("JNI 映射表:");
            log(JSON.stringify(mapping, null, 2));
        } catch (e) {
            logError("导出映射表失败: " + e);
        }
    }

    // 主函数
    function main() {
        log("高德地图 .so 模块分析与 JNI 调用追踪脚本启动");
        log("适用于 Frida 12.9.7，使用 ES5 语法");
        
        // 设置异常处理
        Process.setExceptionHandler(function(exception) {
            logError("异常: " + JSON.stringify(exception));
            // 返回 true 表示已处理异常，脚本将继续运行
            return true;
        });
        
        try {
            // 1. 枚举所有已加载模块
            enumerateLoadedModules();
            
            // 延迟执行其他操作，确保模块信息已获取
            // 增加延迟时间，给应用更多初始化时间
            setTimeout(function() {
                try {
                    // 2. 监控 RegisterNatives 调用
                    hookRegisterNatives();
                    
                    // 再次延迟执行，确保 JNI 环境已准备好
                    setTimeout(function() {
                        try {
                            // 3. 钩住跨 so 调用
                            hookCrossSoCalls();
                            
                            // 4. 钩住 nativeAddMapGestureMsg 的执行流程
                            hookNativeAddMapGestureMsg();
                            
                            // 5. 定期导出映射表
                            setInterval(function() {
                                try {
                                    exportMappingToJSON();
                                } catch (e) {
                                    logError("导出映射表失败: " + e);
                                }
                            }, 30000);
                        } catch (e) {
                            logError("设置钩子失败: " + e);
                        }
                    }, 1000);
                } catch (e) {
                    logError("设置 RegisterNatives 钩子失败: " + e);
                }
            }, 3000);
        } catch (e) {
            logError("脚本初始化失败: " + e);
        }
        
        log("脚本设置完成，等待事件...");
    }
    
    // 启动脚本
    main();
})(); 