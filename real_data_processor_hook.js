/*
 * 高德地图真实数据处理器精确Hook
 * 基于IDA Pro反汇编分析结果 (sub_5C060/sub_10F88)
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[Real Data Processor Hook] 启动真实数据处理器Hook...");
    
    var CONFIG = {
        INIT_DELAY: 3000,
        LOG_DETAILED_PARAMS: true,
        EXTRACT_DATA_CONTENT: true
    };
    
    var interceptResults = {
        sub_5C060_calls: [],
        sub_10F88_calls: [],
        totalInterceptions: 0
    };
    
    // === 地址调整函数 ===
    function getAdjustedAddress(libBase, idaOffset) {
        // 根据IDA和Frida的地址差异进行调整
        // IDA: sub_5C060 (0x5C060) -> Frida: sub_5C394 (0x5C394) = 差异+0x334
        // IDA: sub_10EFC (0x10EFC) -> Frida: sub_10F88 (0x10F88) = 差异+0x8C
        
        if (idaOffset === 0x5C060) {
            return libBase.add(0x5C394); // 已知的Frida地址
        } else if (idaOffset === 0x10EFC) {
            return libBase.add(0x10F88); // 已知的Frida地址
        } else {
            return libBase.add(idaOffset);
        }
    }
    
    // === 数据内容提取器 ===
    function extractDataContent(ptr, size, context) {
        try {
            if (!ptr || ptr.isNull()) {
                return null;
            }
            
            var maxSize = Math.min(size || 256, 512);
            var data = ptr.readByteArray(maxSize);
            var view = new Uint8Array(data);
            
            var extraction = {
                address: ptr,
                size: maxSize,
                context: context,
                hexDump: hexdump(data, {length: maxSize, ansi: false}),
                patterns: {
                    hasType1: false,  // .!9h.
                    hasType2: false,  // .C.Q.
                    hasDICE: false,   // DICE-AM
                    hasANS: false     // ANS header
                },
                readableText: ""
            };
            
            // 检查数据模式
            var headerStr = "";
            for (var i = 0; i < Math.min(16, view.length); i++) {
                if (view[i] >= 32 && view[i] < 127) {
                    headerStr += String.fromCharCode(view[i]);
                } else if (view[i] === 0) {
                    headerStr += "\\0";
                } else {
                    headerStr += ".";
                }
            }
            
            extraction.readableText = headerStr;
            
            // 识别数据类型
            if (headerStr.indexOf('!9h') !== -1) {
                extraction.patterns.hasType1 = true;
            }
            if (headerStr.indexOf('C.Q') !== -1) {
                extraction.patterns.hasType2 = true;
            }
            if (headerStr.indexOf('DICE') !== -1) {
                extraction.patterns.hasDICE = true;
            }
            if (headerStr.indexOf('ANS') !== -1) {
                extraction.patterns.hasANS = true;
            }
            
            return extraction;
            
        } catch (e) {
            console.log("[Extract Error] 数据提取失败: " + e);
            return null;
        }
    }
    
    // === 参数分析器 ===
    function analyzeParameters(args, functionName) {
        var analysis = {
            function: functionName,
            timestamp: Date.now(),
            args: [],
            dataExtractions: []
        };
        
        // 分析前3个参数（通常是主要参数）
        for (var i = 0; i < Math.min(3, args.length); i++) {
            try {
                var arg = args[i];
                var argInfo = {
                    index: i,
                    value: arg.toString(),
                    asInt: arg.toInt32(),
                    asPointer: arg,
                    isValidPointer: false,
                    dataContent: null
                };
                
                // 检查是否为有效指针
                try {
                    if (!arg.isNull()) {
                        var testByte = arg.readU8();
                        argInfo.isValidPointer = true;
                        
                        // 提取数据内容
                        if (CONFIG.EXTRACT_DATA_CONTENT) {
                            argInfo.dataContent = extractDataContent(arg, 256, functionName + "_arg" + i);
                        }
                    }
                } catch (e) {
                    // 不是有效指针
                }
                
                analysis.args.push(argInfo);
                
                if (argInfo.dataContent) {
                    analysis.dataExtractions.push(argInfo.dataContent);
                }
                
            } catch (e) {
                console.log("[Param Error] 参数分析失败 arg[" + i + "]: " + e);
            }
        }
        
        return analysis;
    }
    
    // === Hook sub_5C060 (数据调度器) ===
    function hookSub5C060(libBase) {
        var targetAddr = getAdjustedAddress(libBase, 0x5C060);
        
        console.log("[Hook] 设置 sub_5C060 Hook @ " + targetAddr);
        
        Interceptor.attach(targetAddr, {
            onEnter: function(args) {
                this.startTime = Date.now();
                this.analysis = analyzeParameters(args, "sub_5C060");
                
                console.log("[sub_5C060] 进入数据调度器");
                
                if (CONFIG.LOG_DETAILED_PARAMS) {
                    for (var i = 0; i < this.analysis.args.length; i++) {
                        var arg = this.analysis.args[i];
                        console.log("  arg[" + i + "]: " + arg.value + 
                                   " (int: " + arg.asInt + 
                                   ", ptr: " + (arg.isValidPointer ? "是" : "否") + ")");
                        
                        if (arg.dataContent && arg.dataContent.patterns) {
                            var patterns = arg.dataContent.patterns;
                            if (patterns.hasType1 || patterns.hasType2 || patterns.hasDICE) {
                                console.log("    数据模式: Type1=" + (patterns.hasType1 ? "是" : "否") + 
                                           ", Type2=" + (patterns.hasType2 ? "是" : "否") + 
                                           ", DICE=" + (patterns.hasDICE ? "是" : "否"));
                                console.log("    可读内容: '" + arg.dataContent.readableText + "'");
                            }
                        }
                    }
                }
            },
            
            onLeave: function(retval) {
                var duration = Date.now() - this.startTime;
                var returnCode = retval.toInt32();
                
                console.log("[sub_5C060] 退出数据调度器, 返回码: " + returnCode + ", 耗时: " + duration + "ms");
                
                // 记录调用结果
                this.analysis.duration = duration;
                this.analysis.returnCode = returnCode;
                interceptResults.sub_5C060_calls.push(this.analysis);
                interceptResults.totalInterceptions++;
                
                // 如果发现重要数据，显示详细信息
                if (this.analysis.dataExtractions.length > 0) {
                    console.log("[Data Found] sub_5C060 发现 " + this.analysis.dataExtractions.length + " 个数据块");
                    
                    for (var i = 0; i < this.analysis.dataExtractions.length; i++) {
                        var ext = this.analysis.dataExtractions[i];
                        if (ext.patterns.hasType1 || ext.patterns.hasType2) {
                            console.log("  [" + i + "] " + ext.context + " @ " + ext.address);
                            console.log("      模式: " + JSON.stringify(ext.patterns));
                            console.log("      头部: " + ext.readableText.substring(0, 32));
                        }
                    }
                }
            }
        });
        
        console.log("[Hook] sub_5C060 Hook 已设置");
    }
    
    // === Hook sub_10F88 (数据解析器) ===
    function hookSub10F88(libBase) {
        var targetAddr = getAdjustedAddress(libBase, 0x10EFC);
        
        console.log("[Hook] 设置 sub_10F88 Hook @ " + targetAddr);
        
        Interceptor.attach(targetAddr, {
            onEnter: function(args) {
                this.startTime = Date.now();
                this.analysis = analyzeParameters(args, "sub_10F88");
                
                console.log("[sub_10F88] 进入数据解析器");
                
                if (CONFIG.LOG_DETAILED_PARAMS) {
                    for (var i = 0; i < this.analysis.args.length; i++) {
                        var arg = this.analysis.args[i];
                        console.log("  arg[" + i + "]: " + arg.value + 
                                   " (int: " + arg.asInt + 
                                   ", ptr: " + (arg.isValidPointer ? "是" : "否") + ")");
                        
                        if (arg.dataContent && arg.dataContent.readableText) {
                            console.log("    数据头部: '" + arg.dataContent.readableText + "'");
                        }
                    }
                }
            },
            
            onLeave: function(retval) {
                var duration = Date.now() - this.startTime;
                var returnCode = retval.toInt32();
                
                console.log("[sub_10F88] 退出数据解析器, 返回码: " + returnCode + ", 耗时: " + duration + "ms");
                
                // 记录调用结果
                this.analysis.duration = duration;
                this.analysis.returnCode = returnCode;
                interceptResults.sub_10F88_calls.push(this.analysis);
                interceptResults.totalInterceptions++;
                
                // 显示解析状态
                var status = "未知";
                switch(returnCode) {
                    case 0: status = "成功"; break;
                    case 7: status = "权限错误"; break;
                    case 8: status = "版本不兼容"; break;
                    case 11: status = "数据校验失败"; break;
                    case 26: status = "内存分配失败"; break;
                }
                
                console.log("[Parse Status] " + status);
                
                if (this.analysis.dataExtractions.length > 0) {
                    console.log("[Parse Data] 解析了 " + this.analysis.dataExtractions.length + " 个数据块");
                }
            }
        });
        
        console.log("[Hook] sub_10F88 Hook 已设置");
    }
    
    // === 结果统计 ===
    function generateStatistics() {
        console.log("\n=== 数据处理器Hook统计 ===");
        console.log("总拦截次数: " + interceptResults.totalInterceptions);
        console.log("sub_5C060 调用: " + interceptResults.sub_5C060_calls.length);
        console.log("sub_10F88 调用: " + interceptResults.sub_10F88_calls.length);
        
        // 统计数据类型
        var type1Count = 0, type2Count = 0, diceCount = 0;
        var allCalls = interceptResults.sub_5C060_calls.concat(interceptResults.sub_10F88_calls);
        
        for (var i = 0; i < allCalls.length; i++) {
            var call = allCalls[i];
            for (var j = 0; j < call.dataExtractions.length; j++) {
                var ext = call.dataExtractions[j];
                if (ext.patterns.hasType1) type1Count++;
                if (ext.patterns.hasType2) type2Count++;
                if (ext.patterns.hasDICE) diceCount++;
            }
        }
        
        console.log("\n数据类型统计:");
        console.log("  Type1 (.!9h.): " + type1Count + " 次");
        console.log("  Type2 (.C.Q.): " + type2Count + " 次");
        console.log("  DICE-AM: " + diceCount + " 次");
        
        // 显示最近的几个调用
        if (allCalls.length > 0) {
            console.log("\n最近调用摘要:");
            var recentCalls = allCalls.slice(-3);
            for (var k = 0; k < recentCalls.length; k++) {
                var recent = recentCalls[k];
                console.log("  [" + k + "] " + recent.function + " - 返回码: " + recent.returnCode + 
                           ", 耗时: " + recent.duration + "ms, 数据块: " + recent.dataExtractions.length);
            }
        }
        
        console.log("==============================\n");
    }
    
    // === 库等待函数 ===
    function waitForLibrary(libraryName, callback) {
        var maxAttempts = 30;
        var attempt = 0;
        
        function checkLibrary() {
            try {
                var lib = Module.findBaseAddress(libraryName);
                if (lib) {
                    console.log("[Library] " + libraryName + " 已加载，基址: " + lib);
                    callback(lib);
                    return;
                }
            } catch (e) {
                // 继续等待
            }
            
            attempt++;
            if (attempt < maxAttempts) {
                setTimeout(checkLibrary, 1000);
            } else {
                console.log("[Error] " + libraryName + " 加载超时");
            }
        }
        
        checkLibrary();
    }
    
    // === 主入口 ===
    function main() {
        console.log("[Main] 等待应用初始化完成...");
        
        setTimeout(function() {
            console.log("[Main] 开始初始化精确Hook...");
            
            try {
                waitForLibrary("libamapnsq.so", function(libBase) {
                    try {
                        hookSub5C060(libBase);
                        hookSub10F88(libBase);
                        
                        // 定期生成统计
                        setInterval(function() {
                            if (interceptResults.totalInterceptions > 0) {
                                generateStatistics();
                            }
                        }, 20000); // 每20秒生成统计
                        
                        console.log("[Real Data Processor Hook] 精确Hook已启动!");
                        console.log("现在移动地图以触发数据处理，观察真实的函数调用...");
                        
                    } catch (e) {
                        console.log("[Error] Hook初始化失败: " + e);
                    }
                });
                
            } catch (e) {
                console.log("[Error] 主初始化失败: " + e);
            }
        }, CONFIG.INIT_DELAY);
    }
    
    // === 启动Hook ===
    try {
        Java.perform(function() {
            console.log("[Java] Java环境已准备就绪");
            main();
        });
    } catch (e) {
        console.log("[Error] Java环境初始化失败: " + e);
        main();
    }
    
})(); 