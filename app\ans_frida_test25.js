// # 高德地图数据加载分析专用脚本

// 以下是一个专注于分析手势如何触发地图数据加载的新脚本，重点监控文件操作和解压缩过程：

// ```javascript
// <code_block_to_apply_changes_from>
// ```

// ## 脚本主要功能：

// 1. **简化输出**：减少冗余日志，避免输出过长

// 2. **手势分析**：
//    - 追踪手势开始、进行和结束事件
//    - 区分不同类型的手势（移动、缩放）

// 3. **文件系统监控**：
//    - 监控ANS文件的open、mmap操作
//    - 分析文件路径模式（瓦片、配置、缓存）
//    - 统计文件访问频率

// 4. **内存操作分析**：
//    - 监控文件内存映射
//    - 跟踪解压缩操作

// 5. **线程创建监控**：
//    - 追踪pthread_create调用
//    - 分析手势后的线程创建时间关系

// 6. **定时汇总报告**：
//    - 每10秒生成一次访问统计
//    - 显示访问最频繁的文件

// 这个脚本会帮助你清晰地看到手势结束后触发的数据加载过程，包括文件访问顺序、解压缩操作和多线程处理模式，而且输出更加简洁有条理。
// ```

setTimeout(function() {
  console.log("[+] 高德地图数据加载分析脚本启动");

  // 全局变量用于状态跟踪
  var gestureInProgress = false;
  var lastGestureTime = 0;
  var ansFilesAccessed = {};
  var loadSequence = [];
  
  // 辅助函数：记录加载事件
  function recordEvent(type, details) {
    var now = Date.now();
    var timeSinceGesture = gestureInProgress ? "进行中" : (now - lastGestureTime) + "ms";
    
    var event = {
      type: type,
      time: now,
      timeSinceGesture: timeSinceGesture,
      details: details
    };
    
    loadSequence.push(event);
    
    // 简化输出
    var output = "[" + type + "] " + JSON.stringify(details);
    if (loadSequence.length % 10 === 0) {
      console.log("当前记录了" + loadSequence.length + "个事件");
    }
    return output;
  }
  
  // 辅助函数：分析路径
  function analyzeAnsPath(path) {
    // 分析ANS文件路径特征
    var result = {
      path: path,
      isMapTile: path.indexOf('/tiles/') !== -1 || path.indexOf('/bld/') !== -1,
      isConfig: path.indexOf('config') !== -1,
      isCache: path.indexOf('cache') !== -1
    };
    return result;
  }

  // 监控Java层手势处理
  Java.perform(function() {
    try {
      var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
      
      if (GLMapEngine.addGestureMessage) {
        GLMapEngine.addGestureMessage.implementation = function(engineId, gestureMessage) {
          var gestureType = gestureMessage.getType();
          var gestureDetails = {};
          
          // 获取手势信息
          if (gestureType == 0) {  // 移动手势
            var moveMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.MoveGestureMapMessage"));
            gestureDetails = {
              type: "移动",
              x: moveMsg.mTouchDeltaX.value, 
              y: moveMsg.mTouchDeltaY.value
            };
          } else if (gestureType == 1) {  // 缩放手势
            var scaleMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.ScaleGestureMapMessage"));
            gestureDetails = {
              type: "缩放", 
              scale: scaleMsg.mScaleDelta,
              pivotX: scaleMsg.mPivotX,
              pivotY: scaleMsg.mPivotY
            };
          }
          
          // 记录手势状态
          gestureInProgress = !(gestureDetails.x === 0 && gestureDetails.y === 0);
          if (!gestureInProgress) {
            lastGestureTime = Date.now();
            console.log(recordEvent("手势结束", gestureDetails));
          } else if (Math.abs(gestureDetails.x) > 5 || Math.abs(gestureDetails.y) > 5) {
            console.log(recordEvent("手势进行", gestureDetails));
          }
          
          // 调用原始方法
          var result = this.addGestureMessage(engineId, gestureMessage);
          return result;
        };
        console.log("[+] 成功Hook手势处理方法");
      }
      
      // 监控渲染刷新函数
      if (GLMapEngine.invalidateMap) {
        GLMapEngine.invalidateMap.implementation = function() {
          console.log(recordEvent("地图刷新", {}));
          return this.invalidateMap();
        };
        console.log("[+] 成功Hook地图刷新方法");
      }
      
    } catch(e) {
      console.log("[-] Hook Java方法失败: " + e);
    }
  });
  
  // 监控文件系统操作
  // 1. open - 监控文件打开
  Interceptor.attach(Module.findExportByName("libc.so", "open"), {
    onEnter: function(args) {
      try {
        this.path = args[0].readUtf8String();
        if (this.path.indexOf('.ans') !== -1) {
          var pathInfo = analyzeAnsPath(this.path);
          console.log(recordEvent("ANS文件打开", pathInfo));
          
          // 记录访问频率
          if (!ansFilesAccessed[this.path]) {
            ansFilesAccessed[this.path] = 0;
          }
          ansFilesAccessed[this.path]++;
        }
      } catch(e) {}
    },
    onLeave: function(result) {
      if (this.path && this.path.indexOf('.ans') !== -1 && result.toInt32() > 0) {
        console.log(recordEvent("ANS文件打开成功", { path: this.path, fd: result.toInt32() }));
      }
    }
  });
  
  // 2. mmap - 监控内存映射
  Interceptor.attach(Module.findExportByName("libc.so", "mmap"), {
    onEnter: function(args) {
      this.size = args[1].toInt32();
      this.fd = args[4].toInt32();
    },
    onLeave: function(result) {
      if (this.fd > 0 && this.size > 1024) {
        try {
          var fdPath = "/proc/self/fd/" + this.fd;
          var filePath = new File(fdPath).readlink();
          if (filePath && filePath.indexOf('.ans') !== -1) {
            var mmapInfo = {
              path: filePath,
              size: this.size,
              addr: result
            };
            console.log(recordEvent("ANS文件映射", mmapInfo));
          }
        } catch(e) {}
      }
    }
  });
  
  // 3. pthread_create - 监控线程创建
  var pthread_create = Module.findExportByName(null, "pthread_create");
  if (pthread_create) {
    Interceptor.attach(pthread_create, {
      onEnter: function(args) {
        this.threadPtr = args[0];
        this.startTime = Date.now();
      },
      onLeave: function(result) {
        if (result.toInt32() === 0) { // 成功创建线程
          var threadInfo = {
            threadPtr: this.threadPtr,
            timeSinceGesture: Date.now() - lastGestureTime
          };
          console.log(recordEvent("线程创建", threadInfo));
        }
      }
    });
  }
  
  // 监控ZSTD解压函数
  Process.enumerateModules().forEach(function(module) {
    if (module.name.indexOf('amap') !== -1) {
      module.enumerateExports().forEach(function(exp) {
        if (exp.name.indexOf('ZSTD') !== -1 || exp.name.indexOf('compress') !== -1) {
          Interceptor.attach(exp.address, {
            onEnter: function(args) {
              this.funcName = exp.name;
              this.startTime = Date.now();
              this.inputSize = args[2] ? args[2].toInt32() : 0;
            },
            onLeave: function(result) {
              var decompInfo = {
                function: this.funcName,
                module: module.name,
                inputSize: this.inputSize,
                outputSize: result.toInt32(),
                duration: Date.now() - this.startTime
              };
              console.log(recordEvent("解压数据", decompInfo));
            }
          });
          console.log("[+] 监控解压函数: " + module.name + "!" + exp.name);
        }
      });
    }
  });
  
  // 定时汇总功能
  setInterval(function() {
    // 如果累积了足够多的事件，生成汇总报告
    if (loadSequence.length > 0) {
      console.log("\n[汇总报告] ====================");
      console.log("记录事件总数: " + loadSequence.length);
      
      // 统计ANS文件访问情况
      var fileTypes = {mapTile: 0, config: 0, cache: 0, other: 0};
      for (var path in ansFilesAccessed) {
        var info = analyzeAnsPath(path);
        if (info.isMapTile) fileTypes.mapTile++;
        else if (info.isConfig) fileTypes.config++;
        else if (info.isCache) fileTypes.cache++;
        else fileTypes.other++;
      }
      
      console.log("ANS文件访问统计:");
      console.log("- 地图瓦片文件: " + fileTypes.mapTile);
      console.log("- 配置文件: " + fileTypes.config);
      console.log("- 缓存文件: " + fileTypes.cache);
      console.log("- 其他文件: " + fileTypes.other);
      console.log("访问最频繁的5个文件:");
      
      // 按访问频率排序文件
      var sortedFiles = Object.keys(ansFilesAccessed).sort(function(a, b) {
        return ansFilesAccessed[b] - ansFilesAccessed[a];
      });
      
      // 显示前5个
      for (var i = 0; i < Math.min(sortedFiles.length, 5); i++) {
        var path = sortedFiles[i];
        console.log("  " + (i+1) + ". [" + ansFilesAccessed[path] + "次] " + path);
      }
      
      console.log("[汇总报告结束] ====================\n");
    }
  }, 10000);  // 每10秒生成一次报告
  
  console.log("[+] 地图数据加载分析脚本已启动，请进行地图操作...");
}, 1000);
