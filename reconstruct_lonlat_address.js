#!/usr/bin/env node
/**
 * Gaode offline: reconstruct lon-lat + address (best-effort)
 * Node.js >= 14
 *
 * Inputs (under inputDir, default: ./md-file/gaode_dump):
 *  - 25xx_text.jsonl: extracted chinese name/address groups
 *  - 05_index.jsonl:  in-page segment offsets (for later deep alignment)
 *  - 0d_meta.jsonl:   meta/range info (recorded for reference)
 *  - geom_meta.jsonl: geometry page meta (bc/ce/DICE-AM)
 *  - geom_bin/*.bin:  raw geometry blocks (uncompressed)
 *
 * Outputs (written to outputDir = inputDir):
 *  - texts.csv         : ts, type, text
 *  - geoms.csv         : binFile, magic, pointCount, centroidWGS84Lon, centroidWGS84Lat, centroidGCJ02Lon, centroidGCJ02Lat
 *  - geoms.geojson     : FeatureCollection of sampled point sets
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const inputDir = process.argv[2] ? path.resolve(process.argv[2]) : path.resolve('./md-file/gaode_dump');
const outputDir = inputDir;

function exists(p) { try { fs.accessSync(p); return true; } catch { return false; } }
function readJSONLLines(file) {
  if (!exists(file)) return [];
  const lines = fs.readFileSync(file, 'utf8').split(/\r?\n/).filter(Boolean);
  return lines.map(l => { try { return JSON.parse(l); } catch { return null; } }).filter(Boolean);
}

function writeCSV(file, rows, header) {
  const s = [header.join(',')].concat(rows.map(r => header.map(h => (r[h] != null ? String(r[h]).replace(/[\r\n,]/g, ' ') : '')).join(','))).join('\n');
  fs.writeFileSync(file, s, 'utf8');
  console.log('[write]', file, rows.length, 'rows');
}

function writeGeoJSON(file, geojson) {
  fs.writeFileSync(file, JSON.stringify(geojson, null, 2), 'utf8');
  console.log('[write]', file);
}

// GCJ-02 <-> WGS84 helpers
const a = 6378245.0;
const ee = 0.00669342162296594323;
function outOfChina(lon, lat) {
  return lon < 72.004 || lon > 137.8347 || lat < 0.8293 || lat > 55.8271;
}
function transformLat(x, y) {
  let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y*y + 0.1 * x*y + 0.2 * Math.sqrt(Math.abs(x));
  ret += (20.0*Math.sin(6.0*x*Math.PI) + 20.0*Math.sin(2.0*x*Math.PI)) * 2.0/3.0;
  ret += (20.0*Math.sin(y*Math.PI) + 40.0*Math.sin(y/3.0*Math.PI)) * 2.0/3.0;
  ret += (160.0*Math.sin(y/12.0*Math.PI) + 320*Math.sin(y*Math.PI/30.0)) * 2.0/3.0;
  return ret;
}
function transformLon(x, y) {
  let ret = 300.0 + x + 2.0*y + 0.1*x*x + 0.1*x*y + 0.1*Math.sqrt(Math.abs(x));
  ret += (20.0*Math.sin(6.0*x*Math.PI) + 20.0*Math.sin(2.0*x*Math.PI)) * 2.0/3.0;
  ret += (20.0*Math.sin(x*Math.PI) + 40.0*Math.sin(x/3.0*Math.PI)) * 2.0/3.0;
  ret += (150.0*Math.sin(x/12.0*Math.PI) + 300.0*Math.sin(x/30.0*Math.PI)) * 2.0/3.0;
  return ret;
}
function wgs84ToGcj02(lon, lat) {
  if (outOfChina(lon, lat)) return [lon, lat];
  let dLat = transformLat(lon - 105.0, lat - 35.0);
  let dLon = transformLon(lon - 105.0, lat - 35.0);
  const radLat = lat / 180.0 * Math.PI;
  let magic = Math.sin(radLat);
  magic = 1 - ee * magic * magic;
  const sqrtMagic = Math.sqrt(magic);
  dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * Math.PI);
  dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * Math.PI);
  const mgLat = lat + dLat;
  const mgLon = lon + dLon;
  return [mgLon, mgLat];
}
function gcj02ToWgs84(lon, lat) {
  if (outOfChina(lon, lat)) return [lon, lat];
  const [mgLon, mgLat] = wgs84ToGcj02(lon, lat);
  return [lon * 2 - mgLon, lat * 2 - mgLat];
}

// WebMercator conversions
function metersToLonLatWebMercator(xMeters, yMeters) {
  const R = 6378137.0;
  const lon = (xMeters / R) * 180.0 / Math.PI;
  const lat = (2 * Math.atan(Math.exp(yMeters / R)) - Math.PI / 2) * 180.0 / Math.PI;
  return [lon, lat];
}

// Estimate zoom/world-scale and convert fixed-point to lon/lat
function estimateZoomFromWorldXY(x, y) {
  const absMax = Math.max(Math.abs(x), Math.abs(y), 1);
  // world coordinate = 256 * 2^z
  const z = Math.max(0, Math.min(30, Math.round(Math.log2(absMax / 256))));
  return z;
}
function worldToLonLat(x, y, z) {
  const worldSize = 256 * Math.pow(2, z);
  const lon = (x / worldSize) * 360.0 - 180.0;
  const n = Math.PI - 2 * Math.PI * (y / worldSize);
  const lat = 180.0 / Math.PI * Math.atan(0.5 * (Math.exp(n) - Math.exp(-n)));
  return [lon, lat];
}

// Try parse geometry bin: assume LE int32 pairs as world coords (best-effort)
function parseGeomBinBuffer(buf) {
  const dv = new DataView(buf.buffer, buf.byteOffset, buf.byteLength);
  const total = Math.floor(buf.byteLength / 4);
  const ints = [];
  for (let i = 0; i < total; i++) {
    ints.push(dv.getInt32(i * 4, true));
  }
  const points = [];
  for (let i = 0; i + 1 < ints.length; i += 2) {
    const x = ints[i];
    const y = ints[i+1];
    points.push([x, y]);
  }
  if (points.length === 0) return { pointsLonLatWGS84: [], centroidWGS84: null, centroidGCJ02: null, count: 0 };

  // Estimate a zoom by robust median of absolute world coords
  const sample = points.slice(0, Math.min(points.length, 2000));
  const absVals = sample.flat().map(v => Math.abs(v)).filter(v => isFinite(v) && v > 0);
  absVals.sort((a,b)=>a-b);
  const median = absVals.length ? absVals[Math.floor(absVals.length/2)] : 256;
  const z = estimateZoomFromWorldXY(median, median);

  const lonlat = points.map(([wx, wy]) => worldToLonLat(wx, wy, z));
  // Compute centroid
  const sum = lonlat.reduce((acc, [lon, lat]) => [acc[0]+lon, acc[1]+lat], [0,0]);
  const centroidWGS84 = [sum[0]/lonlat.length, sum[1]/lonlat.length];
  const centroidGCJ02 = wgs84ToGcj02(centroidWGS84[0], centroidWGS84[1]);

  return { pointsLonLatWGS84: lonlat, centroidWGS84, centroidGCJ02, count: lonlat.length, zoom: z };
}

function main() {
  if (!exists(inputDir)) {
    console.error('[error] inputDir not found:', inputDir);
    process.exit(1);
  }
  console.log('[inputDir]', inputDir);

  const textLines = readJSONLLines(path.join(inputDir, '25xx_text.jsonl'));
  const indexLines = readJSONLLines(path.join(inputDir, '05_index.jsonl'));
  const meta0dLines = readJSONLLines(path.join(inputDir, '0d_meta.jsonl'));
  const geomMetaLines = readJSONLLines(path.join(inputDir, 'geom_meta.jsonl'));

  // 1) texts.csv
  const textRows = [];
  for (const rec of textLines) {
    const type = rec.type_id_hex || '';
    const ts = rec.ts || '';
    if (rec.groups && Array.isArray(rec.groups)) {
      for (const g of rec.groups) {
        if (g && typeof g.text === 'string' && g.text.trim().length > 0) {
          textRows.push({ ts: ts, type: type, text: g.text.trim() });
        }
      }
    }
  }
  writeCSV(path.join(outputDir, 'texts.csv'), textRows, ['ts','type','text']);

  // 2) geoms.csv + geoms.geojson
  const geomBinDir = path.join(inputDir, 'geom_bin');
  const geoms = [];
  if (exists(geomBinDir)) {
    const files = fs.readdirSync(geomBinDir).filter(n => n.toLowerCase().endsWith('.bin'));
    // Limit to avoid huge output
    const maxFiles = Math.min(files.length, 100);
    for (let i = 0; i < maxFiles; i++) {
      const f = files[i];
      const fp = path.join(geomBinDir, f);
      try {
        const buf = fs.readFileSync(fp);
        const { pointsLonLatWGS84, centroidWGS84, centroidGCJ02, count, zoom } = parseGeomBinBuffer(buf);
        // find magic type from file name or geom_meta if possible
        let magic = '';
        // try match by ts prefix in file name if present: ts_magic_size.bin
        const parts = f.split('_');
        if (parts.length >= 2) {
          magic = parts[1];
        }
        geoms.push({
          binFile: f,
          magic,
          pointCount: count,
          centroidWGS84Lon: centroidWGS84 ? centroidWGS84[0] : '',
          centroidWGS84Lat: centroidWGS84 ? centroidWGS84[1] : '',
          centroidGCJ02Lon: centroidGCJ02 ? centroidGCJ02[0] : '',
          centroidGCJ02Lat: centroidGCJ02 ? centroidGCJ02[1] : '',
          zoom: (zoom != null ? zoom : '')
        });
      } catch (e) {
        console.warn('[warn] read geom bin failed', f, e.message);
      }
    }
  } else {
    console.warn('[warn] no geom_bin directory found, skip geometry parsing');
  }
  writeCSV(path.join(outputDir, 'geoms.csv'), geoms, [
    'binFile','magic','pointCount','centroidWGS84Lon','centroidWGS84Lat','centroidGCJ02Lon','centroidGCJ02Lat','zoom'
  ]);

  // optional geoms.geojson (sampled points)
  const features = [];
  if (exists(geomBinDir)) {
    const files = fs.readdirSync(geomBinDir).filter(n => n.toLowerCase().endsWith('.bin'));
    const maxFilesGeo = Math.min(files.length, 20);
    for (let i = 0; i < maxFilesGeo; i++) {
      const f = files[i];
      const fp = path.join(geomBinDir, f);
      try {
        const buf = fs.readFileSync(fp);
        const { pointsLonLatWGS84 } = parseGeomBinBuffer(buf);
        const sampled = pointsLonLatWGS84.slice(0, Math.min(pointsLonLatWGS84.length, 2000));
        features.push({
          type: 'Feature',
          properties: { binFile: f },
          geometry: {
            type: 'MultiPoint',
            coordinates: sampled.map(([lon,lat]) => [lon, lat])
          }
        });
      } catch {}
    }
  }
  const geojson = { type: 'FeatureCollection', features };
  writeGeoJSON(path.join(outputDir, 'geoms.geojson'), geojson);

  // 3) persist index and meta 0d snapshot (optional for future alignment)
  const snapFile = path.join(outputDir, 'index_meta_snapshot.json');
  fs.writeFileSync(snapFile, JSON.stringify({
    index: indexLines.slice(0, 50),
    meta0d: meta0dLines.slice(0, 50),
    geomMeta: geomMetaLines.slice(0, 50)
  }, null, 2), 'utf8');
  console.log('[write]', snapFile);

  console.log('[done] outputs written to', outputDir);
  console.log(' - texts.csv        : extracted names (ts, type, text)');
  console.log(' - geoms.csv        : geometry centroid lon/lat (WGS84, GCJ-02) best-effort');
  console.log(' - geoms.geojson    : sampled multipoints for quick map view');
  console.log(' - index_meta_snapshot.json : snapshot of 05 and 0d and geom_meta for reference');
}

main();