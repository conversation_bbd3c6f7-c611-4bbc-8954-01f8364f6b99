Traceback (most recent call last):
  File "documentation_validator.py", line 260, in <module>
    main() 
  File "documentation_validator.py", line 251, in main
    results = validator.run_full_validation()
  File "documentation_validator.py", line 235, in run_full_validation
    print("\U0001f50d ��ʼ��֤ full_execution_flow_analysis.md �ĵ�...")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f50d' in position 0: illegal multibyte sequence
