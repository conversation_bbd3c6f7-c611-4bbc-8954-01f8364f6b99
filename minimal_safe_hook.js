/*
 * 最简单最安全的Hook脚本
 * 只监控函数调用，不读取任何内存数据，避免APP崩溃
 */

console.log("✅ 安全Hook脚本启动...");

var callCount = 0;
var maxCalls = 20;

// 最安全的监控方式 - 只记录调用
function setupSafeHooks() {
    console.log("🔧 设置安全监控Hook...");
    
    try {
        // 1. 监控read系统调用 - 不读取数据
        var readPtr = Module.findExportByName("libc.so", "read");
        if (readPtr) {
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    this.size = args[2].toInt32();
                },
                onLeave: function(retval) {
                    if (this.size > 5000 && this.size < 50000 && callCount < maxCalls) {
                        console.log("[文件读取] " + this.size + " 字节 (可能包含地图数据)");
                        callCount++;
                    }
                }
            });
            console.log("✅ read() 监控设置成功");
        }
        
    } catch (e) {
        console.log("❌ Hook设置失败: " + e.message);
    }
}

// 延迟设置zlib监控
setTimeout(function() {
    try {
        var zlibModule = Process.findModuleByName("libz.so");
        if (zlibModule) {
            var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
            if (uncompressPtr) {
                Interceptor.attach(uncompressPtr, {
                    onEnter: function(args) {
                        this.sourceLen = args[3].toInt32();
                        console.log("🎯 [zlib解压] 开始解压 " + this.sourceLen + " 字节");
                    },
                    onLeave: function(retval) {
                        if (retval.toInt32() === 0) {
                            console.log("✅ [zlib解压] 解压成功");
                            console.log("💡 这里就是你要的解压后数据！但为了稳定性暂不读取内存");
                        }
                    }
                });
                console.log("✅ zlib uncompress 监控设置成功");
            }
        }
    } catch (e) {
        console.log("⚠️ zlib Hook失败: " + e.message);
    }
}, 2000);

// 启动
setupSafeHooks();

console.log("✅ 安全监控模式已激活");
console.log("💡 请在地图中移动，观察解压调用"); 