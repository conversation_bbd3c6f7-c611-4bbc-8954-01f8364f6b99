 /*
 * 高德地图SQLite BLOB数据提取器
 * 基于IDA Pro分析：func_15000 = girf_sqlite3_bind_blob
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[SQLite Data Extractor] 启动SQLite BLOB数据提取器...");
    
    var extractorStats = {
        totalCalls: 0,
        blobFound: 0,
        successfulExtractions: 0,
        extractedSamples: []
    };
    
    var CONFIG = {
        SAMPLE_RATE: 10,           // 每10次调用采样一次
        MAX_SAMPLES: 3,            // 最多收集3个样本
        MIN_BLOB_SIZE: 100,        // 最小BLOB大小（过滤小数据）
        MAX_BLOB_SIZE: 1024 * 100  // 最大BLOB大小（避免过大数据）
    };
    
    // === SQLite BLOB Hook ===
    function setupSQLiteBlobHook(libBase) {
        console.log("[SQLite] 设置SQLite BLOB Hook for girf_sqlite3_bind_blob...");
        
        try {
            var sqliteBlobFunc = libBase.add(0x15000);
            
            Interceptor.attach(sqliteBlobFunc, {
                onEnter: function(args) {
                    extractorStats.totalCalls++;
                    
                    // 采样机制
                    if (extractorStats.totalCalls % CONFIG.SAMPLE_RATE !== 0) {
                        return;
                    }
                    
                    this.shouldAnalyze = true;
                    this.startTime = Date.now();
                    
                    // 分析SQLite BLOB参数
                    this.sqliteStmt = args[0];      // SQLite语句句柄
                    this.paramIndex = args[1];      // 参数索引
                    this.blobData = args[2];        // BLOB数据指针
                    this.blobSize = args[3];        // BLOB数据长度
                    this.destructor = args[4];      // 销毁函数
                    
                    var sampleNum = Math.floor(extractorStats.totalCalls / CONFIG.SAMPLE_RATE);
                    
                    try {
                        var blobSizeInt = this.blobSize.toInt32();
                        var paramIndexInt = this.paramIndex.toInt32();
                        
                        console.log("[SQLite Sample " + sampleNum + "] girf_sqlite3_bind_blob 调用 (第" + extractorStats.totalCalls + "次)");
                        console.log("[SQLite Params] 语句: " + this.sqliteStmt + ", 参数索引: " + paramIndexInt + ", 数据大小: " + blobSizeInt + " 字节");
                        
                        // 检查BLOB大小是否在合理范围内
                        if (blobSizeInt >= CONFIG.MIN_BLOB_SIZE && blobSizeInt <= CONFIG.MAX_BLOB_SIZE) {
                            
                            this.validBlob = true;
                            this.blobSizeInt = blobSizeInt;
                            
                            // 检查BLOB数据是否包含地图数据标识
                            if (!this.blobData.isNull()) {
                                var quickHeader = this.blobData.readByteArray(Math.min(16, blobSizeInt));
                                var headerView = new Uint8Array(quickHeader);
                                var headerStr = "";
                                
                                for (var i = 0; i < Math.min(8, headerView.length); i++) {
                                    if (headerView[i] >= 32 && headerView[i] < 127) {
                                        headerStr += String.fromCharCode(headerView[i]);
                                    } else {
                                        headerStr += ".";
                                    }
                                }
                                
                                console.log("[SQLite Header] 数据头部: '" + headerStr + "'");
                                
                                // 检查地图数据标识
                                if (headerStr.indexOf('ANS') !== -1 || 
                                    headerStr.indexOf('DIC') !== -1 ||
                                    headerStr.indexOf('.!9') !== -1 ||
                                    headerStr.indexOf('.C.') !== -1 ||
                                    headerView[0] === 0x08) { // 压缩标识
                                    
                                    console.log("[MAP DATA FOUND] SQLite BLOB包含地图数据！");
                                    extractorStats.blobFound++;
                                    this.containsMapData = true;
                                    
                                    // 标记提取
                                    if (extractorStats.successfulExtractions < CONFIG.MAX_SAMPLES) {
                                        this.needExtraction = true;
                                        console.log("[Extract] 将提取BLOB数据样本");
                                    }
                                }
                            }
                        } else {
                            console.log("[SQLite Skip] BLOB大小超出范围: " + blobSizeInt + " 字节");
                        }
                        
                    } catch (e) {
                        console.log("[SQLite Error] 参数分析失败: " + e);
                    }
                },
                
                onLeave: function(retval) {
                    if (!this.shouldAnalyze) {
                        return;
                    }
                    
                    var duration = Date.now() - this.startTime;
                    var returnCode = retval.toInt32();
                    
                    console.log("[SQLite Result] 绑定完成, 耗时: " + duration + "ms, 返回码: " + returnCode);
                    
                    // 执行数据提取
                    if (this.needExtraction && this.containsMapData && this.validBlob) {
                        console.log("[Extract] 开始提取SQLite BLOB数据...");
                        extractSQLiteBlobData(this.blobData, this.blobSizeInt, "sqlite_blob_sample_" + extractorStats.successfulExtractions);
                    }
                }
            });
            
            console.log("[SQLite] girf_sqlite3_bind_blob Hook已设置");
            
        } catch (e) {
            console.log("[Error] SQLite Hook设置失败: " + e);
        }
    }
    
    // === SQLite BLOB数据提取 ===
    function extractSQLiteBlobData(blobPtr, blobSize, sampleName) {
        try {
            console.log("[SQLite Extract] 提取 " + sampleName + " (大小: " + blobSize + " 字节)...");
            
            // 读取完整BLOB数据
            var extractSize = Math.min(blobSize, 512); // 限制提取大小避免过大输出
            var blobData = blobPtr.readByteArray(extractSize);
            var dataView = new Uint8Array(blobData);
            
            var sample = {
                name: sampleName,
                address: blobPtr,
                fullSize: blobSize,
                extractedSize: extractSize,
                timestamp: Date.now(),
                format: "unknown",
                isCompressed: false,
                hexHeader: "",
                signature: "",
                magicBytes: []
            };
            
            // 提取前8字节作为魔数
            for (var i = 0; i < Math.min(8, dataView.length); i++) {
                sample.magicBytes.push(dataView[i]);
                var hex = dataView[i].toString(16).toUpperCase();
                if (hex.length === 1) hex = '0' + hex;
                sample.hexHeader += hex + " ";
            }
            
            // 提取可读签名
            for (var j = 0; j < Math.min(32, dataView.length); j++) {
                if (dataView[j] >= 32 && dataView[j] < 127) {
                    sample.signature += String.fromCharCode(dataView[j]);
                } else if (dataView[j] === 0) {
                    sample.signature += "\\0";
                } else {
                    sample.signature += ".";
                }
            }
            
            // 格式识别
            if (sample.signature.indexOf('.!9h') !== -1) {
                sample.format = "Type1_Vector_Data";
            } else if (sample.signature.indexOf('.C.Q') !== -1) {
                sample.format = "Type2_POI_Data";
            } else if (sample.signature.indexOf('.C.U') !== -1) {
                sample.format = "Type3_Unknown_Data";
            } else if (sample.signature.indexOf('DICE') !== -1) {
                sample.format = "DICE_Block";
            } else if (sample.signature.indexOf('ANS') !== -1) {
                sample.format = "ANS_Container";
            } else if (dataView[0] === 0x08) {
                sample.format = "Compressed_Data";
                sample.isCompressed = true;
            }
            
            console.log("=== SQLite BLOB数据样本 ===");
            console.log("样本名称: " + sample.name);
            console.log("数据格式: " + sample.format);
            console.log("完整大小: " + sample.fullSize + " 字节");
            console.log("提取大小: " + sample.extractedSize + " 字节");
            console.log("是否压缩: " + (sample.isCompressed ? "是" : "否"));
            console.log("魔数头部: " + sample.hexHeader);
            console.log("可读签名: '" + sample.signature.substring(0, 40) + "'");
            console.log("数据地址: " + sample.address);
            
            // 显示完整hex dump
            console.log("\n=== 完整Hex Dump ===");
            console.log(hexdump(blobData, {length: extractSize, ansi: false}));
            console.log("=======================\n");
            
            extractorStats.extractedSamples.push(sample);
            extractorStats.successfulExtractions++;
            
            console.log("[SQLite Extract] 样本提取完成！总样本数: " + extractorStats.successfulExtractions);
            
        } catch (e) {
            console.log("[SQLite Extract Error] " + sampleName + " 提取失败: " + e);
            console.log("[SQLite Extract Error] 错误详情: " + e.stack);
        }
    }
    
    // === 生成SQLite报告 ===
    function generateSQLiteReport() {
        console.log("\n=== SQLite BLOB数据提取报告 ===");
        console.log("总调用次数: " + extractorStats.totalCalls);
        console.log("采样次数: " + Math.floor(extractorStats.totalCalls / CONFIG.SAMPLE_RATE));
        console.log("发现BLOB: " + extractorStats.blobFound + " 次");
        console.log("成功提取: " + extractorStats.successfulExtractions + " 个样本");
        
        if (extractorStats.extractedSamples.length > 0) {
            console.log("\n数据样本统计:");
            var formatStats = {};
            for (var i = 0; i < extractorStats.extractedSamples.length; i++) {
                var sample = extractorStats.extractedSamples[i];
                if (!formatStats[sample.format]) {
                    formatStats[sample.format] = 0;
                }
                formatStats[sample.format]++;
            }
            
            for (var format in formatStats) {
                console.log("  " + format + ": " + formatStats[format] + " 个");
            }
            
            console.log("\n最新样本详情:");
            var latest = extractorStats.extractedSamples[extractorStats.extractedSamples.length - 1];
            console.log("  名称: " + latest.name);
            console.log("  格式: " + latest.format);  
            console.log("  大小: " + latest.fullSize + " 字节");
            console.log("  签名: " + latest.signature.substring(0, 30));
        }
        
        console.log("==================================\n");
    }
    
    // === 库等待函数 ===
    function waitForLibrary(name, callback) {
        var attempts = 0;
        function check() {
            try {
                var lib = Module.findBaseAddress(name);
                if (lib) {
                    console.log("[Library] " + name + " 基址: " + lib);
                    callback(lib);
                    return;
                }
            } catch (e) {}
            
            if (++attempts < 30) {
                setTimeout(check, 1000);
            } else {
                console.log("[Error] " + name + " 加载超时");
            }
        }
        check();
    }
    
    // === 主函数 ===
    function main() {
        console.log("[Main] 初始化SQLite BLOB数据提取器...");
        
        setTimeout(function() {
            waitForLibrary("libamapnsq.so", function(libBase) {
                setupSQLiteBlobHook(libBase);
                
                // 定期生成报告
                setInterval(generateSQLiteReport, 20000);
            });
            
            console.log("[SQLite Data Extractor] SQLite数据提取器已启动!");
            console.log("采样率: 1/" + CONFIG.SAMPLE_RATE + ", 最大样本: " + CONFIG.MAX_SAMPLES);
            console.log("现在移动地图，观察SQLite BLOB数据...");
            
        }, 3000);
    }
    
    // === 启动 ===
    main();
    
})();