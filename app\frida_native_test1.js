/******************************************************************************
 * (ES5-兼容) Frida脚本: ans_trace_parser_chain.js
 *
 * 目标: 追踪从高层协调函数到具体解析函数的调用链，捕获原始数据。
 *
 * 核心策略:
 * 1. 同时 Hook sub_5C394 (协调者) 和 sub_10F88 (DICE-AM块解析者)。
 * 2. 验证调用顺序，确认是前者调用后者。
 * 3. 在 sub_10F88 被调用时，捕获并 hexdump 其输入参数，这极有可能是
 *    最原始的、解压后的数据缓冲区。
 *
 * 使用方法:
 * frida -U -f com.autonavi.minimap -l ans_trace_parser_chain.js --no-pause
 ******************************************************************************/

(function() {
    'use strict';

    var analyzer = {
        decompressed: 0,
        structured: 0,
        renderData: 0
    };
    console.log("[解析链追踪] 脚本启动中... (ES5 兼容)");

    var hooked = false;
    var checkModuleInterval = setInterval(function() {
        if (hooked) {
            clearInterval(checkModuleInterval);
            return;
        }

        var libamapnsqModule = Process.findModuleByName("libamapnsq.so");
        if (libamapnsqModule) {
            hooked = true;
            clearInterval(checkModuleInterval);
            
            Java.perform(function() {
                console.log("[解析链追踪] Java环境就绪。");
                console.log("[解析链追踪] 目标模块 libamapnsq.so 已加载，基地址: " + libamapnsqModule.base);
                //hookFunctions(libamapnsqModule);
                uncompressAMStructure()
            });
        }
    }, 500);

    function hookFunctions(module) {
        var coordinatorAddr = module.base.add(0x5C394);
        var parserAddr = module.base.add(0x10F88);

        console.log("[解析链追踪] Hooking 协调者 (sub_5C394) at: " + coordinatorAddr);
        Interceptor.attach(coordinatorAddr, {
            onEnter: function(args) {
                console.log("\n-------------------------------------------------------------");
                console.log("[协调者] sub_5C394 进入");
                console.log("[协调者] sub_5C394 进入",args[0]);
                console.log("[协调者] sub_5C394 进入",args);
            },
        
            onLeave: function(retval) {
                 console.log("[协调者] sub_5C394 退出");
                 console.log("-------------------------------------------------------------\n");
            }
        });

        console.log("[解析链追踪] Hooking 具体解析者 (sub_10F88) at: " + parserAddr);
        Interceptor.attach(parserAddr, {
            onEnter: function(args) {
                console.log("\n*************************************************************");
                console.log("********** 解析器 sub_10F88 命中! (DICE-AM?) **********");
                console.log("*************************************************************");
                console.log("--- args0 输入参数 ---" + hexdump(args[0]));
                console.log("--- args3 输入参数 ---" + hexdump(args[3]));

                // 直接访问已知的参数，不尝试获取参数数量
                var arg0 = args[0];
                var arg1 = args[1];

                console.log("--- sub_10F88 输入参数 ---");
                console.log("  arg0 (数据缓冲区?): " + arg0);
                console.log("  arg1 (上下文?): " + arg1);
                console.log("------------------------------------\n");

                console.log("--- Hexdump arg0 (解压后的原始数据块!) ---");
                try {
                    console.log(hexdump(arg0, {
                        length: 256, // 打印足够多的数据以供分析
                        header: true,
                        ansi: false
                    }));
                } catch (e) {
                    console.log("  无法读取 arg0 指向的内存: " + e.message);
                }
                console.log("*************************************************************\n");
            }
        });

        console.log("\n[解析链追踪] Hook 设置完毕。请操作App以触发。");
    }

    // ============= 解压后数据分析 =============
    function analyzeDecompressedData(buffer, size) {
        console.log("【解压后数据分析】");
        console.log("数据大小:", size, "字节");
        
        try {
            var bytes = new Uint8Array(buffer);
            
            // 1. 检查数据头部签名
            var header = "";
            for (var i = 0; i < Math.min(16, bytes.length); i++) {
                if (bytes[i] >= 32 && bytes[i] < 127) {
                    header += String.fromCharCode(bytes[i]);
                }
            }
            console.log("数据头部签名:", header);
            
            // 2. 根据签名识别数据类型
            if (header.indexOf("DICE-AM") >= 0) {
                console.log(" 识别为: DICE-AM矢量地图数据");
                analyzeDiceAMStructure(buffer);
            } else if (header.indexOf("{") >= 0 || header.indexOf("res_list") >= 0) {
                console.log(" 识别为: JSON配置数据");
                analyzeJSONStructure(buffer, size);
            } else if (hasChineseCharacters(bytes)) {
                console.log(" 识别为: 中文文本数据");
                analyzeTextStructure(buffer, size);
            } else {
                console.log(" 未知格式数据");
                showHexPreview(bytes);
            }
            
        } catch (e) {
            console.log(" 解压数据分析失败:", e.message);
        }
    }

        // DICE-AM矢量数据结构分析
    function analyzeDiceAMStructure(buffer) {
        try {
            var view = new DataView(buffer);
            
            console.log(" DICE-AM结构解析:");
            console.log("  魔数: DICE-AM");
            console.log("  版本:", view.getUint8(7));
            console.log("  数据长度:", view.getUint32(8, true));
            console.log("  矢量点数:", view.getUint32(12, true));
            
            // 解析前3个矢量点作为示例
            var offset = 16;
            for (var i = 0; i < 3 && offset + 12 < buffer.byteLength; i++) {
                var x = view.getFloat32(offset, true);
                var y = view.getFloat32(offset + 4, true);
                var type = view.getUint8(offset + 12);
                
                console.log("   矢量点" + i + ": (" + x.toFixed(6) + ", " + y.toFixed(6) + ") 类型=" + type);
                offset += 13;
            }
            
            console.log(" 这些矢量点将用于GPU渲染道路、建筑等地图要素");
            
        } catch (e) {
            console.log(" DICE-AM解析失败:", e.message);
        }
    }

    function uncompressAMStructure(){
        // Hook 1: zlib解压 - 捕获解压后的原始数据
        console.log("设置zlib解压Hook");

        var uncompress = Module.findExportByName("libz.so", "uncompress");
        if (uncompress) {
            Interceptor.attach(uncompress, {
                onEnter: function(args) {
                    this.dest = args[0];
                    this.destLen = args[1];
                },
                onLeave: function(retval) {
                    if (retval.toInt32() === 0 && analyzer.decompressed < 5) {
                        try {
                            var size = this.destLen.readU32();
                            var buffer = this.dest.readByteArray(size);
                            
                            analyzeDecompressedData(buffer, size);
                            analyzer.decompressed++;
                        } catch (e) {
                            console.log(" Hook处理失败:", e.message);
                        }
                    }
                }
            });
        }
    }
    // JSON配置数据结构分析
    function analyzeJSONStructure(buffer, size) {
        try {
            var bytes = new Uint8Array(buffer);
            var jsonStr = "";
            
            // 提取JSON字符串
            for (var i = 0; i < Math.min(size, 500); i++) {
                if (bytes[i] >= 32 && bytes[i] < 127) {
                    jsonStr += String.fromCharCode(bytes[i]);
                } else if (bytes[i] === 0) break;
            }
            
            console.log("JSON配置结构:");
            console.log("   原始内容:", jsonStr.substring(0, 150) + "...");
            
            // 查找关键配置项
            if (jsonStr.indexOf("style") >= 0) {
                console.log("   包含: 样式配置 (地图外观)");
            }
            if (jsonStr.indexOf("color") >= 0) {
                console.log("   包含: 颜色配置 (渲染颜色)");
            }
            if (jsonStr.indexOf("icon") >= 0) {
                console.log("   包含: 图标配置 (POI图标)");
            }
            
            console.log(" 这些配置将控制地图的视觉样式和渲染参数");
            
        } catch (e) {
            console.log(" JSON解析失败:", e.message);
        }
    }
    // 辅助函数
    function hasChineseCharacters(bytes) {
        for (var i = 0; i < bytes.length - 2; i++) {
            if (bytes[i] >= 0xE0 && bytes[i + 1] >= 0x80 && bytes[i + 2] >= 0x80) {
                return true;
            }
        }
        return false;
    }
    // 中文文本数据结构分析
    function analyzeTextStructure(buffer, size) {
        try {
            var bytes = new Uint8Array(buffer);
            var textCount = 0;
            
            // 统计中文字符
            for (var i = 0; i < size - 2; i++) {
                if (bytes[i] >= 0xE0 && bytes[i + 1] >= 0x80 && bytes[i + 2] >= 0x80) {
                    textCount++;
                    i += 2; // 跳过UTF-8字符的后续字节
                }
            }
            
            console.log(" 中文文本结构:");
            console.log("   检测到约", Math.floor(textCount / 3), "个中文字符");
            console.log("   内容类型: 地名、道路名、POI名称等");
            console.log(" 这些文本将通过字体引擎渲染为地图标注");
            
        } catch (e) {
            console.log(" 文本解析失败:", e.message);
        }
    }

    function showHexPreview(bytes) {
        var hex = "";
        for (var i = 0; i < Math.min(32, bytes.length); i++) {
            hex += bytes[i].toString(16).padStart(2, '0') + " ";
        }
        console.log("原始数据预览:", hex);
    }

})();