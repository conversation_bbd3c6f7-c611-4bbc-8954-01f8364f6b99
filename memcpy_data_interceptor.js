/*
 * 高德地图memcpy数据拦截器
 * 基于IDA Pro发现，Hook memcpy获取真实数据
 * 避开内存保护机制的创新方案
 * 版本: Frida 12.9.7 兼容
 */

console.log("[MemCpy Interceptor] 启动memcpy数据拦截器...");

var interceptCount = 0;
var maxIntercepts = 5;
var libBase = null;

// 安全的数据提取函数
function safeExtractData(srcPtr, size, destPtr) {
    if (interceptCount >= maxIntercepts) return;
    
    try {
        console.log("\n" + "=".repeat(80));
        console.log(" memcpy数据拦截 #" + (interceptCount + 1));
        console.log("源地址: " + srcPtr + " → 目标地址: " + destPtr);
        console.log("数据大小: " + size + " 字节");
        console.log("=".repeat(80));
        
        // 从目标地址读取数据（这应该是安全的）
        if (destPtr && !destPtr.isNull() && size > 0 && size < 5000) {
            // 延迟读取，确保memcpy完成
            setTimeout(function() {
                try {
                    var data = destPtr.readByteArray(Math.min(size, 1024));
                    var bytes = new Uint8Array(data);
                    
                    // 转换为可读文本
                    var text = "";
                    for (var i = 0; i < Math.min(500, bytes.length); i++) {
                        if (bytes[i] >= 32 && bytes[i] < 127) {
                            text += String.fromCharCode(bytes[i]);
                        } else if (bytes[i] === 10) {
                            text += "\n";
                        } else if (bytes[i] === 9) {
                            text += "\t";
                        } else {
                            text += ".";
                        }
                    }
                    
                    console.log(" 真实数据内容 (安全提取):");
                    console.log("----------------------------------------");
                    
                    // 检测数据类型
                    if (text.indexOf('<?xml') >= 0) {
                        console.log(" 数据类型: XML配置文件");
                        console.log(text);
                        interceptCount++;
                    }
                    else if (text.indexOf('{"') >= 0 || text.indexOf('"res_list"') >= 0) {
                        console.log(" 数据类型: JSON配置文件");
                        console.log(text);
                        interceptCount++;
                    }
                    else if (text.indexOf('DICE-AM') >= 0) {
                        console.log(" 数据类型: DICE-AM矢量数据");
                        console.log("ASCII内容: " + text.substring(0, 100));
                        
                        // 显示原始十六进制
                        console.log("十六进制数据:");
                        for (var j = 0; j < Math.min(128, bytes.length); j += 16) {
                            var hexLine = "  ";
                            var asciiLine = "  ";
                            for (var k = 0; k < 16 && (j + k) < bytes.length; k++) {
                                var hex = bytes[j + k].toString(16);
                                if (hex.length === 1) hex = "0" + hex;
                                hexLine += hex + " ";
                                
                                if (bytes[j + k] >= 32 && bytes[j + k] < 127) {
                                    asciiLine += String.fromCharCode(bytes[j + k]);
                                } else {
                                    asciiLine += ".";
                                }
                            }
                            console.log(hexLine + "| " + asciiLine);
                        }
                        interceptCount++;
                    }
                    else {
                        // 检查中文
                        var hasChinese = false;
                        for (var l = 0; l < Math.min(50, bytes.length); l++) {
                            if (bytes[l] >= 0xE4 && bytes[l] <= 0xE9) {
                                hasChinese = true;
                                break;
                            }
                        }
                        
                        if (hasChinese || text.length > 30) {
                            console.log(" 数据类型: " + (hasChinese ? "中文文本" : "通用文本"));
                            console.log(text);
                            
                            if (hasChinese) {
                                console.log("UTF-8字节序列:");
                                var hexStr = "  ";
                                for (var m = 0; m < Math.min(32, bytes.length); m++) {
                                    if (bytes[m] >= 0xE4 && bytes[m] <= 0xE9) {
                                        var hex = bytes[m].toString(16);
                                        if (hex.length === 1) hex = "0" + hex;
                                        hexStr += hex + " ";
                                    }
                                }
                                console.log(hexStr);
                            }
                            interceptCount++;
                        }
                    }
                    
                    console.log("----------------------------------------");
                    console.log(" 通过memcpy安全提取的真实数据！");
                    console.log("=".repeat(80) + "\n");
                    
                } catch (e) {
                    console.log("[延迟读取错误] " + e.message);
                }
            }, 100); // 100ms延迟确保memcpy完成
        }
        
    } catch (e) {
        console.log("[拦截错误] " + e.message);
    }
}

function setupMemcpyInterceptor() {
    setTimeout(function() {
        console.log("[Setup] 设置memcpy拦截器...");
        
        try {
            libBase = Module.findBaseAddress("libamapnsq.so");
            if (!libBase) {
                console.log("[Error] 未找到libamapnsq.so");
                return;
            }
            
            console.log("[Library] libamapnsq.so基址: " + libBase);
            
            // Hook memcpy - 这是关键创新！
            var memcpyPtr = Module.findExportByName("libc.so", "memcpy");
            if (memcpyPtr) {
                Interceptor.attach(memcpyPtr, {
                    onEnter: function(args) {
                        this.dest = args[0];
                        this.src = args[1];
                        this.size = args[2].toInt32();
                        
                        // 检查是否来自libamapnsq.so
                        var caller = this.returnAddress;
                        this.isFromLib = (caller.compare(libBase) >= 0 && 
                                         caller.compare(libBase.add(0x100000)) < 0);
                        
                        // 只关注有意义大小的数据
                        this.isInteresting = (this.size > 50 && this.size < 2000 && this.isFromLib);
                    },
                    onLeave: function(retval) {
                        if (this.isInteresting && interceptCount < maxIntercepts) {
                            console.log("[MemCpy检测] 大小: " + this.size + ", 调用者: " + this.returnAddress);
                            safeExtractData(this.src, this.size, this.dest);
                        }
                    }
                });
                console.log(" memcpy拦截器设置成功");
            }
            
            // 同时Hook memmove作为备选
            var memmovePtr = Module.findExportByName("libc.so", "memmove");
            if (memmovePtr) {
                Interceptor.attach(memmovePtr, {
                    onEnter: function(args) {
                        this.dest = args[0];
                        this.src = args[1];
                        this.size = args[2].toInt32();
                        
                        var caller = this.returnAddress;
                        this.isFromLib = (caller.compare(libBase) >= 0 && 
                                         caller.compare(libBase.add(0x100000)) < 0);
                        this.isInteresting = (this.size > 50 && this.size < 2000 && this.isFromLib);
                    },
                    onLeave: function(retval) {
                        if (this.isInteresting && interceptCount < maxIntercepts) {
                            console.log("[MemMove检测] 大小: " + this.size);
                            safeExtractData(this.src, this.size, this.dest);
                        }
                    }
                });
                console.log(" memmove拦截器设置成功");
            }
            
            console.log("[Ready] memcpy数据拦截器准备就绪");
            console.log(" 请移动地图触发数据复制操作");
            
        } catch (e) {
            console.log("[Setup Error] " + e.message);
        }
    }, 2000);
}

function generateInterceptReport() {
    console.log("\n === memcpy拦截报告 ===");
    console.log("已拦截数据: " + interceptCount + "/" + maxIntercepts);
    
    if (interceptCount >= maxIntercepts) {
        console.log(" 数据拦截完成！");
        console.log(" 成功通过memcpy获取真实运行时数据");
    } else if (interceptCount > 0) {
        console.log(" 已获取部分数据，继续操作地图");
    } else {
        console.log(" 请移动地图触发数据加载和复制");
    }
    console.log("=============================\n");
}

// 启动memcpy拦截器
setupMemcpyInterceptor();
setInterval(generateInterceptReport, 25000);

console.log("[MemCpy Interceptor] memcpy数据拦截器已加载"); 