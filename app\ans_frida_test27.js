// 简化的初始化脚本 - 避免超时
(function() {
  var pendingOperations = [];
  var lastGestureTime = 0;
  var isGestureActive = false;
  var eventSequence = [];
  var ansFileAccessed = {};
  var gestureCount = 0;
  
  // 记录事件函数，简化版
  function recordEvent(type, details) {
    // 只打印关键事件
    if (type === "手势开始" || type === "手势结束" || 
        type === "文件访问" || type === "线程创建") {
      console.log("[" + type + "] " + JSON.stringify(details));
    }
  }

  // 第1阶段：仅设置Java层监控
  function setupJavaMonitoring() {
    console.log("[+] 初始化Java监控...");
    Java.perform(function() {
      try {
        // Hook GLMapEngine的addGestureMessage方法
        var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
        if (GLMapEngine.addGestureMessage) {
          GLMapEngine.addGestureMessage.implementation = function(engineId, gestureMessage) {
            var type = gestureMessage.getType();
            var details = { engineId: engineId, type: type };
            
            // 获取手势信息
            try {
              if (type == 0) {  // 移动手势
                var moveMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.MoveGestureMapMessage"));
                var dx = moveMsg.mTouchDeltaX.value;
                var dy = moveMsg.mTouchDeltaY.value;
                
                // 识别手势开始和结束
                if (dx === 0 && dy === 0) {
                  if (isGestureActive) {
                    isGestureActive = false;
                    lastGestureTime = Date.now();
                    gestureCount++;
                    console.log("[手势结束] " + JSON.stringify({count: gestureCount}));
                  }
                } else if (!isGestureActive) {
                  isGestureActive = true;
                  console.log("[手势开始] " + JSON.stringify({dx: dx, dy: dy}));
                }
              }
            } catch(e) {
              console.log("[-] 手势分析错误: " + e);
            }
            
            // 调用原始方法
            return this.addGestureMessage(engineId, gestureMessage);
          };
          
          console.log("[+] 成功Hook手势处理方法");
        }
      } catch(e) {
        console.log("[-] Java监控错误: " + e);
      }
    });
  }
  
  // 第2阶段：设置文件监控
  function setupFileMonitoring() {
    console.log("[+] 设置文件监控...");
    
    // 只监控open函数
    var openPtr = Module.findExportByName("libc.so", "open");
    if (openPtr) {
      Interceptor.attach(openPtr, {
        onEnter: function(args) {
          try {
            this.path = args[0].readUtf8String();
            if (this.path && this.path.indexOf('.ans') !== -1) {
              this.isAnsFile = true;
            }
          } catch(e) {}
        },
        onLeave: function(result) {
          if (this.isAnsFile && result.toInt32() > 0) {
            console.log("[文件访问] " + JSON.stringify({
              path: this.path,
              fd: result.toInt32(),
              timeSinceGesture: Date.now() - lastGestureTime
            }));
          }
        }
      });
    }
    
    // 延迟设置其他监控
    pendingOperations.push(setupThreadMonitoring);
  }
  
  // 第3阶段：线程监控
  function setupThreadMonitoring() {
    console.log("[+] 设置线程监控...");
    var pthreadCreatePtr = Module.findExportByName(null, "pthread_create");
    if (pthreadCreatePtr) {
      Interceptor.attach(pthreadCreatePtr, {
        onEnter: function(args) {
          this.threadPtr = args[0];
        },
        onLeave: function(result) {
          if (result.toInt32() === 0) {
            var timeSinceGesture = Date.now() - lastGestureTime;
            if (timeSinceGesture < 5000) {
              console.log("[线程创建] " + JSON.stringify({
                ptr: this.threadPtr.toString(),
                timeSinceGesture: timeSinceGesture
              }));
            }
          }
        }
      });
    }
    
    // 延迟设置解压监控
    pendingOperations.push(setupLz4Monitoring);
  }
  
  // 第4阶段：LZ4解压监控（针对性监控）
  function setupLz4Monitoring() {
    console.log("[+] 设置LZ4解压监控...");
    
    // 只监控几个关键库
    var targetModules = ["libamapmain.so", "libamapr.so", "libamapnsq.so"];
    
    for (var i = 0; i < targetModules.length; i++) {
      try {
        var moduleName = targetModules[i];
        var lz4Ptr = Module.findExportByName(moduleName, "LZ4_decompress_safe");
        
        if (lz4Ptr) {
          Interceptor.attach(lz4Ptr, {
            onEnter: function(args) {
              try {
                this.inputSize = args[2] ? args[2].toInt32() : 0;
                this.record = (this.inputSize > 10000); // 只记录大于10KB的
              } catch(e) { this.record = false; }
            },
            onLeave: function(result) {
              if (this.record && result.toInt32() > 0) {
                console.log("[大块解压] LZ4: " + this.inputSize + " -> " + 
                           result.toInt32() + " 字节 (" + 
                           (Date.now() - lastGestureTime) + "ms)");
              }
            }
          });
          
          console.log("[+] 成功Hook " + moduleName + "!LZ4_decompress_safe");
        }
      } catch(e) {
        console.log("[-] 监控 " + moduleName + " 失败: " + e);
      }
    }
    
    console.log("[+] 完成所有监控设置");
  }

  // 分阶段执行初始化
  console.log("[+] 启动轻量级地图加载分析...");
  
  // 第1阶段：立即执行
  setupJavaMonitoring();
  
  // 第2阶段：延迟1秒
  setTimeout(function() {
    setupFileMonitoring();
    
    // 执行挂起的操作
    function executeNextOperation() {
      if (pendingOperations.length > 0) {
        var nextOp = pendingOperations.shift();
        setTimeout(function() {
          nextOp();
          // 执行下一个操作
          setTimeout(executeNextOperation, 1000);
        }, 500);
      }
    }
    
    // 开始执行序列
    setTimeout(executeNextOperation, 1000);
    
  }, 1000);
  
  console.log("[+] 脚本初始化完成，正在设置监控...");
})();

/*
Frida hook .so 必须要先加载


Java 层hook
Java.perform(()=>{

var module = Process.findModuleByName(".so")  获取模块


Interceptor.attach(Module.findExportByName(module,"函数名"),{
onEnter(args){ //方法接收的参数

},
onLeave(r){ //执行完成后的执行
}

})


Java.use()

})



*/