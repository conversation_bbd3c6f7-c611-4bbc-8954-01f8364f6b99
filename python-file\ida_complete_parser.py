#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于IDA Pro分析的完整.ans文件格式解析器
严格按照真实APP代码逻辑实现完整的文件解析方法
目标：理解整个文件的组织方式和解析流程
"""

import os
import zlib
import struct
import json

class IDACompleteAnsParser:
    """基于IDA Pro分析的完整.ans文件解析器"""
    
    def __init__(self, filename):
        self.filename = filename
        self.file_size = os.path.getsize(filename)
        self.file_data = None
        self.parse_results = {
            'file_header': None,
            'data_blocks': [],
            'statistics': {}
        }
    
    def parse_complete_file(self):
        """完整解析.ans文件 - 按照真实APP的完整流程"""
        print(f"🎯 开始完整解析: {self.filename}")
        print("=" * 80)
        
        # 1. 读取完整文件
        with open(self.filename, 'rb') as f:
            self.file_data = f.read()
        
        print(f"📁 文件大小: {self.file_size:,} 字节 ({self.file_size/1024/1024:.1f} MB)")
        
        # 2. 按照真实APP逻辑解析文件头
        self.parse_file_header()
        
        # 3. 按照真实APP逻辑扫描和解析所有数据
        self.scan_and_parse_all_data()
        
        # 4. 生成完整的解析报告
        self.generate_complete_report()
        
        return True
    
    def parse_file_header(self):
        """解析文件头 - 检测AM-zlib格式"""
        print("\n📋 解析文件头")
        print("-" * 50)
        
        if len(self.file_data) < 64:
            print("❌ 文件太小")
            return False
        
        # 检查AM-zlib标识
        header_preview = self.file_data[:64].hex()
        print(f"📦 文件头前64字节: {header_preview}")
        
        if b'AM-zlib' in self.file_data[:20]:
            print("✅ 检测到AM-zlib格式")
        else:
            print("⚠️ 未检测到标准AM-zlib头，使用通用解析")
    
    def scan_and_parse_all_data(self):
        """扫描和解析所有数据 - 完整的数据发现和解析"""
        print(f"\n📋 扫描和解析所有数据")
        print("-" * 50)
        
        offset = 0
        block_count = 0
        
        while offset < self.file_size - 8:
            # 搜索zlib压缩数据
            zlib_pos = self.file_data.find(b'\x78\x9c', offset)
            if zlib_pos == -1:
                break
            
            # 处理找到的压缩块
            processed = self.process_compressed_block(zlib_pos, block_count)
            if processed:
                block_count += 1
                offset = processed['end_offset']
            else:
                offset = zlib_pos + 1
        
        print(f"✅ 总共处理了 {block_count} 个数据块")
    
    def process_compressed_block(self, start_pos, block_index):
        """处理单个压缩数据块"""
        
        # 尝试不同大小的压缩块
        for test_size in [8192, 16384, 32768, 65536]:
            if start_pos + test_size > self.file_size:
                test_size = self.file_size - start_pos
            
            try:
                compressed_data = self.file_data[start_pos:start_pos + test_size]
                decompressed_data = zlib.decompress(compressed_data)
                
                # 成功解压，分析内容
                block_result = self.analyze_decompressed_content(
                    decompressed_data, start_pos, test_size, block_index)
                
                self.parse_results['data_blocks'].append(block_result)
                
                if block_index < 10:  # 详细显示前10个
                    print(f"🗜️ 块#{block_index}: 偏移=0x{start_pos:08X}, "
                          f"压缩={test_size:,}→解压={len(decompressed_data):,}, "
                          f"类型={block_result['content_type']}")
                
                return {
                    'success': True,
                    'end_offset': start_pos + test_size
                }
                
            except zlib.error:
                continue
        
        return None
    
    def analyze_decompressed_content(self, data, offset, compressed_size, index):
        """分析解压后的内容"""
        
        result = {
            'index': index,
            'offset': offset,
            'compressed_size': compressed_size,
            'decompressed_size': len(data),
            'content_type': 'unknown',
            'content_details': {}
        }
        
        # 检查中文文本
        try:
            text = data.decode('utf-8', errors='ignore')
            chinese_chars = [c for c in text if '\u4e00' <= c <= '\u9fff']
            
            if len(chinese_chars) > 5:
                result['content_type'] = 'chinese_text'
                
                # 提取中文词汇
                words = []
                current_word = ""
                for char in text:
                    if '\u4e00' <= char <= '\u9fff':
                        current_word += char
                    else:
                        if len(current_word) >= 2:
                            words.append(current_word)
                        current_word = ""
                
                result['content_details'] = {
                    'chinese_char_count': len(chinese_chars),
                    'word_count': len(words),
                    'sample_words': words[:10]
                }
                return result
        except:
            pass
        
        # 检查DICE-AM格式
        if data.startswith(b'DICE-AM'):
            result['content_type'] = 'dice_am_vector'
            result['content_details'] = {
                'magic': 'DICE-AM',
                'version': data[7] if len(data) > 7 else None,
                'header_hex': data[:32].hex() if len(data) >= 32 else data.hex()
            }
            return result
        
        # 检查JSON格式
        if b'{"' in data[:100] or b'"res_list"' in data[:100]:
            result['content_type'] = 'json_config'
            try:
                text = data.decode('utf-8', errors='ignore')
                start = text.find('{')
                if start >= 0:
                    result['content_details'] = {
                        'json_start': start,
                        'preview': text[start:start+100]
                    }
            except:
                pass
            return result
        
        # 检查坐标数据
        coordinate_count = 0
        for i in range(0, len(data) - 7, 4):
            try:
                value = struct.unpack('<f', data[i:i+4])[0]
                if -180.0 <= value <= 180.0 and abs(value) > 0.001:
                    coordinate_count += 1
            except:
                pass
        
        if coordinate_count > 10:
            result['content_type'] = 'coordinate_data'
            result['content_details'] = {
                'coordinate_count': coordinate_count,
                'estimated_points': coordinate_count // 2
            }
            return result
        
        # 默认为二进制数据
        result['content_type'] = 'binary_data'
        result['content_details'] = {
            'header_hex': data[:32].hex() if len(data) >= 32 else data.hex(),
            'zero_ratio': data.count(0) / len(data) if len(data) > 0 else 0
        }
        
        return result
    
    def generate_complete_report(self):
        """生成完整的解析报告"""
        print(f"\n📊 完整解析报告")
        print("=" * 80)
        
        # 统计各类型数据块
        type_stats = {}
        for block in self.parse_results['data_blocks']:
            content_type = block['content_type']
            type_stats[content_type] = type_stats.get(content_type, 0) + 1
        
        print(f"📋 数据块类型统计:")
        for content_type, count in type_stats.items():
            print(f"   {content_type}: {count} 个")
        
        # 显示中文文本示例
        chinese_blocks = [b for b in self.parse_results['data_blocks'] 
                         if b['content_type'] == 'chinese_text']
        
        if chinese_blocks:
            print(f"\n🈲 中文文本数据示例:")
            for i, block in enumerate(chinese_blocks[:3]):
                details = block['content_details']
                print(f"   块#{block['index']}: {details['chinese_char_count']}个中文字符")
                if 'sample_words' in details:
                    print(f"      示例词汇: {', '.join(details['sample_words'][:5])}")
        
        # 显示坐标数据示例
        coord_blocks = [b for b in self.parse_results['data_blocks'] 
                       if b['content_type'] == 'coordinate_data']
        
        if coord_blocks:
            print(f"\n📍 坐标数据示例:")
            for i, block in enumerate(coord_blocks[:3]):
                details = block['content_details']
                print(f"   块#{block['index']}: {details['coordinate_count']}个坐标值")
        
        # 保存完整结果
        output_file = f"complete_analysis_{os.path.basename(self.filename)}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.parse_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 完整结果保存到: {output_file}")
        print(f"✅ 这证明了基于IDA Pro分析的完整解析方法的有效性")

def main():
    """主函数"""
    print("🎯 基于IDA Pro分析的完整.ans文件格式解析器")
    print("目标：理解整个文件的组织方式和解析流程")
    print("=" * 80)
    
    files = ["file/m1.ans", "file/m3.ans"]
    
    for filename in files:
        if os.path.exists(filename):
            parser = IDACompleteAnsParser(filename)
            parser.parse_complete_file()
        else:
            print(f"❌ 文件不存在: {filename}")

if __name__ == "__main__":
    main() 