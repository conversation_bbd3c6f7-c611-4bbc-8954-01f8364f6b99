(function() {
    console.log("[ANS解析器-V32] 启动高德地图ANS文件解析...");

    // 全局变量
    var ansFileBuffers = {};      // 存储ANS文件数据在内存中的地址 { address: filePath }
    var fileDescriptors = {};     // 存储文件描述符到文件路径的映射 { fd: filePath }
    var lastGestureTime = 0;      // 最后手势时间
    var analysisComplete = false; // 避免重复分析
    var blockStats = {            // 数据块统计
        headerTypes: {},
        sizes: {}
    };

    // 阶段性执行，降低启动压力
    setTimeout(function() {
        // 第1阶段：监控文件打开和读取
        setupFileMonitoring();
        
        // 第2阶段：添加解压监控
        setTimeout(function() {
            setupDecompressionHooks();
            
            // 第3阶段：添加手势监控
            setTimeout(function() {
                setupGestureTracking();
            }, 2000);
        }, 3000);
    }, 1000);
    
    // 设置文件监控
    function setupFileMonitoring() {
        // 监控文件打开
        var openPtr = Module.findExportByName("libc.so", "open");
        if (openPtr) {
            Interceptor.attach(openPtr, {
                onEnter: function(args) {
                    try {
                        this.path = args[0].readUtf8String();
                        if (this.path && (this.path.indexOf("m1.ans") !== -1 || 
                                         this.path.indexOf("m3.ans") !== -1)) {
                            this.isAnsFile = true;
                            console.log("[ANS文件] 打开: " + this.path);
                        }
                    } catch(e) {}
                },
                onLeave: function(result) {
                    if (this.isAnsFile && result.toInt32() > 0) {
                        var fd = result.toInt32();
                        fileDescriptors[fd] = this.path;
                        console.log("[ANS文件] 文件描述符: " + fd + " => " + this.path);
                    }
                }
            });
        }
        
        // 监控文件读取
        var readPtr = Module.findExportByName("libc.so", "read");
        if (readPtr) {
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    var fd = args[0].toInt32();
                    if (fileDescriptors[fd]) {
                        this.fd = fd;
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                        this.path = fileDescriptors[fd];
                    }
                },
                onLeave: function(result) {
                    if (this.fd && result.toInt32() > 0) {
                        var bytesRead = result.toInt32();
                        
                        // 记录ANS文件数据在内存中的位置
                        if (this.path) {
                            ansFileBuffers[this.buffer] = {
                                path: this.path,
                                size: bytesRead,
                                time: Date.now()
                            };
                            
                            console.log("[ANS读取] " + this.path.split("/").pop() + 
                                       " 读取 " + bytesRead + " 字节到地址 " + this.buffer);
                            
                            // 记录数据块大小统计
                            if (!blockStats.sizes[bytesRead]) {
                                blockStats.sizes[bytesRead] = 0;
                            }
                            blockStats.sizes[bytesRead]++;
                            
                            // 检查与手势的时间关系
                            if (lastGestureTime) {
                                var timeSinceGesture = (Date.now() - lastGestureTime) / 1000;
                                console.log("[ANS关联] 手势后 " + timeSinceGesture.toFixed(2) + 
                                          " 秒读取文件数据");
                            }
                            
                            // 为避免内存泄漏，在一段时间后清除记录
                            var currentBuffer = this.buffer;
                            setTimeout(function() {
                                delete ansFileBuffers[currentBuffer];
                            }, 10000); // 10秒后清除
                            
                            // 捕获8K块头部信息
                            if (bytesRead == 8192) {
                                try {
                                    // 读取头部4字节作为类型识别
                                    var headerBytes = Memory.readByteArray(this.buffer, 4);
                                    var headerView = new Uint8Array(headerBytes);
                                    var headerHex = "";
                                    
                                    for (var i = 0; i < 4; i++) {
                                        var byteHex = headerView[i].toString(16);
                                        if (byteHex.length < 2) byteHex = "0" + byteHex;
                                        headerHex += byteHex;
                                    }
                                    
                                    // 检查是否有zlib压缩标记 (78 9c)
                                    if (headerView[0] === 0x78 && headerView[1] === 0x9c) {
                                        console.log("[ANS数据] 发现zlib压缩数据块标志 (78 9c)!");
                                    }
                                    
                                    // 统计头部类型
                                    if (!blockStats.headerTypes[headerHex]) {
                                        blockStats.headerTypes[headerHex] = 0;
                                        console.log("[ANS块类型] 发现新块类型: 0x" + headerHex);
                                    }
                                    blockStats.headerTypes[headerHex]++;
                                } catch(e) {
                                    console.log("[ANS错误] 分析块失败: " + e);
                                }
                            }
                        }
                    }
                }
            });
        }
        console.log("[ANS文件] 文件监控已设置");
    }
    
    // 设置解压函数监控
    function setupDecompressionHooks() {
        // 监控zlib的uncompress函数
        var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
        
        if (uncompressPtr) {
            console.log("[ANS解压] 找到zlib.uncompress函数: " + uncompressPtr);
            
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    this.destBuffer = args[0];     // 目标缓冲区 (解压后的数据)
                    this.destLenPtr = args[1];     // 目标缓冲区长度指针
                    this.sourceBuffer = args[2];   // 源缓冲区 (压缩的数据)
                    this.sourceLen = args[3].toInt32(); // 源长度
                    
                    // 检查这个压缩数据是否来自我们追踪的ANS文件
                    var isAnsFileData = false;
                    var ansFileInfo = null;
                    
                    // 查找距离最近的ANS文件缓冲区地址
                    var nearestAddress = null;
                    var minDistance = Number.MAX_VALUE;
                    
                    for (var addr in ansFileBuffers) {
                        var addrValue = parseInt(addr);
                        var sourceValue = parseInt(this.sourceBuffer);
                        var distance = Math.abs(sourceValue - addrValue);
                        
                        // 如果在100字节范围内，可能是同一块数据
                        if (distance < minDistance && distance < 100) {
                            nearestAddress = addr;
                            minDistance = distance;
                            ansFileInfo = ansFileBuffers[addr];
                            isAnsFileData = true;
                        }
                    }
                    
                    if (isAnsFileData && !analysisComplete) {
                        analysisComplete = true;  // 只分析一次
                        
                        console.log("\n★★★★★ 发现ANS文件解析逻辑入口! ★★★★★");
                        console.log("  [*] 文件: " + ansFileInfo.path);
                        console.log("  [*] zlib.uncompress 被以下调用:");
                        
                        // 记录调用栈
                        try {
                            var backtrace = Thread.backtrace(this.context, Backtracer.ACCURATE);
                            if (backtrace && backtrace.length > 0) {
                                console.log("[ANS解析调用栈]:");
                                
                                for (var i = 0; i < Math.min(10, backtrace.length); i++) {
                                    var addr = backtrace[i];
                                    var sym = DebugSymbol.fromAddress(addr);
                                    var modInfo = Process.findModuleByAddress(addr);
                                    var modName = modInfo ? modInfo.name : "unknown";
                                    
                                    if (i == 1) { // 直接调用者
                                        var offset = addr.sub(modInfo.base);
                                        console.log("  [*] 模块: " + modName + " (基址: " + modInfo.base + ")");
                                        console.log("  [*] 解析函数入口地址: " + addr + " (偏移量: " + offset + ")");
                                        
                                        // 打印周围指令
                                        console.log("\n--- 解析函数核心逻辑 (汇编代码) ---");
                                        var contextStart = addr.sub(10 * 4); // 调用前10条指令
                                        var contextEnd = addr.add(10 * 4);   // 调用后10条指令
                                        
                                        for (var p = contextStart; p.compare(contextEnd) <= 0; p = p.add(4)) {
                                            try {
                                                var currentInstr = Instruction.parse(p);
                                                var highlight = p.equals(addr) ? "  <-- 对uncompress的调用" : "";
                                                console.log("  " + p + "\t" + currentInstr.toString() + highlight);
                                            } catch(e) {
                                                console.log("  " + p + "\t[无法解析指令: " + e.message + "]");
                                            }
                                        }
                                        console.log("--- 代码逻辑结束 ---\n");
                                    } else {
                                        console.log("  " + i + ": " + (sym.name || "???") + " (" + modName + ")");
                                    }
                                }
                            }
                        } catch(e) {
                            console.log("[ANS错误] 获取调用栈失败: " + e);
                        }
                    }
                    
                    // 记录输入数据
                    try {
                        if (this.sourceBuffer && this.sourceLen > 0) {
                            var firstBytes = Memory.readByteArray(this.sourceBuffer, Math.min(4, this.sourceLen));
                            var view = new Uint8Array(firstBytes);
                            
                            // 检查是否有zlib压缩标记
                            if (view[0] === 0x78 && view[1] === 0x9c) {
                                console.log("[ANS解压] 源数据有zlib压缩标记 (78 9c)");
                                console.log("[ANS解压] 压缩数据 (前16字节):");
                                console.log(hexdump(this.sourceBuffer, {
                                    offset: 0,
                                    length: Math.min(16, this.sourceLen),
                                    header: true,
                                    ansi: false
                                }));
                            }
                        }
                    } catch(e) {}
                    
                    // 记录目标缓冲区大小
                    try {
                        this.originalDestLen = this.destLenPtr.readULong();
                        console.log("[ANS解压] 目标缓冲区大小: " + this.originalDestLen + " 字节");
                    } catch(e) {}
                    
                    console.log("[ANS解压] 调用 uncompress(dest=" + this.destBuffer + 
                               ", destLen=" + this.originalDestLen + 
                               ", source=" + this.sourceBuffer + 
                               ", sourceLen=" + this.sourceLen + ")");
                },
                onLeave: function(retval) {
                    var ret = retval.toInt32();
                    if (ret === 0) {
                        console.log("[ANS解压] 解压成功! 返回: " + ret);
                        
                        // 读取解压后的大小
                        var decompressedSize = this.destLenPtr.readULong();
                        console.log("[ANS解压] 解压后大小: " + decompressedSize + " 字节");
                        
                        // 查看解压后的数据
                        if (this.destBuffer && !this.destBuffer.isNull() && decompressedSize > 0) {
                            console.log("[ANS解压] 解压后数据 (前32字节):");
                            try {
                                console.log(hexdump(this.destBuffer, {
                                    offset: 0,
                                    length: Math.min(32, decompressedSize),
                                    header: true,
                                    ansi: false
                                }));
                            } catch(e) {}
                        }
                    } else {
                        console.log("[ANS解压] 解压失败，返回码: " + ret);
                    }
                }
            });
        } else {
            console.log("[!!!][ANS解析器-V32] 致命错误: 未能在libz.so中找到 uncompress 函数！");
        }
        
        console.log("[ANS解压] 解压函数监控已设置");
    }
    
    // 设置手势监控
    function setupGestureTracking() {
        try {
            Java.perform(function() {
                try {
                    var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                    if (GLMapEngine.addGestureMessage) {
                        console.log("[ANS手势] 找到手势处理方法");
                        
                        GLMapEngine.addGestureMessage.implementation = function(engineId, gestureMessage) {
                            try {
                                if (gestureMessage) {
                                    var type = gestureMessage.getType();
                                    lastGestureTime = Date.now();
                                    
                                    // 移动手势
                                    if (type == 0) {
                                        var moveMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.MoveGestureMapMessage"));
                                        var dx = moveMsg.mTouchDeltaX.value;
                                        var dy = moveMsg.mTouchDeltaY.value;
                                        
                                        if (Math.abs(dx) > 10 || Math.abs(dy) > 10) {
                                            console.log("[ANS手势] 移动: dx=" + dx + ", dy=" + dy);
                                        }
                                    }
                                    // 缩放手势
                                    else if (type == 1) {
                                        try {
                                            var scaleMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.ScaleGestureMapMessage"));
                                            var factor = scaleMsg.mScaleFactor.value;
                                            console.log("[ANS手势] 缩放: factor=" + factor.toFixed(2));
                                        } catch(e) {}
                                    }
                                }
                            } catch(e) {
                                console.log("[ANS手势] 处理错误: " + e);
                            }
                            return this.addGestureMessage(engineId, gestureMessage);
                        };
                    }
                } catch(e) {
                    console.log("[ANS手势] 设置失败: " + e);
                }
            });
        } catch(e) {
            console.log("[ANS手势] Java加载失败: " + e);
        }
        console.log("[ANS手势] 监控已设置");
    }
    
    console.log("[ANS解析器-V32] 设置完成，请操作地图...");
})();
