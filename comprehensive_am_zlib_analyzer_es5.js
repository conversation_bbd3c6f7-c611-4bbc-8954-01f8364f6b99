// 全面AM-zlib分析器 - 解决之前脚本的问题
// 目标: 找到真正的AM-zlib处理过程

console.log("[全面AM-zlib分析] 开始全面分析AM-zlib处理...");
console.log("[修复] 解决之前脚本的问题");

// 全面分析状态
var comprehensiveAnalysis = {
    fileOperations: [],        // 文件操作记录
    memoryMappings: [],        // 内存映射记录
    functionCalls: [],         // 函数调用记录
    dataPatterns: [],          // 数据模式记录
    startTime: Date.now()
};

// 安全的字节数组处理
function safeByteArrayToHex(byteArray, maxLen) {
    var hexBytes = [];
    var len = Math.min(maxLen || 16, byteArray.length);
    for (var i = 0; i < len; i++) {
        hexBytes.push(('0' + byteArray[i].toString(16)).slice(-2));
    }
    return hexBytes.join(' ');
}

// 检查AM-zlib魔数
function isAMZlibData(data) {
    if (data.length >= 8) {
        return data[0] === 0x41 && data[1] === 0x4d && data[2] === 0x2d && 
               data[3] === 0x7a && data[4] === 0x6c && data[5] === 0x69 && 
               data[6] === 0x62 && data[7] === 0x00;
    }
    return false;
}

// 检查.ans文件名
function isAnsFile(filename) {
    return filename && filename.toLowerCase().indexOf('.ans') !== -1;
}

// 1. Hook所有文件操作 - 不只是mmap
console.log("[1] Hook所有文件操作...");

try {
    var libc = Process.getModuleByName("libc.so");
    
    // Hook open
    var openPtr = libc.getExportByName("open");
    Interceptor.attach(openPtr, {
        onEnter: function(args) {
            this.filename = args[0].readCString();
        },
        onLeave: function(retval) {
            var fd = retval.toInt32();
            var filename = this.filename;
            
            if (fd > 0 && isAnsFile(filename)) {
                console.log("[文件打开] " + filename + " -> fd=" + fd);
                
                comprehensiveAnalysis.fileOperations.push({
                    type: "open",
                    filename: filename,
                    fd: fd,
                    timestamp: Date.now()
                });
            }
        }
    });
    
    // Hook read
    var readPtr = libc.getExportByName("read");
    Interceptor.attach(readPtr, {
        onEnter: function(args) {
            this.fd = args[0].toInt32();
            this.buf = args[1];
            this.count = args[2].toInt32();
        },
        onLeave: function(retval) {
            var bytesRead = retval.toInt32();
            var fd = this.fd;
            var count = this.count;
            
            // 关注大文件读取
            if (bytesRead > 1000000) { // 1MB+
                console.log("[大文件读取] fd=" + fd + ", 读取=" + (bytesRead/1024/1024).toFixed(1) + "MB");
                
                try {
                    var data = this.buf.readByteArray(Math.min(64, bytesRead));
                    var header = new Uint8Array(data);
                    
                    if (isAMZlibData(header)) {
                        console.log("  [发现AM-zlib数据!] fd=" + fd);
                        console.log("  头部: " + safeByteArrayToHex(header, 16));
                        
                        comprehensiveAnalysis.dataPatterns.push({
                            type: "am_zlib_read",
                            fd: fd,
                            size: bytesRead,
                            header: safeByteArrayToHex(header, 64),
                            timestamp: Date.now()
                        });
                        
                        // 设置对这个缓冲区的监控
                        setupBufferMonitoring(this.buf, bytesRead);
                    }
                } catch (e) {
                    console.log("  [错误] 读取数据失败: " + e.message);
                }
            }
        }
    });
    
    // Hook mmap
    var mmapPtr = libc.getExportByName("mmap");
    Interceptor.attach(mmapPtr, {
        onEnter: function(args) {
            this.length = args[1].toInt32();
            this.fd = args[4].toInt32();
        },
        onLeave: function(retval) {
            var mappedAddr = retval;
            var length = this.length;
            var fd = this.fd;
            
            if (!mappedAddr.equals(ptr(-1)) && length > 5000000) { // 5MB+
                console.log("[内存映射] fd=" + fd + ", 大小=" + (length/1024/1024).toFixed(1) + "MB, 地址=" + mappedAddr);
                
                comprehensiveAnalysis.memoryMappings.push({
                    address: mappedAddr.toString(),
                    fd: fd,
                    size: length,
                    timestamp: Date.now()
                });
                
                try {
                    var data = mappedAddr.readByteArray(Math.min(64, length));
                    var header = new Uint8Array(data);
                    console.log("  映射头部: " + safeByteArrayToHex(header, 16));
                    
                    if (isAMZlibData(header)) {
                        console.log("  [发现AM-zlib映射!] " + mappedAddr);
                        setupBufferMonitoring(mappedAddr, length);
                    }
                } catch (e) {
                    console.log("  [错误] 读取映射失败: " + e.message);
                }
            }
        }
    });
    
    console.log("[✓] 文件操作Hook设置成功");
} catch (e) {
    console.log("[✗] 文件操作Hook失败: " + e.message);
}

// 2. 设置缓冲区监控
function setupBufferMonitoring(bufferAddr, size) {
    console.log("[设置缓冲区监控] 地址=" + bufferAddr + ", 大小=" + (size/1024/1024).toFixed(1) + "MB");
    
    try {
        // 监控对这个缓冲区的访问
        var memcpyPtr = Process.getModuleByName("libc.so").getExportByName("memcpy");
        
        Interceptor.attach(memcpyPtr, {
            onEnter: function(args) {
                this.dest = args[0];
                this.src = args[1];
                this.n = args[2].toInt32();
                
                // 检查是否从AM-zlib缓冲区复制数据
                var srcAddr = this.src;
                if (srcAddr.compare(bufferAddr) >= 0 && srcAddr.compare(bufferAddr.add(size)) < 0) {
                    var offset = srcAddr.sub(bufferAddr).toInt32();
                    console.log("  [缓冲区访问] 偏移=" + offset + ", 大小=" + this.n);
                    
                    try {
                        var srcData = srcAddr.readByteArray(Math.min(32, this.n));
                        var srcHeader = new Uint8Array(srcData);
                        console.log("    数据: " + safeByteArrayToHex(srcHeader, 16));
                        
                        // 检查是否是压缩数据特征
                        if (srcHeader[0] === 0x00 && srcHeader[1] === 0x01 && 
                            srcHeader[2] === 0x00 && srcHeader[3] === 0x00) {
                            console.log("    [重要] 可能是压缩数据头部!");
                        }
                    } catch (e) {
                        // 忽略读取错误
                    }
                }
            }
        });
        
    } catch (e) {
        console.log("  [错误] 缓冲区监控设置失败: " + e.message);
    }
}

// 3. 强制搜索所有模块的所有函数
console.log("[3] 强制搜索所有可能的解压函数...");

function forceSearchAllFunctions() {
    try {
        var modules = Process.enumerateModules();
        console.log("[搜索] 总共 " + modules.length + " 个模块");
        
        for (var i = 0; i < modules.length; i++) {
            var module = modules[i];
            
            // 搜索所有模块，不只是高德相关的
            console.log("[搜索模块] " + module.name + " (大小: " + (module.size/1024/1024).toFixed(1) + "MB)");
            
            try {
                // 搜索导出函数
                var exports = Module.enumerateExports(module.name);
                var relevantFunctions = [];
                
                for (var j = 0; j < exports.length; j++) {
                    var exp = exports[j];
                    if (exp.name && (
                        exp.name.toLowerCase().indexOf("decompress") !== -1 ||
                        exp.name.toLowerCase().indexOf("decode") !== -1 ||
                        exp.name.toLowerCase().indexOf("inflate") !== -1 ||
                        exp.name.toLowerCase().indexOf("unpack") !== -1 ||
                        exp.name.toLowerCase().indexOf("zlib") !== -1 ||
                        exp.name.toLowerCase().indexOf("zstd") !== -1 ||
                        exp.name.toLowerCase().indexOf("ackor") !== -1 ||
                        exp.name.toLowerCase().indexOf("am") !== -1
                    )) {
                        relevantFunctions.push(exp);
                        console.log("  [相关函数] " + exp.name + " @ " + exp.address);
                    }
                }
                
                // Hook相关函数
                for (var k = 0; k < relevantFunctions.length; k++) {
                    hookRelevantFunction(relevantFunctions[k]);
                }
                
            } catch (e) {
                // 忽略模块搜索错误
            }
        }
    } catch (e) {
        console.log("[错误] 强制搜索失败: " + e.message);
    }
}

// Hook相关函数
function hookRelevantFunction(funcInfo) {
    try {
        Interceptor.attach(funcInfo.address, {
            onEnter: function(args) {
                console.log("  [调用] " + funcInfo.name);
                this.funcName = funcInfo.name;
                this.startTime = Date.now();
                
                // 分析前几个参数
                for (var i = 0; i < 3; i++) {
                    try {
                        if (args[i] && !args[i].isNull()) {
                            var argData = args[i].readByteArray(Math.min(16, 1024));
                            if (argData) {
                                var header = new Uint8Array(argData);
                                console.log("    参数" + i + ": " + safeByteArrayToHex(header, 8));
                                
                                // 检查是否是AM-zlib或压缩数据
                                if (isAMZlibData(header)) {
                                    console.log("    [发现] 参数" + i + "是AM-zlib数据!");
                                } else if (header[0] === 0x00 && header[1] === 0x01 && 
                                          header[2] === 0x00 && header[3] === 0x00) {
                                    console.log("    [发现] 参数" + i + "可能是压缩数据!");
                                }
                            }
                        }
                    } catch (e) {
                        // 忽略参数分析错误
                    }
                }
            },
            onLeave: function(retval) {
                var duration = Date.now() - this.startTime;
                console.log("  [返回] " + this.funcName + " -> " + retval + " (耗时: " + duration + "ms)");
                
                comprehensiveAnalysis.functionCalls.push({
                    function_name: this.funcName,
                    return_value: retval.toString(),
                    duration: duration,
                    timestamp: Date.now()
                });
            }
        });
    } catch (e) {
        // 忽略Hook错误
    }
}

// 延迟执行强制搜索
setTimeout(forceSearchAllFunctions, 2000);

// 4. 定期输出全面分析报告
setInterval(function() {
    var runtime = Math.floor((Date.now() - comprehensiveAnalysis.startTime) / 1000);
    
    console.log("\n[全面分析报告] ==========================================");
    console.log("运行时间: " + runtime + "s");
    console.log("");
    
    console.log("文件操作:");
    console.log("  .ans文件打开: " + comprehensiveAnalysis.fileOperations.length + " 次");
    for (var i = 0; i < Math.min(3, comprehensiveAnalysis.fileOperations.length); i++) {
        var op = comprehensiveAnalysis.fileOperations[i];
        console.log("  " + (i+1) + ". " + op.filename + " (fd=" + op.fd + ")");
    }
    
    console.log("");
    console.log("内存映射:");
    console.log("  大文件映射: " + comprehensiveAnalysis.memoryMappings.length + " 次");
    for (var i = 0; i < Math.min(3, comprehensiveAnalysis.memoryMappings.length); i++) {
        var mapping = comprehensiveAnalysis.memoryMappings[i];
        console.log("  " + (i+1) + ". " + (mapping.size/1024/1024).toFixed(1) + "MB @ " + mapping.address);
    }
    
    console.log("");
    console.log("数据模式:");
    console.log("  AM-zlib数据发现: " + comprehensiveAnalysis.dataPatterns.length + " 次");
    for (var i = 0; i < Math.min(3, comprehensiveAnalysis.dataPatterns.length); i++) {
        var pattern = comprehensiveAnalysis.dataPatterns[i];
        console.log("  " + (i+1) + ". fd=" + pattern.fd + ", " + (pattern.size/1024/1024).toFixed(1) + "MB");
    }
    
    console.log("");
    console.log("函数调用:");
    console.log("  相关函数调用: " + comprehensiveAnalysis.functionCalls.length + " 次");
    for (var i = 0; i < Math.min(3, comprehensiveAnalysis.functionCalls.length); i++) {
        var call = comprehensiveAnalysis.functionCalls[i];
        console.log("  " + (i+1) + ". " + call.function_name + " (耗时: " + call.duration + "ms)");
    }
    
    console.log("===============================================\n");
}, 20000);

console.log("[全面分析] 脚本已启动...");
console.log("[目标] 全面分析AM-zlib处理过程");
console.log("[提示] 请打开地图并移动以触发数据处理");
