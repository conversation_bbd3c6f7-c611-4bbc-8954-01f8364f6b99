     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[验证脚本] 开始验证高德地图离线数据处理流程...
[版本信息] ES5兼容版本，适用于Frida 12.9.7
[\u2717] libamapnsq.so 未找到
[\u2713] libz.so 已加载，基址: 0x7f8cab1000
[\u2713] libc.so 已加载，基址: 0x7f8dc9d000
[验证] uncompress 函数地址: 0x7f8cac667c
[\u2713] zlib uncompress Hook 设置成功
[\u2713] libc read Hook 设置成功
[验证脚本] 验证脚本已启动，等待地图操作...
[提示] 请在地图上进行移动操作以触发数据加载流程
[IDA Pro验证] 关键函数地址已通过IDA Pro静态分析验证
  - girf_sqlite3_bind_blob: 0x15000
  - 数据处理函数: 0x5c060
  - AES解密函数: 0xb55c
[ES5兼容] 脚本已优化为ES5语法，兼容Frida 12.9.7
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [Hook] 检测到可能的.ans文件读取 (#1)
  文件描述符: 41
  读取字节数: 4096
  数据头部: 08 04 05 06 04 04 05 04
[Hook] 检测到可能的.ans文件读取 (#2)
  文件描述符: 41
  读取字节数: 4096
  数据头部: 08 07 05 03 04 05 07 08
[Hook] 检测到可能的.ans文件读取 (#3)
  文件描述符: 41
  读取字节数: 4096
  数据头部: 08 03 06 47 36 41 07 03
[Hook] 检测到可能的.ans文件读取 (#4)
  文件描述符: 41
  读取字节数: 4096
  数据头部: 08 23 64 15 06 06 0a 02
[Hook] zlib uncompress 被调用 (#1)
Error: access violation accessing 0xb61
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 4700
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 58 12 00 00 58 12 00 00  x.......X...X...
[Hook] zlib uncompress 被调用 (#2)
Error: access violation accessing 0x60c
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 2684
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 78 0a 00 00 78 0a 00 00  x.......x...x...
[Hook] zlib uncompress 被调用 (#3)
Error: access violation accessing 0x5e6
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 10408
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 a4 28 00 00 a4 28 00 00  x........(...(..
[Hook] zlib uncompress 被调用 (#4)
Error: access violation accessing 0xc2d
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 5300
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 b0 14 00 00 b0 14 00 00  x...............
[Hook] zlib uncompress 被调用 (#5)
Error: access violation accessing 0x6f2
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 3096
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 14 0c 00 00 14 0c 00 00  x...............
[Hook] zlib uncompress 被调用 (#6)
Error: access violation accessing 0x86a
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 11712
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 bc 2d 00 00 bc 2d 00 00  x........-...-..
[Hook] zlib uncompress 被调用 (#7)
Error: access violation accessing 0xc35
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 5304
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 b4 14 00 00 b4 14 00 00  x...............
[Hook] zlib uncompress 被调用 (#8)
Error: access violation accessing 0x77f
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 3336
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 04 0d 00 00 04 0d 00 00  x...............
[Hook] zlib uncompress 被调用 (#9)
[Hook] zlib 解压成功，解压后大小: 11760
Error: access violation accessing 0x883
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 ec 2d 00 00 ec 2d 00 00  x........-...-..
[Hook] zlib uncompress 被调用 (#10)
Error: access violation accessing 0xc35
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 5304
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 b4 14 00 00 b4 14 00 00  x...............
[Hook] zlib uncompress 被调用 (#11)
Error: access violation accessing 0x874
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 3712
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 7c 0e 00 00 7c 0e 00 00  x.......|...|...
[Hook] zlib uncompress 被调用 (#12)
Error: access violation accessing 0x8b8
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 12024
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 f4 2e 00 00 f4 2e 00 00  x...............
[Hook] zlib uncompress 被调用 (#13)
Error: access violation accessing 0xdc9
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 6304
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 9c 18 00 00 9c 18 00 00  x...............
[Hook] zlib uncompress 被调用 (#14)
Error: access violation accessing 0x800
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 3612
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 18 0e 00 00 18 0e 00 00  x...............
[Hook] zlib uncompress 被调用 (#15)
Error: access violation accessing 0x9af
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 13288
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 e4 33 00 00 e4 33 00 00  x........3...3..
[Hook] zlib uncompress 被调用 (#16)
Error: access violation accessing 0xd9c
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 5924
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 20 17 00 00 20 17 00 00  x....... ... ...
[Hook] zlib uncompress 被调用 (#17)
Error: access violation accessing 0x979
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 4372
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 10 11 00 00 10 11 00 00  x...............
[Hook] zlib uncompress 被调用 (#18)
Error: access violation accessing 0x910
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 13144
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 54 33 00 00 54 33 00 00  x.......T3..T3..
[Hook] zlib uncompress 被调用 (#19)
Error: access violation accessing 0xc2d
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 5300
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 b0 14 00 00 b0 14 00 00  x...............
[Hook] zlib uncompress 被调用 (#20)
Error: access violation accessing 0x6f2
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 3096
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 14 0c 00 00 14 0c 00 00  x...............
[Hook] zlib uncompress 被调用 (#21)
Error: access violation accessing 0x86a
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 11712
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 bc 2d 00 00 bc 2d 00 00  x........-...-..
[Hook] zlib uncompress 被调用 (#22)
Error: access violation accessing 0xb61
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 4700
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 58 12 00 00 58 12 00 00  x.......X...X...
[Hook] zlib uncompress 被调用 (#23)
Error: access violation accessing 0x60c
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 2684
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 78 0a 00 00 78 0a 00 00  x.......x...x...
[Hook] zlib uncompress 被调用 (#24)
Error: access violation accessing 0x5e6
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 10408
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 a4 28 00 00 a4 28 00 00  x........(...(..
[Hook] 检测到可能的.ans文件读取 (#5)
  文件描述符: 111
  读取字节数: 531
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用 (#25)
Error: access violation accessing 0x213
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2e d7 00 00 00 00 fe fe 00 00 2e d7  ................
[Hook] 检测到可能的.ans文件读取 (#6)
  文件描述符: 111
  读取字节数: 531
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用 (#26)
Error: access violation accessing 0x213
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2e d7 00 00 00 00 fe fe 00 00 2e d7  ................
[Hook] 检测到可能的.ans文件读取 (#7)
  文件描述符: 111
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用 (#27)
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取 (#8)
  文件描述符: 111
  读取字节数: 531
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用 (#28)
Error: access violation accessing 0x213
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2e d7 00 00 00 00 fe fe 00 00 2e d7  ................
[Hook] zlib uncompress 被调用 (#29)
Error: access violation accessing 0x9e6
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 4572
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 d8 11 00 00 d8 11 00 00  x...............
[Hook] zlib uncompress 被调用 (#30)
Error: access violation accessing 0x950
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 3920
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 4c 0f 00 00 4c 0f 00 00  x.......L...L...
[Hook] zlib uncompress 被调用 (#31)
Error: access violation accessing 0x64f
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 12136
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 64 2f 00 00 64 2f 00 00  x.......d/..d/..
[Hook] zlib uncompress 被调用 (#32)
Error: access violation accessing 0x134e
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 8700
    at /verification_script_es5.js:140
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 f8 21 00 00 f8 21 00 00  x........!...!..
[Hook] zlib uncompress 被调用 (#33)
Error: access violation accessing 0xcb6
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 6356
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 d0 18 00 00 d0 18 00 00  x...............
[Hook] zlib uncompress 被调用 (#34)
Error: access violation accessing 0xba7
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 15376
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 0c 3c 00 00 0c 3c 00 00  x........<...<..
[Hook] zlib uncompress 被调用 (#35)
[Hook] zlib 解压成功，解压后大小: 3436
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 68 0d 00 00 68 0d 00 00  x.......h...h...
[Hook] zlib uncompress 被调用 (#36)
[Hook] zlib 解压成功，解压后大小: 2972
Error: access violation accessing 0x869
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
Error: access violation accessing 0x707
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 98 0b 00 00 98 0b 00 00  x...............
[Hook] zlib uncompress 被调用 (#37)
Error: access violation accessing 0x4d1
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 9136
    at /verification_script_es5.js:140
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 ac 23 00 00 ac 23 00 00  x........#...#..
[Hook] zlib uncompress 被调用 (#38)
Error: access violation accessing 0x1747
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 10664
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 a4 29 00 00 a4 29 00 00  x........)...)..
[Hook] zlib uncompress 被调用 (#39)
Error: access violation accessing 0xcc1
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 6340
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 c0 18 00 00 c0 18 00 00  x...............
[Hook] zlib uncompress 被调用 (#40)
Error: access violation accessing 0xe8f
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 17480
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 44 44 00 00 44 44 00 00  x.......DD..DD..
[Hook] zlib uncompress 被调用 (#41)
Error: access violation accessing 0x145d
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 10200
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 d4 27 00 00 d4 27 00 00  x........'...'..
[Hook] zlib uncompress 被调用 (#42)
Error: access violation accessing 0xe81
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 6748
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 58 1a 00 00 58 1a 00 00  x.......X...X...
[Hook] zlib uncompress 被调用 (#43)
Error: access violation accessing 0xc80
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 17664
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 fc 44 00 00 fc 44 00 00  x........D...D..
[Hook] 检测到可能的.ans文件读取 (#9)
  文件描述符: 153
  读取字节数: 8192
  数据头部: 08 cf 0d 3c 63 fa 59 bd
[Hook] 检测到可能的.ans文件读取 (#10)
  文件描述符: 152
  读取字节数: 4096
  数据头部: 08 cf 0d 3c 63 fa 59 bd
[Hook] 检测到可能的.ans文件读取 (#11)
  文件描述符: 153
  读取字节数: 8192
  数据头部: 08 00 00 00 5b 00 00 00
[Hook] 检测到可能的.ans文件读取 (#12)
  文件描述符: 173
  读取字节数: 4096
  数据头部: 08 fb c4 a8 2e 7b e8 b8
[Hook] zlib uncompress 被调用 (#44)
Error: access violation accessing 0xe32
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 6440
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 24 19 00 00 24 19 00 00  x.......$...$...
[Hook] zlib uncompress 被调用 (#45)
Error: access violation accessing 0xaba
    at frida/runtime/core.js:144
[Hook] zlib 解压成功，解压后大小: 5332
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 d0 14 00 00 d0 14 00 00  x...............
    at /verification_script_es5.js:140
[Hook] zlib uncompress 被调用 (#46)
Error: access violation accessing 0xaa7
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 14952
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 64 3a 00 00 64 3a 00 00  x.......d:..d:..
[Hook] 检测到可能的.ans文件读取 (#13)
  文件描述符: 153
  读取字节数: 8192
  数据头部: 08 00 00 00 1b 00 00 00
[Hook] 检测到可能的.ans文件读取 (#14)
  文件描述符: 166
  读取字节数: 4096
  数据头部: 08 c3 db 4a 34 a4 04 ee
[Hook] 检测到可能的.ans文件读取 (#15)
  文件描述符: 193
  读取字节数: 4096
  数据头部: 08 00 00 9b 00 e2 5a ce
[Hook] 检测到可能的.ans文件读取 (#16)
  文件描述符: 193
  读取字节数: 4096
  数据头部: 08 00 00 9b 00 e2 5a ce
[Hook] 检测到可能的.ans文件读取 (#17)
  文件描述符: 153
  读取字节数: 4096
  数据头部: 08 00 00 00 1b 00 00 00
[Hook] 检测到可能的.ans文件读取 (#18)
  文件描述符: 153
  读取字节数: 4096
  数据头部: 08 70 0d 3b cb 67 4b bd
[Hook] 检测到可能的.ans文件读取 (#19)
  文件描述符: 98
  读取字节数: 4096
  数据头部: 08 21 a4 fa 66 d8 9b bf
[Hook] 检测到可能的.ans文件读取 (#20)
  文件描述符: 193
  读取字节数: 4096
  数据头部: 08 01 72 65 73 2f 6c 61
[Hook] 检测到可能的.ans文件读取 (#21)
  文件描述符: 193
  读取字节数: 4096
  数据头部: 08 01 72 65 73 2f 6c 61
[Hook] 检测到可能的.ans文件读取 (#22)
  文件描述符: 204
  读取字节数: 4096
  数据头部: 08 cf 0d 3c 63 fa 59 bd
[Hook] zlib uncompress 被调用 (#47)
Error: access violation accessing 0xa78
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 5456
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 4c 15 00 00 4c 15 00 00  x.......L...L...
[Hook] zlib uncompress 被调用 (#48)
Error: access violation accessing 0xb37
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 4656
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 2c 12 00 00 2c 12 00 00  x.......,...,...
[Hook] zlib uncompress 被调用 (#49)
Error: access violation accessing 0x929
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 15080
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 e4 3a 00 00 e4 3a 00 00  x........:...:..
[Hook] zlib uncompress 被调用 (#50)
Error: access violation accessing 0x89c
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 4776
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 a4 12 00 00 a4 12 00 00  x...............
[Hook] zlib uncompress 被调用 (#51)
Error: access violation accessing 0xa10
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 4380
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 01 00 00 18 11 00 00 18 11 00 00  x...............
[Hook] zlib uncompress 被调用 (#52)
Error: access violation accessing 0x766
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 13112
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 01 03 00 00 34 33 00 00 34 33 00 00  x.......43..43..
[Hook] 检测到可能的.ans文件读取 (#23)
  文件描述符: 209
  读取字节数: 4096
  数据头部: 08 00 00 00 1b 00 00 00
[Hook] 检测到可能的.ans文件读取 (#24)
  文件描述符: 103
  读取字节数: 8
  数据头部: 08 00 00 00 00 00 00 00
[统计] 运行时间: 10s, 文件读取: 24, zlib解压: 52, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取 (#25)
  文件描述符: 209
  读取字节数: 4096
  数据头部: 08 70 0d 3b cb 67 4b bd
[Hook] 检测到可能的.ans文件读取 (#26)
  文件描述符: 224
  读取字节数: 8000
  数据头部: 08 a2 5c 57 6a 14 f1 8a
[Hook] 检测到可能的.ans文件读取 (#27)
  文件描述符: 232
  读取字节数: 8192
  数据头部: 08 21 96 3d ac 5b 30 3d
[Hook] 检测到可能的.ans文件读取 (#28)
  文件描述符: 232
  读取字节数: 8192
  数据头部: 08 df 9e bd 5f d0 22 3e
[Hook] 检测到可能的.ans文件读取 (#29)
  文件描述符: 232
  读取字节数: 8192
  数据头部: 08 49 16 be f5 49 de bb
[Hook] 检测到可能的.ans文件读取 (#30)
  文件描述符: 232
  读取字节数: 8192
  数据头部: 08 a8 07 bd df 98 42 3d
[Hook] 检测到可能的.ans文件读取 (#31)
  文件描述符: 224
  读取字节数: 8000
  数据头部: 08 c0 cb 18 c0 8b 4a 95
[Hook] 检测到可能的.ans文件读取 (#32)
  文件描述符: 232
  读取字节数: 8192
  数据头部: 08 cf 88 bd 6f d5 da bd
[Hook] 检测到可能的.ans文件读取 (#33)
  文件描述符: 232
  读取字节数: 8192
  数据头部: 08 f9 84 bd 88 97 0a bd
[Hook] 检测到可能的.ans文件读取 (#34)
  文件描述符: 224
  读取字节数: 8000
  数据头部: 08 cc 47 4e bd bd e3 a8
[Hook] 检测到可能的.ans文件读取 (#35)
  文件描述符: 232
  读取字节数: 8192
  数据头部: 08 7d a3 3d b5 d4 89 bd
[Hook] 检测到可能的.ans文件读取 (#36)
  文件描述符: 234
  读取字节数: 4096
  数据头部: 08 00 00 00 66 73 6d 6e
[Hook] 检测到可能的.ans文件读取 (#37)
  文件描述符: 166
  读取字节数: 4096
  数据头部: 08 c3 db 4a 34 a4 04 ee
[Hook] 检测到可能的.ans文件读取 (#38)
  文件描述符: 238
  读取字节数: 4096
  数据头部: 08 c3 00 15 fe c8 ff 9a
[Hook] 检测到可能的.ans文件读取 (#39)
  文件描述符: 238
  读取字节数: 4096
  数据头部: 08 bd 05 6a f7 59 07 e7
[Hook] 检测到可能的.ans文件读取 (#40)
  文件描述符: 111
  读取字节数: 531
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用 (#53)
Error: access violation accessing 0x213
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2e d7 00 00 00 00 fe fe 00 00 2e d7  ................
[Hook] 检测到可能的.ans文件读取 (#41)
  文件描述符: 232
  读取字节数: 8192
  数据头部: 08 50 e8 3d d6 4b d8 be
[Hook] 检测到可能的.ans文件读取 (#42)
  文件描述符: 224
  读取字节数: 8000
  数据头部: 08 48 bb e1 38 43 d1 71
[Hook] 检测到可能的.ans文件读取 (#43)
  文件描述符: 193
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用 (#54)
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
[Hook] 检测到可能的.ans文件读取 (#44)
  文件描述符: 224
  读取字节数: 8000
  数据头部: 08 25 ba c8 07 32 e9 72
[Hook] 检测到可能的.ans文件读取 (#45)
  文件描述符: 193
  读取字节数: 345
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用 (#55)
Error: access violation accessing 0x159
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad  ....,.........,.
[Hook] 检测到可能的.ans文件读取 (#46)
  文件描述符: 193
  读取字节数: 139
  数据头部: 78 9c ed ca 31 0a c2 30
[Hook] zlib uncompress 被调用 (#56)
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 04 1f a2 00 1f e0 1f ca 1f bc 1f a2  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取 (#47)
  文件描述符: 224
  读取字节数: 8000
  数据头部: 08 3f 88 0b 22 a3 fd 19
[Hook] 检测到可能的.ans文件读取 (#48)
  文件描述符: 232
  读取字节数: 8192
  数据头部: 08 35 ea 3d 71 60 5e be
[Hook] 检测到可能的.ans文件读取 (#49)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 68 34 86 19 aa b7 d9
[Hook] 检测到可能的.ans文件读取 (#50)
  文件描述符: 224
  读取字节数: 987
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用 (#57)
Error: access violation accessing 0x3db
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 7b 05 00 00 00 00 fe fe 00 00 7b 05  ....{.........{.
[Hook] 检测到可能的.ans文件读取 (#51)
  文件描述符: 224
  读取字节数: 987
  数据头部: 78 9c 73 f1 74 76 d5 75
[Hook] zlib uncompress 被调用 (#58)
Error: access violation accessing 0x3db
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  44 49 43 45 2d 41 4d 00 aa 00 89 8d cf 8d 00 00  DICE-AM.........
00000010  00 01 00 00 7b 05 00 00 00 00 fe fe 00 00 7b 05  ....{.........{.
[Hook] 检测到可能的.ans文件读取 (#52)
  文件描述符: 224
  读取字节数: 2813
  数据头部: 78 9c ed 57 6f 6c 1c c5
[Hook] zlib uncompress 被调用 (#59)
Error: access violation accessing 0xafd
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 8192
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  0d 00 00 00 02 09 92 00 0d 85 09 92 00 00 00 00  ................
00000010  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Hook] 检测到可能的.ans文件读取 (#53)
  文件描述符: 243
  读取字节数: 4096
  数据头部: 08 00 00 9b 00 e2 5a ce
[Hook] 检测到可能的.ans文件读取 (#54)
  文件描述符: 232
  读取字节数: 8192
  数据头部: 08 0a 10 3d f9 02 33 be
[Hook] 检测到可能的.ans文件读取 (#55)
  文件描述符: 238
  读取字节数: 4096
  数据头部: 08 01 00 00 00 1c 08 01
[Hook] 检测到可能的.ans文件读取 (#56)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 9c 6b 65 0a 63 30 60
[Hook] 检测到可能的.ans文件读取 (#57)
  文件描述符: 238
  读取字节数: 4096
  数据头部: 08 02 54 08 02 00 00 00
[Hook] 检测到可能的.ans文件读取 (#58)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 17 52 68 a7 0b 5d 01
[Hook] 检测到可能的.ans文件读取 (#59)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 21 96 3d ac 5b 30 3d
[Hook] 检测到可能的.ans文件读取 (#60)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 90 94 3d 7a 2c a2 3c
[Hook] 检测到可能的.ans文件读取 (#61)
  文件描述符: 243
  读取字节数: 4096
  数据头部: 08 01 72 65 73 2f 6c 61
[Hook] 检测到可能的.ans文件读取 (#62)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 e9 d2 3c 2e 12 d9 3d
[Hook] 检测到可能的.ans文件读取 (#63)
  文件描述符: 166
  读取字节数: 4096
  数据头部: 08 c3 db 4a 34 a4 04 ee
[Hook] 检测到可能的.ans文件读取 (#64)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 50 5a 98 0a e7 5a 0f
[Hook] 检测到可能的.ans文件读取 (#65)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 46 0e a4 c1 ba de 99
[Hook] 检测到可能的.ans文件读取 (#66)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 62 92 cd ce b0 42 b6
[Hook] 检测到可能的.ans文件读取 (#67)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 df 9e bd 5f d0 22 3e
[Hook] 检测到可能的.ans文件读取 (#68)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 ac c8 26 78 fc b3 00
[Hook] 检测到可能的.ans文件读取 (#69)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 34 dc 66 0d 5f 2f 28
[Hook] 检测到可能的.ans文件读取 (#70)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 36 3b 8e e5 eb f3 1e
[Hook] 检测到可能的.ans文件读取 (#71)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 e7 2a 69 e1 a8 50 48
[Hook] 检测到可能的.ans文件读取 (#72)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 c9 77 10 bd 56 9e 37
[Hook] 检测到可能的.ans文件读取 (#73)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 d7 c2 bd a7 ba ae bd
[Hook] 检测到可能的.ans文件读取 (#74)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 ea f1 77 54 c6 db f9
[Hook] 检测到可能的.ans文件读取 (#75)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 cb e5 d0 36 11 35 40
[Hook] 检测到可能的.ans文件读取 (#76)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 90 db 67 84 dd eb 25
[Hook] 检测到可能的.ans文件读取 (#77)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 db 53 43 c5 0c 4b b1
[Hook] 检测到可能的.ans文件读取 (#78)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 ca 8a a5 68 b4 bc bb
[Hook] zlib uncompress 被调用 (#60)
Error: access violation accessing 0xc35
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 5304
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 b4 14 00 00 b4 14 00 00  x...............
[Hook] zlib uncompress 被调用 (#61)
Error: access violation accessing 0x865
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 3704
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  bc bc bc bc 02 00 01 00 03 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 01 00 00 74 0e 00 00 74 0e 00 00  x.......t...t...
[Hook] zlib uncompress 被调用 (#62)
Error: access violation accessing 0x8aa
    at frida/runtime/core.js:144
    at /verification_script_es5.js:140
[Hook] zlib 解压成功，解压后大小: 12024
  解压数据预览:            0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  ce ca 0b b1 02 00 01 00 0d 00 00 00 50 00 00 00  ............P...
00000010  78 00 13 00 00 03 01 00 f4 2e 00 00 f4 2e 00 00  x...............
[Hook] 检测到可能的.ans文件读取 (#79)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 9c 24 4d 97 d2 b2 4a
[Hook] 检测到可能的.ans文件读取 (#80)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 68 5b 51 92 54 c8 3b
[统计] 运行时间: 20s, 文件读取: 80, zlib解压: 62, SQLite绑定: 0, AES解密: 0, 数据分发: 0
[Hook] 检测到可能的.ans文件读取 (#81)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 49 16 be f5 49 de bb
[Hook] 检测到可能的.ans文件读取 (#82)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 cf be bc f7 35 bf 3d
[Hook] 检测到可能的.ans文件读取 (#83)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 20 3f b6 5a bb 69 e7
[Hook] 检测到可能的.ans文件读取 (#84)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 a8 07 bd df 98 42 3d
[Hook] 检测到可能的.ans文件读取 (#85)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 50 f5 3b e0 04 14 bd
[Hook] 检测到可能的.ans文件读取 (#86)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 b6 05 7a 40 ca 26 3b
[Hook] 检测到可能的.ans文件读取 (#87)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 ba be 11 be 58 91 e0
[Hook] 检测到可能的.ans文件读取 (#88)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 d8 f9 b4 27 72 8d fa
[Hook] 检测到可能的.ans文件读取 (#89)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 7b b7 03 dc ca cf 95
[Hook] 检测到可能的.ans文件读取 (#90)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 0b a6 14 ae 8d bc 63
[Hook] 检测到可能的.ans文件读取 (#91)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 36 1b bc c4 37 68 bd
[Hook] 检测到可能的.ans文件读取 (#92)
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 02 02 02 03 06 20 99
[Hook] 检测到可能的.ans文件读取 (#93)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 2e ed d5 ae 4e 8b 75
[Hook] 检测到可能的.ans文件读取 (#94)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 88 e0 9b 5f b5 ad 05
[Hook] 检测到可能的.ans文件读取 (#95)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 81 34 bb 9d 09 2f a3
[Hook] 检测到可能的.ans文件读取 (#96)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 88 ac 7a d1 4e ea 11
[Hook] 检测到可能的.ans文件读取 (#97)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 53 55 3e be 79 40 be
[Hook] 检测到可能的.ans文件读取 (#98)
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 03 00 00 04 0e 05 7f
[Hook] 检测到可能的.ans文件读取 (#99)
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 00 01 12 14 03 03 05
[Hook] 检测到可能的.ans文件读取 (#100)
  文件描述符: 280
  读取字节数: 4096
  数据头部: 08 04 08 03 07 0b 03 03
[Hook] 检测到可能的.ans文件读取 (#101)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 03 2d 95 44 f0 70 9d
[Hook] 检测到可能的.ans文件读取 (#102)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 cf 88 bd 6f d5 da bd
[Hook] 检测到可能的.ans文件读取 (#103)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 bd c0 cd 5f 68 18 48
[Hook] 检测到可能的.ans文件读取 (#104)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 f9 84 bd 88 97 0a bd
[Hook] 检测到可能的.ans文件读取 (#105)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 49 9a 3e e9 48 ae 3d
[Hook] 检测到可能的.ans文件读取 (#106)
  文件描述符: 10
  读取字节数: 16
  数据头部: 08 58 b5 2b 94 67 45 f4
[Hook] 检测到可能的.ans文件读取 (#107)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 dc 29 00 cc 29 df 00
[Hook] 检测到可能的.ans文件读取 (#108)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 7d a3 3d b5 d4 89 bd
[Hook] 检测到可能的.ans文件读取 (#109)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 39 d6 c6 cb f4 01 60
[Hook] 检测到可能的.ans文件读取 (#110)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 86 a8 3e 25 bb 28 be
[Hook] 检测到可能的.ans文件读取 (#111)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 8a 00 3a 50 96 93 4f
[Hook] 检测到可能的.ans文件读取 (#112)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 f9 2f e6 1b 8c 5f 7f
[Hook] 检测到可能的.ans文件读取 (#113)
  文件描述符: 279
  读取字节数: 4096
  数据头部: 08 02 02 02 03 06 20 99
[Hook] 检测到可能的.ans文件读取 (#114)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 3b 5b 5f 7b 26 98 04
[Hook] 检测到可能的.ans文件读取 (#115)
  文件描述符: 166
  读取字节数: 4096
  数据头部: 08 ab b5 a8 ff 01 85 e5
[Hook] 检测到可能的.ans文件读取 (#116)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 31 80 52 e8 7f bf a9
[Hook] 检测到可能的.ans文件读取 (#117)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 09 40 92 48 01 08 8b
[Hook] 检测到可能的.ans文件读取 (#118)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 f9 7f d3 e2 03 17 aa
[Hook] 检测到可能的.ans文件读取 (#119)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 fa a3 b1 88 e5 1f 54
[Hook] 检测到可能的.ans文件读取 (#120)
  文件描述符: 279
  读取字节数: 4096
  数据头部: 08 03 00 00 04 0e 05 7f
[Hook] 检测到可能的.ans文件读取 (#121)
  文件描述符: 279
  读取字节数: 4096
  数据头部: 08 00 01 12 14 03 03 05
[Hook] 检测到可能的.ans文件读取 (#122)
  文件描述符: 279
  读取字节数: 4096
  数据头部: 08 04 08 03 07 0b 03 03
[Hook] 检测到可能的.ans文件读取 (#123)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 41 00 91 08 00 00 f9
[Hook] 检测到可能的.ans文件读取 (#124)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 09 40 f9 00 01 3f d6
[Hook] 检测到可能的.ans文件读取 (#125)
  文件描述符: 166
  读取字节数: 4096
  数据头部: 08 ab b5 a8 ff 01 85 e5
[Hook] 检测到可能的.ans文件读取 (#126)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 e1 24 91 88 86 00 f8
[Hook] 检测到可能的.ans文件读取 (#127)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 93 40 4f 3c 23 1a d6
[Hook] 检测到可能的.ans文件读取 (#128)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 b5 94 1a 69 12 01 b9
[Hook] 检测到可能的.ans文件读取 (#129)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 01 40 f9 08 21 40 79
[Hook] 检测到可能的.ans文件读取 (#130)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 00 40 f9 08 09 40 f9
[Hook] 检测到可能的.ans文件读取 (#131)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 00 00 90 e0 23 00 91
[Hook] 检测到可能的.ans文件读取 (#132)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 50 e8 3d d6 4b d8 be
[Hook] 检测到可能的.ans文件读取 (#133)
  文件描述符: 276
  读取字节数: 4096
  数据头部: 08 00 00 9b 00 e2 5a ce
[Hook] 检测到可能的.ans文件读取 (#134)
  文件描述符: 276
  读取字节数: 4096
  数据头部: 08 00 00 9b 00 e2 5a ce
[Hook] 检测到可能的.ans文件读取 (#135)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 0b 99 21 44 12 c0 25
[Hook] 检测到可能的.ans文件读取 (#136)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 81 40 f9 00 01 3f d6
[Hook] 检测到可能的.ans文件读取 (#137)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 00 40 f9 08 11 40 f9
[Hook] 检测到可能的.ans文件读取 (#138)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 5d 40 f9 00 01 3f d6
[Hook] 检测到可能的.ans文件读取 (#139)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 fa a3 b1 88 e5 1f 54
[Hook] 检测到可能的.ans文件读取 (#140)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 f5 e7 3d db 40 8a be
[Hook] 检测到可能的.ans文件读取 (#141)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 01 00 34 01 05 00 51
[Hook] 检测到可能的.ans文件读取 (#142)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 61 43 f9 01 01 40 f9
[Hook] 检测到可能的.ans文件读取 (#143)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 5e e9 72 8e e3 d1 63
[Hook] 检测到可能的.ans文件读取 (#144)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 0c 40 f9 08 09 40 f9
[Hook] 检测到可能的.ans文件读取 (#145)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 27 ff 7d 53 d0 85 b3
[Hook] 检测到可能的.ans文件读取 (#146)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 04 40 f9 f3 03 00 aa
[Hook] 检测到可能的.ans文件读取 (#147)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 51 18 91 f4 03 00 aa
[Hook] 检测到可能的.ans文件读取 (#148)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 10 d0 3c b3 79 16 3e
[Hook] 检测到可能的.ans文件读取 (#149)
  文件描述符: 281
  读取字节数: 131072
  数据头部: 08 09 40 f9 34 39 40 f9
[Hook] 检测到可能的.ans文件读取 (#150)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 6a 8e f9 a0 fa de e1
[Hook] 检测到可能的.ans文件读取 (#151)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 f8 56 5a 1e 8f ed 8c
[Hook] 检测到可能的.ans文件读取 (#152)
  文件描述符: 166
  读取字节数: 4096
  数据头部: 08 c3 db 4a 34 a4 04 ee
[Hook] 检测到可能的.ans文件读取 (#153)
  文件描述符: 276
  读取字节数: 4096
  数据头部: 08 01 72 65 73 2f 6c 61
[Hook] 检测到可能的.ans文件读取 (#154)
  文件描述符: 276
  读取字节数: 4096
  数据头部: 08 01 72 65 73 2f 6c 61
[Hook] 检测到可能的.ans文件读取 (#155)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 35 ea 3d 71 60 5e be
[Hook] 检测到可能的.ans文件读取 (#156)
  文件描述符: 279
  读取字节数: 131072
  数据头部: 08 20 00 91 09 fd 5f c8
[Hook] 检测到可能的.ans文件读取 (#157)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 e2 8a b9 87 f3 d3 87
[Hook] 检测到可能的.ans文件读取 (#158)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 4f 9d 7e 81 9a 83 03
[Hook] 检测到可能的.ans文件读取 (#159)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 08 80 bb ed 42 be 3d
[Hook] 检测到可能的.ans文件读取 (#160)
  文件描述符: 296
  读取字节数: 1024
  数据头部: 08 f3 5e bb 3b a2 94 5a
[Hook] 检测到可能的.ans文件读取 (#161)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 20 d3 97 08 52 33 b9
[Hook] 检测到可能的.ans文件读取 (#162)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 c9 c7 d9 c5 41 43 cc
[Hook] 检测到可能的.ans文件读取 (#163)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 f8 2b a0 55 cf 63 2a
[Hook] 检测到可能的.ans文件读取 (#164)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 8a 5d 93 14 62 a8 58
[Hook] 检测到可能的.ans文件读取 (#165)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 0a 10 3d f9 02 33 be
[Hook] 检测到可能的.ans文件读取 (#166)
  文件描述符: 166
  读取字节数: 4096
  数据头部: 08 ab b5 a8 ff 01 85 e5
[Hook] 检测到可能的.ans文件读取 (#167)
  文件描述符: 228
  读取字节数: 4096
  数据头部: 08 62 f6 3d 00 f1 88 bd
[Hook] 检测到可能的.ans文件读取 (#168)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 83 ea cd 10 08 12 9b
[Hook] 检测到可能的.ans文件读取 (#169)
  文件描述符: 277
  读取字节数: 1024
  数据头部: 08 20 b0 f5 26 9b 5c 4f

[Remote::com.autonavi.minimap]-> [Hook] 检测到可能的.ans文件读取 (#170)
  文件描述符: 90
  读取字节数: 4096
  数据头部: 08 38 c6 7b 4b d1 34 09
[Hook] 检测到可能的.ans文件读取 (#171)
  文件描述符: 221
  读取字节数: 8000
  数据头部: 08 db 30 87 49 0a fc a6
