/*
 * 简单有效的高德地图数据解析脚本
 * 直接获取解压后数据和结构化数据，不废话
 */

console.log(" 启动简单有效Hook - 直接获取核心数据");

var count = 0;
var maxCount = 3;

// Hook zlib解压 - 获取解压后的真实数据
function hookUncompress() {
    var uncompress = Module.findExportByName("libz.so", "uncompress");
    if (!uncompress) {
        console.log("找不到libz.so:uncompress");
        return;
    }

    console.log(" Hook zlib解压");
    Interceptor.attach(uncompress, {
        onLeave: function(retval) {
            if (retval.toInt32() === 0 && count < maxCount) {
                try {
                    var destLen = this.context.r1.readU32();  // ARM64寄存器
                    var buffer = this.context.r0.readByteArray(Math.min(destLen, 1000));
                    
                    console.log("\n 解压后数据 #" + count);
                    console.log("大小:", destLen, "字节");
                    
                    // 检查数据内容
                    var bytes = new Uint8Array(buffer);
                    var header = "";
                    for (var i = 0; i < Math.min(16, bytes.length); i++) {
                        if (bytes[i] >= 32 && bytes[i] < 127) {
                            header += String.fromCharCode(bytes[i]);
                        }
                    }
                    console.log("数据头:", header);
                    
                    // 如果是DICE-AM数据，直接解析
                    if (header.indexOf("DICE-AM") >= 0) {
                        console.log(" DICE-AM矢量数据:");
                        try {
                            var view = new DataView(buffer);
                            var version = view.getUint8(7);
                            var pointCount = view.getUint32(12, true);
                            console.log("   版本:", version);
                            console.log("   点数:", pointCount);
                            
                            // 读取第一个坐标点
                            if (buffer.byteLength >= 24) {
                                var x = view.getFloat32(16, true);
                                var y = view.getFloat32(20, true);
                                console.log("   坐标1: (" + x.toFixed(6) + ", " + y.toFixed(6) + ")");
                            }
                        } catch (e) {
                            console.log("解析失败:", e.message);
                        }
                    }
                    
                    count++;
                    
                } catch (e) {
                    console.log(" 读取数据失败:", e.message);
                }
            }
        }
    });
}

// 启动
hookUncompress();

console.log(" Hook设置完成");
console.log(" 在地图中移动触发数据加载..."); 