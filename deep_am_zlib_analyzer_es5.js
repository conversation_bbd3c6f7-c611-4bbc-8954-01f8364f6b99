// 深度AM-zlib分析器 - 重新分析真实的解压过程
// 目标: 找到真正的AM-zlib解压函数和正确的数据格式

console.log("[深度AM-zlib分析] 开始重新分析AM-zlib解压过程...");
console.log("[目标] 找到真正的解压函数，而不是标准zlib");

// 深度分析状态
var deepAnalysis = {
    amZlibFiles: [],           // AM-zlib文件映射
    customDecompression: [],   // 自定义解压调用
    memoryPatterns: [],        // 内存模式分析
    functionCalls: [],         // 函数调用记录
    startTime: Date.now()
};

// 安全的字节数组处理
function safeByteArrayToHex(byteArray, maxLen) {
    var hexBytes = [];
    var len = Math.min(maxLen || 16, byteArray.length);
    for (var i = 0; i < len; i++) {
        hexBytes.push(('0' + byteArray[i].toString(16)).slice(-2));
    }
    return hexBytes.join(' ');
}

// 检查AM-zlib魔数
function isAMZlibData(data) {
    if (data.length >= 8) {
        return data[0] === 0x41 && data[1] === 0x4d && data[2] === 0x2d && 
               data[3] === 0x7a && data[4] === 0x6c && data[5] === 0x69 && 
               data[6] === 0x62 && data[7] === 0x00;
    }
    return false;
}

// 检查压缩数据特征
function hasCompressionPattern(data) {
    if (data.length >= 12) {
        return data[0] === 0x00 && data[1] === 0x01 && data[2] === 0x00 && data[3] === 0x00 &&
               data[10] === 0xfe && data[11] === 0xfe;
    }
    return false;
}

// 1. Hook mmap - 重点关注AM-zlib文件
console.log("[1] Hook mmap，寻找AM-zlib文件映射...");

try {
    var libc = Process.getModuleByName("libc.so");
    var mmapPtr = libc.getExportByName("mmap");
    
    Interceptor.attach(mmapPtr, {
        onEnter: function(args) {
            this.addr = args[0];
            this.length = args[1].toInt32();
            this.prot = args[2].toInt32();
            this.flags = args[3].toInt32();
            this.fd = args[4].toInt32();
            this.offset = args[5].toInt32();
        },
        onLeave: function(retval) {
            var mappedAddr = retval;
            var length = this.length;
            var fd = this.fd;
            
            // 关注大文件映射
            if (!mappedAddr.equals(ptr(-1)) && length > 10000000) { // 10MB+
                console.log("[大文件映射] fd=" + fd + ", 大小=" + (length/1024/1024).toFixed(1) + "MB");
                
                try {
                    var data = mappedAddr.readByteArray(Math.min(64, length));
                    var header = new Uint8Array(data);
                    
                    if (isAMZlibData(header)) {
                        console.log("  [发现AM-zlib文件!] " + mappedAddr);
                        console.log("  头部: " + safeByteArrayToHex(header, 16));
                        
                        // 保存AM-zlib文件信息
                        deepAnalysis.amZlibFiles.push({
                            address: mappedAddr.toString(),
                            fd: fd,
                            size: length,
                            header: safeByteArrayToHex(header, 64),
                            timestamp: Date.now()
                        });
                        
                        // 分析压缩数据部分
                        var compressedData = mappedAddr.add(16).readByteArray(Math.min(64, length - 16));
                        var compHeader = new Uint8Array(compressedData);
                        console.log("  压缩数据头: " + safeByteArrayToHex(compHeader, 32));
                        
                        if (hasCompressionPattern(compHeader)) {
                            console.log("  [确认] 发现压缩数据特征模式!");
                            
                            // 设置内存监控，监控对这个区域的访问
                            console.log("  [设置监控] 监控AM-zlib数据访问...");
                            setupAMZlibMonitoring(mappedAddr, length);
                        }
                    }
                } catch (e) {
                    console.log("  [错误] 读取映射数据失败: " + e.message);
                }
            }
        }
    });
    
    console.log("[✓] mmap Hook设置成功");
} catch (e) {
    console.log("[✗] mmap Hook失败: " + e.message);
}

// 2. 设置AM-zlib数据访问监控
function setupAMZlibMonitoring(baseAddr, size) {
    try {
        // 监控对AM-zlib数据的读取
        var readPtr = Process.getModuleByName("libc.so").getExportByName("memcpy");
        
        Interceptor.attach(readPtr, {
            onEnter: function(args) {
                this.dest = args[0];
                this.src = args[1];
                this.n = args[2].toInt32();
                
                // 检查是否从AM-zlib区域读取数据
                var srcAddr = this.src;
                if (srcAddr.compare(baseAddr) >= 0 && srcAddr.compare(baseAddr.add(size)) < 0) {
                    var offset = srcAddr.sub(baseAddr).toInt32();
                    console.log("[AM-zlib访问] 偏移=" + offset + ", 大小=" + this.n);
                    
                    try {
                        var srcData = srcAddr.readByteArray(Math.min(32, this.n));
                        var srcHeader = new Uint8Array(srcData);
                        console.log("  读取数据: " + safeByteArrayToHex(srcHeader, 16));
                        
                        // 记录访问模式
                        deepAnalysis.memoryPatterns.push({
                            type: "am_zlib_read",
                            offset: offset,
                            size: this.n,
                            data_header: safeByteArrayToHex(srcHeader, 16),
                            timestamp: Date.now()
                        });
                    } catch (e) {
                        // 忽略读取错误
                    }
                }
            }
        });
        
        console.log("  [✓] AM-zlib访问监控设置成功");
    } catch (e) {
        console.log("  [✗] AM-zlib访问监控设置失败: " + e.message);
    }
}

// 3. Hook可能的自定义解压函数
console.log("[3] 搜索自定义解压函数...");

// 搜索包含"decompress", "decode", "inflate"等关键词的函数
function searchCustomDecompressionFunctions() {
    try {
        var modules = Process.enumerateModules();
        
        for (var i = 0; i < modules.length; i++) {
            var module = modules[i];
            
            // 重点关注高德相关的模块
            if (module.name.indexOf("libgaode") !== -1 || 
                module.name.indexOf("libamap") !== -1 ||
                module.name.indexOf("base.odex") !== -1 ||
                module.name.indexOf("classes") !== -1) {
                
                console.log("[搜索模块] " + module.name);
                
                try {
                    // 搜索导出函数
                    var exports = Module.enumerateExports(module.name);
                    for (var j = 0; j < exports.length; j++) {
                        var exp = exports[j];
                        if (exp.name && (
                            exp.name.toLowerCase().indexOf("decompress") !== -1 ||
                            exp.name.toLowerCase().indexOf("decode") !== -1 ||
                            exp.name.toLowerCase().indexOf("inflate") !== -1 ||
                            exp.name.toLowerCase().indexOf("unpack") !== -1 ||
                            exp.name.toLowerCase().indexOf("zlib") !== -1
                        )) {
                            console.log("  [可能的解压函数] " + exp.name + " @ " + exp.address);
                            hookCustomFunction(exp.address, exp.name);
                        }
                    }
                } catch (e) {
                    // 忽略模块分析错误
                }
            }
        }
    } catch (e) {
        console.log("[错误] 搜索解压函数失败: " + e.message);
    }
}

// Hook自定义函数
function hookCustomFunction(address, name) {
    try {
        Interceptor.attach(address, {
            onEnter: function(args) {
                console.log("  [调用] " + name);
                this.funcName = name;
                this.startTime = Date.now();
                
                // 尝试分析参数
                try {
                    for (var i = 0; i < 4; i++) {
                        if (args[i]) {
                            var argData = args[i].readByteArray(Math.min(16, 1024));
                            if (argData) {
                                var header = new Uint8Array(argData);
                                console.log("    参数" + i + ": " + safeByteArrayToHex(header, 8));
                                
                                // 检查是否是压缩数据
                                if (hasCompressionPattern(header)) {
                                    console.log("    [发现] 参数" + i + "包含压缩数据特征!");
                                }
                            }
                        }
                    }
                } catch (e) {
                    // 忽略参数分析错误
                }
            },
            onLeave: function(retval) {
                var duration = Date.now() - this.startTime;
                console.log("  [返回] " + this.funcName + " -> " + retval + " (耗时: " + duration + "ms)");
                
                // 记录函数调用
                deepAnalysis.functionCalls.push({
                    function_name: this.funcName,
                    return_value: retval.toString(),
                    duration: duration,
                    timestamp: Date.now()
                });
            }
        });
    } catch (e) {
        // 忽略Hook错误
    }
}

// 延迟搜索函数
setTimeout(searchCustomDecompressionFunctions, 3000);

// 4. Hook标准zlib函数，但更仔细地分析
console.log("[4] 重新Hook标准zlib函数...");

try {
    var zlibModule = Process.getModuleByName("libz.so");
    var inflatePtr = zlibModule.getExportByName("inflate");
    
    Interceptor.attach(inflatePtr, {
        onEnter: function(args) {
            this.strm = args[0];
            this.flush = args[1].toInt32();
            
            try {
                // 读取zlib流结构
                var next_in = this.strm.readPointer();
                var avail_in = this.strm.add(8).readU32();
                
                if (next_in && avail_in > 0 && avail_in < 1000000) {
                    var inputData = next_in.readByteArray(Math.min(32, avail_in));
                    var inputHeader = new Uint8Array(inputData);
                    
                    console.log("[zlib inflate] 输入大小: " + avail_in);
                    console.log("  输入数据: " + safeByteArrayToHex(inputHeader, 16));
                    
                    // 检查是否来自AM-zlib数据
                    if (hasCompressionPattern(inputHeader)) {
                        console.log("  [重要] 这可能是AM-zlib压缩数据的一部分!");
                    }
                }
            } catch (e) {
                // 忽略分析错误
            }
        },
        onLeave: function(retval) {
            try {
                var next_out = this.strm.add(12).readPointer();
                var avail_out = this.strm.add(20).readU32();
                var total_out = this.strm.add(24).readU32();
                
                if (next_out && total_out > 0) {
                    console.log("[zlib inflate] 输出大小: " + total_out);
                    
                    if (total_out <= 1024) {
                        var outputData = next_out.readByteArray(Math.min(32, total_out));
                        var outputHeader = new Uint8Array(outputData);
                        console.log("  输出数据: " + safeByteArrayToHex(outputHeader, 16));
                        
                        // 检查输出是否是有意义的数据
                        if (outputHeader[0] === 0x44 && outputHeader[1] === 0x49 && 
                            outputHeader[2] === 0x43 && outputHeader[3] === 0x45) {
                            console.log("  [发现] 输出是DICE格式数据!");
                        }
                    }
                }
            } catch (e) {
                // 忽略分析错误
            }
        }
    });
    
    console.log("[✓] zlib Hook设置成功");
} catch (e) {
    console.log("[✗] zlib Hook失败: " + e.message);
}

// 5. 定期输出深度分析报告
setInterval(function() {
    var runtime = Math.floor((Date.now() - deepAnalysis.startTime) / 1000);
    
    console.log("\n[深度分析报告] ==========================================");
    console.log("运行时间: " + runtime + "s");
    console.log("");
    
    console.log("AM-zlib文件发现:");
    console.log("  发现数量: " + deepAnalysis.amZlibFiles.length + " 个");
    
    for (var i = 0; i < deepAnalysis.amZlibFiles.length; i++) {
        var file = deepAnalysis.amZlibFiles[i];
        console.log("  文件" + (i+1) + ": " + (file.size/1024/1024).toFixed(1) + "MB");
        console.log("    地址: " + file.address);
        console.log("    头部: " + file.header.substring(0, 32));
    }
    
    console.log("");
    console.log("内存访问模式:");
    console.log("  AM-zlib访问: " + deepAnalysis.memoryPatterns.length + " 次");
    
    for (var i = 0; i < Math.min(5, deepAnalysis.memoryPatterns.length); i++) {
        var pattern = deepAnalysis.memoryPatterns[i];
        console.log("  访问" + (i+1) + ": 偏移=" + pattern.offset + ", 大小=" + pattern.size);
        console.log("    数据: " + pattern.data_header);
    }
    
    console.log("");
    console.log("函数调用统计:");
    console.log("  自定义函数调用: " + deepAnalysis.functionCalls.length + " 次");
    
    for (var i = 0; i < Math.min(3, deepAnalysis.functionCalls.length); i++) {
        var call = deepAnalysis.functionCalls[i];
        console.log("  调用" + (i+1) + ": " + call.function_name);
        console.log("    返回值: " + call.return_value + ", 耗时: " + call.duration + "ms");
    }
    
    console.log("===============================================\n");
}, 30000);

console.log("[深度分析] 脚本已启动...");
console.log("[目标] 找到真正的AM-zlib解压算法");
console.log("[提示] 请打开地图并移动以触发数据处理");
