// 节点1: 离线文件读取流程分析器
// 目标: 深入分析.ans文件的读取、映射和头部解析过程

console.log("[节点1分析] 离线文件读取流程分析器启动");
console.log("[目标] 追踪.ans文件从磁盘到内存的完整流程");

// 分析状态
var fileReadingAnalysis = {
    ansFiles: [],              // .ans文件操作记录
    memoryMappings: [],        // 内存映射记录
    fileDescriptors: {},       // 文件描述符映射
    readOperations: [],        // 读取操作记录
    headerParsing: [],         // 头部解析记录
    callStacks: [],            // 调用栈记录
    startTime: Date.now()
};

// 安全的字节数组处理
function safeByteArrayToHex(byteArray, maxLen) {
    var hexBytes = [];
    var len = Math.min(maxLen || 32, byteArray.length);
    for (var i = 0; i < len; i++) {
        hexBytes.push(('0' + byteArray[i].toString(16)).slice(-2));
    }
    return hexBytes.join(' ');
}

// 检查是否是.ans文件
function isAnsFile(filename) {
    return filename && filename.toLowerCase().indexOf('.ans') !== -1;
}

// 检查AM-zlib头部
function isAMZlibHeader(data) {
    if (data.length >= 8) {
        return data[0] === 0x41 && data[1] === 0x4d && data[2] === 0x2d && 
               data[3] === 0x7a && data[4] === 0x6c && data[5] === 0x69 && 
               data[6] === 0x62 && data[7] === 0x00;
    }
    return false;
}

// 获取调用栈
function getCallStack() {
    try {
        var stack = Thread.backtrace(this.context, Backtracer.ACCURATE);
        var stackInfo = [];
        for (var i = 0; i < Math.min(10, stack.length); i++) {
            var frame = stack[i];
            var symbol = DebugSymbol.fromAddress(frame);
            stackInfo.push({
                address: frame.toString(),
                symbol: symbol.name || "unknown",
                module: symbol.moduleName || "unknown"
            });
        }
        return stackInfo;
    } catch (e) {
        return [];
    }
}

console.log("[1] Hook文件系统调用...");

try {
    var libc = Process.getModuleByName("libc.so");
    
    // Hook open/openat - 文件打开
    var openPtr = libc.getExportByName("open");
    var openatPtr = libc.getExportByName("openat");
    
    Interceptor.attach(openPtr, {
        onEnter: function(args) {
            this.filename = args[0].readCString();
            this.flags = args[1].toInt32();
            this.mode = args[2] ? args[2].toInt32() : 0;
        },
        onLeave: function(retval) {
            var fd = retval.toInt32();
            var filename = this.filename;
            
            if (fd > 0 && isAnsFile(filename)) {
                console.log("[文件打开] " + filename);
                console.log("  fd: " + fd + ", flags: 0x" + this.flags.toString(16));
                
                // 记录文件描述符映射
                fileReadingAnalysis.fileDescriptors[fd] = {
                    filename: filename,
                    flags: this.flags,
                    mode: this.mode,
                    openTime: Date.now(),
                    callStack: getCallStack.call(this)
                };
                
                fileReadingAnalysis.ansFiles.push({
                    type: "open",
                    filename: filename,
                    fd: fd,
                    flags: this.flags,
                    timestamp: Date.now(),
                    callStack: getCallStack.call(this)
                });
            }
        }
    });
    
    // Hook mmap - 内存映射
    var mmapPtr = libc.getExportByName("mmap");
    Interceptor.attach(mmapPtr, {
        onEnter: function(args) {
            this.addr = args[0];
            this.length = args[1].toInt32();
            this.prot = args[2].toInt32();
            this.flags = args[3].toInt32();
            this.fd = args[4].toInt32();
            this.offset = args[5].toInt32();
        },
        onLeave: function(retval) {
            var mappedAddr = retval;
            var length = this.length;
            var fd = this.fd;
            var offset = this.offset;
            
            if (!mappedAddr.equals(ptr(-1)) && fd in fileReadingAnalysis.fileDescriptors) {
                var fileInfo = fileReadingAnalysis.fileDescriptors[fd];
                
                console.log("[内存映射] " + fileInfo.filename);
                console.log("  地址: " + mappedAddr + ", 大小: " + (length/1024/1024).toFixed(1) + "MB");
                console.log("  偏移: " + offset + ", 保护: 0x" + this.prot.toString(16));
                
                try {
                    // 读取并分析文件头部
                    var headerData = mappedAddr.readByteArray(Math.min(64, length));
                    var header = new Uint8Array(headerData);
                    
                    console.log("  文件头部: " + safeByteArrayToHex(header, 32));
                    
                    if (isAMZlibHeader(header)) {
                        console.log("  [确认] AM-zlib格式文件!");
                        
                        // 解析AM-zlib头部
                        var version = header[8];
                        var flags = header[9];
                        var geometryType = header[10];
                        var geometryFlags = header[11];
                        
                        console.log("    版本: " + version);
                        console.log("    几何类型: 0x" + geometryType.toString(16));
                        console.log("    几何标志: 0x" + geometryFlags.toString(16));
                        
                        // 分析压缩数据头部
                        if (length > 16) {
                            var compressedHeader = mappedAddr.add(16).readByteArray(Math.min(48, length - 16));
                            var compHeader = new Uint8Array(compressedHeader);
                            console.log("    压缩数据头: " + safeByteArrayToHex(compHeader, 32));
                            
                            // 解析自定义头部结构
                            if (compHeader.length >= 32) {
                                var magic = (compHeader[3] << 24) | (compHeader[2] << 16) | (compHeader[1] << 8) | compHeader[0];
                                var field1 = (compHeader[7] << 24) | (compHeader[6] << 16) | (compHeader[5] << 8) | compHeader[4];
                                var field2 = (compHeader[11] << 24) | (compHeader[10] << 16) | (compHeader[9] << 8) | compHeader[8];
                                
                                console.log("      魔数: 0x" + magic.toString(16));
                                console.log("      字段1: " + field1 + " (可能是块数)");
                                console.log("      字段2: 0x" + field2.toString(16) + " (包含fe fe)");
                            }
                        }
                        
                        // 设置内存访问监控
                        setupMemoryAccessMonitoring(mappedAddr, length, fileInfo.filename);
                    }
                    
                } catch (e) {
                    console.log("  [错误] 读取头部失败: " + e.message);
                }
                
                fileReadingAnalysis.memoryMappings.push({
                    filename: fileInfo.filename,
                    fd: fd,
                    address: mappedAddr.toString(),
                    length: length,
                    offset: offset,
                    protection: this.prot,
                    timestamp: Date.now(),
                    callStack: getCallStack.call(this)
                });
            }
        }
    });
    
    // Hook read - 文件读取
    var readPtr = libc.getExportByName("read");
    Interceptor.attach(readPtr, {
        onEnter: function(args) {
            this.fd = args[0].toInt32();
            this.buf = args[1];
            this.count = args[2].toInt32();
        },
        onLeave: function(retval) {
            var bytesRead = retval.toInt32();
            var fd = this.fd;
            var count = this.count;
            
            if (bytesRead > 0 && fd in fileReadingAnalysis.fileDescriptors) {
                var fileInfo = fileReadingAnalysis.fileDescriptors[fd];
                
                // 只记录大的读取操作
                if (bytesRead > 1000) {
                    console.log("[文件读取] " + fileInfo.filename);
                    console.log("  读取: " + bytesRead + "/" + count + " 字节");
                    
                    try {
                        var data = this.buf.readByteArray(Math.min(64, bytesRead));
                        var header = new Uint8Array(data);
                        console.log("  数据头部: " + safeByteArrayToHex(header, 32));
                        
                        if (isAMZlibHeader(header)) {
                            console.log("  [确认] 读取到AM-zlib数据!");
                        }
                        
                    } catch (e) {
                        console.log("  [错误] 读取数据失败: " + e.message);
                    }
                    
                    fileReadingAnalysis.readOperations.push({
                        filename: fileInfo.filename,
                        fd: fd,
                        bytesRead: bytesRead,
                        requestedBytes: count,
                        timestamp: Date.now(),
                        callStack: getCallStack.call(this)
                    });
                }
            }
        }
    });
    
    // Hook close - 文件关闭
    var closePtr = libc.getExportByName("close");
    Interceptor.attach(closePtr, {
        onEnter: function(args) {
            this.fd = args[0].toInt32();
        },
        onLeave: function(retval) {
            var fd = this.fd;
            
            if (fd in fileReadingAnalysis.fileDescriptors) {
                var fileInfo = fileReadingAnalysis.fileDescriptors[fd];
                console.log("[文件关闭] " + fileInfo.filename + " (fd=" + fd + ")");
                
                delete fileReadingAnalysis.fileDescriptors[fd];
            }
        }
    });
    
    console.log("[✓] 文件系统Hook设置成功");
    
} catch (e) {
    console.log("[✗] 文件系统Hook失败: " + e.message);
}

// 设置内存访问监控
function setupMemoryAccessMonitoring(baseAddr, size, filename) {
    console.log("[设置内存监控] " + filename + " @ " + baseAddr);
    
    try {
        // 监控对映射内存的访问
        var memcpyPtr = Process.getModuleByName("libc.so").getExportByName("memcpy");
        
        Interceptor.attach(memcpyPtr, {
            onEnter: function(args) {
                this.dest = args[0];
                this.src = args[1];
                this.n = args[2].toInt32();
                
                // 检查是否从.ans文件映射区域复制数据
                var srcAddr = this.src;
                if (srcAddr.compare(baseAddr) >= 0 && srcAddr.compare(baseAddr.add(size)) < 0) {
                    var offset = srcAddr.sub(baseAddr).toInt32();
                    
                    if (this.n > 100) {  // 只记录大的复制操作
                        console.log("  [内存访问] " + filename);
                        console.log("    偏移: " + offset + ", 大小: " + this.n);
                        
                        try {
                            var srcData = srcAddr.readByteArray(Math.min(32, this.n));
                            var srcHeader = new Uint8Array(srcData);
                            console.log("    数据: " + safeByteArrayToHex(srcHeader, 16));
                            
                            // 检查是否是压缩数据特征
                            if (srcHeader[0] === 0x78 && srcHeader[1] === 0x9c) {
                                console.log("    [发现] zlib压缩数据!");
                            } else if (srcHeader[0] === 0x0d && srcHeader[1] === 0x00) {
                                console.log("    [发现] TEXT格式数据!");
                            }
                            
                        } catch (e) {
                            // 忽略读取错误
                        }
                    }
                }
            }
        });
        
    } catch (e) {
        console.log("  [错误] 内存监控设置失败: " + e.message);
    }
}

console.log("[2] Hook高德地图特定函数...");

// 搜索高德地图模块中可能的文件处理函数
setTimeout(function() {
    try {
        var modules = Process.enumerateModules();
        
        for (var i = 0; i < modules.length; i++) {
            var module = modules[i];
            
            // 重点关注高德相关模块
            if (module.name.indexOf("libgaode") !== -1 || 
                module.name.indexOf("libamap") !== -1 ||
                module.name.indexOf("base.odex") !== -1) {
                
                console.log("[搜索模块] " + module.name);
                
                try {
                    var exports = Module.enumerateExports(module.name);
                    var relevantFunctions = [];
                    
                    for (var j = 0; j < exports.length; j++) {
                        var exp = exports[j];
                        if (exp.name && (
                            exp.name.toLowerCase().indexOf("file") !== -1 ||
                            exp.name.toLowerCase().indexOf("read") !== -1 ||
                            exp.name.toLowerCase().indexOf("load") !== -1 ||
                            exp.name.toLowerCase().indexOf("parse") !== -1 ||
                            exp.name.toLowerCase().indexOf("ans") !== -1
                        )) {
                            relevantFunctions.push(exp);
                            console.log("  [相关函数] " + exp.name + " @ " + exp.address);
                        }
                    }
                    
                    // Hook相关函数
                    for (var k = 0; k < Math.min(5, relevantFunctions.length); k++) {
                        hookGaodeFunction(relevantFunctions[k]);
                    }
                    
                } catch (e) {
                    // 忽略模块分析错误
                }
            }
        }
    } catch (e) {
        console.log("[错误] 模块搜索失败: " + e.message);
    }
}, 3000);

// Hook高德函数
function hookGaodeFunction(funcInfo) {
    try {
        Interceptor.attach(funcInfo.address, {
            onEnter: function(args) {
                console.log("  [高德函数调用] " + funcInfo.name);
                this.funcName = funcInfo.name;
                this.startTime = Date.now();
                
                // 分析参数
                for (var i = 0; i < 3; i++) {
                    try {
                        if (args[i] && !args[i].isNull()) {
                            var argData = args[i].readByteArray(Math.min(32, 1024));
                            if (argData) {
                                var header = new Uint8Array(argData);
                                console.log("    参数" + i + ": " + safeByteArrayToHex(header, 16));
                                
                                if (isAMZlibHeader(header)) {
                                    console.log("    [发现] 参数" + i + "包含AM-zlib数据!");
                                }
                            }
                        }
                    } catch (e) {
                        // 忽略参数分析错误
                    }
                }
            },
            onLeave: function(retval) {
                var duration = Date.now() - this.startTime;
                console.log("  [返回] " + this.funcName + " -> " + retval + " (耗时: " + duration + "ms)");
            }
        });
    } catch (e) {
        // 忽略Hook错误
    }
}

// 定期输出分析报告
setInterval(function() {
    var runtime = Math.floor((Date.now() - fileReadingAnalysis.startTime) / 1000);
    
    console.log("\n[节点1分析报告] ==========================================");
    console.log("运行时间: " + runtime + "s");
    console.log("");
    
    console.log(".ans文件操作:");
    console.log("  文件打开: " + fileReadingAnalysis.ansFiles.length + " 次");
    for (var i = 0; i < Math.min(3, fileReadingAnalysis.ansFiles.length); i++) {
        var file = fileReadingAnalysis.ansFiles[i];
        console.log("  " + (i+1) + ". " + file.filename + " (fd=" + file.fd + ")");
    }
    
    console.log("");
    console.log("内存映射:");
    console.log("  映射操作: " + fileReadingAnalysis.memoryMappings.length + " 次");
    for (var i = 0; i < Math.min(3, fileReadingAnalysis.memoryMappings.length); i++) {
        var mapping = fileReadingAnalysis.memoryMappings[i];
        console.log("  " + (i+1) + ". " + mapping.filename + " -> " + (mapping.length/1024/1024).toFixed(1) + "MB");
    }
    
    console.log("");
    console.log("读取操作:");
    console.log("  大文件读取: " + fileReadingAnalysis.readOperations.length + " 次");
    for (var i = 0; i < Math.min(3, fileReadingAnalysis.readOperations.length); i++) {
        var read = fileReadingAnalysis.readOperations[i];
        console.log("  " + (i+1) + ". " + read.filename + " -> " + (read.bytesRead/1024).toFixed(1) + "KB");
    }
    
    console.log("===============================================\n");
}, 30000);

console.log("[节点1分析] 脚本已启动，开始监控离线文件读取流程...");
console.log("[提示] 请在高德地图中触发离线地图加载操作");
