     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
[Rendering Pipeline Monitor] 启动渲染管道监控器...
[Java] Java环境已准备就绪
[Main] 等待应用初始化完成...
[Remote::com.autonavi.minimap]-> [Main] 开始渲染管道监控...
[File Monitor] 设置文件I/O监控...
[File Monitor] 文件I/O监控设置完成
[Memory Monitor] 设置内存分配监控...
[Memory Monitor] 内存监控设置完成
[Render Monitor] 设置渲染监控...
[Render Monitor] 渲染监控设置完成
[Library] libamapnsq.so 已加载，基址: 0x7f60779000
[Enhanced Monitor] 设置增强数据处理监控...
[Enhanced Monitor] 增强监控设置完成
[Rendering Pipeline Monitor] 渲染管道监控已启动!
现在移动地图到新区域，观察完整的数据流...
[File IO] 读取数据: FD=321, 大小=837字节
[File IO] 读取数据: FD=321, 大小=367字节
[File IO] 读取数据: FD=321, 大小=14字节
[File IO] 读取数据: FD=321, 大小=837字节
[Memory] 大内存分配: 23360字节 @ 0x7f509a7000
[Render] glDrawElements: 模式=4, 顶点数=159
[Render] glDrawElements: 模式=4, 顶点数=294
[Render] glDrawElements: 模式=4, 顶点数=171
[Render] glDrawElements: 模式=4, 顶点数=6159
[Render] glDrawElements: 模式=4, 顶点数=264
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=9933
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=264
[Render] glDrawElements: 模式=4, 顶点数=366
[Render] glDrawElements: 模式=4, 顶点数=411
[Render] glDrawElements: 模式=4, 顶点数=9069
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=3810
[Render] glDrawElements: 模式=4, 顶点数=8850
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=870
[Render] glDrawElements: 模式=4, 顶点数=5118
[Render] glDrawElements: 模式=4, 顶点数=1878
[Render] glDrawElements: 模式=4, 顶点数=6108
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=1956
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=2946
[Render] glDrawElements: 模式=4, 顶点数=2706
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=444
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1074
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=1086
[Render] glDrawElements: 模式=4, 顶点数=1770
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=3810
[Render] glDrawElements: 模式=4, 顶点数=8850
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=870
[Render] glDrawElements: 模式=4, 顶点数=5118
[Render] glDrawElements: 模式=4, 顶点数=1878
[Render] glDrawElements: 模式=4, 顶点数=6108
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=1956
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=2946
[Render] glDrawElements: 模式=4, 顶点数=2706
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=444
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1074
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=1086
[Render] glDrawElements: 模式=4, 顶点数=1770
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=390
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=270
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=324
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=411
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=390
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=420
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=270
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=228
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=324
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=876
[Render] glDrawElements: 模式=4, 顶点数=174
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=174
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 23360字节 @ 0x7f31df5000
[Render] glDrawElements: 模式=4, 顶点数=159
[Render] glDrawElements: 模式=4, 顶点数=294
[Render] glDrawElements: 模式=4, 顶点数=171
[Render] glDrawElements: 模式=4, 顶点数=6159
[Render] glDrawElements: 模式=4, 顶点数=264
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=9933
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=264
[Render] glDrawElements: 模式=4, 顶点数=366
[Render] glDrawElements: 模式=4, 顶点数=411
[Render] glDrawElements: 模式=4, 顶点数=9069
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=3810
[Render] glDrawElements: 模式=4, 顶点数=8850
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=870
[Render] glDrawElements: 模式=4, 顶点数=5118
[Render] glDrawElements: 模式=4, 顶点数=1878
[Render] glDrawElements: 模式=4, 顶点数=6108
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=1956
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=2946
[Render] glDrawElements: 模式=4, 顶点数=2706
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=444
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1074
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=1086
[Render] glDrawElements: 模式=4, 顶点数=1770
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=3810
[Render] glDrawElements: 模式=4, 顶点数=8850
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=870
[Render] glDrawElements: 模式=4, 顶点数=5118
[Render] glDrawElements: 模式=4, 顶点数=1878
[Render] glDrawElements: 模式=4, 顶点数=6108
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=1956
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=2946
[Render] glDrawElements: 模式=4, 顶点数=2706
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=444
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1074
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=1086
[Render] glDrawElements: 模式=4, 顶点数=1770
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=390
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=270
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=324
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=411
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=390
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=420
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=270
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=228
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=324
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=876
[Render] glDrawElements: 模式=4, 顶点数=174
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=174
[Render] glDrawElements: 模式=4, 顶点数=1077
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=912字节
[File IO] 读取数据: FD=321, 大小=99字节
[File IO] 读取数据: FD=321, 大小=99字节
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Memory] 大内存分配: 23360字节 @ 0x7f44910000
[Render] glDrawElements: 模式=4, 顶点数=159
[Render] glDrawElements: 模式=4, 顶点数=294
[Render] glDrawElements: 模式=4, 顶点数=171
[Render] glDrawElements: 模式=4, 顶点数=6159
[Render] glDrawElements: 模式=4, 顶点数=264
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=9933
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=264
[Render] glDrawElements: 模式=4, 顶点数=366
[Render] glDrawElements: 模式=4, 顶点数=411
[Render] glDrawElements: 模式=4, 顶点数=9069
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=3810
[Render] glDrawElements: 模式=4, 顶点数=8850
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=870
[Render] glDrawElements: 模式=4, 顶点数=5118
[Render] glDrawElements: 模式=4, 顶点数=1878
[Render] glDrawElements: 模式=4, 顶点数=6108
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=1956
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=2946
[Render] glDrawElements: 模式=4, 顶点数=2706
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=444
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1074
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=1086
[Render] glDrawElements: 模式=4, 顶点数=1770
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=3810
[Render] glDrawElements: 模式=4, 顶点数=8850
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=870
[Render] glDrawElements: 模式=4, 顶点数=5118
[Render] glDrawElements: 模式=4, 顶点数=1878
[Render] glDrawElements: 模式=4, 顶点数=6108
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=1956
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=2946
[Render] glDrawElements: 模式=4, 顶点数=2706
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=444
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1074
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=1086
[Render] glDrawElements: 模式=4, 顶点数=1770
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=390
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=270
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=324
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=411
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=390
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=420
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=270
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=228
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=324
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=876
[Render] glDrawElements: 模式=4, 顶点数=174
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=174
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Memory] 大内存分配: 13352字节 @ 0x7f3f88e800
[Memory] 大内存分配: 21600字节 @ 0x7f32049000
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=159
[Render] glDrawElements: 模式=4, 顶点数=294
[Render] glDrawElements: 模式=4, 顶点数=171
[Render] glDrawElements: 模式=4, 顶点数=6159
[Render] glDrawElements: 模式=4, 顶点数=264
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=9933
[Render] glDrawElements: 模式=4, 顶点数=264
[Render] glDrawElements: 模式=4, 顶点数=366
[Render] glDrawElements: 模式=4, 顶点数=411
[Render] glDrawElements: 模式=4, 顶点数=9069
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3810
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=8850
[Render] glDrawElements: 模式=4, 顶点数=870
[Render] glDrawElements: 模式=4, 顶点数=5118
[Render] glDrawElements: 模式=4, 顶点数=1878
[Render] glDrawElements: 模式=4, 顶点数=6108
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=1956
[Render] glDrawElements: 模式=4, 顶点数=2946
[Render] glDrawElements: 模式=4, 顶点数=2706
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=444
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=1074
[Render] glDrawElements: 模式=4, 顶点数=1086
[Render] glDrawElements: 模式=4, 顶点数=1770
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3810
[Render] glDrawElements: 模式=4, 顶点数=8850
[Render] glDrawElements: 模式=4, 顶点数=870
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=5118
[Render] glDrawElements: 模式=4, 顶点数=1878
[Render] glDrawElements: 模式=4, 顶点数=6108
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=1956
[Render] glDrawElements: 模式=4, 顶点数=2946
[Render] glDrawElements: 模式=4, 顶点数=2706
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=444
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=1074
[Render] glDrawElements: 模式=4, 顶点数=1086
[Render] glDrawElements: 模式=4, 顶点数=1770
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=390
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=270
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=324
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=411
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=390
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=420
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=270
[Render] glDrawElements: 模式=4, 顶点数=684
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=228
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=324
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=810
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=174
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 26424字节 @ 0x7f496de000
[Memory] 大内存分配: 17280字节 @ 0x7f31f02000
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=120
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=648
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=174
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 15360字节 @ 0x7f47d82000
[Memory] 大内存分配: 30720字节 @ 0x7f44902000
[Memory] 大内存分配: 17280字节 @ 0x7f32d9a000
[File IO] 读取数据: FD=142, 大小=8192字节
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[File IO] 读取数据: FD=146, 大小=8192字节
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[File IO] 读取数据: FD=323, 大小=3738字节
[Render] glDrawElements: 模式=4, 顶点数=5490
[Memory] 大内存分配: 21360字节 @ 0x7f4497a000
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[File IO] 读取数据: FD=323, 大小=3732字节
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[File IO] 读取数据: FD=324, 大小=3738字节
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[File IO] 读取数据: FD=109, 大小=8192字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 18ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=3452字节
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=2972字节
[File IO] 读取数据: FD=224, 大小=8192字节
[Render] glDrawElements: 模式=4, 顶点数=150
[File IO] 读取数据: FD=224, 大小=4316字节
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=120
[File IO] 读取数据: FD=224, 大小=4580字节
[File IO] 读取数据: FD=224, 大小=4775字节
[File IO] 读取数据: FD=224, 大小=4256字节
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=648
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=174
[Render] glDrawElements: 模式=4, 顶点数=1077
[File IO] 读取数据: FD=224, 大小=4377字节
[File IO] 读取数据: FD=224, 大小=5249字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 4ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=109, 大小=8192字节
[Memory] 大内存分配: 16392字节 @ 0x7f31e56000
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 2ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4775字节
[File IO] 读取数据: FD=224, 大小=4256字节
[File IO] 读取数据: FD=224, 大小=4377字节
[File IO] 读取数据: FD=224, 大小=5249字节
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 2ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=109, 大小=8192字节
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 4ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=3901字节
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4602字节
[File IO] 读取数据: FD=224, 大小=4677字节
[File IO] 读取数据: FD=224, 大小=4426字节
[File IO] 读取数据: FD=224, 大小=4334字节
[File IO] 读取数据: FD=224, 大小=4528字节
[File IO] 读取数据: FD=224, 大小=6131字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 4ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=109, 大小=8192字节
[Memory] 大内存分配: 15360字节 @ 0x7f47d82000
[Memory] 大内存分配: 30720字节 @ 0x7f44902000
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 5ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[Memory] 大内存分配: 59392字节 @ 0x7f38b42000
[Memory] 大内存分配: 17280字节 @ 0x7f38a6f000
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=3748字节
[File IO] 读取数据: FD=224, 大小=4821字节
[File IO] 读取数据: FD=224, 大小=4916字节
[File IO] 读取数据: FD=224, 大小=4360字节
[File IO] 读取数据: FD=224, 大小=4493字节
[File IO] 读取数据: FD=224, 大小=4754字节
[Memory] 大内存分配: 21360字节 @ 0x7f44910000
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[File IO] 读取数据: FD=224, 大小=6212字节
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 3ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=234
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 6ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=5490
[File IO] 读取数据: FD=224, 大小=8192字节
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4916字节
[Memory] 大内存分配: 15360字节 @ 0x7f47d82000
[Render] glDrawElements: 模式=4, 顶点数=405
[File IO] 读取数据: FD=224, 大小=4360字节
[File IO] 读取数据: FD=224, 大小=4493字节
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[File IO] 读取数据: FD=224, 大小=4754字节
[Render] glDrawElements: 模式=4, 顶点数=504
[File IO] 读取数据: FD=224, 大小=6212字节
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=120
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=648
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=258
[Render] glDrawElements: 模式=4, 顶点数=246
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 30720字节 @ 0x7f44902000
[Memory] 大内存分配: 21360字节 @ 0x7f4492a000
[Memory] 大内存分配: 17280字节 @ 0x7f38a74000
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=120
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=648
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=258
[Render] glDrawElements: 模式=4, 顶点数=246
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 17280字节 @ 0x7f31f02000
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=120
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=648
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=258
[Render] glDrawElements: 模式=4, 顶点数=246
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 15360字节 @ 0x7f47d82000
[Memory] 大内存分配: 30720字节 @ 0x7f44902000
[Memory] 大内存分配: 17280字节 @ 0x7f32d9a000
[Memory] 大内存分配: 21360字节 @ 0x7f4497a000
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=120
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=648
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=288
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 20960字节 @ 0x7f4497a000
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 3ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=120
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 4ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=786
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=318
[Render] glDrawElements: 模式=4, 顶点数=294
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Render] glDrawElements: 模式=4, 顶点数=1077
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=2972字节
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4316字节
[File IO] 读取数据: FD=224, 大小=4580字节
[File IO] 读取数据: FD=224, 大小=4775字节
[File IO] 读取数据: FD=224, 大小=4256字节
[File IO] 读取数据: FD=224, 大小=4377字节
[File IO] 读取数据: FD=224, 大小=5249字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 7ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Memory] 大内存分配: 20960字节 @ 0x7f4492a000
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 3ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4775字节
[File IO] 读取数据: FD=224, 大小=4256字节
[File IO] 读取数据: FD=224, 大小=4377字节
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[File IO] 读取数据: FD=224, 大小=5249字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 3ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 22ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=8192字节
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[File IO] 读取数据: FD=224, 大小=3901字节
[Render] glDrawElements: 模式=4, 顶点数=150
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[File IO] 读取数据: FD=224, 大小=8192字节
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[File IO] 读取数据: FD=224, 大小=4602字节
[File IO] 读取数据: FD=224, 大小=4677字节
[File IO] 读取数据: FD=224, 大小=4426字节
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[File IO] 读取数据: FD=224, 大小=4334字节
[File IO] 读取数据: FD=224, 大小=4528字节
[File IO] 读取数据: FD=224, 大小=6131字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 4ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=132
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=786
[Render] glDrawElements: 模式=4, 顶点数=144
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 4ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[Render] glDrawElements: 模式=4, 顶点数=276
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1077
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=3748字节
[File IO] 读取数据: FD=224, 大小=4821字节
[File IO] 读取数据: FD=224, 大小=4916字节
[File IO] 读取数据: FD=224, 大小=4360字节
[File IO] 读取数据: FD=224, 大小=4493字节
[Memory] 大内存分配: 20960字节 @ 0x7f44910000
[Render] glDrawElements: 模式=4, 顶点数=168
[File IO] 读取数据: FD=224, 大小=4754字节
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[File IO] 读取数据: FD=224, 大小=6212字节
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Render] glDrawElements: 模式=4, 顶点数=330
[sub_10F88] 解析器完成, 耗时: 4ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=132
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=786
[Render] glDrawElements: 模式=4, 顶点数=144
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 5ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=210
[Render] glDrawElements: 模式=4, 顶点数=1077
[File IO] 读取数据: FD=224, 大小=8192字节
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4916字节
[File IO] 读取数据: FD=224, 大小=4360字节
[File IO] 读取数据: FD=224, 大小=4493字节
[File IO] 读取数据: FD=224, 大小=4754字节
[File IO] 读取数据: FD=224, 大小=6212字节
[Memory] 大内存分配: 20960字节 @ 0x7f38a74000
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=132
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=786
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=210
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Memory] 大内存分配: 20960字节 @ 0x7f4497a000
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=240
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=132
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=786
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=210
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Memory] 大内存分配: 19520字节 @ 0x7f31f02000
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=468
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Memory] 大内存分配: 31720字节 @ 0x7f564c2000
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=306
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=108
[Render] glDrawElements: 模式=4, 顶点数=732
[Render] glDrawElements: 模式=4, 顶点数=132
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=210
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Memory] 大内存分配: 18136字节 @ 0x7f3b895000
[Memory] 大内存分配: 26424字节 @ 0x7f4491d000
[Memory] 大内存分配: 17600字节 @ 0x7f38a6f000
[Render] glDrawElements: 模式=4, 顶点数=189
[Render] glDrawElements: 模式=4, 顶点数=804
[Render] glDrawElements: 模式=4, 顶点数=165
[Render] glDrawElements: 模式=4, 顶点数=552
[Render] glDrawElements: 模式=4, 顶点数=6630
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=4206
[Render] glDrawElements: 模式=4, 顶点数=7968
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=4692
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=924
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=1014
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=450
[Render] glDrawElements: 模式=4, 顶点数=996
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=138
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=330
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=522
[Render] glDrawElements: 模式=4, 顶点数=120
[Render] glDrawElements: 模式=4, 顶点数=660
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=210
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 15360字节 @ 0x7f47d82000
[Memory] 大内存分配: 30720字节 @ 0x7f44902000
[File IO] 读取数据: FD=109, 大小=8192字节
[File IO] 读取数据: FD=109, 大小=8192字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 2ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=109, 大小=8192字节
[File IO] 读取数据: FD=109, 大小=8192字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 5ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 2ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=2972字节
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4316字节
[File IO] 读取数据: FD=224, 大小=4580字节
[File IO] 读取数据: FD=224, 大小=4775字节
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[File IO] 读取数据: FD=224, 大小=4256字节
[File IO] 读取数据: FD=224, 大小=4377字节
[File IO] 读取数据: FD=224, 大小=5249字节
[File IO] 读取数据: FD=224, 大小=5963字节
[File IO] 读取数据: FD=224, 大小=4316字节
[File IO] 读取数据: FD=224, 大小=4580字节
[File IO] 读取数据: FD=224, 大小=4775字节
[File IO] 读取数据: FD=224, 大小=4256字节
[File IO] 读取数据: FD=224, 大小=4377字节
[File IO] 读取数据: FD=224, 大小=5249字节
[File IO] 读取数据: FD=224, 大小=5963字节
[File IO] 读取数据: FD=224, 大小=5529字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 2ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=109, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=5587字节
[Memory] 大内存分配: 23640字节 @ 0x7f5f76e000
[File IO] 读取数据: FD=224, 大小=4316字节
[File IO] 读取数据: FD=224, 大小=4580字节
[File IO] 读取数据: FD=142, 大小=8192字节
[File IO] 读取数据: FD=142, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4775字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 2ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 3ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=146, 大小=8192字节
[File IO] 读取数据: FD=109, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=8192字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 3ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[File IO] 读取数据: FD=224, 大小=4821字节
[File IO] 读取数据: FD=224, 大小=4916字节
[File IO] 读取数据: FD=224, 大小=4360字节
[File IO] 读取数据: FD=224, 大小=4493字节
[File IO] 读取数据: FD=224, 大小=4754字节
[File IO] 读取数据: FD=224, 大小=6212字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 3ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 2ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Memory] 大内存分配: 20640字节 @ 0x7f79589000
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4821字节
[File IO] 读取数据: FD=224, 大小=4916字节
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[File IO] 读取数据: FD=224, 大小=4360字节
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[File IO] 读取数据: FD=224, 大小=4493字节
[File IO] 读取数据: FD=224, 大小=4754字节
[File IO] 读取数据: FD=224, 大小=6212字节
[File IO] 读取数据: FD=224, 大小=5060字节
[File IO] 读取数据: FD=224, 大小=5002字节
[File IO] 读取数据: FD=224, 大小=5587字节
[Memory] 大内存分配: 23423字节 @ 0x7f564d1000
[File IO] 读取数据: FD=224, 大小=4821字节
[File IO] 读取数据: FD=224, 大小=4916字节
[File IO] 读取数据: FD=109, 大小=8192字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 4ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Memory] 大内存分配: 16392字节 @ 0x7f31e56000
[Memory] 大内存分配: 59392字节 @ 0x7f38bc2000
[Memory] 大内存分配: 16640字节 @ 0x7f3a00c000
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Memory] 大内存分配: 15360字节 @ 0x7f47d82000
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=120
[Render] glDrawElements: 模式=4, 顶点数=624
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=288
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 30720字节 @ 0x7f44902000
[Memory] 大内存分配: 20400字节 @ 0x7f449a5000
[Memory] 大内存分配: 16640字节 @ 0x7f32d9a000
[Render] glBufferData: 目标=34962, 大小=2688字节
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=252
[Memory] 大内存分配: 12868字节 @ 0x7f34260800
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 4ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=405
[Memory] 大内存分配: 21044字节 @ 0x7f44910000
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 5ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=114
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[File IO] 读取数据: FD=224, 大小=8192字节
[Memory] 大内存分配: 15360字节 @ 0x7f47d82000
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4316字节
[File IO] 读取数据: FD=224, 大小=4580字节
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=1008
[File IO] 读取数据: FD=224, 大小=4775字节
[File IO] 读取数据: FD=224, 大小=4256字节
[Memory] 大内存分配: 30720字节 @ 0x7f44902000
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=510
[File IO] 读取数据: FD=224, 大小=4377字节
[File IO] 读取数据: FD=224, 大小=5249字节
[File IO] 读取数据: FD=224, 大小=5963字节
[File IO] 读取数据: FD=224, 大小=5529字节
[File IO] 读取数据: FD=224, 大小=5587字节
[Render] glDrawElements: 模式=4, 顶点数=120
[Render] glDrawElements: 模式=4, 顶点数=624
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=288
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 20400字节 @ 0x7f564f8000
[Memory] 大内存分配: 16640字节 @ 0x7f38a6f000
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=120
[Render] glDrawElements: 模式=4, 顶点数=624
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=288
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=1077
[Memory] 大内存分配: 16640字节 @ 0x7f3a00c000
[Render] glBufferData: 目标=34962, 大小=1220字节
[Render] glBufferData: 目标=34962, 大小=21040字节
[Render] glBufferData: 目标=34963, 大小=6330字节
[Render] glBufferData: 目标=34962, 大小=12864字节
[Render] glBufferData: 目标=34963, 大小=3618字节
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=114
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 3ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 5ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=510
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4316字节
[File IO] 读取数据: FD=224, 大小=4580字节
[Render] glDrawElements: 模式=4, 顶点数=120
[Render] glDrawElements: 模式=4, 顶点数=624
[Render] glDrawElements: 模式=4, 顶点数=144
[Render] glDrawElements: 模式=4, 顶点数=288
[Render] glDrawElements: 模式=4, 顶点数=234
[File IO] 读取数据: FD=224, 大小=4775字节
[File IO] 读取数据: FD=224, 大小=4256字节
[File IO] 读取数据: FD=224, 大小=4377字节
[Render] glDrawElements: 模式=4, 顶点数=1077
[File IO] 读取数据: FD=224, 大小=5249字节
[File IO] 读取数据: FD=224, 大小=5963字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 3ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 4ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[File IO] 读取数据: FD=224, 大小=8192字节
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Memory] 大内存分配: 16640字节 @ 0x7f564f8000
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Render] glDrawElements: 模式=4, 顶点数=801
[Render] glDrawElements: 模式=4, 顶点数=1077
[Render] glDrawElements: 模式=4, 顶点数=390
[Render] glDrawElements: 模式=4, 顶点数=531
[Render] glDrawElements: 模式=4, 顶点数=168
[Render] glDrawElements: 模式=4, 顶点数=693
[Render] glDrawElements: 模式=4, 顶点数=180
[Render] glDrawElements: 模式=4, 顶点数=4263
[Render] glDrawElements: 模式=4, 顶点数=126
[Render] glDrawElements: 模式=4, 顶点数=768
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=8241
[Render] glDrawElements: 模式=4, 顶点数=222
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4821字节
[Render] glDrawElements: 模式=4, 顶点数=3228
[File IO] 读取数据: FD=224, 大小=4916字节
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[File IO] 读取数据: FD=224, 大小=4360字节
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[File IO] 读取数据: FD=224, 大小=4493字节
[File IO] 读取数据: FD=224, 大小=4754字节
[File IO] 读取数据: FD=224, 大小=6212字节
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 40 5e 54 7f 00 00 00 08 4f 82 74 7f 00 00 00  .@^T.....O.t....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[Render] glDrawElements: 模式=4, 顶点数=252
[Render] glDrawElements: 模式=4, 顶点数=1224
[sub_10F88] 解析器完成, 耗时: 18ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=5490
[Render] glDrawElements: 模式=4, 顶点数=4290
[Render] glDrawElements: 模式=4, 顶点数=1620
[Render] glDrawElements: 模式=4, 顶点数=3228
[Render] glDrawElements: 模式=4, 顶点数=3150
[Render] glDrawElements: 模式=4, 顶点数=2988
[Render] glDrawElements: 模式=4, 顶点数=186
[Render] glDrawElements: 模式=4, 顶点数=204
[Render] glDrawElements: 模式=4, 顶点数=198
[Render] glDrawElements: 模式=4, 顶点数=252
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[sub_10F88] 数据解析器调用 - 增强分析
[Input 0] 输入数据:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  88 8f b7 3a 7f 00 00 00 28 5f c0 3a 7f 00 00 00  ...:....(_.:....
00000010  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
[sub_10F88] 解析器完成, 耗时: 2ms, 返回码: 0
[Parse Success] 解析成功，立即扫描新数据...
[Render] glDrawElements: 模式=4, 顶点数=1224
[Render] glDrawElements: 模式=4, 顶点数=1818
[Render] glDrawElements: 模式=4, 顶点数=234
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=405
[Render] glDrawElements: 模式=4, 顶点数=150
[Render] glDrawElements: 模式=4, 顶点数=504
[Render] glDrawElements: 模式=4, 顶点数=300
[Render] glDrawElements: 模式=4, 顶点数=114
[Render] glDrawElements: 模式=4, 顶点数=738
[Render] glDrawElements: 模式=4, 顶点数=1008
[Render] glDrawElements: 模式=4, 顶点数=336
[Render] glDrawElements: 模式=4, 顶点数=510
[Render] glDrawElements: 模式=4, 顶点数=120
[Render] glDrawElements: 模式=4, 顶点数=624
[Render] glDrawElements: 模式=4, 顶点数=144
[File IO] 读取数据: FD=224, 大小=8192字节
[Render] glDrawElements: 模式=4, 顶点数=288
[Render] glDrawElements: 模式=4, 顶点数=234
[Scan] 快速扫描新生成的数据...
[New Data] 发现新坐标数据 @ 0x12c000c0: 324.680, 0.000, 324.682, 0.000
[New Data] 发现新坐标数据 @ 0x12c00140: 324.238, 0.000, 324.240, 0.000
[New Data] 发现新坐标数据 @ 0x12c02f40: 8.059551806253946e+29, 0.000, 564.045, 852.045
[New Data] 发现新坐标数据 @ 0x12c02f80: 8.059551806253946e+29, 0.000, 13.224, 0.679
[Render] glDrawElements: 模式=4, 顶点数=1077
[File IO] 读取数据: FD=224, 大小=8192字节
[File IO] 读取数据: FD=224, 大小=4821字节
[File IO] 读取数据: FD=224, 大小=4916字节
[File IO] 读取数据: FD=224, 大小=4360字节
[File IO] 读取数据: FD=224, 大小=4493字节
[File IO] 读取数据: FD=224, 大小=4754字节
[File IO] 读取数据: FD=224, 大小=6212字节
[File IO] 读取数据: FD=224, 大小=5060字节
[File IO] 读取数据: FD=224, 大小=5002字节
[File IO] 读取数据: FD=224, 大小=5587字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节

[Remote::com.autonavi.minimap]-> [File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=1024字节
[File IO] 读取数据: FD=321, 大小=928字节
[File IO] 读取数据: FD=321, 大小=99字节
[File IO] 读取数据: FD=321, 大小=99字节
exit

Thank you for using Frida!
