// 精确验证脚本 - 深度分析高德地图数据处理流程
// 目标: 精确定位.ans文件读取、解压、解析的完整调用链

console.log("[精确验证模式] 开始深度分析高德地图离线数据处理流程...");
console.log("[目标] 精确定位.ans文件读取、解压、解析的完整调用链");

// 全局状态跟踪
var verificationState = {
    fileOperations: [],
    memoryOperations: [],
    callStacks: [],
    dataFlows: [],
    startTime: Date.now()
};

// 调用栈跟踪
function captureCallStack(context, operation) {
    try {
        var stack = Thread.backtrace(context, Backtracer.ACCURATE);
        var stackInfo = {
            operation: operation,
            timestamp: Date.now(),
            stack: stack.map(function(addr) {
                try {
                    var module = Process.findModuleByAddress(addr);
                    var symbol = DebugSymbol.fromAddress(addr);
                    return {
                        address: addr.toString(),
                        module: module ? module.name : "unknown",
                        symbol: symbol.name || "unknown",
                        offset: symbol.address ? addr.sub(symbol.address).toString() : "0"
                    };
                } catch (e) {
                    return {
                        address: addr.toString(),
                        module: "unknown",
                        symbol: "unknown",
                        offset: "0"
                    };
                }
            })
        };
        verificationState.callStacks.push(stackInfo);
        return stackInfo;
    } catch (e) {
        console.log("[警告] 调用栈捕获失败: " + e.message);
        return null;
    }
}

// 1. Hook所有文件操作 - 精确模式
console.log("[1] 设置文件操作Hook (精确模式)...");

// Hook open系统调用
var libc = null;
try {
    libc = Process.getModuleByName("libc.so");
    var openPtr = libc.getExportByName("open");
    
    Interceptor.attach(openPtr, {
        onEnter: function(args) {
            this.filename = args[0].readCString();
            this.flags = args[1].toInt32();
        },
        onLeave: function(retval) {
            var fd = retval.toInt32();
            if (fd > 0 && this.filename) {
                var isAnsFile = this.filename.indexOf('.ans') !== -1;
                var isMapFile = this.filename.indexOf('map') !== -1 || 
                               this.filename.indexOf('Map') !== -1;
                
                if (isAnsFile || isMapFile) {
                    var fileOp = {
                        operation: "open",
                        filename: this.filename,
                        fd: fd,
                        flags: this.flags,
                        timestamp: Date.now(),
                        callStack: captureCallStack(this.context, "file_open")
                    };
                    verificationState.fileOperations.push(fileOp);
                    
                    console.log("[文件操作] OPEN: " + this.filename + " (fd=" + fd + ")");
                    if (isAnsFile) {
                        console.log("[关键发现] .ans文件被打开!");
                    }
                }
            }
        }
    });
    console.log("[✓] open() Hook设置成功");
} catch (e) {
    console.log("[✗] open() Hook失败: " + e.message);
}

// Hook read系统调用 - 增强版
try {
    var readPtr = libc.getExportByName("read");
    
    Interceptor.attach(readPtr, {
        onEnter: function(args) {
            this.fd = args[0].toInt32();
            this.buf = args[1];
            this.count = args[2].toInt32();
            this.isTrackedFd = false;
            
            // 检查是否是我们关注的文件描述符
            for (var i = 0; i < verificationState.fileOperations.length; i++) {
                if (verificationState.fileOperations[i].fd === this.fd) {
                    this.isTrackedFd = true;
                    this.trackedFile = verificationState.fileOperations[i];
                    break;
                }
            }
        },
        onLeave: function(retval) {
            var bytesRead = retval.toInt32();
            if (bytesRead > 0 && this.isTrackedFd) {
                try {
                    var data = this.buf.readByteArray(Math.min(32, bytesRead));
                    var header = new Uint8Array(data);

                    // 安全的数组切片
                    var headerBytes = [];
                    var headerHexBytes = [];
                    for (var i = 0; i < Math.min(16, header.length); i++) {
                        headerBytes.push(header[i]);
                        headerHexBytes.push(('0' + header[i].toString(16)).slice(-2));
                    }

                    var readOp = {
                        operation: "read",
                        fd: this.fd,
                        filename: this.trackedFile.filename,
                        bytesRead: bytesRead,
                        requestedBytes: this.count,
                        header: headerBytes,
                        headerHex: headerHexBytes.join(' '),
                        timestamp: Date.now(),
                        callStack: captureCallStack(this.context, "file_read")
                    };
                    verificationState.fileOperations.push(readOp);
                    
                    console.log("[文件读取] " + this.trackedFile.filename + 
                               " 读取 " + bytesRead + " 字节");
                    console.log("  头部数据: " + readOp.headerHex);
                    
                    // 检查是否是压缩数据
                    if (header.length >= 2) {
                        if (header[0] === 0x78 && header[1] === 0x9c) {
                            console.log("  [数据类型] zlib压缩数据");
                        } else if (header[0] === 0x08) {
                            console.log("  [数据类型] AM-zlib容器");
                        } else if (header[0] === 0x44 && header[1] === 0x49 && 
                                  header[2] === 0x43 && header[3] === 0x45) {
                            console.log("  [数据类型] DICE-AM矢量数据");
                        } else if (header[0] === 0xbc && header[1] === 0xbc) {
                            console.log("  [数据类型] CONFIG配置数据");
                        }
                    }
                } catch (e) {
                    console.log("[警告] 读取数据分析失败: " + e.message);
                }
            }
        }
    });
    console.log("[✓] read() Hook设置成功");
} catch (e) {
    console.log("[✗] read() Hook失败: " + e.message);
}

// 2. Hook内存操作 - 寻找解压函数
console.log("[2] 设置内存操作Hook...");

// Hook mmap - 寻找内存映射的.ans文件
try {
    var mmapPtr = libc.getExportByName("mmap");
    
    Interceptor.attach(mmapPtr, {
        onEnter: function(args) {
            this.addr = args[0];
            this.length = args[1].toInt32();
            this.prot = args[2].toInt32();
            this.flags = args[3].toInt32();
            this.fd = args[4].toInt32();
            this.offset = args[5].toInt32();
        },
        onLeave: function(retval) {
            var mappedAddr = retval;
            if (!mappedAddr.isNull() && this.length > 0) {
                // 检查是否映射了我们关注的文件
                var isTrackedFd = false;
                var trackedFile = null;
                
                for (var i = 0; i < verificationState.fileOperations.length; i++) {
                    if (verificationState.fileOperations[i].fd === this.fd) {
                        isTrackedFd = true;
                        trackedFile = verificationState.fileOperations[i];
                        break;
                    }
                }
                
                if (isTrackedFd) {
                    var memOp = {
                        operation: "mmap",
                        filename: trackedFile.filename,
                        fd: this.fd,
                        mappedAddr: mappedAddr.toString(),
                        length: this.length,
                        offset: this.offset,
                        timestamp: Date.now()
                    };
                    verificationState.memoryOperations.push(memOp);
                    
                    console.log("[内存映射] " + trackedFile.filename + 
                               " 映射到 " + mappedAddr + " (大小: " + this.length + ")");
                    
                    // 分析映射的数据
                    try {
                        var mappedData = mappedAddr.readByteArray(Math.min(64, this.length));
                        var header = new Uint8Array(mappedData);

                        // 安全的数组处理
                        var headerHexBytes = [];
                        for (var i = 0; i < Math.min(16, header.length); i++) {
                            headerHexBytes.push(('0' + header[i].toString(16)).slice(-2));
                        }
                        console.log("  映射数据头部: " + headerHexBytes.join(' '));
                    } catch (e) {
                        console.log("  [警告] 无法读取映射数据: " + e.message);
                    }
                }
            }
        }
    });
    console.log("[✓] mmap() Hook设置成功");
} catch (e) {
    console.log("[✗] mmap() Hook失败: " + e.message);
}

// 3. Hook zlib相关函数 - 在所有可能的库中搜索
console.log("[3] 搜索并Hook zlib函数...");

var zlibModules = ["libz.so", "libz.so.1", "libc.so", "libamapmain.so"];
var zlibFunctions = ["uncompress", "inflate", "inflateInit", "inflateEnd", "zlib_uncompress"];

for (var i = 0; i < zlibModules.length; i++) {
    try {
        var module = Process.getModuleByName(zlibModules[i]);
        console.log("[搜索] 在 " + zlibModules[i] + " 中搜索zlib函数...");
        
        for (var j = 0; j < zlibFunctions.length; j++) {
            try {
                var funcPtr = module.getExportByName(zlibFunctions[j]);
                console.log("[发现] " + zlibModules[i] + "::" + zlibFunctions[j] + " @ " + funcPtr);
                
                // Hook找到的函数
                Interceptor.attach(funcPtr, {
                    onEnter: function(args) {
                        this.funcName = zlibFunctions[j];
                        this.args = args;
                        console.log("[zlib调用] " + this.funcName + " 被调用");
                    },
                    onLeave: function(retval) {
                        console.log("[zlib返回] " + this.funcName + " 返回: " + retval);
                    }
                });
            } catch (e) {
                // 函数不存在，继续搜索
            }
        }
    } catch (e) {
        // 模块不存在，继续搜索
    }
}

// 4. Hook libamapmain.so中的关键函数
console.log("[4] 分析libamapmain.so中的关键函数...");

try {
    var amapModule = Process.getModuleByName("libamapmain.so");
    console.log("[模块信息] libamapmain.so 基址: " + amapModule.base + " 大小: " + amapModule.size);
    
    // 搜索可能的数据处理函数
    var exports = amapModule.enumerateExports();
    console.log("[导出函数] 找到 " + exports.length + " 个导出函数");
    
    // 寻找可能与数据处理相关的函数
    for (var i = 0; i < Math.min(exports.length, 20); i++) {
        var exp = exports[i];
        if (exp.name.indexOf('data') !== -1 || 
            exp.name.indexOf('read') !== -1 || 
            exp.name.indexOf('parse') !== -1 ||
            exp.name.indexOf('decode') !== -1) {
            console.log("[可疑函数] " + exp.name + " @ " + exp.address);
        }
    }
} catch (e) {
    console.log("[警告] 无法分析libamapmain.so: " + e.message);
}

// 5. 定期输出验证报告
setInterval(function() {
    var runtime = Math.floor((Date.now() - verificationState.startTime) / 1000);
    
    console.log("\n[精确验证报告] ==========================================");
    console.log("运行时间: " + runtime + "s");
    console.log("");
    console.log("文件操作统计:");
    console.log("  总操作数: " + verificationState.fileOperations.length);
    
    var ansFiles = verificationState.fileOperations.filter(function(op) {
        return op.filename && op.filename.indexOf('.ans') !== -1;
    });
    console.log("  .ans文件操作: " + ansFiles.length);
    
    if (ansFiles.length > 0) {
        console.log("  .ans文件列表:");
        for (var i = 0; i < Math.min(ansFiles.length, 5); i++) {
            console.log("    " + ansFiles[i].filename + " (fd=" + ansFiles[i].fd + ")");
        }
    }
    
    console.log("");
    console.log("内存操作统计:");
    console.log("  内存映射数: " + verificationState.memoryOperations.length);
    
    console.log("");
    console.log("调用栈统计:");
    console.log("  捕获的调用栈: " + verificationState.callStacks.length);
    
    console.log("===============================================\n");
}, 20000);

console.log("[精确验证] 验证脚本已启动，等待地图操作...");
console.log("[提示] 请移动地图、缩放或切换图层以触发数据处理");
