// 高德地图内部函数Hook脚本 - 不Hook系统调用
// 严格基于 ans_frida_test50.js 成功模式
// 适用于 Frida 12.9.7，使用 ES5 语法

(function() {
    'use strict';
    
    // ==================== 配置 ====================
    var config = {
        libName: "libamapnsq.so",          // 数据解析模块
        offsets: {
            // 基于IDA Pro分析的关键数据处理函数
            sub_5C394: 0x5C394,            // 解析调度器（数据分发）
            sub_10F88: 0x10F88,            // DICE-AM数据解析器
            sub_C654: 0xC654,              // 解压协调函数
            girf_sqlite3_bind_blob: 0x15000 // SQLite数据绑定
        },
        debugLevel: 1,                      // 0=仅错误, 1=基本信息, 2=详细信息
        includeBacktrace: false,            // 不记录调用栈以提高稳定性
        maxDataLog: 10                      // 最大数据日志数量
    };
    
    // ==================== 全局变量 ====================
    var baseAddr = null;
    var addresses = {};
    var gDataCount = 0;
    var gCallStats = {
        parseDispatcher: 0,     // sub_5C394 调用次数
        diceParser: 0,          // sub_10F88 调用次数
        decompressCoord: 0,     // sub_C654 调用次数
        sqliteBind: 0,          // girf_sqlite3_bind_blob 调用次数
        total: 0
    };
    var gStartTime = new Date().getTime();
    var hookHandles = [];
    
    // ==================== 工具函数 ====================
    function log(message) {
        console.log("[高德内部Hook] " + message);
    }
    
    function logError(message) {
        console.log("[高德内部Hook-错误] " + message);
    }
    
    function logDebug(level, message) {
        if (config.debugLevel >= level) {
            console.log("[高德内部Hook-调试] " + message);
        }
    }
    
    function formatAddress(addr) {
        return "0x" + addr.toString(16);
    }
    
    function getRuntime() {
        var now = new Date().getTime();
        return ((now - gStartTime) / 1000).toFixed(1) + "s";
    }
    
    function safeReadParams(args, index, defaultValue) {
        try {
            if (args && args[index]) {
                return args[index];
            }
        } catch (e) {
            // 忽略参数读取错误
        }
        return defaultValue || null;
    }
    
    // ==================== 模块初始化 ====================
    function initializeModule() {
        log("初始化 " + config.libName + " 模块...");
        
        try {
            var module = Process.findModuleByName(config.libName);
            if (!module) {
                logError(config.libName + " 模块未找到");
                return false;
            }
            
            baseAddr = module.base;
            log(config.libName + " 基址: " + formatAddress(baseAddr) + 
               " 大小: 0x" + module.size.toString(16));
            
            // 计算实际地址
            for (var funcName in config.offsets) {
                addresses[funcName] = baseAddr.add(config.offsets[funcName]);
                logDebug(2, funcName + " 地址: " + formatAddress(addresses[funcName]));
            }
            
            return true;
        } catch (e) {
            logError("模块初始化失败: " + e);
            return false;
        }
    }
    
    // ==================== Hook函数 ====================
    function hookParseDispatcher() {
        // Hook sub_5C394 - 解析调度器（这是数据流的关键节点）
        var funcName = "sub_5C394";
        var targetAddr = addresses[funcName];
        
        if (!targetAddr) {
            logError(funcName + " 地址无效");
            return false;
        }
        
        try {
            log("设置 " + funcName + " Hook (解析调度器)...");
            
            var hook = Interceptor.attach(targetAddr, {
                onEnter: function(args) {
                    this.dataPtr = safeReadParams(args, 0);
                    this.dataSize = 0;
                    
                    try {
                        if (args[1]) {
                            this.dataSize = args[1].toInt32();
                        }
                    } catch (e) {
                        // 忽略大小读取错误
                    }
                    
                    gCallStats.parseDispatcher++;
                    gCallStats.total++;
                    
                    logDebug(1, "[" + getRuntime() + "] " + funcName + 
                           " 调用 #" + gCallStats.parseDispatcher + 
                           " (数据大小: ~" + this.dataSize + " 字节)");
                },
                onLeave: function(retval) {
                    try {
                        var result = retval ? retval.toInt32() : 0;
                        
                        if (result === 0 && gDataCount < config.maxDataLog) {
                            log("🎯 [解析调度成功] 数据大小: " + this.dataSize + 
                               " 字节 -> 已分发给具体解析器");
                            gDataCount++;
                        }
                        
                        logDebug(2, funcName + " 返回: " + result);
                    } catch (e) {
                        // 忽略返回值处理错误
                    }
                }
            });
            
            hookHandles.push(hook);
            log("✅ " + funcName + " Hook 设置成功");
            return true;
            
        } catch (e) {
            logError("设置 " + funcName + " Hook 失败: " + e);
            return false;
        }
    }
    
    function hookDiceParser() {
        // Hook sub_10F88 - DICE-AM数据解析器
        var funcName = "sub_10F88";
        var targetAddr = addresses[funcName];
        
        if (!targetAddr) {
            logError(funcName + " 地址无效");
            return false;
        }
        
        try {
            log("设置 " + funcName + " Hook (DICE-AM解析器)...");
            
            var hook = Interceptor.attach(targetAddr, {
                onEnter: function(args) {
                    this.dicePtr = safeReadParams(args, 0);
                    
                    gCallStats.diceParser++;
                    gCallStats.total++;
                    
                    logDebug(1, "[" + getRuntime() + "] " + funcName + 
                           " 调用 #" + gCallStats.diceParser + " (DICE-AM数据处理)");
                },
                onLeave: function(retval) {
                    try {
                        var result = retval ? retval.toInt32() : 0;
                        
                        if (result === 0 && gDataCount < config.maxDataLog) {
                            log("🎯 [DICE-AM解析成功] 矢量地图数据已解析 -> 包含坐标和地名信息");
                            gDataCount++;
                        }
                        
                        logDebug(2, funcName + " 返回: " + result);
                    } catch (e) {
                        // 忽略返回值处理错误
                    }
                }
            });
            
            hookHandles.push(hook);
            log("✅ " + funcName + " Hook 设置成功");
            return true;
            
        } catch (e) {
            logError("设置 " + funcName + " Hook 失败: " + e);
            return false;
        }
    }
    
    function hookDecompressCoordinator() {
        // Hook sub_C654 - 解压协调函数
        var funcName = "sub_C654";
        var targetAddr = addresses[funcName];
        
        if (!targetAddr) {
            logError(funcName + " 地址无效");
            return false;
        }
        
        try {
            log("设置 " + funcName + " Hook (解压协调器)...");
            
            var hook = Interceptor.attach(targetAddr, {
                onEnter: function(args) {
                    this.compressedPtr = safeReadParams(args, 0);
                    this.compressedSize = 0;
                    
                    try {
                        if (args[1]) {
                            this.compressedSize = args[1].toInt32();
                        }
                    } catch (e) {
                        // 忽略大小读取错误
                    }
                    
                    gCallStats.decompressCoord++;
                    gCallStats.total++;
                    
                    logDebug(1, "[" + getRuntime() + "] " + funcName + 
                           " 调用 #" + gCallStats.decompressCoord + 
                           " (压缩数据: ~" + this.compressedSize + " 字节)");
                },
                onLeave: function(retval) {
                    try {
                        var result = retval ? retval.toInt32() : 0;
                        
                        if (result === 0 && gDataCount < config.maxDataLog) {
                            log("🎯 [解压协调成功] 压缩数据已解压 -> 准备送往解析器");
                            gDataCount++;
                        }
                        
                        logDebug(2, funcName + " 返回: " + result);
                    } catch (e) {
                        // 忽略返回值处理错误
                    }
                }
            });
            
            hookHandles.push(hook);
            log("✅ " + funcName + " Hook 设置成功");
            return true;
            
        } catch (e) {
            logError("设置 " + funcName + " Hook 失败: " + e);
            return false;
        }
    }
    
    function hookSqliteBind() {
        // Hook girf_sqlite3_bind_blob - SQLite数据绑定
        var funcName = "girf_sqlite3_bind_blob";
        var targetAddr = addresses[funcName];
        
        if (!targetAddr) {
            logError(funcName + " 地址无效");
            return false;
        }
        
        try {
            log("设置 " + funcName + " Hook (SQLite数据绑定)...");
            
            var hook = Interceptor.attach(targetAddr, {
                onEnter: function(args) {
                    this.blobPtr = safeReadParams(args, 2);
                    this.blobSize = 0;
                    
                    try {
                        if (args[3]) {
                            this.blobSize = args[3].toInt32();
                        }
                    } catch (e) {
                        // 忽略大小读取错误
                    }
                    
                    gCallStats.sqliteBind++;
                    gCallStats.total++;
                    
                    logDebug(1, "[" + getRuntime() + "] " + funcName + 
                           " 调用 #" + gCallStats.sqliteBind + 
                           " (数据: ~" + this.blobSize + " 字节)");
                },
                onLeave: function(retval) {
                    try {
                        var result = retval ? retval.toInt32() : 0;
                        
                        if (result === 0 && gDataCount < config.maxDataLog) {
                            log("🎯 [SQLite绑定成功] 解析后数据已存储到数据库");
                            gDataCount++;
                        }
                        
                        logDebug(2, funcName + " 返回: " + result);
                    } catch (e) {
                        // 忽略返回值处理错误
                    }
                }
            });
            
            hookHandles.push(hook);
            log("✅ " + funcName + " Hook 设置成功");
            return true;
            
        } catch (e) {
            logError("设置 " + funcName + " Hook 失败: " + e);
            return false;
        }
    }
    
    function setupStatisticsReporter() {
        // 定期报告统计信息
        setInterval(function() {
            try {
                if (gCallStats.total > 0) {
                    log("\n========== 数据处理流程统计 ==========");
                    log("运行时间: " + getRuntime());
                    log("📥 解压协调: " + gCallStats.decompressCoord + " 次");
                    log("🎯 解析调度: " + gCallStats.parseDispatcher + " 次");
                    log("🗺️  DICE-AM解析: " + gCallStats.diceParser + " 次");
                    log("💾 SQLite存储: " + gCallStats.sqliteBind + " 次");
                    log("总调用数: " + gCallStats.total + " 次");
                    log("======================================\n");
                }
            } catch (e) {
                // 忽略报告错误
            }
        }, 8000);
    }
    
    // ==================== 异常处理 ====================
    function setupExceptionHandler() {
        Process.setExceptionHandler(function(exception) {
            try {
                logError("捕获异常: " + exception.type + " @ " + formatAddress(exception.address));
                logError("异常消息: " + (exception.message || "无"));
            } catch (e) {
                // 异常处理中的异常，忽略
            }
            return true;  // 继续执行
        });
    }
    
    // ==================== 主函数 ====================
    function main() {
        log("高德地图内部函数Hook脚本启动");
        log("模式: 只Hook应用内部函数，不触碰系统调用");
        log("适用于 Frida 12.9.7，使用 ES5 语法");
        
        // 设置异常处理
        setupExceptionHandler();
        
        try {
            // 初始化模块
            if (!initializeModule()) {
                logError("模块初始化失败，脚本终止");
                return;
            }
            
            // 延迟设置Hook，确保模块完全加载
            setTimeout(function() {
                try {
                    log("开始设置内部函数Hook...");
                    
                    var successCount = 0;
                    
                    // 设置各个Hook
                    if (hookDecompressCoordinator()) successCount++;
                    if (hookParseDispatcher()) successCount++;
                    if (hookDiceParser()) successCount++;
                    if (hookSqliteBind()) successCount++;
                    
                    if (successCount > 0) {
                        setupStatisticsReporter();
                        
                        log("✅ 内部函数Hook设置完成 (" + successCount + "/4)");
                        log("💡 请在地图中移动以触发数据处理流程");
                        log("💡 脚本将监控真实的解压->解析->存储过程");
                        log("💡 这些就是您要的'解压解析的数据'处理过程！");
                    } else {
                        logError("所有Hook设置失败");
                    }
                    
                } catch (e) {
                    logError("设置Hook失败: " + e);
                }
            }, 3000);
            
        } catch (e) {
            logError("脚本初始化失败: " + e);
        }
        
        log("脚本设置完成，等待初始化...");
    }
    
    // 启动脚本
    main();
})(); 