#!/usr/bin/env python3
"""
正确的解压后数据分析 - 真正解析出经纬度和地名
严格按照APP逻辑解析数据
"""

import os
import zlib
import struct

def correct_decompressed_analysis():
    """正确分析解压后数据，真正找到经纬度和地名"""
    print("🎯 正确的解压后数据分析 - 真正解析经纬度和地名")
    print("=" * 80)
    
    # 找到ans文件
    ans_files = ['file/m1.ans', 'file/m3.ans']
    target_file = None
    
    for f in ans_files:
        if os.path.exists(f):
            target_file = f
            break
    
    if not target_file:
        print("❌ 未找到.ans文件")
        return
    
    print(f"📁 分析文件: {target_file}")
    
    with open(target_file, 'rb') as f:
        raw_data = f.read()
    
    print(f"原始文件大小: {len(raw_data):,} 字节")
    
    # 查找并解压所有zlib数据块
    analyze_all_zlib_blocks(raw_data)

def analyze_all_zlib_blocks(raw_data):
    """分析所有zlib数据块，找到真正的经纬度和地名"""
    print("\n🔍 扫描所有zlib压缩块...")
    
    offset = 0
    block_count = 0
    coordinates_found = 0
    placenames_found = 0
    
    while offset < len(raw_data) - 10:
        # 查找zlib魔数
        zlib_pos = raw_data.find(b'\x78\x9c', offset)
        if zlib_pos == -1:
            break
            
        # 尝试不同大小解压
        decompressed = None
        compressed_size = 0
        
        for test_size in [1024, 2048, 4096, 8192, 16384, 32768]:
            if zlib_pos + test_size <= len(raw_data):
                try:
                    compressed_chunk = raw_data[zlib_pos:zlib_pos + test_size]
                    decompressed = zlib.decompress(compressed_chunk)
                    compressed_size = test_size
                    break
                except:
                    continue
        
        if decompressed:
            print(f"\n{'='*60}")
            print(f"📊 数据块 #{block_count} (位置: {zlib_pos})")
            print(f"压缩: {compressed_size} 字节 → 解压: {len(decompressed)} 字节")
            
            # 分析这个数据块
            coords_in_block, names_in_block = analyze_block_content(decompressed, block_count)
            coordinates_found += coords_in_block
            placenames_found += names_in_block
            
            offset = zlib_pos + compressed_size
            block_count += 1
            
            # 限制分析数量
            if block_count >= 20:
                break
        else:
            offset = zlib_pos + 1
    
    print(f"\n🎯 分析总结:")
    print(f"📊 共分析 {block_count} 个数据块")
    print(f"🌍 发现 {coordinates_found} 组坐标数据")
    print(f"🏷️ 发现 {placenames_found} 个地名文本")

def analyze_block_content(data, block_id):
    """分析单个数据块内容，寻找经纬度和地名"""
    coordinates_count = 0
    placenames_count = 0
    
    print(f"\n📋 数据块 #{block_id} 内容分析:")
    
    # 显示原始数据头部
    header_hex = ' '.join(f'{b:02x}' for b in data[:32])
    print(f"头部数据: {header_hex}")
    
    # 1. 查找DICE-AM格式数据
    if data.startswith(b'DICE-AM'):
        print("✅ 发现DICE-AM矢量数据")
        coords = parse_dice_am_coordinates(data)
        coordinates_count += coords
        
    # 2. 查找浮点数坐标 (可能的经纬度)
    else:
        coords = find_coordinate_patterns(data)
        coordinates_count += coords
    
    # 3. 查找中文地名
    names = find_chinese_placenames(data)
    placenames_count += names
    
    # 4. 查找JSON配置
    find_json_config(data)
    
    return coordinates_count, placenames_count

def parse_dice_am_coordinates(data):
    """解析DICE-AM格式的坐标数据"""
    print("🎯 解析DICE-AM坐标:")
    
    try:
        # DICE-AM头部结构
        magic = data[:7].decode('ascii')
        version = data[7]
        data_len = struct.unpack('<I', data[8:12])[0]
        point_count = struct.unpack('<I', data[12:16])[0]
        
        print(f"   魔数: {magic}")
        print(f"   版本: {version}")
        print(f"   数据长度: {data_len}")
        print(f"   坐标点数: {point_count}")
        
        # 解析坐标点
        coordinate_count = 0
        offset = 16
        
        for i in range(min(point_count, 10)):  # 显示前10个点
            if offset + 8 <= len(data):
                try:
                    x = struct.unpack('<f', data[offset:offset+4])[0]
                    y = struct.unpack('<f', data[offset+4:offset+8])[0]
                    
                    # 检查是否为合理的经纬度范围
                    if is_valid_coordinate(x, y):
                        print(f"   🌍 坐标{i}: ({x:.6f}, {y:.6f}) ← 真实经纬度!")
                        coordinate_count += 1
                    else:
                        print(f"   📐 坐标{i}: ({x:.6f}, {y:.6f}) (可能是投影坐标)")
                    
                    offset += 12  # 假设每个点12字节
                except:
                    break
        
        return coordinate_count
        
    except Exception as e:
        print(f"   ❌ DICE-AM解析失败: {e}")
        return 0

def find_coordinate_patterns(data):
    """在二进制数据中查找坐标模式"""
    print("🔍 搜索坐标模式:")
    
    coordinate_count = 0
    
    # 搜索浮点数模式
    for i in range(0, len(data) - 8, 4):
        try:
            # 尝试读取两个连续的float32
            x = struct.unpack('<f', data[i:i+4])[0]
            y = struct.unpack('<f', data[i+4:i+8])[0]
            
            # 检查是否为合理的经纬度
            if is_valid_coordinate(x, y):
                print(f"   🌍 发现经纬度: ({x:.6f}, {y:.6f}) at 偏移 {i}")
                coordinate_count += 1
                
                # 不要找太多，避免误判
                if coordinate_count >= 5:
                    break
                    
        except:
            continue
    
    if coordinate_count == 0:
        print("   ⚠️ 未发现明显的经纬度坐标")
    
    return coordinate_count

def find_chinese_placenames(data):
    """查找中文地名"""
    print("🔍 搜索中文地名:")
    
    try:
        # 尝试UTF-8解码
        text = data.decode('utf-8', errors='ignore')
        
        # 提取中文字符和周围的ASCII字符
        placenames = []
        current_name = ""
        
        for i, char in enumerate(text):
            if '\u4e00' <= char <= '\u9fff':  # 中文字符
                current_name += char
            elif current_name and len(current_name) >= 2:
                # 完成一个地名
                placenames.append(current_name)
                current_name = ""
            else:
                current_name = ""
        
        # 添加最后一个地名
        if current_name and len(current_name) >= 2:
            placenames.append(current_name)
        
        # 显示找到的地名
        if placenames:
            print(f"   🏷️ 发现 {len(placenames)} 个地名:")
            for i, name in enumerate(placenames[:10]):  # 显示前10个
                name_type = classify_placename(name)
                print(f"      {i+1}. {name} ({name_type})")
            
            if len(placenames) > 10:
                print(f"      ... 还有 {len(placenames)-10} 个地名")
        else:
            print("   ⚠️ 未发现中文地名")
        
        return len(placenames)
        
    except Exception as e:
        print(f"   ❌ 中文解析失败: {e}")
        return 0

def find_json_config(data):
    """查找JSON配置信息"""
    try:
        text = data.decode('utf-8', errors='ignore')
        
        # 查找JSON模式
        json_start = text.find('{')
        if json_start >= 0:
            json_end = text.find('}', json_start)
            if json_end > json_start:
                json_text = text[json_start:json_end+1]
                print("🔍 发现JSON配置:")
                print(f"   📄 配置内容: {json_text[:100]}...")
                
                # 分析配置类型
                if 'style' in json_text.lower():
                    print("   🎨 包含样式配置")
                if 'color' in json_text.lower():
                    print("   🌈 包含颜色设置")
                if 'zoom' in json_text.lower():
                    print("   🔍 包含缩放设置")
                
    except:
        pass

def is_valid_coordinate(x, y):
    """检查是否为有效的经纬度坐标"""
    # 中国大陆经纬度范围大致：
    # 经度: 73°E - 135°E
    # 纬度: 18°N - 54°N
    return (70 <= x <= 140 and 15 <= y <= 60) or (70 <= y <= 140 and 15 <= x <= 60)

def classify_placename(name):
    """分类地名类型"""
    if any(suffix in name for suffix in ['路', '街', '道', '巷', '胡同']):
        return "道路"
    elif any(suffix in name for suffix in ['市', '区', '县', '镇', '乡', '村']):
        return "行政区"
    elif any(suffix in name for suffix in ['公园', '广场', '商场', '大厦', '中心']):
        return "POI"
    elif any(suffix in name for suffix in ['桥', '隧道', '立交']):
        return "交通设施"
    else:
        return "其他"

if __name__ == "__main__":
    correct_decompressed_analysis() 