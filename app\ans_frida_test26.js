
// ## 脚本功能亮点：

// 1. **全面监控**：
//    - 手势识别和分析
//    - 文件操作（open/mmap/read）
//    - SQLite数据库访问
//    - 线程创建与时序分析
//    - 大内存分配监控
//    - 解压缩函数监控
//    - 渲染调用分析

// 2. **智能过滤**：
//    - 只记录关键事件和大型操作
//    - 根据文件路径特征进行分类
//    - 避免过量日志输出

// 3. **定期汇总报告**：
//    - 文件访问分类统计
//    - 线程创建时间分析
//    - 高频访问文件排序

// 4. **性能影响最小化**：
//    - 有选择地挂钩函数
//    - 减少重复信息
//    - 对大量调用函数（如渲染）进行采样

// 这个脚本将帮您全面了解高德地图从手势输入到数据加载再到渲染刷新的完整链路，特别是ANS文件的访问模

setTimeout(function() {
  console.log("[+] 高德地图数据加载全面分析脚本启动");

  // ========== 全局变量 ==========
  var gestureInProgress = false;
  var lastGestureTime = 0;
  var ansFilesAccessed = {};
  var loadSequence = [];
  var threadCreationTimes = [];
  var renderCalls = 0;
  
  // ========== 辅助函数 ==========
  function recordEvent(type, details) {
    var now = Date.now();
    var timeSinceGesture = gestureInProgress ? "进行中" : (now - lastGestureTime) + "ms";
    
    var event = {
      type: type,
      time: now,
      timeSinceGesture: timeSinceGesture,
      details: details
    };
    
    loadSequence.push(event);
    
    // 简化输出
    var output = "[" + type + "] " + JSON.stringify(details);
    if (loadSequence.length % 10 === 0) {
      console.log("当前记录了" + loadSequence.length + "个事件");
    }
    return output;
  }
  
  // 分析ANS文件路径特征
  function analyzeAnsPath(path) {
    var result = {
      path: path,
      isMapTile: path.indexOf('/tiles/') !== -1 || path.indexOf('/bld/') !== -1,
      isConfig: path.indexOf('config') !== -1,
      isCache: path.indexOf('cache') !== -1,
      isVector: path.indexOf('vector') !== -1
    };
    return result;
  }

  // ========== Java层监控 ==========
  Java.perform(function() {
    try {
      console.log("[+] 设置Java监控...");
      
      // 监控手势处理
      var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
      if (GLMapEngine.addGestureMessage) {
        GLMapEngine.addGestureMessage.implementation = function(engineId, gestureMessage) {
          var gestureType = gestureMessage.getType();
          var gestureDetails = { type: gestureType };
          
          // 获取手势信息
          if (gestureType == 0) {  // 移动手势
            var moveMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.MoveGestureMapMessage"));
            gestureDetails = {
              type: "移动",
              x: moveMsg.mTouchDeltaX.value, 
              y: moveMsg.mTouchDeltaY.value
            };
          } else if (gestureType == 1) {  // 缩放手势
            var scaleMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.ScaleGestureMapMessage"));
            gestureDetails = {
              type: "缩放", 
              scale: scaleMsg.mScaleDelta,
              pivotX: scaleMsg.mPivotX,
              pivotY: scaleMsg.mPivotY
            };
          } else if (gestureType == 2) {  // 旋转手势
            var rotateMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.RotateGestureMapMessage"));
            gestureDetails = {
              type: "旋转", 
              angle: rotateMsg.mAngleDelta,
              pivotX: rotateMsg.mPivotX,
              pivotY: rotateMsg.mPivotY
            };
          }
          
          // 记录手势状态
          if (gestureType == 0) {
            gestureInProgress = !(gestureDetails.x === 0 && gestureDetails.y === 0);
            if (!gestureInProgress) {
              lastGestureTime = Date.now();
              console.log(recordEvent("手势结束", gestureDetails));
            } else if (Math.abs(gestureDetails.x) > 5 || Math.abs(gestureDetails.y) > 5) {
              console.log(recordEvent("手势进行", gestureDetails));
            }
          } else {
            console.log(recordEvent("其他手势", gestureDetails));
          }
          
          // 调用原始方法
          var result = this.addGestureMessage(engineId, gestureMessage);
          return result;
        };
        
        console.log("[+] 成功Hook手势处理方法");
      }
      
      // 监控地图刷新函数
      if (GLMapEngine.invalidateMap) {
        GLMapEngine.invalidateMap.implementation = function() {
          console.log(recordEvent("地图刷新", { count: ++renderCalls }));
          return this.invalidateMap();
        };
        console.log("[+] 成功Hook地图刷新方法");
      }
      
      // 尝试监控瓦片加载
      try {
        var tileLoader = Java.use("com.amap.api.maps.AMapTileProvider");
        if (tileLoader) {
          tileLoader.getTileImage.implementation = function(x, y, zoom) {
            var tileInfo = { x: x, y: y, zoom: zoom };
            console.log(recordEvent("瓦片请求", tileInfo));
            var result = this.getTileImage(x, y, zoom);
            return result;
          };
          console.log("[+] 成功Hook瓦片加载方法");
        }
      } catch(e) {
        console.log("[-] 瓦片加载Hook失败: " + e);
      }
      
    } catch(e) {
      console.log("[-] Java监控设置失败: " + e);
    }
  });
  
  // ========== 文件系统监控 ==========
  console.log("[+] 设置文件系统监控...");
  
  // 1. open - 文件打开
  Interceptor.attach(Module.findExportByName("libc.so", "open"), {
    onEnter: function(args) {
      try {
        this.path = args[0].readUtf8String();
        if (this.path.indexOf('.ans') !== -1) {
          var pathInfo = analyzeAnsPath(this.path);
          console.log(recordEvent("ANS文件打开", pathInfo));
          
          // 记录访问频率
          if (!ansFilesAccessed[this.path]) {
            ansFilesAccessed[this.path] = 0;
          }
          ansFilesAccessed[this.path]++;
        }
      } catch(e) {}
    },
    onLeave: function(result) {
      if (this.path && this.path.indexOf('.ans') !== -1 && result.toInt32() > 0) {
        console.log(recordEvent("ANS文件打开成功", { path: this.path, fd: result.toInt32() }));
      }
    }
  });
  
  // 2. mmap - 内存映射
  Interceptor.attach(Module.findExportByName("libc.so", "mmap"), {
    onEnter: function(args) {
      this.size = args[1].toInt32();
      this.fd = args[4].toInt32();
    },
    onLeave: function(result) {
      if (this.fd > 0 && this.size > 1024) {
        try {
          var fdPath = "/proc/self/fd/" + this.fd;
          var filePath = new File(fdPath).readlink();
          if (filePath && filePath.indexOf('.ans') !== -1) {
            var mmapInfo = {
              path: filePath,
              size: this.size,
              addr: result
            };
            console.log(recordEvent("ANS文件映射", mmapInfo));
          }
        } catch(e) {}
      }
    }
  });
  
  // 3. read - 文件读取
  Interceptor.attach(Module.findExportByName("libc.so", "read"), {
    onEnter: function(args) {
      this.fd = args[0].toInt32();
      this.buffer = args[1];
      this.size = args[2].toInt32();
      
      // 只关注大于10KB的读取
      if (this.size > 10240) {
        try {
          var fdPath = "/proc/self/fd/" + this.fd;
          var filePath = new File(fdPath).readlink();
          if (filePath && filePath.indexOf('.ans') !== -1) {
            this.filePath = filePath;
          }
        } catch(e) {}
      }
    },
    onLeave: function(result) {
      if (this.filePath && result.toInt32() > 0) {
        var readInfo = {
          path: this.filePath,
          requestedSize: this.size,
          actualSize: result.toInt32()
        };
        console.log(recordEvent("ANS文件读取", readInfo));
      }
    }
  });
  
  // ========== SQLite监控 ==========
  console.log("[+] 设置SQLite监控...");
  
  // SQLite数据库操作
  var sqlite_funcs = ["sqlite3_open", "sqlite3_prepare_v2", "sqlite3_step", "sqlite3_exec"];
  sqlite_funcs.forEach(function(funcName) {
    var funcPtr = Module.findExportByName(null, funcName);
    if (funcPtr) {
      Interceptor.attach(funcPtr, {
        onEnter: function(args) {
          try {
            if (funcName === "sqlite3_open") {
              this.dbPath = args[0].readUtf8String();
              console.log(recordEvent("SQLite打开", { path: this.dbPath }));
            } else if (funcName === "sqlite3_prepare_v2" || funcName === "sqlite3_exec") {
              this.sql = args[1].readUtf8String();
              // 只记录与地图数据相关的SQL语句
              if (this.sql.indexOf('tile') !== -1 || 
                  this.sql.indexOf('map') !== -1 || 
                  this.sql.indexOf('cache') !== -1) {
                console.log(recordEvent("SQLite查询", { 
                  function: funcName, 
                  sql: this.sql.substring(0, 100) // 截断长SQL
                }));
              }
            }
          } catch(e) {}
        }
      });
      console.log("[+] 监控SQLite函数: " + funcName);
    }
  });
  
  // ========== 线程和内存监控 ==========
  console.log("[+] 设置线程和内存监控...");
  
  // 1. pthread_create - 线程创建
  var pthread_create = Module.findExportByName(null, "pthread_create");
  if (pthread_create) {
    Interceptor.attach(pthread_create, {
      onEnter: function(args) {
        this.threadPtr = args[0];
        this.startTime = Date.now();
      },
      onLeave: function(result) {
        if (result.toInt32() === 0) { // 成功创建线程
          var timeSinceGesture = Date.now() - lastGestureTime;
          threadCreationTimes.push(timeSinceGesture);
          
          var threadInfo = {
            threadPtr: this.threadPtr,
            timeSinceGesture: timeSinceGesture
          };
          console.log(recordEvent("线程创建", threadInfo));
        }
      }
    });
    console.log("[+] 监控线程创建");
  }
  
  // 2. malloc - 大内存分配
  var malloc = Module.findExportByName("libc.so", "malloc");
  if (malloc) {
    Interceptor.attach(malloc, {
      onEnter: function(args) {
        var size = args[0].toInt32();
        if (size > 1048576) { // 大于1MB的分配
          this.size = size;
        }
      },
      onLeave: function(result) {
        if (this.size) {
          console.log(recordEvent("大内存分配", { 
            size: this.size, 
            timeSinceGesture: Date.now() - lastGestureTime 
          }));
        }
      }
    });
    console.log("[+] 监控大内存分配");
  }
  
  // ========== 解压缩监控 ==========
  console.log("[+] 设置解压缩监控...");
  
  // 各种可能的解压函数
  var decompress_funcs = [
    "ZSTD_decompress", "ZSTD_decompressDCtx",
    "inflate", "uncompress",
    "LZ4_decompress_safe", "LZ4_decompress_fast",
    "lzma_code", "BZ2_bzDecompress"
  ];
  
  // 遍历所有模块查找解压函数
  Process.enumerateModules().forEach(function(module) {
    if (module.name.indexOf('amap') !== -1) {
      decompress_funcs.forEach(function(funcName) {
        var funcPtr = Module.findExportByName(module.name, funcName);
        if (funcPtr) {
          Interceptor.attach(funcPtr, {
            onEnter: function(args) {
              this.funcName = funcName;
              this.startTime = Date.now();
              // 不同解压函数参数位置可能不同，这里假设常见布局
              try {
                this.inputSize = args[2] ? args[2].toInt32() : 0;
              } catch(e) {
                this.inputSize = 0;
              }
            },
            onLeave: function(result) {
              var decompInfo = {
                function: this.funcName,
                module: module.name,
                inputSize: this.inputSize,
                outputSize: result.toInt32(),
                duration: Date.now() - this.startTime,
                timeSinceGesture: Date.now() - lastGestureTime
              };
              console.log(recordEvent("解压数据", decompInfo));
            }
          });
          console.log("[+] 监控解压函数: " + module.name + "!" + funcName);
        }
      });
      
      // 查找其他可能的自定义解压函数
      module.enumerateExports().forEach(function(exp) {
        if (exp.name.toLowerCase().indexOf('decompress') !== -1 || 
            exp.name.toLowerCase().indexOf('inflate') !== -1 || 
            exp.name.toLowerCase().indexOf('unzip') !== -1) {
          Interceptor.attach(exp.address, {
            onEnter: function(args) {
              this.funcName = exp.name;
              this.startTime = Date.now();
            },
            onLeave: function(result) {
              console.log(recordEvent("自定义解压", {
                function: this.funcName, 
                module: module.name,
                duration: Date.now() - this.startTime,
                timeSinceGesture: Date.now() - lastGestureTime
              }));
            }
          });
          console.log("[+] 监控自定义解压: " + module.name + "!" + exp.name);
        }
      });
    }
  });
  
  // ========== 渲染调用监控 ==========
  console.log("[+] 设置渲染监控...");
  
  // OpenGL/GLES函数
  var render_funcs = [
    "eglSwapBuffers", 
    "glDrawElements", 
    "glDrawArrays"
  ];
  
  // 仅监控渲染开始和结束，避免过多日志
  var renderStartTime = 0;
  var frameCount = 0;
  render_funcs.forEach(function(funcName) {
    var funcPtr = Module.findExportByName(null, funcName);
    if (funcPtr) {
      Interceptor.attach(funcPtr, {
        onEnter: function() {
          frameCount++;
          if (funcName === "eglSwapBuffers" && frameCount % 10 === 0) {
            // 每10帧记录一次
            console.log(recordEvent("渲染更新", { 
              frames: frameCount,
              timeSinceGesture: Date.now() - lastGestureTime 
            }));
          }
        }
      });
      console.log("[+] 监控渲染函数: " + funcName);
    }
  });
  
  // ========== 定时汇总功能 ==========
  setInterval(function() {
    // 如果累积了足够多的事件，生成汇总报告
    if (loadSequence.length > 0) {
      console.log("\n[汇总报告] ====================");
      console.log("记录事件总数: " + loadSequence.length);
      
      // 统计ANS文件访问情况
      var fileTypes = {mapTile: 0, config: 0, cache: 0, vector: 0, other: 0};
      for (var path in ansFilesAccessed) {
        var info = analyzeAnsPath(path);
        if (info.isMapTile) fileTypes.mapTile++;
        else if (info.isConfig) fileTypes.config++;
        else if (info.isCache && info.isVector) fileTypes.vector++;
        else if (info.isCache) fileTypes.cache++;
        else fileTypes.other++;
      }
      
      console.log("ANS文件访问统计:");
      console.log("- 地图瓦片文件: " + fileTypes.mapTile);
      console.log("- 矢量数据文件: " + fileTypes.vector);
      console.log("- 配置文件: " + fileTypes.config);
      console.log("- 其他缓存文件: " + fileTypes.cache);
      console.log("- 未分类文件: " + fileTypes.other);
      
      // 线程创建分析
      if (threadCreationTimes.length > 0) {
        console.log("\n线程创建时间分析:");
        var timeGroups = {
          immediate: 0,  // 0-200ms
          quick: 0,      // 200-1000ms
          delayed: 0,    // 1-5秒
          background: 0  // >5秒
        };
        
        threadCreationTimes.forEach(function(time) {
          if (time < 200) timeGroups.immediate++;
          else if (time < 1000) timeGroups.quick++;
          else if (time < 5000) timeGroups.delayed++;
          else timeGroups.background++;
        });
        
        console.log("- 立即创建(0-200ms): " + timeGroups.immediate);
        console.log("- 快速创建(200-1000ms): " + timeGroups.quick);
        console.log("- 延迟创建(1-5秒): " + timeGroups.delayed);
        console.log("- 后台创建(>5秒): " + timeGroups.background);
      }
      
      console.log("\n访问最频繁的5个文件:");
      
      // 按访问频率排序文件
      var sortedFiles = Object.keys(ansFilesAccessed).sort(function(a, b) {
        return ansFilesAccessed[b] - ansFilesAccessed[a];
      });
      
      // 显示前5个
      for (var i = 0; i < Math.min(sortedFiles.length, 5); i++) {
        var path = sortedFiles[i];
        console.log("  " + (i+1) + ". [" + ansFilesAccessed[path] + "次] " + path);
      }
      
      console.log("[汇总报告结束] ====================\n");
    }
  }, 15000);  // 每15秒生成一次报告
  
  console.log("[+] 地图数据加载全面分析脚本已启动，请进行地图操作...");
}, 1000);

