#!/usr/bin/env node
/**
 * Refine lon/lat decoding and texts cleaning, then re-join.
 * Usage:
 *   node refine_lonlat_and_texts.js md-file/gaode_dump
 * Outputs:
 *   geoms_fixed.csv
 *   cleaned_texts.csv
 *   joined_texts_geoms_fixed.csv
 */
const fs = require('fs');
const path = require('path');

const inputDir = process.argv[2] ? path.resolve(process.argv[2]) : path.resolve('./md-file/gaode_dump');
const geomBinDir = path.join(inputDir, 'geom_bin');
const MATCH_THRESHOLD_MS = 15000; // widen time window to improve recall

function exists(p){ try{ fs.accessSync(p); return true; }catch{ return false; } }
function readJSONLLines(file){
  if(!exists(file)) return [];
  return fs.readFileSync(file,'utf8').split(/\r?\n/).filter(Boolean).map(l=>{ try{return JSON.parse(l);}catch{return null;} }).filter(Boolean);
}
function writeCSV(file, rows, header){
  const s = [header.join(',')].concat(rows.map(r => header.map(h => (r[h]!=null? String(r[h]).replace(/[\r\n,]/g,' ') : '')).join(','))).join('\n');
  fs.writeFileSync(file, s, 'utf8');
  console.log('[write]', file, rows.length, 'rows');
}

/* ===== GCJ/WGS utils ===== */
const a = 6378245.0, ee = 0.00669342162296594323;
function outOfChina(lon, lat){ return lon<72.004 || lon>137.8347 || lat<0.8293 || lat>55.8271; }
function tLat(x, y){ let r=-100+2*x+3*y+0.2*y*y+0.1*x*y+0.2*Math.sqrt(Math.abs(x)); r+=(20*Math.sin(6*x*Math.PI)+20*Math.sin(2*x*Math.PI))*2/3; r+=(20*Math.sin(y*Math.PI)+40*Math.sin(y/3*Math.PI))*2/3; r+=(160*Math.sin(y/12*Math.PI)+320*Math.sin(y*Math.PI/30))*2/3; return r; }
function tLon(x, y){ let r=300+x+2*y+0.1*x*x+0.1*x*y+0.1*Math.sqrt(Math.abs(x)); r+=(20*Math.sin(6*x*Math.PI)+20*Math.sin(2*x*Math.PI))*2/3; r+=(20*Math.sin(x*Math.PI)+40*Math.sin(x/3*Math.PI))*2/3; r+=(150*Math.sin(x/12*Math.PI)+300*Math.sin(x/30*Math.PI))*2/3; return r; }
function wgs84ToGcj02(lon, lat){
  if(outOfChina(lon,lat)) return [lon,lat];
  let dLat=tLat(lon-105,lat-35), dLon=tLon(lon-105,lat-35);
  const radLat=lat/180*Math.PI; let magic=Math.sin(radLat); magic=1-ee*magic*magic; const sqrtMagic=Math.sqrt(magic);
  dLat=(dLat*180)/((a*(1-ee))/(magic*sqrtMagic)*Math.PI); dLon=(dLon*180)/(a/sqrtMagic*Math.cos(radLat)*Math.PI);
  return [lon+dLon, lat+dLat];
}

/* ===== models: world tile and mercator meters ===== */
function worldToLonLat(x,y,z){
  const W = 256*Math.pow(2,z);
  const lon = (x/W)*360 - 180;
  const n = Math.PI - 2*Math.PI*(y/W);
  const lat = 180/Math.PI * Math.atan(0.5*(Math.exp(n)-Math.exp(-n)));
  return [lon,lat];
}
function metersToLonLat(xMeters,yMeters){
  const R=6378137.0;
  const lon = (xMeters/R)*180/Math.PI;
  const lat = (2*Math.atan(Math.exp(yMeters/R)) - Math.PI/2)*180/Math.PI;
  return [lon,lat];
}

/* ===== parse geom bin to Int32 pairs ===== */
function parsePairs(buf){
  const dv = new DataView(buf.buffer, buf.byteOffset, buf.byteLength);
  const n = Math.floor(buf.byteLength/4);
  const ints = new Int32Array(n);
  for(let i=0;i<n;i++) ints[i] = dv.getInt32(i*4, true);
  const pts = [];
  for(let i=0;i+1<ints.length;i+=2) pts.push([ints[i], ints[i+1]]);
  return pts;
}

/* ===== quality scoring ===== */
function scoreLonLat(ll){
  if(ll.length===0) return {score:0, inRange:0, total:0, bbox:null};
  let inCount=0, minLon=1e9,maxLon=-1e9,minLat=1e9,maxLat=-1e9;
  for(const [lon,lat] of ll){
    if(isFinite(lon)&&isFinite(lat) && lon>=-180 && lon<=180 && lat>=-85 && lat<=85) inCount++;
    if(isFinite(lon)){ if(lon<minLon)minLon=lon; if(lon>maxLon)maxLon=lon; }
    if(isFinite(lat)){ if(lat<minLat)minLat=lat; if(lat>maxLat)maxLat=lat; }
  }
  const frac = inCount/ll.length;
  const spanLon = maxLon-minLon, spanLat = maxLat-minLat;
  // reward inRange fraction and reasonable span
  const spanPenalty = (spanLon<=0 || spanLat<=0)? 0.1 : 1.0;
  const score = frac * spanPenalty;
  return {score, inRange: inCount, total: ll.length, bbox: [minLon,minLat,maxLon,maxLat]};
}

/* ===== try decode with multiple models, pick best ===== */
function decodeBest(buf){
  const pts = parsePairs(buf);
  if(pts.length===0) return null;
  const sample = pts.slice(0, Math.min(pts.length, 4000));

  const candidates = [];

  // world tile z search
  const zList = []; for(let z=14; z<=24; z++) zList.push(z);
  for(const z of zList){
    const ll = sample.map(([x,y])=>worldToLonLat(x,y,z));
    const s = scoreLonLat(ll);
    candidates.push({model:'world', param:z, score:s.score, detail:s, ll});
  }

  // mercator meters with scale search (int/scale -> meters)
  const scales = [1,10,100,256,512,1024,2048,4096,8192,16384,0.1,0.01,0.001];
  for(const sc of scales){
    const ll = sample.map(([x,y])=>metersToLonLat(x/sc, y/sc));
    const s = scoreLonLat(ll);
    candidates.push({model:'meters', param:sc, score:s.score, detail:s, ll});
  }

  candidates.sort((a,b)=>b.score - a.score);
  const best = candidates[0];
  // if very poor, return null
  if(!best || best.score < 0.5) return { model:'unknown', param:null, centroidW:null, centroidG:null, count:pts.length, sampleScore:best?best.score:0 };

  // transform all points using best
  const llAll = pts.map(([x,y])=>{
    if(best.model==='world') return worldToLonLat(x,y,best.param);
    else return metersToLonLat(x/best.param, y/best.param);
  });
  const sum = llAll.reduce((acc,[lon,lat])=>[acc[0]+lon,acc[1]+lat],[0,0]);
  const centroidW = [sum[0]/llAll.length, sum[1]/llAll.length];
  const centroidG = wgs84ToGcj02(centroidW[0], centroidW[1]);
  return {
    model: best.model, param: best.param,
    centroidW, centroidG,
    count: llAll.length,
    sampleScore: best.score
  };
}

/* ===== texts cleaning ===== */
function cleanText(s){
  if(!s) return s;
  let t = s.trim();

  // remove repeated filler phrases
  const fillers = [
    '期块号','号楼','弄号','公共地面停车场','停车点','停车场'
  ];
  for(const f of fillers){
    const re = new RegExp('(' + f + ')+','g');
    t = t.replace(re, f);
  }

  // drop excessive repeats (simple sliding window de-dup)
  t = t.replace(/(.{2,6})\1{1,}/g, '$1');

  // collapse spaces
  t = t.replace(/\s+/g,'').trim();

  // truncate extreme length
  if(t.length>100) t = t.slice(0,100);

  return t;
}

/* ===== main ===== */
function main(){
  if(!exists(inputDir)){ console.error('[error] inputDir not found', inputDir); process.exit(1); }
  console.log('[inputDir]', inputDir);

  // 1) decode geometry with auto model selection
  const geomsFixed = [];
  if(exists(geomBinDir)){
    const files = fs.readdirSync(geomBinDir).filter(x=>x.toLowerCase().endsWith('.bin'));
    for(const f of files){
      let ts=0, magic=''; const parts=f.split('_'); if(parts.length>=1){ const n=Number(parts[0]); if(Number.isFinite(n)) ts=n; } if(parts.length>=2) magic=parts[1];
      try{
        const buf = fs.readFileSync(path.join(geomBinDir,f));
        const r = decodeBest(buf);
        if(r){
          geomsFixed.push({
            ts, file:f, magic,
            model:r.model, param:r.param, score:r.sampleScore,
            lonWGS84: r.centroidW? r.centroidW[0] : '',
            latWGS84: r.centroidW? r.centroidW[1] : '',
            lonGCJ02: r.centroidG? r.centroidG[0] : '',
            latGCJ02: r.centroidG? r.centroidG[1] : '',
            pointCount: r.count
          });
        } else {
          geomsFixed.push({ ts, file:f, magic, model:'none', param:'', score:0, lonWGS84:'', latWGS84:'', lonGCJ02:'', latGCJ02:'', pointCount:'' });
        }
      }catch(e){
        geomsFixed.push({ ts, file:f, magic, model:'err', param:'', score:0, lonWGS84:'', latWGS84:'', lonGCJ02:'', latGCJ02:'', pointCount:'', err:e.message });
      }
    }
  } else {
    console.warn('[warn] no geom_bin dir, skip');
  }
  geomsFixed.sort((a,b)=>a.ts-b.ts);
  writeCSV(path.join(inputDir,'geoms_fixed.csv'), geomsFixed, [
    'ts','file','magic','model','param','score','lonWGS84','latWGS84','lonGCJ02','latGCJ02','pointCount'
  ]);

  // 2) clean texts
  const textLines = readJSONLLines(path.join(inputDir,'25xx_text.jsonl'));
  const texts = [];
  for(const rec of textLines){
    const ts = Number(rec.ts||0)||0;
    const type = rec.type_id_hex||'';
    if(Array.isArray(rec.groups)){
      for(const g of rec.groups){
        if(g && g.text && g.text.trim()){
          const cleaned = cleanText(g.text);
          if(cleaned) texts.push({ ts, type, text: cleaned });
        }
      }
    }
  }
  texts.sort((a,b)=>a.ts-b.ts);
  writeCSV(path.join(inputDir,'cleaned_texts.csv'), texts, ['ts','type','text']);

  // 3) re-join by nearest ts with wider window
  const rows = [];
  let j=0;
  for(const t of texts){
    while(j+1<geomsFixed.length && Math.abs(geomsFixed[j+1].ts - t.ts) <= Math.abs(geomsFixed[j].ts - t.ts)) j++;
    const g = geomsFixed[j] || null;
    if(g && Math.abs(g.ts - t.ts) <= MATCH_THRESHOLD_MS && g.lonWGS84!=='' && g.latWGS84!==''){
      rows.push({
        tsText: t.ts, type: t.type, text: t.text,
        tsGeom: g.ts, binFile: g.file, magic: g.magic,
        model: g.model, param: g.param, score: g.score,
        lonWGS84: g.lonWGS84, latWGS84: g.latWGS84,
        lonGCJ02: g.lonGCJ02, latGCJ02: g.latGCJ02,
        pointCount: g.pointCount
      });
    } else {
      rows.push({
        tsText: t.ts, type: t.type, text: t.text,
        tsGeom: '', binFile: '', magic: '',
        model:'', param:'', score:'',
        lonWGS84:'', latWGS84:'', lonGCJ02:'', latGCJ02:'', pointCount:''
      });
    }
  }
  writeCSV(path.join(inputDir,'joined_texts_geoms_fixed.csv'), rows, [
    'tsText','type','text','tsGeom','binFile','magic','model','param','score','lonWGS84','latWGS84','lonGCJ02','latGCJ02','pointCount'
  ]);

  console.log('[done] outputs:', path.join(inputDir,'geoms_fixed.csv'), path.join(inputDir,'cleaned_texts.csv'), path.join(inputDir,'joined_texts_geoms_fixed.csv'));
}

main();
