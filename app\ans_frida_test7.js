(function() {
    console.log("--- [高德地图ANS文件分析] 脚本启动 ---");
    
    // 全局变量
    var ansFileFDs = {};  // 文件描述符到路径的映射
    var targetFunction = "weftlattimqugxvvpvso"; // 目标函数名
    var lastUserAction = ""; // 记录最后的用户操作
    var m1AnsOperations = []; // 记录m1.ans的操作
    var m3AnsOperations = []; // 记录m3.ans的操作
    
    // 1. 防止应用崩溃 - 简单版本
    var exit_ptr = Module.findExportByName("libc.so", "exit");
    if (exit_ptr) {
      Interceptor.replace(exit_ptr, new NativeCallback(function() {
        return 0;
      }, 'void', ['int']));
      console.log("[+] 拦截exit()调用");
    }
    
    // 2. 监控文件打开操作 - 针对m1.ans和m3.ans
    var open_ptr = Module.findExportByName("libc.so", "open");
    if (open_ptr) {
      Interceptor.attach(open_ptr, {
        onEnter: function(args) {
          try {
            var path = args[0].readUtf8String();
            if (path && path.endsWith(".ans")) {
              this.isAnsFile = true;
              this.path = path;
              this.fileName = path.split("/").pop();
              
              // 专门记录m1.ans和m3.ans
              if (path.indexOf("m1.ans") !== -1) {
                console.log("[场景分析] 打开m1.ans文件: " + path);
                console.log("[上下文] 当前用户操作: " + (lastUserAction || "未知"));
                this.isM1Ans = true;
              } 
              else if (path.indexOf("m3.ans") !== -1) {
                console.log("[场景分析] 打开m3.ans文件: " + path);
                console.log("[上下文] 当前用户操作: " + (lastUserAction || "未知"));
                this.isM3Ans = true;
              }
              else {
                // 简化输出，只显示文件名
                console.log("[ANS] 打开: " + this.fileName);
              }
            }
          } catch (e) {}
        },
        onLeave: function(retval) {
          if (this.isAnsFile && retval.toInt32() > 0) {
            var fd = retval.toInt32();
            ansFileFDs[fd] = {
              path: this.path,
              fileName: this.fileName,
              isM1Ans: this.isM1Ans,
              isM3Ans: this.isM3Ans,
              openTime: new Date().toISOString()
            };
            
            if (this.isM1Ans || this.isM3Ans) {
              console.log("[文件] 文件描述符: " + fd);
              
              // 获取详细调用栈
              try {
                var stack = Thread.backtrace(this.context, Backtracer.ACCURATE)
                  .map(DebugSymbol.fromAddress);
                console.log("[调用栈] 打开ANS文件:");
                for (var i = 0; i < Math.min(stack.length, 5); i++) {
                  var symbol = stack[i];
                  if (symbol) {
                    console.log("  " + i + ": " + symbol.toString());
                    
                    // 检查是否包含目标函数
                    if (symbol.toString().indexOf(targetFunction) !== -1) {
                      console.log("[确认] 由目标函数" + targetFunction + "打开");
                    }
                  }
                }
              } catch(e) {}
            }
          }
        }
      });
      console.log("[+] 监控open()调用");
    }
    
    // 3. 监控read操作 - 详细记录m1.ans和m3.ans
    var read_ptr = Module.findExportByName("libc.so", "read");
    if (read_ptr) {
      Interceptor.attach(read_ptr, {
        onEnter: function(args) {
          this.fd = args[0].toInt32();
          if (ansFileFDs[this.fd]) {
            this.fileInfo = ansFileFDs[this.fd];
            this.buffer = args[1];
            this.size = args[2].toInt32();
          }
        },
        onLeave: function(retval) {
          if (!this.fileInfo) return;
          
          var bytesRead = retval.toInt32();
          if (bytesRead <= 0) return;
          
          // 记录m1.ans和m3.ans的读取操作
          if (this.fileInfo.isM1Ans || this.fileInfo.isM3Ans) {
            var opInfo = {
              type: "read",
              fd: this.fd,
              requestedSize: this.size,
              actualSize: bytesRead,
              timestamp: new Date().toISOString()
            };
            
            if (this.fileInfo.isM1Ans) {
              m1AnsOperations.push(opInfo);
              console.log("[m1.ans读取] 大小: " + bytesRead + " 字节" + 
                          (m1AnsOperations.length % 10 === 0 ? " (累计操作: " + m1AnsOperations.length + ")" : ""));
            } else {
              m3AnsOperations.push(opInfo);
              console.log("[m3.ans读取] 大小: " + bytesRead + " 字节" + 
                          (m3AnsOperations.length % 10 === 0 ? " (累计操作: " + m3AnsOperations.length + ")" : ""));
            }
            
            // 只详细分析小块读取
            if (bytesRead <= 32) {
              try {
                // 检查是否是ANS文件头 (00 00 fe fe)
                var data = new Uint8Array(Memory.readByteArray(this.buffer, Math.min(bytesRead, 16)));
                if (data[0] === 0 && data[1] === 0 && data[2] === 0xfe && data[3] === 0xfe) {
                  console.log("[文件头] 检测到ANS文件头特征!");
                  console.log(hexdump(this.buffer, {length: Math.min(bytesRead, 32)}));
                  
                  // 分析文件头结构
                  console.log("[文件头分析]");
                  console.log("  Magic:   " + data[0].toString(16).padStart(2,'0') + data[1].toString(16).padStart(2,'0') + " " + 
                                            data[2].toString(16).padStart(2,'0') + data[3].toString(16).padStart(2,'0'));
                  console.log("  Version: " + data[4].toString(16).padStart(2,'0') + data[5].toString(16).padStart(2,'0') + " " + 
                                            data[6].toString(16).padStart(2,'0') + data[7].toString(16).padStart(2,'0'));
                  console.log("  Flags:   " + data[8].toString(16).padStart(2,'0') + data[9].toString(16).padStart(2,'0') + " " + 
                                            data[10].toString(16).padStart(2,'0') + data[11].toString(16).padStart(2,'0'));
                  
                  // 获取调用栈
                  try {
                    var stack = Thread.backtrace(this.context, Backtracer.ACCURATE)
                      .map(DebugSymbol.fromAddress);
                    console.log("[调用栈] 读取文件头:");
                    for (var i = 0; i < Math.min(stack.length, 3); i++) {
                      console.log("  " + i + ": " + (stack[i] ? stack[i].toString() : "???"));
                    }
                  } catch(e) {}
                }
              } catch(e) {}
            }
          }
        }
      });
      console.log("[+] 监控read()调用");
    }
    
    // 4. 监控lseek操作 - 详细记录m1.ans和m3.ans
    var lseek_ptr = Module.findExportByName("libc.so", "lseek");
    if (lseek_ptr) {
      Interceptor.attach(lseek_ptr, {
        onEnter: function(args) {
          this.fd = args[0].toInt32();
          if (ansFileFDs[this.fd]) {
            this.fileInfo = ansFileFDs[this.fd];
            this.offset = args[1].toInt32();
            this.whence = args[2].toInt32();
            
            // 记录m1.ans和m3.ans的seek操作
            if (this.fileInfo.isM1Ans || this.fileInfo.isM3Ans) {
              var whenceStr = this.whence === 0 ? "SET" : (this.whence === 1 ? "CUR" : "END");
              
              var opInfo = {
                type: "lseek",
                fd: this.fd,
                offset: this.offset,
                whence: whenceStr,
                timestamp: new Date().toISOString()
              };
              
              if (this.fileInfo.isM1Ans) {
                m1AnsOperations.push(opInfo);
                console.log("[m1.ans定位] 偏移: " + this.offset + ", 模式: " + whenceStr);
              } else {
                m3AnsOperations.push(opInfo);
                console.log("[m3.ans定位] 偏移: " + this.offset + ", 模式: " + whenceStr);
              }
              
              // 记录特殊偏移
              if (this.offset === 0 && this.whence === 0) {
                console.log("[关键] 访问文件起始位置");
              } else if (this.offset === 24 && this.whence === 0) {
                console.log("[关键] 访问偏移量24处的数据");
              } else if (this.offset === 8192 && this.whence === 0) {
                console.log("[关键] 访问8K边界处的数据");
              }
            }
          }
        }
      });
      console.log("[+] 监控lseek()调用");
    }
    
    // 5. 监控Java层触摸事件，记录用户操作
    setTimeout(function() {
      Java.perform(function() {
        try {
          console.log("[+] Java层分析开始");
          
          // 基本反调试
          var Debug = Java.use("android.os.Debug");
          Debug.isDebuggerConnected.implementation = function() {
            return false;
          };
          
          // 监控地图移动事件
          try {
            var MapView = Java.use("com.autonavi.ae.gmap.GLMapView");
            if (MapView.onTouchEvent) {
              MapView.onTouchEvent.implementation = function(motionEvent) {
                try {
                  var action = motionEvent.getAction();
                  if (action === 2) { // ACTION_MOVE
                    lastUserAction = "地图移动";
                    console.log("[用户操作] 地图移动");
                  }
                } catch(e) {}
                return this.onTouchEvent(motionEvent);
              };
              console.log("[+] 监控地图移动事件成功");
            }
          } catch(e) {
            console.log("[错误] 监控地图移动事件失败: " + e);
          }
          
          // 监控搜索框输入
          try {
            var SearchBox = Java.use("com.autonavi.minimap.widget.SearchBox");
            if (SearchBox.onTextChanged) {
              SearchBox.onTextChanged.implementation = function(charSequence, start, before, count) {
                lastUserAction = "搜索框输入: " + charSequence;
                console.log("[用户操作] 搜索框输入: " + charSequence);
                return this.onTextChanged(charSequence, start, before, count);
              };
              console.log("[+] 监控搜索框输入成功");
            }
          } catch(e) {
            console.log("[错误] 监控搜索框输入失败: " + e);
          }
          
          // 监控AjxBLFactoryController类
          try {
            var AjxBLFactoryController = Java.use("com.autonavi.jni.ajx3.bl.AjxBLFactoryController");
            console.log("[类] 找到AjxBLFactoryController");
            
            // 监控init4WarmStart方法
            if (AjxBLFactoryController.init4WarmStart) {
              AjxBLFactoryController.init4WarmStart.implementation = function() {
                console.log("[调用] init4WarmStart(" + Array.prototype.slice.call(arguments).join(", ") + ")");
                var result = this.init4WarmStart.apply(this, arguments);
                console.log("[返回] init4WarmStart = " + result);
                return result;
              };
              console.log("[+] 监控init4WarmStart成功");
            }
          } catch(e) {
            console.log("[错误] AjxBLFactoryController分析失败: " + e);
          }
          
          console.log("[+] Java层分析完成");
        } catch(e) {
          console.log("[错误] Java层分析异常: " + e);
        }
      });
    }, 5000); // 延迟5秒执行
    
    // 6. 定期输出统计信息
    setInterval(function() {
      if (m1AnsOperations.length > 0 || m3AnsOperations.length > 0) {
        console.log("\n[统计信息] ==================");
        console.log("m1.ans 操作总数: " + m1AnsOperations.length + " (读取: " + 
                    m1AnsOperations.filter(function(op) { return op.type === "read"; }).length + 
                    ", 定位: " + 
                    m1AnsOperations.filter(function(op) { return op.type === "lseek"; }).length + ")");
        
        console.log("m3.ans 操作总数: " + m3AnsOperations.length + " (读取: " + 
                    m3AnsOperations.filter(function(op) { return op.type === "read"; }).length + 
                    ", 定位: " + 
                    m3AnsOperations.filter(function(op) { return op.type === "lseek"; }).length + ")");
        
        console.log("最后用户操作: " + (lastUserAction || "未知"));
        console.log("[统计信息] ==================\n");
      }
    }, 30000); // 每30秒输出一次
    
    console.log("--- [高德地图ANS文件分析] 脚本设置完成 ---");
  })(); 