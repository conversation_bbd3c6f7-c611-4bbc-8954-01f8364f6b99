/*
 * 高德 离线解析 IDA引导版 Hook 脚本（Frida 12.9.7, ES5）
 * 目标：按 IDA 反编译结果，直接 Hook 解析入口与变长整数解析函数，打印关键参数与返回值，辅助还原结构。
 * 钩子：
 *  - sub_20120 (libamapnsq.so + 0x20120)：文本/属性链主入口（在 0x20208 有主循环）。
 *  - sub_228C4 (libamapnsq.so + 0x228C4)：7-bit varint 双字段解析，写出到 a3 结构。
 *  - sub_5C060 (libamapnsq.so + 0x5C060)：分发/调度器。
 * 输出：仅 ASCII 中文注释与日志，避免特殊符号。
 */

console.log("[启动] IDA引导版 Hook 脚本 已启动");

// 开关：默认仅启用 sub_228C4，避免对热路径造成影响
var ENABLE_20120 = false;
var ENABLE_5C060 = false;

var hookCounters = { c20120: 0, c228C4: 0, c5C060: 0 };

// ===== 安全读取工具 =====
function getReadableLimit(ptr, desiredLen) {
	try {
		var r = Process.findRangeByAddress(ptr);
		if (!r) return 0;
		var offsetInRange = ptr.sub(r.base).toInt32();
		var remain = r.size - offsetInRange;
		if (remain <= 0) return 0;
		return Math.max(0, Math.min(remain, desiredLen));
	} catch (e) { return 0; }
}

function safeReadByteArray(ptr, desiredLen) {
	var len = getReadableLimit(ptr, desiredLen);
	if (len <= 0) return null;
	try { return ptr.readByteArray(len); } catch (e) { return null; }
}

function hexPreview(ptr, size, limit) {
	var lim = Math.min(size, limit || 64);
	var arr = safeReadByteArray(ptr, lim);
	if (!arr) return "<预览不可用>";
	var bytes = new Uint8Array(arr);
	var hex = "";
	for (var i = 0; i < bytes.length; i++) {
		var b = bytes[i].toString(16);
		if (b.length === 1) b = "0" + b;
		hex += b + (i + 1 < bytes.length ? " " : "");
	}
	return hex;
}

function readU8(p, o) { return p.add(o).readU8(); }
function readU16(p, o) { return p.add(o).readU16(); }
function readU32(p, o) { return p.add(o).readU32(); }
function readPtr(p, o) { return p.add(o).readPointer(); }

function decodeVarint7(ptr, maxBytes) {
	// 读取最多 maxBytes 个字节的 7-bit varint（正向拼接，高位在左）。返回 {value, used}
	var value = 0;
	var used = 0;
	for (var i = 0; i < maxBytes; i++) {
		var b = 0; try { b = ptr.add(i).readU8(); } catch (e) { break; }
		var cont = (b & 0x80) !== 0;
		var part = (b & 0x7F);
		value = (value << 7) | part;
		used++;
		if (!cont) break;
	}
	return { value: value >>> 0, used: used };
}

// 新增：等待目标模块加载后再安装钩子（兼容 libamapnsq.so / libamaprsq.so）
function install_hooks(mod) {
	var f_20120 = mod.base.add(0x20120);
	var f_228C4 = mod.base.add(0x228C4);
	var f_5C060 = mod.base.add(0x5C060);
	console.log("[模块] " + mod.name + " base=" + mod.base + " 安装钩子偏移= 0x20120, 0x228C4, 0x5C060");
	try {
		Interceptor.attach(f_228C4, {
			onEnter: function(args) {
				hookCounters.c228C4++;
				this.a2 = args[1];
				this.a3 = args[2];
				if (hookCounters.c228C4 <= 3) {
					var cur = ptr(0), end = ptr(0);
					try { cur = this.a2.readPointer(); } catch (e) {}
					try { end = this.a2.add(8).readPointer(); } catch (e) {}
					var prev = cur.isNull() ? "<预览不可用>" : hexPreview(cur, Math.max(0, end.sub(cur).toInt32()), 32);
					console.log("[sub_228C4 enter] cur=" + cur + " end=" + end + " 预览=" + prev);
				}
			},
			onLeave: function(retval) {
				if (hookCounters.c228C4 <= 10 || (hookCounters.c228C4 % 200) === 0) {
					var a3 = this.a3;
					var v1 = 0, v2_qw = ptr(0), nextPtr = ptr(0), off20 = 0, off22 = 0, off24 = 0;
					try { v1 = a3.add(16).readU32(); } catch (e) {}
					try { v2_qw = a3.readPointer(); } catch (e) {}
					try { nextPtr = a3.add(8).readPointer(); } catch (e) {}
					try { off20 = a3.add(20).readU16(); } catch (e) {}
					try { off22 = a3.add(22).readU16(); } catch (e) {}
					try { off24 = a3.add(24).readU16(); } catch (e) {}
					console.log("[sub_228C4 leave] cnt=" + hookCounters.c228C4 + " v1=" + v1 + " v2(qw)=" + v2_qw + " nextPtr=" + nextPtr + " w20=" + off20 + " w22=" + off22 + " w24=" + off24);
				}
			}
		});
		console.log("[成功] Hook sub_228C4 at " + f_228C4);
	} catch (e) { console.log("[失败] Hook sub_228C4: " + e); }
	try {
		if (ENABLE_20120) {
			Interceptor.attach(f_20120, {
				onEnter: function(args) {
					hookCounters.c20120++;
					this.a1 = args[0]; this.a2 = args[1]; this.a3 = args[2]; this.a4 = args[3];
					if (hookCounters.c20120 <= 5 || (hookCounters.c20120 % 500) === 0) {
						console.log("[sub_20120 enter] cnt=" + hookCounters.c20120 + " a1=" + this.a1 + " a2=" + this.a2 + " a3=" + this.a3 + " a4=" + this.a4);
					}
				},
				onLeave: function(retval) {
					if (hookCounters.c20120 <= 3 || (hookCounters.c20120 % 500) === 0) {
						console.log("[sub_20120 leave] cnt=" + hookCounters.c20120 + " ret=" + retval);
					}
				}
			});
			console.log("[成功] Hook sub_20120 at " + f_20120);
		} else {
			console.log("[跳过] sub_20120 未启用");
		}
	} catch (e) { console.log("[失败] Hook sub_20120: " + e); }
	try {
		if (ENABLE_5C060) {
			Interceptor.attach(f_5C060, {
				onEnter: function(args) {
					hookCounters.c5C060++;
					this.a1 = args[0]; this.a2 = args[1]; this.a3 = args[2]; this.a4 = args[3];
					if (hookCounters.c5C060 <= 5 || (hookCounters.c5C060 % 500) === 0) {
						console.log("[sub_5C060 enter] cnt=" + hookCounters.c5C060 + " a1=" + this.a1 + " a2=" + this.a2 + " a3=" + this.a3 + " a4=" + this.a4);
					}
				},
				onLeave: function(retval) {
					if (hookCounters.c5C060 <= 3 || (hookCounters.c5C060 % 500) === 0) {
						console.log("[sub_5C060 leave] cnt=" + hookCounters.c5C060 + " ret=" + retval);
					}
				}
			});
			console.log("[成功] Hook sub_5C060 at " + f_5C060);
		} else {
			console.log("[跳过] sub_5C060 未启用");
		}
	} catch (e) { console.log("[失败] Hook sub_5C060: " + e); }
}

function waitForModuleAndHook() {
	var names = ["libamapnsq.so", "libamaprsq.so"]; // 兼容命名差异
	var tries = 0;
	var maxTries = 300; // 约 60 秒
	var timer = setInterval(function(){
		tries++;
		for (var i = 0; i < names.length; i++) {
			var m = Process.findModuleByName(names[i]);
			if (m) {
				clearInterval(timer);
				console.log("[模块] 发现 " + names[i] + "，安装钩子");
				setTimeout(function(){ install_hooks(m); }, 800); // 延迟安装，避免早期抖动
				return;
			}
		}
		if (tries === 1) console.log("[等待] 正在等待目标模块加载...");
		if (tries >= maxTries) {
			clearInterval(timer);
			console.log("[超时] 未加载目标模块，请在地图中多次操作或重试脚本");
		}
	}, 2000);
}

setImmediate(function(){ waitForModuleAndHook(); console.log("[就绪] 请在地图中平移缩放触发解析"); });
