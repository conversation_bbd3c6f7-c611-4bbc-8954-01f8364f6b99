setTimeout(function() {
  console.log("[+] 开始监控高德地图JNI函数");

  // 方法1: 直接截获RegisterNatives调用来发现动态注册的函数
  var RegisterNatives = null;

  // 查找不同Android版本的RegisterNatives符号
  var symbols = [
    "_ZN3art3JNI15RegisterNativesEP7_JNIEnvP7_jclassPK15JNINativeMethodi", // Android 7.0+
    "_ZN3art3JNI15RegisterNativesEP7_JNIEnvP7_jclassPK15JNINativeMethodj"  // 较旧版本
  ];

  for (var i = 0; i < symbols.length; i++) {
    var addr = Module.findExportByName("libart.so", symbols[i]);
    if (addr) {
      RegisterNatives = addr;
      console.log("[+] 找到RegisterNatives函数: " + addr);
      break;
    }
  }

  if (RegisterNatives) {
    Interceptor.attach(RegisterNatives, {
      onEnter: function(args) {
        try {
          var env = args[0];
          var classHandle = args[1];
          var methodsPtr = args[2];
          var methodCount = args[3].toInt32();
          
          // 获取类名
          var getObjectClass = new NativeFunction(
            this.readPointer(env).add(Process.pointerSize * 5).readPointer(),
            'pointer', ['pointer', 'pointer']);
          var getClassName = new NativeFunction(
            this.readPointer(env).add(Process.pointerSize * 6).readPointer(),
            'pointer', ['pointer', 'pointer']);
          var className = getClassName(env, getObjectClass(env, classHandle)).readCString();
          
          console.log("[+] RegisterNatives调用: " + className + ", 方法数: " + methodCount);
          
          // 查找相关方法
          for (var i = 0; i < methodCount; i++) {
            var methodNamePtr = methodsPtr.add(i * 3 * Process.pointerSize).readPointer();
            var signaturePtr = methodsPtr.add(i * 3 * Process.pointerSize + Process.pointerSize).readPointer();
            var fnPtrPtr = methodsPtr.add(i * 3 * Process.pointerSize + 2 * Process.pointerSize);
            var fnPtr = fnPtrPtr.readPointer();
            
            var methodName = methodNamePtr.readCString();
            var signature = signaturePtr.readCString();
            
            // 找到我们的目标方法
            if (methodName.indexOf("nativeAddMapGestureMsg") !== -1) {

              console.log("[!] 找到目标方法: " + methodName);
              console.log("    签名: " + signature);
              console.log("    函数地址: " + fnPtr);
              
              // Hook目标函数
              Interceptor.attach(fnPtr, {
                onEnter: function(args) {
                  // args[0]是JNIEnv*, args[1]是jobject或jclass
                  // args[2]开始才是实际参数
                  console.log("[调用] nativeAddMapGestureMsg:");
                  console.log("  引擎ID: " + args[2]);
                  console.log("  native实例: " + args[3]);
                  console.log("  手势类型: " + args[4]);
                  console.log("  X值: " + args[5].toFloat32());
                  console.log("  Y值: " + args[6].toFloat32());
                  console.log("  额外参数: " + args[7].toFloat32());
                  console.log("  用户数据: " + args[8]);
                },
                onLeave: function(retval) {
                  console.log("[返回] nativeAddMapGestureMsg");
                }
              });
            }
          }
        } catch(e) {
          console.log("[-] 分析错误: " + e);
        }
      }
    });
  }
  
  // 方法2: 通过符号搜索并直接hook
  var amapModules = Process.enumerateModules().filter(function(m) { 
    return m.name.indexOf("amap") !== -1; 
  });
  
  console.log("[+] 找到相关模块数量: " + amapModules.length);
  
  amapModules.forEach(function(module) {
    console.log("[+] 扫描模块: " + module.name);
    
    // 尝试扫描导出函数
    module.enumerateExports().forEach(function(exp) {
      if (exp.name.indexOf("JNI_OnLoad") !== -1) {
        console.log("[+] 找到JNI_OnLoad: " + exp.name + " @ " + exp.address);
        Interceptor.attach(exp.address, {
          onLeave: function(retval) {
            console.log("[+] JNI_OnLoad已执行完成");
          }
        });
      }
    });
    
    // 查找字符串引用
    var pattern = utf8ToHex("nativeAddMapGestureMsg");
    var ranges = Process.enumerateRanges({
      protection: 'r--',
      coalesce: true
    });
    
    ranges.forEach(function(range) {
      try {
        Memory.scan(range.base, range.size, pattern, {
          onMatch: function(address, size) {
            console.log("[+] 找到字符串: nativeAddMapGestureMsg @ " + address);
            // 尝试查找引用
            var references = findReferencesToAddress(address);
            console.log("    引用数量: " + references.length);
          },
          onError: function(reason) {
            // console.log('扫描错误:', reason);
          },
          onComplete: function() { }
        });
      } catch(e) { }
    });
  });
  
  // 方法3: Hook Java侧方法来跟踪调用链
  Java.perform(function() {
    try {
      var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
      if (GLMapEngine && GLMapEngine.addGestureMessage) {
        GLMapEngine.addGestureMessage.implementation = function(engineId, gestureMessage) {
          console.log("[Java] 调用addGestureMessage: " + engineId);
          
          try {
            // 尝试打印更详细的堆栈跟踪
            console.log("[堆栈] ========================");
            var bt = Thread.backtrace(this.context, Backtracer.FUZZY);
            // 如果还是失败，可以尝试手动获取JNI函数地址
            if (bt.length === 0) {
              console.log("  尝试其他方法查找JNI调用...");
              
              // 在libamapnsq.so中查找特定的符号模式
              var glMapEngineClass = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
              var field = glMapEngineClass.class.getDeclaredField("mNativeMapengineInstance");
              field.setAccessible(true);
              var nativePtr = field.getLong(this);
              
              console.log("  Native实例指针: 0x" + nativePtr.toString(16));
              // 尝试查找该指针所在的内存区域
            }
            console.log("[堆栈] ========================");
          } catch(e) {
            console.log("[-] 无法获取堆栈: " + e.message);
          }
          
          // 获取手势详细信息
          console.log("       手势类型: " + gestureMessage.getType());
          if (gestureMessage.getType() == 0) {  // 移动手势
            var moveMsg = Java.cast(gestureMessage, Java.use("com.autonavi.ae.gmap.MoveGestureMapMessage"));
            console.log("       移动X: " + moveMsg.mTouchDeltaX.value);
            console.log("       移动Y: " + moveMsg.mTouchDeltaY.value);
          }
          
          // 调用原始方法
          var result = this.addGestureMessage(engineId, gestureMessage);
          console.log("[Java] addGestureMessage返回");
          return result;
        };
        console.log("[+] 成功Hook Java层的addGestureMessage方法");
      }
    } catch(e) {
      console.log("[-] Hook Java方法失败: " + e);
    }
  });
  
  // 辅助函数: 查找对地址的引用
  // 修复findReferencesToAddress函数
  function findReferencesToAddress(targetAddress) {
    var references = [];
    var alignment = Process.pointerSize;
    var ranges = Process.enumerateRanges({protection: 'r--', coalesce: true});
    
    // 将目标地址转换为适合搜索的十六进制表示
    var searchPattern = '';
    var targetAddressValue = ptr(targetAddress).toString();
    var bytes = Memory.readByteArray(targetAddress, 1);
    
    // 直接对内存进行比较搜索
    ranges.forEach(function(range) {
      try {
        var rangeData = Memory.readByteArray(range.base, range.size);
        if (!rangeData) return;
        
        // 直接输出找到的字符串
        console.log("[+] 在内存区域" + range.base + "中寻找引用...");
        
        // 检查是否有指向我们目标的指针
        for (var i = 0; i < range.size - Process.pointerSize; i += Process.pointerSize) {
          try {
            var currentPtr = Memory.readPointer(range.base.add(i));
            if (currentPtr.equals(targetAddress)) {
              references.push(range.base.add(i));
              console.log("[!] 发现引用: " + range.base.add(i));
              dumpMemoryAround(range.base.add(i), 48); // 显示引用附近的内存
            }
          } catch(e) {
            // 忽略读取错误
          }
        }
      } catch(e) {
        // 忽略处理错误
      }
    });
    
    return references;
  }
  
  // 修复dumpMemoryAround函数实现
  function dumpMemoryAround(address, range) {
    try {
      var start = ptr(address).sub(range/2);
      var data = Memory.readByteArray(start, range);
      console.log(hexdump(data, {offset: 0, length: range, header: true, ansi: false}));
    } catch(e) {
      console.log("[-] 内存读取错误: " + e.message);
    }
  }

  // 找到关键模块的JNI_OnLoad
  var libamapnsq = Process.findModuleByName("libamapnsq.so");
  if (libamapnsq) {
    var JNI_OnLoad = libamapnsq.findExportByName("JNI_OnLoad");
    if (JNI_OnLoad) {
      console.log("[+] 准备hook libamapnsq.so的JNI_OnLoad @ " + JNI_OnLoad);
      
      // 添加内存访问监视点，监控RegisterNatives调用
      traceJNIRegisterCalls(libamapnsq.base, libamapnsq.size);
      
      Interceptor.attach(JNI_OnLoad, {
        onEnter: function(args) {
          console.log("[+] 进入libamapnsq JNI_OnLoad, vm=" + args[0]);
        },
        onLeave: function(retval) {
          console.log("[+] 离开libamapnsq JNI_OnLoad, 返回=" + retval);
        }
      });
    }
  }
  
  // 尝试找到RegisterNatives函数的调用模式
  function traceJNIRegisterCalls(moduleBase, moduleSize) {
    // 只分析代码段
    var executableRanges = Process.enumerateRangesSync({
      protection: 'r-x',
      coalesce: true
    });
    
    var filteredRanges = [];
    for (var i = 0; i < executableRanges.length; i++) {
      var range = executableRanges[i];
      if (range.base.compare(moduleBase) >= 0 && 
          range.base.add(range.size).compare(moduleBase.add(moduleSize)) <= 0) {
        filteredRanges.push(range);
      }
    }
    
    console.log("[+] 找到" + filteredRanges.length + "个可执行内存区域");
    
    // 分析可能调用RegisterNatives的指令
    for (var j = 0; j < filteredRanges.length; j++) {
      try {
        var range = filteredRanges[j];
        // 此处可以通过内存读取分析，这里只做简单示范
        var moduleOffset = range.base.sub(moduleBase);
        console.log("  分析区域: " + moduleOffset + " (大小: " + range.size + ")");
      } catch(e) {}
    }
  }
  
}, 1000);

function utf8ToHex(str) {
  var result = "";
  for (var i = 0; i < str.length; i++) {
    var hex = str.charCodeAt(i).toString(16);
    // 确保两位十六进制表示
    if (hex.length === 1) {
      hex = "0" + hex;
    }
    result += hex + " ";
  }
  return result.trim();
}
