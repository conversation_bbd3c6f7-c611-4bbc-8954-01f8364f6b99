     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[深度AM-zlib分析] 开始重新分析AM-zlib解压过程...
[目标] 找到真正的解压函数，而不是标准zlib
[1] Hook mmap，寻找AM-zlib文件映射...
[\u2713] mmap Hook设置成功
[3] 搜索自定义解压函数...
[4] 重新Hook标准zlib函数...
[\u2713] zlib Hook设置成功
[深度分析] 脚本已启动...
[目标] 找到真正的AM-zlib解压算法
[提示] 请打开地图并移动以触发数据处理
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [zlib inflate] 输入大小: 299
  输入数据: 95 51 3b 4e c4 30 10 9d 59 e7 63 92 26 2b 51 80
[zlib inflate] 输出大小: 2132135660
[zlib inflate] 输入大小: 197
  输入数据: 75 4e 3d 0b c2 30 14 7c 69 5a 9b a5 e2 e0 a0 e0
[zlib inflate] 输出大小: 2324738760
[zlib inflate] 输入大小: 194
  输入数据: 63 66 e0 60 d8 c1 c8 c0 c0 c8 20 c3 b0 84 81 01
[zlib inflate] 输出大小: 2324738808
[zlib inflate] 输入大小: 200
  输入数据: 85 50 b1 0a c2 30 14 bc 98 54 53 68 41 d0 c1 c1
[zlib inflate] 输出大小: 2322194412
[zlib inflate] 输入大小: 221
  输入数据: 75 50 cb 6e c2 30 10 9c 25 38 c9 91 03 87 22 d1
[zlib inflate] 输出大小: 2136823780
[zlib inflate] 输入大小: 247
  输入数据: 6d 50 c1 4a c4 30 14 7c cf a4 db 22 2c ac 52 c5
[zlib inflate] 输出大小: 2136823800
[zlib inflate] 输入大小: 210
  输入数据: 5d 8f bd 0e 82 30 14 85 ef a5 20 8c f8 33 18 63
[zlib inflate] 输出大小: 2136986876
[zlib inflate] 输入大小: 208
  输入数据: 5d 8f 41 6e 83 30 14 44 e7 63 08 2c 49 cb a2 8a
[zlib inflate] 输出大小: 2136986876
[zlib inflate] 输入大小: 288
  输入数据: 85 91 bf 4a c4 40 10 c6 67 6e f3 ef 72 cd 09 16
[zlib inflate] 输出大小: 2217325032
[zlib inflate] 输入大小: 247
  输入数据: 6d 50 c1 4a c4 30 14 7c cf a4 db 22 2c ac 52 c5
[zlib inflate] 输出大小: 2136824824
[zlib inflate] 输入大小: 1227
  输入数据: 68 de ed 9a cb 4f 54 57 1c c7 3f 30 3c 86 87 c0
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1116
  输入数据: 68 68 68 68 68 68 68 68 68 68 38 1d 09 31 e8 d3
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1115
  输入数据: 68 68 68 68 68 68 68 68 68 38 1d 09 31 e8 d3 4d
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1114
  输入数据: 68 68 68 68 68 68 68 68 38 1d 09 31 e8 d3 4d 35
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1114
  输入数据: 68 68 68 68 68 68 68 68 38 1d 09 31 e8 d3 4d 35
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1113
  输入数据: 68 68 68 68 68 68 68 38 1d 09 31 e8 d3 4d 35 85
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1112
  输入数据: 68 68 68 68 68 68 38 1d 09 31 e8 d3 4d 35 85 14
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1112
  输入数据: 68 68 68 68 68 68 38 1d 09 31 e8 d3 4d 35 85 14
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1111
  输入数据: 68 68 68 68 68 38 1d 09 31 e8 d3 4d 35 85 14 b0
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1110
  输入数据: 68 68 68 68 38 1d 09 31 e8 d3 4d 35 85 14 b0 c2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1109
  输入数据: 68 68 68 38 1d 09 31 e8 d3 4d 35 85 14 b0 c2 0c
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1109
  输入数据: 68 68 68 38 1d 09 31 e8 d3 4d 35 85 14 b0 c2 0c
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1108
  输入数据: 68 68 38 1d 09 31 e8 d3 4d 35 85 14 b0 c2 0c d7
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1107
  输入数据: 68 38 1d 09 31 e8 d3 4d 35 85 14 b0 c2 0c d7 b9
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1107
  输入数据: 68 38 1d 09 31 e8 d3 4d 35 85 14 b0 c2 0c d7 b9
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1106
  输入数据: 38 1d 09 31 e8 d3 4d 35 85 14 b0 c2 0c d7 b9 c2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1105
  输入数据: 1d 09 31 e8 d3 4d 35 85 14 b0 c2 0c d7 b9 c2 aa
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1105
  输入数据: 1d 09 31 e8 d3 4d 35 85 14 b0 c2 0c d7 b9 c2 aa
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1103
  输入数据: 31 e8 d3 4d 35 85 14 b0 c2 0c d7 b9 c2 aa f3 49
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1086
  输入数据: e6 2c bb 84 1d e0 5b 02 f6 de c2 65 33 e5 74 be
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1074
  输入数据: 33 e5 74 be 22 57 29 c9 60 3f d7 79 e2 64 d2 a7
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1059
  输入数据: a7 d8 17 56 96 41 16 b7 ec bc 49 a2 ad 94 73 29
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1047
  输入数据: ad 94 73 29 33 2c 2f 0d 51 df 51 a4 1b 4d e6 48
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1034
  输入数据: 4d e6 48 02 8d 4e 25 bd 83 52 61 75 d2 a9 68 bd
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1026
  输入数据: 83 52 61 75 d2 a9 68 bd c3 99 a4 1b 44 6f ab b4
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1014
  输入数据: 44 6f ab b4 d3 2e 16 bb 44 1a 9c 48 7a bb 32 9e
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1001
  输入数据: bb 32 9e af 30 cf 3c 57 45 49 19 2f 38 8f 74 a3
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 986
  输入数据: a3 58 89 56 69 07 a0 4d 68 ed b2 4f 6b bb 48 7b
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 970
  输入数据: 28 17 56 37 73 00 cc d1 2d 4a cb d9 ee 2c d2 0d
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 955
  输入数据: 0d 42 e7 67 b4 05 af db 78 26 b4 3e e6 24 d2 39
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 940
  输入数据: 39 78 85 75 8d d9 e0 f5 2c d7 44 8d 17 8f 73 48
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 923
  输入数据: 23 49 e8 dc aa d4 b5 b2 16 bc 4e a2 de 29 a4 b3
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 907
  输入数据: a9 10 d6 1f 21 e1 51 40 d1 ba 82 6c 67 90 ae 17
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 892
  输入数据: 17 3a af 89 f1 fc ff b8 b6 59 eb cd 93 ce 52 74
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 878
  输入数据: 52 74 ee 61 3a cc 63 9a 1e 61 55 92 15 7f d2 75
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 863
  输入数据: 75 a4 08 9d 5b 0d 7d e4 b8 4e a6 2e de a4 b7 51
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 848
  输入数据: 51 25 ac 5e a6 0c bd a6 e8 15 56 15 db e2 4b fa
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 832
  输入数据: a8 05 9d 43 b5 4e d9 ac d6 9b 23 9d 49 b5 b0 fa
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 820
  输入数据: 49 b5 b0 fa 98 30 f5 9c a0 4f d1 3a 33 7e a4 6b
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 804
  输入数据: 49 15 96 6f 5d 5f 59 9b 4a 6d bc 48 67 70 58 58
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 791
  输入数据: 70 58 58 37 19 5f d7 7b 9c 9b c2 3a 42 46 7c 48
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 774
  输入数据: e0 b6 ac 73 b8 d6 35 f1 20 9d ce 11 61 dd c2 bf
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 764
  输入数据: ce 11 61 dd c2 bf 61 0b 3f fd 8a d6 e9 5b 4f ba
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 748
  输入数据: 86 b4 88 74 06 68 11 d7 69 d1 6b 1d 2d e9 34 45
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 734
  输入数据: 34 45 e7 db 8c 59 6a 35 c6 6d d3 c7 de 02 d2 ea
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 719
  输入数据: ea cb 6d b1 dc ae c5 f4 c1 63 4e da ad 2c 59 77
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 707
  输入数据: ad 2c 59 77 b8 6f b9 e5 7d ee 28 4b a6 7b eb 48
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 690
  输入数据: 8e 52 e7 50 ef 74 65 d1 8c 29 69 75 6b 18 60 24
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 679
  输入数据: 75 6b 18 60 24 a2 d6 23 0c 98 6e 4f 31 24 5d ad
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 664
  输入数据: ad 6c c2 be 88 db fb 4c 03 81 98 91 4e e1 a8 b0
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 651
  输入数据: e1 a8 b0 06 19 8e b8 87 61 06 4d 42 ae 98 91 56
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 635
  输入数据: 03 4b 5f 54 73 c2 67 1a dc c6 84 b4 1a 56 0e 31
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 623
  输入数据: 1a 56 0e 31 14 15 69 b5 5d 7d a4 5a 47 4a ba 42
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 608
  输入数据: 42 f9 58 6a 21 5a b4 28 5a 57 c6 92 74 b2 f2 59
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 595
  输入数据: b2 f2 59 3a cc dd a8 49 df 55 e6 42 1d c9 91 34
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 577
  输入数据: 38 9e 4b 84 75 41 24 65 42 51 c8 19 0e 11 e0 1f
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 548
  输入数据: 64 9a c4 0e 36 c2 65 13 bf 3c 4e 73 02 0f 1e ca
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 513
  输入数据: e0 3d 61 fd 60 78 66 95 cb 29 3e 66 67 d0 de 49
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 481
  输入数据: 44 93 88 ca ee d1 6c 90 69 fa 88 4f d8 1b 56 be
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 447
  输入数据: 90 d0 05 7e 64 26 24 54 fd 80 4f 79 d5 64 62 27
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 414
  输入数据: bf 91 26 11 24 8d f2 b3 32 d6 6b 39 4b d1 06 7d
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 378
  输入数据: af 72 08 74 31 98 fc 4a c4 cb e7 1c 30 58 b2 1e
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 340
  输入数据: d0 d9 cf 4f cf af 0e f0 19 5e 83 98 78 85 0e ce
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 298
  输入数据: a5 ca e1 c4 45 a6 80 37 38 43 ad 41 d2 65 8d df
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 255
  输入数据: 2e 8e e8 c7 b9 44 3e a7 39 2e 46 a3 cc 7e 9c e3
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 208
  输入数据: 2d 8e 78 2e 93 c7 bb 26 5b 73 33 f7 4c 7a 78 99
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 177
  输入数据: 9b 0d f3 41 63 34 2b 5f 24 46 28 e2 24 f9 1b f8
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 155
  输入数据: 15 8c 52 b4 4e 6d 80 5f e9 b5 30 81 06 f8 8b 12
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 130
  输入数据: 42 b1 49 cd 22 bf 70 c1 ca e4 79 8e 09 ba 58 24
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 104
  输入数据: 9f 42 83 43 e2 47 f8 f8 9e 11 6b 1b 82 58 5d 46
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 75
  输入数据: 4f 5e 14 61 10 3c a5 83 f3 0c 44 f9 7f d2 2a c3
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 43
  输入数据: 97 83 78 c8 64 82 7e 7a 58 60 f3 f0 50 c1 3e f2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 19
  输入数据: 43 43 43 43 43 43 43 43 43 43 43 23 be f8 0f 5b
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 18
  输入数据: 43 43 43 43 43 43 43 43 43 43 23 be f8 0f 5b 72
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 17
  输入数据: 43 43 43 43 43 43 43 43 43 23 be f8 0f 5b 72 14
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 17
  输入数据: 43 43 43 43 43 43 43 43 43 23 be f8 0f 5b 72 14
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 16
  输入数据: 43 43 43 43 43 43 43 43 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 15
  输入数据: 43 43 43 43 43 43 43 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 14
  输入数据: 43 43 43 43 43 43 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 14
  输入数据: 43 43 43 43 43 43 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 13
  输入数据: 43 43 43 43 43 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 12
  输入数据: 43 43 43 43 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 12
  输入数据: 43 43 43 43 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 11
  输入数据: 43 43 43 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 10
  输入数据: 43 43 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 10
  输入数据: 43 43 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 9
  输入数据: 43 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 8
  输入数据: 23 be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 7
  输入数据: be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 7
  输入数据: be f8 0f 5b 72 14 b2
[zlib inflate] 输出大小: 2136537940
[zlib inflate] 输入大小: 1346
  输入数据: 68 de ed 9a 5d 6c 54 45 18 86 1f 2b fd 03 fa 03
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1231
  输入数据: c1 83 07 0f 1e 3c 78 f0 e0 61 5e e0 09 cd ef 2b
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1230
  输入数据: 83 07 0f 1e 3c 78 f0 e0 61 5e e0 09 cd ef 2b a4
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1229
  输入数据: 07 0f 1e 3c 78 f0 e0 61 5e e0 09 cd ef 2b a4 84
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1229
  输入数据: 07 0f 1e 3c 78 f0 e0 61 5e e0 09 cd ef 2b a4 84
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1228
  输入数据: 0f 1e 3c 78 f0 e0 61 5e e0 09 cd ef 2b a4 84 42
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1227
  输入数据: 1e 3c 78 f0 e0 61 5e e0 09 cd ef 2b a4 84 42 f2
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1227
  输入数据: 1e 3c 78 f0 e0 61 5e e0 09 cd ef 2b a4 84 42 f2
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1225
  输入数据: 78 f0 e0 61 5e e0 09 cd ef 2b a4 84 42 f2 c8 24
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1224
  输入数据: f0 e0 61 5e e0 09 cd ef 2b a4 84 42 f2 c8 24 03
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1223
  输入数据: e0 61 5e e0 09 cd ef 2b a4 84 42 f2 c8 24 03 08
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1223
  输入数据: e0 61 5e e0 09 cd ef 2b a4 84 42 f2 c8 24 03 08
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1222
  输入数据: 61 5e e0 09 cd ef 2b a4 84 42 f2 c8 24 03 08 13
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1221
  输入数据: 5e e0 09 cd ef 2b a4 84 42 f2 c8 24 03 08 13 e2
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1221
  输入数据: 5e e0 09 cd ef 2b a4 84 42 f2 c8 24 03 08 13 e2
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1218
  输入数据: cd ef 2b a4 84 42 f2 c8 24 03 08 13 e2 2e 83 0c
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1197
  输入数据: 49 0d eb f0 91 13 e3 fb 20 bf 71 89 2b 4c cd 17
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1180
  输入数据: 0b d9 cc 26 16 d9 e8 f9 90 1f 69 e5 d1 5c 93 4e
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1164
  输入数据: e5 45 5e 65 a1 83 27 1e d1 c2 59 26 e6 8e f4 06
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1149
  输入数据: 06 76 b0 d4 c5 73 23 1c e7 5c 22 6b d1 2d 16 f0
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1136
  输入数据: 2d 16 f0 16 af 91 e9 ea d9 4c ea 59 4a 17 91 d9
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1118
  输入数据: 2c de 63 b5 e9 37 7d f4 13 e4 3e 41 20 87 5c 72
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1090
  输入数据: b0 2f 6a 59 4c d3 cd 65 3a b8 67 d2 7f 09 b5 d4
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1064
  输入数据: 05 7c 1c b5 f5 ba 38 16 77 f8 12 1a f0 47 6d cb
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1042
  输入数据: e4 00 4b 44 cb 00 4d 7c c7 83 b8 4f 3e e0 3c 3d
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 1015
  输入数据: 29 bc cf 2a d1 72 91 23 0e e6 ea 2e e7 28 a0 48
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 990
  输入数据: eb 6c 10 f1 71 be 75 98 e5 a6 f8 85 08 95 62 b9
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 968
  输入数据: 78 47 c4 5f 73 c6 d5 46 ee e1 1e b5 4a 5c 4e 27
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 948
  输入数据: 5d 96 29 d1 69 4e b9 3e e5 fb c9 a0 5c cc f6 cf
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 929
  输入数据: 7d 54 e3 53 a2 4e 8e 25 94 4d 8f d1 a9 44 3e aa
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 910
  输入数据: 85 06 25 1a a5 c9 6d 3e fb 0f 11 9a 18 55 e2 06
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 889
  输入数据: a7 58 89 4e 12 4a b8 3a 0c 71 52 89 8a a9 d7 4f
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 872
  输入数据: a3 f2 f9 0e 6d 5a aa f0 36 ee c4 18 41 0b e9 6c
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 857
  输入数据: 6c aa 94 e8 84 9e 62 9e 29 4e 28 51 95 48 3b 1a
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 839
  输入数据: 95 84 3f 46 40 9b 72 0a 30 a6 94 14 7e bd a4 7d
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 823
  输入数据: a2 ce 88 5f c2 e7 51 4d 35 79 71 fb 4d 88 b4 e2
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 803
  输入数据: 83 5a 86 76 c4 e9 bb 8c b7 67 32 5e 37 df 30 6c
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 778
  输入数据: 54 91 54 3a 2d fb 56 71 50 49 d2 95 1c 14 bb 21
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 754
  输入数据: af ac e8 b0 38 5d 8d 58 c4 1e d2 45 4b 3a 7b 2c
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 728
  输入数据: 23 cc 00 2b 6c 33 31 11 72 d8 66 f9 4c 30 c6 48
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 709
  输入数据: e7 ce ba b0 a9 71 d0 6a f6 c6 f4 e4 90 b6 56 75
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 695
  输入数据: 56 75 4b 1c b4 9a bd 51 23 e9 31 b1 29 ad 10 8e
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 685
  输入数据: 31 b1 29 ad 10 8e 91 b0 ad b7 b9 f9 48 09 92 7e
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 669
  输入数据: 28 94 86 15 7a 4d 5b 6f 58 3e b3 58 68 46 6d a4
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 652
  输入数据: 3f 60 91 a5 18 6e 31 a9 fd 22 b4 58 4a eb 22 a1
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 632
  输入数据: 62 5c 11 b6 85 16 3d 6f d2 6a f2 43 6e 5a 6a fb
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 607
  输入数据: 84 3e 25 aa b5 ec 7b 9c 66 65 65 8e d1 2c 8a a2
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 580
  输入数据: 95 14 fb 4c 1c 99 d5 c6 15 d6 f3 14 f0 17 97 4c
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 554
  输入数据: 17 d9 3c f3 b9 94 8a 38 af bf c7 69 9b ef ad 10
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 532
  输入数据: bf f0 f2 b7 6a 2b 4d b7 8a 9d d3 af 5b b9 a8 5a
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 517
  输入数据: 5a a5 92 e7 b5 50 de 28 fc 8f b3 fa e5 56 9b 28
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 503
  输入数据: 9b 28 94 76 b9 f2 a5 25 72 d9 29 4e e8 9f f4 93
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 486
  输入数据: 17 eb 34 93 dd 09 df 22 bc 29 dc ed 53 f6 b2 a1
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 467
  输入数据: 0f d6 2b 45 66 3e 11 7a 12 a0 bc 83 4d 62 3d 7f
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 445
  输入数据: c2 6d e1 e4 f9 98 8c 91 b6 e3 63 0b db 45 dc e4
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 423
  输入数据: 4c 16 65 4a bc c6 e5 6c bf 2c 8c 1f 38 c3 0f 4e
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 402
  输入数据: e3 27 57 9c 23 d3 8e 69 bf c2 1b 22 ee e3 0b 67
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 382
  输入数据: 11 ae 52 2f ee 01 7c 64 d3 65 db 5f 4e 61 17 5b
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 356
  输入数据: 10 97 a9 13 b4 cb 58 41 87 2d fb 26 8d bd 86 13
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 331
  输入数据: 44 c0 40 bb 10 3f 1d 71 8f ac 2c f6 b3 46 b4 8c
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 306
  输入数据: 08 50 2b 34 76 2e 6b e9 12 62 c1 88 42 0e 08 0b
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 281
  输入数据: c2 04 a8 22 4b 69 59 c8 b3 dc 88 39 6f e5 7c 64
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 255
  输入数据: 61 2e b0 52 98 38 69 3c c7 20 b7 4c fa ae 65 1f
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 229
  输入数据: 30 c9 05 96 51 22 ce 86 75 84 f9 dd d0 ef 25 76
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 203
  输入数据: a6 09 b0 80 a7 45 9b 9f 45 5c 9b 39 02 53 d8 c9
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 175
  输入数据: 6f ba 09 52 23 68 ad a4 84 5f 99 02 52 d9 cb 0b
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 150
  输入数据: e8 e3 26 75 42 03 2d a7 92 0e d2 d8 6f 30 1f c7
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 125
  输入数据: 15 7c 60 f0 f1 87 80 02 83 11 f1 b9 a5 2e 9f 75
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 108
  输入数据: 90 c7 87 2c b7 f8 fe 36 9f b9 3d e2 92 b1 3c fe
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 92
  输入数据: 4f 38 ed ac 8a e9 fd f7 72 38 8e df 3a 27 a4 61
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 76
  输入数据: 82 76 c3 65 fd 63 2d df 18 c3 e5 9b 73 d2 10 21
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 62
  输入数据: 10 21 40 9a b8 3e 06 f8 9e 66 4d b7 61 49 21 0d
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 46
  输入数据: 70 8d 01 56 cd 94 53 23 7c 65 5f 65 cf fe 46 94
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 30
  输入数据: 25 68 05 c5 c0 9f 5c 57 5c 40 0f 1e 3c 78 f0 e0
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 19
  输入数据: 1e 3c 78 f0 e0 c1 83 07 0f 1e 3c cc 03 fc 03 40
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 19
  输入数据: 1e 3c 78 f0 e0 c1 83 07 0f 1e 3c cc 03 fc 03 40
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 17
  输入数据: 78 f0 e0 c1 83 07 0f 1e 3c cc 03 fc 03 40 bb 31
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 16
  输入数据: f0 e0 c1 83 07 0f 1e 3c cc 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 15
  输入数据: e0 c1 83 07 0f 1e 3c cc 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 15
  输入数据: e0 c1 83 07 0f 1e 3c cc 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 14
  输入数据: c1 83 07 0f 1e 3c cc 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 13
  输入数据: 83 07 0f 1e 3c cc 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 13
  输入数据: 83 07 0f 1e 3c cc 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 12
  输入数据: 07 0f 1e 3c cc 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 11
  输入数据: 0f 1e 3c cc 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 10
  输入数据: 1e 3c cc 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 10
  输入数据: 1e 3c cc 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 8
  输入数据: cc 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 7
  输入数据: 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 7
  输入数据: 03 fc 03 40 bb 31 ad
[zlib inflate] 输出大小: 2136537492
[zlib inflate] 输入大小: 645
  输入数据: 78 da ed 98 49 82 e3 20 0c 45 c5 24 c4 cc fd 4f
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 608
  输入数据: 73 73 73 73 b3 00 9a a4 43 91 f6 0f ce d5 98 0c
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 608
  输入数据: 73 73 73 73 b3 00 9a a4 43 91 f6 0f ce d5 98 0c
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 607
  输入数据: 73 73 73 b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 607
  输入数据: 73 73 73 b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 607
  输入数据: 73 73 73 b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 606
  输入数据: 73 73 b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 606
  输入数据: 73 73 b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 606
  输入数据: 73 73 b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 605
  输入数据: 73 b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f 41
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 605
  输入数据: 73 b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f 41
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 605
  输入数据: 73 b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f 41
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 604
  输入数据: b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f 41 a5
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 604
  输入数据: b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f 41 a5
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 604
  输入数据: b3 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f 41 a5
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 603
  输入数据: 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f 41 a5 60
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 603
  输入数据: 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f 41 a5 60
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 603
  输入数据: 00 9a a4 43 91 f6 0f ce d5 98 0c c2 3f 41 a5 60
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 589
  输入数据: a5 60 3b 82 57 f0 4b 48 38 3b a1 78 fc 8d 70 b4
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 581
  输入数据: 38 3b a1 78 fc 8d 70 b4 ef 90 1f 8b 93 b6 0c 9f
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 574
  输入数据: b4 ef 90 1f 8b 93 b6 0c 9f 8a b7 54 70 38 03 bb
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 568
  输入数据: b6 0c 9f 8a b7 54 70 38 03 bb 60 fc e1 ae 7a 63
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 563
  输入数据: 54 70 38 03 bb 60 fc e1 ae 7a 63 08 01 90 54 4e
[zlib inflate] 输出大小: 2217336858
[zlib inflate] 输入大小: 557
Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=000002770DD3A460)

Thread 0x000030b0 (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x000096dc (most recent call first):
<no Python frame>
