(function() {
  console.log("[ANS文件分析器] 启动");
  
  // 全局变量
  var m1_ans_fd = -1;
  var m1_ans_address = null;
  var m1_ans_size = 0;
  var ansFiles = {};
  var backtracesCollected = 0;
  var maxBacktraces = 5;
  var mapHeaderBytes = 32;
  var memoryScanned = false;
  var functionHooked = {};
  
  // 工具函数：检查数组是否全为0
  function isAllZeros(array, start, length) {
    var end = start + length;
    if (end > array.length) end = array.length;
    
    for (var i = start; i < end; i++) {
      if (array[i] !== 0) return false;
    }
    return true;
  }
  
  // 工具函数：打印内存区域的十六进制表示
  function hexdump(addr, size) {
    try {
      var bytes = Memory.readByteArray(addr, size);
      var array = new Uint8Array(bytes);
      var result = "";
      
      for (var i = 0; i < array.length; i++) {
        var hex = array[i].toString(16);
        if (hex.length === 1) hex = "0" + hex;
        result += hex + " ";
        
        if ((i + 1) % 16 === 0) result += "\n";
      }
      
      return result;
    } catch(e) {
      return "[错误] " + e;
    }
  }
  
  // 监控库加载
  Interceptor.attach(Module.findExportByName(null, "dlopen"), {
    onEnter: function(args) {
      try {
        this.path = args[0].readUtf8String();
      } catch(e) {}
    },
    onLeave: function(retval) {
      if (this.path && this.path.indexOf("libamaploc.so") !== -1) {
        console.log("[库加载] " + this.path);
        
        // 延迟监控关键内存操作函数
        setTimeout(function() {
          // 监控可能处理ANS数据的内存函数
          var memFuncs = ["memcpy", "memset", "memmove"];
          memFuncs.forEach(function(funcName) {
            if (functionHooked[funcName]) return;
            
            var funcPtr = Module.findExportByName(null, funcName);
            if (funcPtr) {
              functionHooked[funcName] = true;
              Interceptor.attach(funcPtr, {
                onEnter: function(args) {
                  // 只关注m1.ans数据区域的内存操作
                  if (m1_ans_address && args[0]) {
                    var targetAddr = ptr(args[0]);
                    var dataLen = parseInt(args[2].toString());
                    
                    // 过滤掉大小为0的操作和过小的操作
                    if (dataLen <= 8) return; // 忽略小于或等于8字节的操作
                    
                    // 检查目标地址是否在m1.ans映射区域内
                    if (targetAddr.compare(m1_ans_address) >= 0 && 
                        targetAddr.add(dataLen).compare(m1_ans_address.add(m1_ans_size)) <= 0) {
                      
                      var offset = targetAddr.sub(m1_ans_address);
                      
                      // 限制日志频率 - 使用静态计数器
                      if (!this.operationCounter) this.operationCounter = 0;
                      this.operationCounter++;
                      
                      // 每10次操作只输出一次
                      if (this.operationCounter % 10 === 0) {
                        console.log("[内存操作] " + funcName + " 处理m1.ans数据, 偏移: 0x" + 
                                  offset.toString(16) + ", 大小: " + dataLen + " 字节");
                      }
                      
                      // 较大操作才记录详情
                      if (dataLen > 1024) {
                        console.log("[大块内存] " + funcName + " 处理大块m1.ans数据, 偏移: 0x" + 
                                  offset.toString(16) + ", 大小: " + dataLen + " 字节");
                        
                        // 记录调用栈
                        if (backtracesCollected < maxBacktraces) {
                          console.log("[调用栈] 内存操作位置:");
                          Thread.backtrace(this.context, Backtracer.ACCURATE)
                            .map(DebugSymbol.fromAddress)
                            .forEach(function(frame) {
                              if (frame.toString().indexOf("libamaploc.so") !== -1) {
                                console.log("    " + frame);
                              }
                            });
                          backtracesCollected++;
                        }
                      }
                    }
                  }
                }
              });
              console.log("[函数监控] 已Hook " + funcName);
            }
          });
        }, 2000);
      }
    }
  });
  
  // 监控文件打开
  Interceptor.attach(Module.findExportByName("libc.so", "open"), {
    onEnter: function(args) {
      try {
        var path = args[0].readUtf8String();
        if (path && path.indexOf(".ans") !== -1) {
          this.path = path;
          this.isAnsFile = true;
          
          if (path.indexOf("m1.ans") !== -1) {
            this.isM1Ans = true;
            console.log("[M1.ANS打开] " + path);
            
            // 收集堆栈
            if (backtracesCollected < maxBacktraces) {
              console.log("[调用栈] m1.ans打开位置:");
              var stack = Thread.backtrace(this.context, Backtracer.ACCURATE)
                .map(DebugSymbol.fromAddress);
              
              for (var i = 0; i < stack.length; i++) {
                var frame = stack[i].toString();
                if (frame.indexOf("libamaploc.so") !== -1) {
                  console.log("    " + frame);
                }
              }
              backtracesCollected++;
            }
          }
        }
      } catch(e) {}
    },
    onLeave: function(retval) {
      if (!this.isAnsFile) return;
      
      var fd = retval.toInt32();
      if (fd > 0) {
        ansFiles[fd] = {
          path: this.path,
          filename: this.path.split("/").pop()
        };
        
        if (this.isM1Ans) {
          m1_ans_fd = fd;
          console.log("[M1.ANS] 文件描述符: " + fd);
        } else {
          console.log("[ANS文件] " + this.path.split("/").pop() + " (fd: " + fd + ")");
        }
      }
    }
  });
  
  // 监控内存映射
  Interceptor.attach(Module.findExportByName("libc.so", "mmap"), {
    onEnter: function(args) {
      this.addr = args[0];
      this.length = parseInt(args[1].toString());
      this.prot = args[2].toInt32();
      this.flags = args[3].toInt32();
      this.fd = args[4].toInt32();
      this.offset = args[5].toInt32();
      
      if (this.fd === m1_ans_fd) {
        this.isM1Ans = true;
      } else if (ansFiles[this.fd]) {
        this.isAnsFile = true;
        this.filename = ansFiles[this.fd].filename;
      }
    },
    onLeave: function(retval) {
      if (retval.isNull()) return;
      
      var mapAddr = retval;
      
      if (this.isM1Ans) {
        m1_ans_address = mapAddr;
        m1_ans_size = this.length;
        console.log("[M1.ANS映射] 地址: " + mapAddr + 
                  ", 大小: " + this.length + " 字节, 偏移: " + this.offset);
        
        // 记录文件头部数据
        try {
          var headerBytes = Memory.readByteArray(mapAddr, Math.min(mapHeaderBytes, this.length));
          var header = new Uint8Array(headerBytes);
          var headerHex = "";
          
          for (var i = 0; i < header.length; i++) {
            var hex = header[i].toString(16);
            if (hex.length === 1) hex = "0" + hex;
            headerHex += hex + " ";
          }
          
          console.log("[M1.ANS头部] " + headerHex);
          
          // 延迟一段时间后扫描内存，寻找非零数据块
          // 这可以帮助发现m1.ans是否被程序在映射后填充数据
          setTimeout(function() {
            if (m1_ans_address && !memoryScanned) {
              memoryScanned = true;
              console.log("[扫描] 开始扫描m1.ans内存区域，寻找非零数据...");
              
              try {
                // 分段扫描，避免一次读取过多内存
                var blockSize = 4096;
                var blocksToScan = Math.min(20, Math.ceil(m1_ans_size / blockSize));
                var nonZeroBlocksFound = 0;
                
                for (var i = 0; i < blocksToScan; i++) {
                  var offset = i * blockSize;
                  var scanSize = Math.min(blockSize, m1_ans_size - offset);
                  
                  var dataBytes = Memory.readByteArray(m1_ans_address.add(offset), scanSize);
                  var dataArray = new Uint8Array(dataBytes);
                  
                  // 每16字节检查一次，提高效率
                  for (var j = 0; j < dataArray.length; j += 16) {
                    if (!isAllZeros(dataArray, j, 16)) {
                      console.log("[数据块] 发现非零数据，偏移: 0x" + (offset + j).toString(16));
                      console.log(hexdump(m1_ans_address.add(offset + j), Math.min(64, scanSize - j)));
                      nonZeroBlocksFound++;
                      
                      // 限制输出数量
                      if (nonZeroBlocksFound >= 3) {
                        console.log("[数据块] 已发现多个非零数据块，停止详细输出...");
                        i = blocksToScan;
                        break;
                      }
                    }
                  }
                }
                
                if (nonZeroBlocksFound === 0) {
                  console.log("[数据块] 扫描结束，未发现非零数据块");
                } else {
                  console.log("[数据块] 扫描结束，共发现 " + nonZeroBlocksFound + " 个非零数据块");
                }
              } catch(e) {
                console.log("[扫描错误] " + e);
              }
            }
          }, 3000); // 等待3秒，让程序有时间填充数据
        } catch(e) {
          console.log("[错误] 读取m1.ans头部失败: " + e);
        }
        
        // 收集堆栈
        if (backtracesCollected < maxBacktraces) {
          console.log("[调用栈] m1.ans内存映射位置:");
          var stack = Thread.backtrace(this.context, Backtracer.ACCURATE)
            .map(DebugSymbol.fromAddress);
          
          for (var i = 0; i < stack.length; i++) {
            var frame = stack[i].toString();
            if (frame.indexOf("libamaploc.so") !== -1) {
              console.log("    " + frame);
            }
          }
          backtracesCollected++;
        }
      } else if (this.isAnsFile) {
        console.log("[ANS映射] " + this.filename + 
                  " -> " + mapAddr + ", 大小: " + this.length);
      }
    }
  });
  
  // 监控读取操作
  Interceptor.attach(Module.findExportByName("libc.so", "read"), {
    onEnter: function(args) {
      this.fd = args[0].toInt32();
      this.buffer = args[1];
      this.size = parseInt(args[2].toString());
      
      if (this.fd === m1_ans_fd) {
        this.isM1Ans = true;
        console.log("[M1.ANS读取] 请求读取 " + this.size + " 字节到 " + this.buffer);
      }
    },
    onLeave: function(retval) {
      if (!this.isM1Ans) return;
      
      var bytesRead = retval.toInt32();
      if (bytesRead > 0) {
        console.log("[M1.ANS读取] 成功读取 " + bytesRead + " 字节");
        
        // 读取前16字节数据
        try {
          if (bytesRead >= 16) {
            var data = Memory.readByteArray(this.buffer, 16);
            var dataArray = new Uint8Array(data);
            var dataHex = "";
            
            for (var i = 0; i < dataArray.length; i++) {
              var hex = dataArray[i].toString(16);
              if (hex.length === 1) hex = "0" + hex;
              dataHex += hex + " ";
            }
            
            console.log("[M1.ANS读取数据] " + dataHex + "...");
          }
        } catch(e) {}
      }
    }
  });
  
  // 监控内存解除映射 (munmap)
  Interceptor.attach(Module.findExportByName("libc.so", "munmap"), {
    onEnter: function(args) {
      this.addr = args[0];
      this.length = parseInt(args[1].toString());
      
      if (m1_ans_address && this.addr.equals(m1_ans_address)) {
        console.log("[M1.ANS解除映射] 地址: " + this.addr + 
                  ", 大小: " + this.length + " 字节");
        m1_ans_address = null;
      }
    }
  });
  
  // 监控文件关闭
  Interceptor.attach(Module.findExportByName("libc.so", "close"), {
    onEnter: function(args) {
      this.fd = args[0].toInt32();
      
      if (this.fd === m1_ans_fd) {
        console.log("[M1.ANS关闭] 文件描述符: " + this.fd);
        m1_ans_fd = -1;
      } else if (ansFiles[this.fd]) {
        console.log("[ANS关闭] " + ansFiles[this.fd].filename);
        delete ansFiles[this.fd];
      }
    }
  });
  
  // 搜索特定的导航相关函数，在加载完成后进行
  setTimeout(function() {
    var libamaploc = Process.findModuleByName("libamaploc.so");
    if (libamaploc) {
      console.log("[库信息] 找到libamaploc.so，基址: " + libamaploc.base);
      
      // 只搜索可能的符号，不进行大量导出函数枚举
      var possibleSymbols = ["NaviDataParser", "LoadNaviData", "ANSParser", "ParseANS"];
      possibleSymbols.forEach(function(symName) {
        var sym = Module.findExportByName("libamaploc.so", symName);
        if (sym) {
          console.log("[符号] 找到 " + symName + " @ " + sym);
        }
      });
    }
  }, 5000);
  
  console.log("[ANS文件分析器] 初始化完成");
})();
