/*
 [+] 高德地图ANS文件解析分析脚本 v3 - 专注解析方法
[+] ANS文件解析分析脚本设置完成
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [+] 启动ANS文件解析分析模式
[ANS文件] 打开m1.ans: /storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/data/navi/compile_v3/chn/a78/m1.ans
[ANS文件] 文件描述符: 117
[详细调用栈] 打开ANS文件:
  0: 0x7f7045d954 libamapnsq.so!weftlattimqugxvvpvso+0x35b8
  1: 0x7f9036e608
[类] 找到 AjxBLFactoryController
[AjxBLFactoryController 方法列表]:
  public static boolean com.autonavi.jni.ajx3.bl.AjxBLFactoryController.bindJsVmapEngineId(int,int)
  public static void com.autonavi.jni.ajx3.bl.AjxBLFactoryController.init4WarmStart(int,int,int)
  private static native boolean com.autonavi.jni.ajx3.bl.AjxBLFactoryController.nativeBindJsVmapEngineId(int,int)
  private static native void com.autonavi.jni.ajx3.bl.AjxBLFactoryController.nativeInit4WarmStart(int,int,int)
  private static native boolean com.autonavi.jni.ajx3.bl.AjxBLFactoryController.nativeUnbindJsVmapEngineId(int,int)
  private static native void com.autonavi.jni.ajx3.bl.AjxBLFactoryController.nativeUninit4WarmDestory(int,int)
  public static boolean com.autonavi.jni.ajx3.bl.AjxBLFactoryController.unbindJsVmapEngineId(int,int)
  public static void com.autonavi.jni.ajx3.bl.AjxBLFactoryController.uninit4WarmDestory(int,int)
[Hook成功] init4WarmStart
[Hook成功] nativeInit4WarmStart
[Hook成功] uninit4WarmDestory
[Hook成功] nativeUninit4WarmDestory
[类] 找到: com.autonavi.ae.MapCloudBundleLoaderUtil
[MapCloudBundleLoaderUtil 方法列表]:
  public void com.autonavi.ae.MapCloudBundleLoaderUtil.cancel(int)
  public int com.autonavi.ae.MapCloudBundleLoaderUtil.download(java.lang.String,java.lang.String,int,com.autonavi.ae.gmap.glinterface.IMapCloudBundleCallback)
[类] 找到: com.autonavi.minimap.offline.nativesupport.AmapCompat
[AmapCompat 方法列表]:
  onDatabaseBackup
  onDatabaseCheckAndUpgrade
[+] Java层分析完成
[关键调用] init4WarmStart()
  参数数量: 3
[关键调用] nativeInit4WarmStart(0, 158210000)
Error: nativeInit4WarmStart(): argument types do not match any of:
        .overload('int', 'int', 'int')
    at pe (frida/node_modules/frida-java-bridge/lib/class-factory.js:553)
    at frida/node_modules/frida-java-bridge/lib/class-factory.js:955
    at /ans_frida_test5.js:329
    at we (frida/node_modules/frida-java-bridge/lib/class-factory.js:602)
    at frida/node_modules/frida-java-bridge/lib/class-factory.js:585
[关键返回] init4WarmStart() 返回: undefined, 耗时: 74ms
[关联] init4WarmStart被调用后，已加载ANS文件:
  - m1.ans 已加载
[ANS读取] m1.ans 读取 8192 字节
[关联] ANS文件读取发生在init方法调用后
[ANS读取] m1.ans 读取 139 字节
[关联] ANS文件读取发生在init方法调用后
 *
 */
(function() {
  console.log("[+] 高德地图ANS文件解析分析脚本 v3 - 专注解析方法");
  
  // 全局变量
  var ansFileFDs = {};  // 存储文件描述符到路径的映射
  var ansFileInfo = {}; // 存储ANS文件信息
  var m1AnsFound = false; // 是否找到m1.ans文件
  var m3AnsFound = false; // 是否找到m3.ans文件
  var methodCalled = {}; // 记录方法是否被调用
  var ansMemoryRanges = []; // 存储ANS文件在内存中的位置
  var libamapnsqBase = null; // libamapnsq.so基地址
  
  // 1. 基础反调试 - 只处理strstr
  var strstr_ptr = Module.findExportByName("libc.so", "strstr");
  if (strstr_ptr) {
    Interceptor.attach(strstr_ptr, {
      onEnter: function(args) {
        try {
          var str = args[1].readUtf8String();
          if (str && (str.indexOf("frida") !== -1 || str.indexOf("gum") !== -1)) {
            this.shouldFake = true;
          }
        } catch (e) {}
      },
      onLeave: function(retval) {
        if (this.shouldFake) {
          retval.replace(0);
        }
      }
    });
  }
  
  // 2. 拦截可能导致崩溃的系统调用
  var exit_ptr = Module.findExportByName("libc.so", "exit");
  if (exit_ptr) {
    Interceptor.replace(exit_ptr, new NativeCallback(function() {
      return 0;
    }, 'void', ['int']));
  }
  
  // 3. 查找libamapnsq.so库并获取其基地址
  Process.enumerateModules({
    onMatch: function(module) {
      if (module.name === "libamapnsq.so") {
        console.log("[+] 找到libamapnsq.so库，基地址: " + module.base);
        libamapnsqBase = module.base;
        
        // 列出所有导出函数
        console.log("[+] 列出libamapnsq.so导出函数:");
        module.enumerateExports({
          onMatch: function(exp) {
            console.log("  [导出] " + exp.name + " @ " + exp.address);
          },
          onComplete: function() {}
        });
        
        // 列出所有符号
        console.log("[+] 列出libamapnsq.so符号:");
        module.enumerateSymbols({
          onMatch: function(sym) {
            if (sym.name.indexOf("ans") !== -1 || 
                sym.name.indexOf("parse") !== -1 || 
                sym.name.indexOf("load") !== -1 ||
                sym.name.indexOf("init") !== -1 ||
                sym.name.indexOf("map") !== -1 ||
                sym.name.indexOf("file") !== -1) {
              console.log("  [符号] " + sym.name + " @ " + sym.address);
              
              // Hook这些可能的ANS相关函数
              try {
                Interceptor.attach(sym.address, {
                  onEnter: function(args) {
                    console.log("[调用] " + sym.name);
                    
                    // 记录调用栈
                    var stack = Thread.backtrace(this.context, Backtracer.ACCURATE)
                      .map(DebugSymbol.fromAddress);
                    console.log("[调用栈] " + sym.name + ":");
                    stack.forEach(function(frame, i) {
                      if (frame && frame.name) {
                        console.log("  " + i + ": " + frame.name);
                      }
                    });
                    
                    // 保存参数
                    this.args = [];
                    for (var i = 0; i < 6; i++) { // 保存前6个参数
                      try {
                        this.args.push(args[i]);
                      } catch(e) {
                        break;
                      }
                    }
                  },
                  onLeave: function(retval) {
                    console.log("[返回] " + sym.name + " = " + retval);
                    
                    // 如果返回值是指针，尝试查看内存内容
                    if (retval && !retval.isNull()) {
                      try {
                        console.log("[内存] 返回值指向内容:");
                        console.log(hexdump(retval, {length: 32}));
                      } catch(e) {}
                    }
                  }
                });
                console.log("[Hook] 成功Hook " + sym.name);
              } catch(e) {
                console.log("[错误] Hook " + sym.name + " 失败: " + e);
              }
            }
          },
          onComplete: function() {}
        });
      }
    },
    onComplete: function() {}
  });
  
  // 4. 延迟启动分析模式 (5秒后启动，确保应用完全初始化)
  setTimeout(function() {
    console.log("[+] 启动ANS文件解析分析模式");
    
    // 4.1 监控文件打开操作
    var open_ptr = Module.findExportByName("libc.so", "open");
    if (open_ptr) {
      Interceptor.attach(open_ptr, {
        onEnter: function(args) {
          try {
            var path = args[0].readUtf8String();
            if (path) {
              if (path.indexOf("m1.ans") !== -1) {
                this.isAnsFile = true;
                this.path = path;
                this.isM1 = true;
                console.log("[ANS文件] 打开m1.ans: " + path);
                m1AnsFound = true;
              } else if (path.indexOf("m3.ans") !== -1) {
                this.isAnsFile = true;
                this.path = path;
                this.isM3 = true;
                console.log("[ANS文件] 打开m3.ans: " + path);
                m3AnsFound = true;
              }
            }
          } catch (e) {}
        },
        onLeave: function(retval) {
          if (this.isAnsFile && retval.toInt32() > 0) {
            var fd = retval.toInt32();
            console.log("[ANS文件] 文件描述符: " + fd);
            ansFileFDs[fd] = {
              path: this.path,
              isM1: this.isM1,
              isM3: this.isM3
            };
            
            // 获取详细调用栈
            try {
              var stack = Thread.backtrace(this.context, Backtracer.ACCURATE)
                .map(DebugSymbol.fromAddress);
              console.log("[详细调用栈] 打开ANS文件:");
              stack.forEach(function(symbol, i) {
                console.log("  " + i + ": " + (symbol ? symbol.toString() : "???"));
              });
            } catch(e) {
              console.log("[错误] 获取调用栈失败: " + e);
            }
          }
        }
      });
    }
    
    // 4.2 监控mmap调用 - 详细实现
    var mmap_ptr = Module.findExportByName("libc.so", "mmap");
    if (mmap_ptr) {
      Interceptor.attach(mmap_ptr, {
        onEnter: function(args) {
          this.fd = args[4].toInt32();
          if (ansFileFDs[this.fd]) {
            this.isAnsFile = true;
            this.fileInfo = ansFileFDs[this.fd];
            this.size = args[1].toInt32();
            this.addr = args[0]; // 可能的预指定地址
            this.prot = args[2].toInt32(); // 内存保护标志
            this.flags = args[3].toInt32(); // mmap标志
          }
        },
        onLeave: function(retval) {
          if (!this.isAnsFile) return;
          
          var fileName = this.fileInfo.path.split("/").pop();
          var fileType = this.fileInfo.isM1 ? "m1.ans" : "m3.ans";
          console.log("[ANS映射] " + fileType + " 映射到内存");
          console.log("  地址: " + retval);
          console.log("  大小: " + this.size + " 字节");
          console.log("  保护: " + this.prot + " (读=" + !!(this.prot & 1) + 
                     ", 写=" + !!(this.prot & 2) + ", 执行=" + !!(this.prot & 4) + ")");
          console.log("  标志: " + this.flags);
          
          // 存储映射信息
          var mapInfo = {
            path: this.fileInfo.path,
            addr: retval,
            size: this.size,
            fd: this.fd,
            isM1: this.fileInfo.isM1,
            isM3: this.fileInfo.isM3
          };
          ansFileInfo[fileName] = mapInfo;
          ansMemoryRanges.push(mapInfo);
          
          // 分析文件头 (读取32字节以获取更多信息)
          try {
            console.log("[文件头] " + fileName + ":");
            console.log(hexdump(retval, {length: Math.min(64, this.size)}));
            
            // 分析ANS文件头结构
            var header = new Uint8Array(Memory.readByteArray(retval, Math.min(32, this.size)));
            console.log("[ANS头部解析]");
            console.log("  Magic: " + header[0].toString(16).padStart(2,'0') + header[1].toString(16).padStart(2,'0') + " " + 
                                     header[2].toString(16).padStart(2,'0') + header[3].toString(16).padStart(2,'0'));
            console.log("  Version: " + header[4].toString(16).padStart(2,'0') + header[5].toString(16).padStart(2,'0') + " " + 
                                       header[6].toString(16).padStart(2,'0') + header[7].toString(16).padStart(2,'0'));
            console.log("  Flags: " + header[8].toString(16).padStart(2,'0') + header[9].toString(16).padStart(2,'0') + " " + 
                                     header[10].toString(16).padStart(2,'0') + header[11].toString(16).padStart(2,'0'));
            
            // 获取详细调用栈
            try {
              var stack = Thread.backtrace(this.context, Backtracer.ACCURATE)
                .map(DebugSymbol.fromAddress);
              console.log("[详细调用栈] mmap ANS文件:");
              stack.forEach(function(symbol, i) {
                console.log("  " + i + ": " + (symbol ? symbol.toString() : "???"));
              });
            } catch(e) {
              console.log("[错误] 获取调用栈失败: " + e);
            }
            
            // 设置内存访问监控
            try {
              var baseAddr = retval;
              Memory.protect(baseAddr, 4096, 'rwx');
              
              // 监控前4KB的内存访问
              var memoryAccessCallback = {
                onAccess: function(details) {
                  console.log("[内存访问] ANS文件内存被访问:");
                  console.log("  操作: " + details.operation + ", 地址: " + details.from + 
                             ", 目标: " + details.address);
                  
                  // 获取访问时的调用栈
                  try {
                    var accessStack = Thread.backtrace(details.context, Backtracer.ACCURATE)
                      .map(DebugSymbol.fromAddress);
                    console.log("[内存访问调用栈]:");
                    accessStack.forEach(function(symbol, i) {
                      if (symbol && symbol.name) {
                        console.log("  " + i + ": " + symbol.name);
                      }
                    });
                  } catch(e) {}
                }
              };
              
              // 监控读取操作
              Memory.protect(baseAddr, 4096, 'r--');
              var readWatchpoint = Stalker.addCallProbe(baseAddr, memoryAccessCallback.onAccess);
              console.log("[监控] 设置ANS文件内存读取监控");
            } catch(e) {
              console.log("[错误] 设置内存访问监控失败: " + e);
            }
          } catch(e) {
            console.log("[错误] 分析ANS文件头失败: " + e);
          }
        }
      });
    }
    
    // 4.3 Java层分析
    Java.perform(function() {
      try {
        // 基本反调试
        var Debug = Java.use("android.os.Debug");
        Debug.isDebuggerConnected.implementation = function() {
          return false;
        };
        
        // 重点监控AjxBLFactoryController类
        try {
          var AjxBLFactoryController = Java.use("com.autonavi.jni.ajx3.bl.AjxBLFactoryController");
          console.log("[类] 找到 AjxBLFactoryController");
          
          // 列出所有方法
          var methods = AjxBLFactoryController.class.getDeclaredMethods();
          console.log("[AjxBLFactoryController 方法列表]:");
          for (var i = 0; i < methods.length; i++) {
            console.log("  " + methods[i].toString());
          }
          
          // 监控init4WarmStart方法 - 这是ANS文件初始化的关键方法
          if (AjxBLFactoryController.init4WarmStart) {
            AjxBLFactoryController.init4WarmStart.implementation = function() {
              console.log("[关键调用] init4WarmStart()");
              console.log("  参数数量: " + arguments.length);
              // 记录调用时间
              var startTime = new Date().getTime();
              var result = this.init4WarmStart.apply(this, arguments);
              var endTime = new Date().getTime();
              console.log("[关键返回] init4WarmStart() 返回: " + result + ", 耗时: " + (endTime - startTime) + "ms");
              methodCalled["init4WarmStart"] = true;
              
              // 检查ANS文件状态
              if (m1AnsFound || m3AnsFound) {
                console.log("[关联] init4WarmStart被调用后，已加载ANS文件:");
                if (m1AnsFound) console.log("  - m1.ans 已加载");
                if (m3AnsFound) console.log("  - m3.ans 已加载");
              }
              
              return result;
            };
            console.log("[Hook成功] init4WarmStart");
          }
          
          // 监控nativeInit4WarmStart方法 - 这是Native层初始化ANS的入口
          if (AjxBLFactoryController.nativeInit4WarmStart) {
            AjxBLFactoryController.nativeInit4WarmStart.implementation = function(arg1, arg2) {
              console.log("[关键调用] nativeInit4WarmStart(" + arg1 + ", " + arg2 + ")");
              var result = this.nativeInit4WarmStart(arg1, arg2);
              console.log("[关键返回] nativeInit4WarmStart() 返回: " + result);
              methodCalled["nativeInit4WarmStart"] = true;
              return result;
            };
            console.log("[Hook成功] nativeInit4WarmStart");
          }
          
          // 其他可能相关的方法
          var methodsToHook = ["uninit4WarmDestory", "nativeUninit4WarmDestory"];
          for (var i = 0; i < methodsToHook.length; i++) {
            var methodName = methodsToHook[i];
            if (AjxBLFactoryController[methodName]) {
              (function(name) {
                var original = AjxBLFactoryController[name];
                AjxBLFactoryController[name].implementation = function() {
                  console.log("[方法调用] " + name + "()");
                  var result = original.apply(this, arguments);
                  console.log("[方法返回] " + name + "() 返回: " + result);
                  methodCalled[name] = true;
                  return result;
                };
                console.log("[Hook成功] " + name);
              })(methodName);
            }
          }
        } catch(e) {
          console.log("[错误] AjxBLFactoryController类监控失败: " + e);
        }
        
        // 监控MapCloudBundleLoaderUtil类 - 可能负责加载ANS资源
        try {
          var MapCloudBundleLoaderUtil = Java.use("com.autonavi.ae.MapCloudBundleLoaderUtil");
          if (MapCloudBundleLoaderUtil) {
            console.log("[类] 找到: com.autonavi.ae.MapCloudBundleLoaderUtil");
            
            // 列出所有方法
            var methods = MapCloudBundleLoaderUtil.class.getDeclaredMethods();
            console.log("[MapCloudBundleLoaderUtil 方法列表]:");
            for (var i = 0; i < methods.length; i++) {
              console.log("  " + methods[i].toString());
            }
            
            // 监控loadFromFile方法
            if (MapCloudBundleLoaderUtil.loadFromFile) {
              MapCloudBundleLoaderUtil.loadFromFile.implementation = function(path) {
                console.log("[资源加载] loadFromFile(" + path + ")");
                var result = this.loadFromFile(path);
                console.log("[资源加载] loadFromFile 返回: " + result);
                return result;
              };
              console.log("[Hook成功] MapCloudBundleLoaderUtil.loadFromFile");
            }
            
            // 尝试Hook其他可能的加载方法
            var loaderMethods = ["loadFromAssets", "loadFromStream", "loadFromMemory"];
            for (var i = 0; i < loaderMethods.length; i++) {
              var methodName = loaderMethods[i];
              if (MapCloudBundleLoaderUtil[methodName]) {
                (function(name) {
                  var original = MapCloudBundleLoaderUtil[name];
                  MapCloudBundleLoaderUtil[name].implementation = function() {
                    console.log("[资源加载] " + name + "(" + JSON.stringify(arguments) + ")");
                    var result = original.apply(this, arguments);
                    console.log("[资源加载] " + name + " 返回: " + result);
                    return result;
                  };
                  console.log("[Hook成功] MapCloudBundleLoaderUtil." + name);
                })(methodName);
              }
            }
          }
        } catch(e) {
          console.log("[错误] MapCloudBundleLoaderUtil类监控失败: " + e);
        }
        
        // 尝试找出AmapCompat中与ANS相关的方法
        try {
          var AmapCompat = Java.use("com.autonavi.minimap.offline.nativesupport.AmapCompat");
          if (AmapCompat) {
            console.log("[类] 找到: com.autonavi.minimap.offline.nativesupport.AmapCompat");
            
            // 列出所有方法
            var methods = AmapCompat.class.getDeclaredMethods();
            console.log("[AmapCompat 方法列表]:");
            for (var i = 0; i < methods.length; i++) {
              var methodName = methods[i].getName();
              console.log("  " + methodName);
              
              // 尝试Hook可能与ANS相关的方法
              if (methodName.indexOf("ans") !== -1 || 
                  methodName.indexOf("parse") !== -1 || 
                  methodName.indexOf("load") !== -1 ||
                  methodName.indexOf("init") !== -1 ||
                  methodName.indexOf("map") !== -1 ||
                  methodName.indexOf("file") !== -1) {
                try {
                  (function(name) {
                    var original = AmapCompat[name];
                    if (original) {
                      AmapCompat[name].implementation = function() {
                        console.log("[调用] AmapCompat." + name + "(" + JSON.stringify(arguments) + ")");
                        var result = original.apply(this, arguments);
                        console.log("[返回] AmapCompat." + name + " = " + result);
                        return result;
                      };
                      console.log("[Hook成功] AmapCompat." + name);
                    }
                  })(methodName);
                } catch(e) {
                  console.log("[错误] Hook AmapCompat." + methodName + " 失败: " + e);
                }
              }
            }
          }
        } catch(e) {
          console.log("[错误] AmapCompat类监控失败: " + e);
        }
        
        console.log("[+] Java层分析完成");
      } catch(e) {
        console.log("[错误] Java层分析失败: " + e);
      }
    });
    
    // 4.4 监控ANS文件读取操作
    var read_ptr = Module.findExportByName("libc.so", "read");
    if (read_ptr) {
      Interceptor.attach(read_ptr, {
        onEnter: function(args) {
          this.fd = args[0].toInt32();
          if (ansFileFDs[this.fd]) {
            this.isAnsFile = true;
            this.fileInfo = ansFileFDs[this.fd];
            this.size = args[2].toInt32();
            this.buffer = args[1];
          }
        },
        onLeave: function(retval) {
          if (!this.isAnsFile) return;
          
          var bytesRead = retval.toInt32();
          if (bytesRead <= 0) return;
          
          var fileName = this.fileInfo.path.split("/").pop();
          var fileType = this.fileInfo.isM1 ? "m1.ans" : "m3.ans";
          console.log("[ANS读取] " + fileType + " 读取 " + bytesRead + " 字节");
          
          // 只分析小块数据，避免性能问题
          if (bytesRead <= 32) {
            try {
              console.log(hexdump(this.buffer, {length: bytesRead}));
              
              // 检查是否是ANS文件头 (00 00 fe fe)
              var data = new Uint8Array(Memory.readByteArray(this.buffer, Math.min(bytesRead, 16)));
              if (data[0] === 0 && data[1] === 0 && data[2] === 0xfe && data[3] === 0xfe) {
                console.log("[ANS头部] 检测到ANS文件头特征!");
                
                // 获取详细调用栈
                try {
                  var stack = Thread.backtrace(this.context, Backtracer.ACCURATE)
                    .map(DebugSymbol.fromAddress);
                  console.log("[详细调用栈] 读取ANS文件头:");
                  stack.forEach(function(symbol, i) {
                    console.log("  " + i + ": " + (symbol ? symbol.toString() : "???"));
                  });
                } catch(e) {}
              }
            } catch(e) {}
          }
          
          // 检查是否在关键方法调用后
          if (methodCalled["init4WarmStart"] || methodCalled["nativeInit4WarmStart"]) {
            console.log("[关联] ANS文件读取发生在init方法调用后");
          }
        }
      });
    }
    
    // 4.5 监控内存搜索 - 查找ANS文件特征
    setTimeout(function() {
      console.log("[+] 开始在内存中搜索ANS文件特征");
      
      // ANS文件头特征 (00 00 fe fe)
      var pattern = [0x00, 0x00, 0xfe, 0xfe];
      
      // 搜索所有可读内存区域
      Process.enumerateRanges('r--', {
        onMatch: function(range) {
          // 跳过太小的内存区域
          if (range.size < 16) return;
          
          // 只搜索可能的数据区域
          if (range.protection.indexOf('x') === -1) { // 非可执行区域
            try {
              Memory.scan(range.base, range.size, pattern.map(function(b) { return b.toString(16).padStart(2, '0'); }).join(' '), {
                onMatch: function(address, size) {
                  console.log("[内存特征] 在地址 " + address + " 找到ANS文件头特征");
                  
                  // 查看找到的数据
                  try {
                    console.log(hexdump(address, {length: 64}));
                    
                    // 尝试获取调用栈
                    var stack = Thread.backtrace(this.context, Backtracer.ACCURATE)
                      .map(DebugSymbol.fromAddress);
                    console.log("[内存特征调用栈]:");
                    stack.forEach(function(symbol, i) {
                      console.log("  " + i + ": " + (symbol ? symbol.toString() : "???"));
                    });
                  } catch(e) {}
                },
                onError: function(reason) {
                  // 忽略错误
                },
                onComplete: function() {
                  // 搜索完成
                }
              });
            } catch(e) {
              // 忽略错误
            }
          }
        },
        onComplete: function() {
          console.log("[+] ANS文件特征内存搜索完成");
        }
      });
    }, 20000); // 延迟20秒执行，等待应用加载更多数据
    
  }, 5000); // 延迟5秒，确保应用完全启动
  
  console.log("[+] ANS文件解析分析脚本设置完成");
})(); 