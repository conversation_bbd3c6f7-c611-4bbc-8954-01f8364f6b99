/*
frida14
仅在Android 8.1下测试成功，其他版本可能需要重新修改适配
原作者: Simp1er
修改: 添加安全的参数长度检查
*/
var STD_STRING_SIZE = 3 * Process.pointerSize;

function StdString() {
    this.handle = Memory.alloc(STD_STRING_SIZE);
}

StdString.prototype.dispose = function() {
    var dataAndIsTiny = this._getData();
    var data = dataAndIsTiny[0];
    var isTiny = dataAndIsTiny[1];
    if (!isTiny) {
        Java.api.$delete(data);
    }
};

StdString.prototype.disposeToString = function() {
    var result = this.toString();
    this.dispose();
    return result;
};

StdString.prototype.toString = function() {
    var dataAndIsTiny = this._getData();
    var data = dataAndIsTiny[0];
    return data.readUtf8String();
};

StdString.prototype._getData = function() {
    var str = this.handle;
    var isTiny = (str.readU8() & 1) === 0;
    var data = isTiny ? str.add(1) : str.add(2 * Process.pointerSize).readPointer();
    return [data, isTiny];
};

// 安全获取参数数量的函数
function safeGetArgsLength(args) {
    var count = 0;
    try {
        // 方法1: 尝试使用Object.keys
        var keys = Object.keys(args);
        return keys.length;
    } catch (e1) {
        try {
            // 方法2: 手动计数
            for (var i = 0; i < 20; i++) {
                if (args[i] !== undefined && args[i] !== null) {
                    count++;
                } else {
                    break;
                }
            }
            return count;
        } catch (e2) {
            console.log("获取参数长度失败:", e2.message);
            return 0;
        }
    }
}

function prettyMethod(method_id, withSignature) {
    var result = new StdString();
    Java.api['art::ArtMethod::PrettyMethod'](result, method_id, withSignature ? 1 : 0);
    return result.disposeToString();
}

function readStdString(str) {
    if ((str.readU8() & 1) === 1) { // size LSB (=1) indicates if it's a long string
        return str.add(2 * Process.pointerSize).readPointer().readUtf8String();
    }
    return str.add(1).readUtf8String();
}

function attach(addr) {
    Interceptor.attach(addr, {
        onEnter: function (args) {
            this.arg0 = args[0]; // this
            
            // 安全获取参数长度
            var argLength = safeGetArgsLength(args);
            console.log("数据长度为：" + argLength);
            
            // 安全打印每个参数
            for (var i = 0; i < argLength && i < 10; i++) {
                try {
                    console.log("参数 " + i + ":", args[i]);
                } catch (e) {
                    console.log("访问参数 " + i + " 失败:", e.message);
                }
            }
        },
        onLeave: function (retval) {
            var modulemap = new ModuleMap();
            modulemap.update();
            var module = modulemap.find(retval);
            
            if (module != null) {
                console.log('<' + module.name + '> method_name =>',
                    prettyMethod(this.arg0, 1),
                    ',offset=>', ptr(retval).sub(module.base), ',module_name=>', module.name);
            } else {
                console.log('<anonymous> method_name =>', "unknown", ', addr =>', ptr(retval));
            }
        }
    });
}

function hook_RegisterNative() {
    var libart = Process.findModuleByName('libart.so');
    var symbols = libart.enumerateSymbols();
    for (var i = 0; i < symbols.length; i++) {
        if (symbols[i].name.indexOf('RegisterNative') > -1 && symbols[i].name.indexOf('ArtMethod') > -1 && symbols[i].name.indexOf('RuntimeCallbacks') < 0) {
            //art::RuntimeCallbacks::RegisterNativeMethod(art::ArtMethod*, void const*, void**)
            attach(symbols[i].address);
        }
    }
}

function main() {
    hook_RegisterNative();
}

setImmediate(main);
