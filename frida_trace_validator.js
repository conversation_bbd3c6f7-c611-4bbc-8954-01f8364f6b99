/*
 * 高德地图执行流程验证器 - 基于 full_execution_flow_analysis.md
 * 使用Frida动态跟踪验证文档中描述的函数调用流程
 */

(function() {
    'use strict';
    
    console.log("[🔍流程验证器] 启动高德地图执行流程验证...");
    console.log("验证目标: full_execution_flow_analysis.md 中描述的执行流程");
    
    var verificationStats = {
        // libamapr.so 函数验证
        nativeAddMapGestureMsg: false,
        getMapEngineInstance: false,
        processGestureMessage: false,
        triggerRenderUpdate: false,
        updateMapView: false,
        finalizeProcessing: false,
        
        // libamapnsq.so 函数验证
        sub_C654: false,
        sub_5C394: false,
        sub_10F88: false,
        girf_sqlite3_bind_blob: false,
        
        // 系统函数验证
        read: false,
        uncompress: false,
        
        // 统计信息
        totalCalls: 0,
        verifiedFunctions: 0
    };
    
    // === 1. 验证 libamapr.so 中的关键函数 ===
    function verifyLibamaprFunctions() {
        console.log("[📚libamapr.so] 开始验证Native手势处理函数...");
        
        try {
            var libamapr = Module.findBaseAddress("libamapr.so");
            if (!libamapr) {
                console.log("[❌] libamapr.so 未找到");
                return;
            }
            
            console.log("[✅] libamapr.so 基址: " + libamapr);
            
            // 验证文档中提到的关键函数地址
            var functions = [
                {name: "nativeAddMapGestureMsg", offset: 0x6ee70c, desc: "JNI入口点"},
                {name: "getMapEngineInstance", offset: 0x6FB98C, desc: "地图引擎实例获取"},
                {name: "processGestureMessage", offset: 0x6FB530, desc: "手势消息处理"},
                {name: "triggerRenderUpdate", offset: 0x6FBC78, desc: "渲染触发"},
                {name: "updateMapView", offset: 0x6FB9E0, desc: "视图更新"},
                {name: "finalizeProcessing", offset: 0x6FB550, desc: "清理收尾"}
            ];
            
            functions.forEach(function(func) {
                try {
                    var addr = libamapr.add(func.offset);
                    Interceptor.attach(addr, {
                        onEnter: function(args) {
                            console.log("[🎯" + func.name + "] 调用 @" + addr + " - " + func.desc);
                            verificationStats[func.name] = true;
                            verificationStats.totalCalls++;
                        }
                    });
                    console.log("[🔧Hook] " + func.name + " @" + addr);
                } catch (e) {
                    console.log("[❌Hook错误] " + func.name + ": " + e);
                }
            });
            
        } catch (e) {
            console.log("[❌libamapr错误] " + e);
        }
    }
    
    // === 2. 验证 libamapnsq.so 中的数据处理函数 ===
    function verifyLibamapnsqFunctions() {
        console.log("[📚libamapnsq.so] 开始验证数据处理函数...");
        
        try {
            var libamapnsq = Module.findBaseAddress("libamapnsq.so");
            if (!libamapnsq) {
                console.log("[❌] libamapnsq.so 未找到");
                return;
            }
            
            console.log("[✅] libamapnsq.so 基址: " + libamapnsq);
            
            // 验证数据处理函数
            var dataFunctions = [
                {name: "sub_C654", offset: 0xC654, desc: "解压协调函数"},
                {name: "sub_5C394", offset: 0x5C394, desc: "解析调度器"},
                {name: "sub_10F88", offset: 0x10F88, desc: "数据解析器"},
                {name: "girf_sqlite3_bind_blob", offset: 0x15000, desc: "SQLite数据绑定"}
            ];
            
            dataFunctions.forEach(function(func) {
                try {
                    var addr = libamapnsq.add(func.offset);
                    Interceptor.attach(addr, {
                        onEnter: function(args) {
                            console.log("[🎯" + func.name + "] 调用 @" + addr + " - " + func.desc);
                            console.log("  参数: args[0]=" + args[0] + ", args[1]=" + args[1] + ", args[2]=" + args[2]);
                            verificationStats[func.name] = true;
                            verificationStats.totalCalls++;
                        },
                        onLeave: function(retval) {
                            console.log("[🎯" + func.name + "] 返回值: " + retval);
                        }
                    });
                    console.log("[🔧Hook] " + func.name + " @" + addr);
                } catch (e) {
                    console.log("[❌Hook错误] " + func.name + ": " + e);
                }
            });
            
        } catch (e) {
            console.log("[❌libamapnsq错误] " + e);
        }
    }
    
    // === 3. 验证系统库函数 ===
    function verifySystemFunctions() {
        console.log("[📚系统库] 开始验证系统函数...");
        
        // 验证 libc.so:read
        try {
            var readPtr = Module.findExportByName("libc.so", "read");
            if (readPtr) {
                Interceptor.attach(readPtr, {
                    onEnter: function(args) {
                        var fd = args[0].toInt32();
                        var size = args[2].toInt32();
                        if (size > 1000) { // 只记录大的读取操作
                            console.log("[🎯read] 文件读取: fd=" + fd + ", size=" + size);
                            verificationStats.read = true;
                            verificationStats.totalCalls++;
                        }
                    }
                });
                console.log("[🔧Hook] libc.so:read");
            }
        } catch (e) {
            console.log("[❌read Hook错误] " + e);
        }
        
        // 验证 libz.so:uncompress
        try {
            var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
            if (uncompressPtr) {
                Interceptor.attach(uncompressPtr, {
                    onEnter: function(args) {
                        console.log("[🎯uncompress] zlib解压开始");
                        verificationStats.uncompress = true;
                        verificationStats.totalCalls++;
                    },
                    onLeave: function(retval) {
                        console.log("[🎯uncompress] zlib解压结果: " + retval);
                    }
                });
                console.log("[🔧Hook] libz.so:uncompress");
            }
        } catch (e) {
            console.log("[❌uncompress Hook错误] " + e);
        }
    }
    
    // === 4. 生成验证报告 ===
    function generateVerificationReport() {
        console.log("\n🎯=== 执行流程验证报告 ===");
        console.log("📊 验证基于: full_execution_flow_analysis.md v4.0");
        console.log("🔢 总函数调用: " + verificationStats.totalCalls);
        
        console.log("\n📚 libamapr.so 函数验证:");
        console.log("  nativeAddMapGestureMsg: " + (verificationStats.nativeAddMapGestureMsg ? "✅" : "❌"));
        console.log("  getMapEngineInstance: " + (verificationStats.getMapEngineInstance ? "✅" : "❌"));
        console.log("  processGestureMessage: " + (verificationStats.processGestureMessage ? "✅" : "❌"));
        console.log("  triggerRenderUpdate: " + (verificationStats.triggerRenderUpdate ? "✅" : "❌"));
        console.log("  updateMapView: " + (verificationStats.updateMapView ? "✅" : "❌"));
        console.log("  finalizeProcessing: " + (verificationStats.finalizeProcessing ? "✅" : "❌"));
        
        console.log("\n📚 libamapnsq.so 函数验证:");
        console.log("  sub_C654 (解压协调): " + (verificationStats.sub_C654 ? "✅" : "❌"));
        console.log("  sub_5C394 (解析调度): " + (verificationStats.sub_5C394 ? "✅" : "❌"));
        console.log("  sub_10F88 (数据解析): " + (verificationStats.sub_10F88 ? "✅" : "❌"));
        console.log("  girf_sqlite3_bind_blob: " + (verificationStats.girf_sqlite3_bind_blob ? "✅" : "❌"));
        
        console.log("\n📚 系统函数验证:");
        console.log("  libc.so:read: " + (verificationStats.read ? "✅" : "❌"));
        console.log("  libz.so:uncompress: " + (verificationStats.uncompress ? "✅" : "❌"));
        
        // 计算验证覆盖率
        var totalFunctions = 12;
        var verifiedCount = 0;
        Object.keys(verificationStats).forEach(function(key) {
            if (key !== 'totalCalls' && key !== 'verifiedFunctions' && verificationStats[key]) {
                verifiedCount++;
            }
        });
        
        var coverage = (verifiedCount / totalFunctions * 100).toFixed(1);
        console.log("\n📈 验证覆盖率: " + verifiedCount + "/" + totalFunctions + " (" + coverage + "%)");
        
        if (coverage >= 70) {
            console.log("🎉 文档验证状态: 高度一致");
        } else if (coverage >= 50) {
            console.log("⚠️ 文档验证状态: 部分一致");
        } else {
            console.log("❌ 文档验证状态: 需要更新");
        }
        
        console.log("🎯========================\n");
    }
    
    // === 主函数 ===
    function main() {
        console.log("[🚀主程序] 初始化执行流程验证器...");
        
        setTimeout(function() {
            verifyLibamaprFunctions();
            verifyLibamapnsqFunctions();
            verifySystemFunctions();
            
            // 定期生成验证报告
            setInterval(generateVerificationReport, 15000);
            
            console.log("[🔍流程验证器] 验证器已启动!");
            console.log("💡 现在使用地图应用，观察函数调用验证...");
            
        }, 3000);
    }
    
    // === 启动 ===
    try {
        Java.perform(function() {
            console.log("[☕Java] 环境就绪");
            main();
        });
    } catch (e) {
        console.log("[❌Java错误] " + e);
        main();
    }
    
})(); 