[离线追踪] 脚本启动中... (Frida 12.9.7 / ES5)
[libc] Hooked open @ 0x7f7a7b9a50
[libc] Hooked open64 @ 0x7f7a7b9a50
[open] fd=11 path=/dev/ashmem
[open] fd=11 path=/dev/ashmem
[libc] Hooked openat @ 0x7f7a7b9adc
[libc] Hooked openat64 @ 0x7f7a7b9adc
[open] fd=17 path=/dev/ashmem
[open] fd=17 path=/dev/ashmem
[libc] Hooked read @ 0x7f7a7ffef4
[libc] Hooked pread64 @ 0x7f7a7ffe7c
[libc] Hooked close @ 0x7f7a7b5f58
[libc] Hooked fopen @ 0x7f7a7ecfd8
[libc] Hooked fread @ 0x7f7a809edc
[libc] Hooked fclose @ 0x7f7a7ebf78
[libc] Hooked mmap @ 0x7f7a7ffd5c
[libc] Hooked munmap @ 0x7f7a7ffe04
[zlib] Hooked uncompress @ 0x7f795c067c
[zlib] Hooked inflateInit2_ @ 0x7f795b96cc
[zlib] Hooked inflate @ 0x7f795b9858
[zlib] Hooked inflateEnd @ 0x7f795bbae8
[libandroid] Hooked AAssetManager_open @ 0x7f6a7d3b50
[libandroid] Hooked AAsset_read @ 0x7f6a7d3dd0
[open] fd=20 path=/system/framework/QPerformance.jar
[open] fd=20 path=/system/framework/QPerformance.jar
[read] fd=20 path=/system/framework/QPerformance.jar bytes=4
[read] fd=20 path=/system/framework/QPerformance.jar bytes=205
[mmap] ret=0x7f7749a000 len=183 fd=20 off=0 path=/system/framework/QPerformance.jar
[close] fd=20 path=/system/framework/QPerformance.jar
[libandroid] Hooked AAsset_close @ 0x7f6a7d3e3c
[munmap] addr=0x7f7749a000 len=183
[sqlite] Hooked sqlite3_open @ 0x7f79f8d7bc
[sqlite] Hooked sqlite3_open_v2 @ 0x7f79f8d7c8
[open] fd=20 path=/system/lib64/libqti_performance.so
[open] fd=20 path=/system/lib64/libqti_performance.so
[close] fd=20 path=/system/lib64/libqti_performance.so
[open] fd=20 path=/dev/ashmem
[open] fd=20 path=/dev/ashmem
[mmap] ret=0x7f76247000 len=131072 fd=20 off=0 path=/dev/ashmem
[close] fd=20 path=/dev/ashmem
[pread64] fd=6 off=6444200 path=fd:6 bytes=30
[pread64] fd=6 off=6444230 path=fd:6 bytes=19
[mmap] ret=0x7f76490000 len=20378 fd=6 off=6443008 path=fd:6
[munmap] addr=0x7f76490000 len=20378
[inflateEnd] z=0x7f77417978
[inflateEnd] ret=0xfffffffe
[pread64] fd=12 off=1166308 path=fd:12 bytes=30
[pread64] fd=12 off=1166338 path=fd:12 bytes=19
[mmap] ret=0x7f76493000 len=5256 fd=12 off=1163264 path=fd:12
[munmap] addr=0x7f76493000 len=5256
[pread64] fd=13 off=194996 path=fd:13 bytes=30
[pread64] fd=13 off=195026 path=fd:13 bytes=19
[mmap] ret=0x7f7749a000 len=3593 fd=13 off=192512 path=fd:13
[munmap] addr=0x7f7749a000 len=3593
[pread64] fd=14 off=737788 path=fd:14 bytes=30
[pread64] fd=14 off=737818 path=fd:14 bytes=19
[mmap] ret=0x7f7749a000 len=2103 fd=14 off=737280 path=fd:14
[munmap] addr=0x7f7749a000 len=2103
[open] fd=20 path=/data/app/com.autonavi.minimap-1/base.apk
[open] fd=20 path=/data/app/com.autonavi.minimap-1/base.apk
[read] fd=20 path=/data/app/com.autonavi.minimap-1/base.apk bytes=65557
[mmap] ret=0x7f75935000 len=557657 fd=20 off=171896832 path=/data/app/com.autonavi.minimap-1/base.apk
[mmap] ret=0x7f76207000 len=262144 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f76207000 len=262144
[mmap] ret=0x7f70e7c000 len=520192 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f70e7c000 len=16384
[munmap] addr=0x7f70ec0000 len=241664
[pread64] fd=20 off=12201298 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=20 off=12201328 path=/data/app/com.autonavi.minimap-1/base.apk bytes=19
[mmap] ret=0x7f76360000 len=36153 fd=20 off=12197888 path=/data/app/com.autonavi.minimap-1/base.apk
[munmap] addr=0x7f76360000 len=36153
[inflateEnd] z=0x7f77417dd8
[inflateEnd] ret=0xfffffffe
[pread64] fd=20 off=17536813 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=20 off=17536843 path=/data/app/com.autonavi.minimap-1/base.apk bytes=14
[mmap] ret=0x7f70ab9000 len=2908604 fd=20 off=17534976 path=/data/app/com.autonavi.minimap-1/base.apk
[open] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[mmap] ret=0x7f76493000 len=8192 fd=21 off=0 path=/dev/ashmem
[close] fd=21 path=/dev/ashmem
[mmap] ret=0x7f709b4000 len=1069056 fd=-1 off=0 path=fd:-1
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=4
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=65557
[mmap] ret=0x7f61ea3000 len=557657 fd=21 off=171896832 path=/data/app/com.autonavi.minimap-1/base.apk
[mmap] ret=0x7f70e40000 len=262144 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f7648e000 len=20480 fd=-1 off=0 path=fd:-1
[pread64] fd=21 off=3276671 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=21 off=3276701 path=/data/app/com.autonavi.minimap-1/base.apk bytes=11
[close] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[pread64] fd=20 off=12705213 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[munmap] addr=0x7f61ea3000 len=557657
[pread64] fd=20 off=12705243 path=/data/app/com.autonavi.minimap-1/base.apk bytes=43
[mmap] ret=0x7f7749a000 len=3889 fd=20 off=12701696 path=/data/app/com.autonavi.minimap-1/base.apk
[inflateInit2_]
[inflateInit2_] ret=0x0

==================== zlib::inflate ENTER ====================
[inflate] z=0x7f70ab7a30 flush=0
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=4
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=65557
[mmap] ret=0x7f57007000 len=557657 fd=21 off=171896832 path=/data/app/com.autonavi.minimap-1/base.apk
[inflate] LEAVE ret=0x1
[inflateEnd] z=0x7f70ab7a30
[inflateEnd] ret=0x0
[munmap] addr=0x7f7749a000 len=3889
[pread64] fd=21 off=115261907 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=21 off=115261937 path=/data/app/com.autonavi.minimap-1/base.apk bytes=12
[close] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[munmap] addr=0x7f57007000 len=557657
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=4
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=65557
[mmap] ret=0x7f57007000 len=557657 fd=21 off=171896832 path=/data/app/com.autonavi.minimap-1/base.apk
[pread64] fd=20 off=14124304 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=20 off=14124334 path=/data/app/com.autonavi.minimap-1/base.apk bytes=41
[mmap] ret=0x7f7635f000 len=39524 fd=20 off=14123008 path=/data/app/com.autonavi.minimap-1/base.apk
[mmap] ret=0x7f70e00000 len=262144 fd=-1 off=0 path=fd:-1
[pread64] fd=21 off=6860609 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=21 off=6860639 path=/data/app/com.autonavi.minimap-1/base.apk bytes=12
[close] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[munmap] addr=0x7f57007000 len=557657
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=4
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=65557
[mmap] ret=0x7f57007000 len=557657 fd=21 off=171896832 path=/data/app/com.autonavi.minimap-1/base.apk
[pread64] fd=21 off=119332866 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=21 off=119332896 path=/data/app/com.autonavi.minimap-1/base.apk bytes=12
[close] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[munmap] addr=0x7f57007000 len=557657
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=4
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=65557
[mmap] ret=0x7f57007000 len=557657 fd=21 off=171896832 path=/data/app/com.autonavi.minimap-1/base.apk
[pread64] fd=21 off=122775228 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=21 off=122775258 path=/data/app/com.autonavi.minimap-1/base.apk bytes=12
[close] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[munmap] addr=0x7f57007000 len=557657
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=4
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=65557
[mmap] ret=0x7f57007000 len=557657 fd=21 off=171896832 path=/data/app/com.autonavi.minimap-1/base.apk
[pread64] fd=21 off=0 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=21 off=30 path=/data/app/com.autonavi.minimap-1/base.apk bytes=12
[close] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[munmap] addr=0x7f57007000 len=557657
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[open] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=4
[read] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk bytes=65557
[mmap] ret=0x7f57007000 len=557657 fd=21 off=171896832 path=/data/app/com.autonavi.minimap-1/base.apk
[pread64] fd=21 off=10587015 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=21 off=10587045 path=/data/app/com.autonavi.minimap-1/base.apk bytes=12
[close] fd=21 path=/data/app/com.autonavi.minimap-1/base.apk
[munmap] addr=0x7f57007000 len=557657
[mmap] ret=0x7f57050000 len=262144 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f57050000 len=262144
[mmap] ret=0x7f57011000 len=520192 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f57011000 len=192512
[munmap] addr=0x7f57080000 len=65536
[mmap] ret=0x7f57000000 len=262144 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f7635f000 len=39524
[pread64] fd=20 off=13173568 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=20 off=13173598 path=/data/app/com.autonavi.minimap-1/base.apk bytes=41
[mmap] ret=0x7f76365000 len=15124 fd=20 off=13172736 path=/data/app/com.autonavi.minimap-1/base.apk
[mmap] ret=0x7f56fc0000 len=262144 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f56f00000 len=786432 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f56d40000 len=1835008 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f56cc0000 len=524288 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f56c80000 len=262144 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f76365000 len=15124
[munmap] addr=0x7f76493000 len=8192
[munmap] addr=0x7f7648e000 len=20480
[mmap] ret=0x7f70a79000 len=262144 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f70a79000 len=262144
[mmap] ret=0x7f70a3a000 len=520192 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f70a3a000 len=24576
[munmap] addr=0x7f70a80000 len=233472
[mmap] ret=0x7f70a00000 len=262144 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f70980000 len=524288 fd=-1 off=0 path=fd:-1
[open] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[mmap] ret=0x7f75915000 len=131072 fd=21 off=0 path=/dev/ashmem
[close] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[mmap] ret=0x7f76493000 len=8192 fd=21 off=0 path=/dev/ashmem
[close] fd=21 path=/dev/ashmem
[mmap] ret=0x7f56b7b000 len=1069056 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f7648e000 len=20480 fd=-1 off=0 path=fd:-1
[open] fd=21 path=/data/user/0/com.autonavi.minimap/shared_prefs/appLanguage.xml
[open] fd=21 path=/data/user/0/com.autonavi.minimap/shared_prefs/appLanguage.xml
[read] fd=21 path=/data/user/0/com.autonavi.minimap/shared_prefs/appLanguage.xml bytes=326
[close] fd=21 path=/data/user/0/com.autonavi.minimap/shared_prefs/appLanguage.xml
[munmap] addr=0x7f76493000 len=8192
[munmap] addr=0x7f7648e000 len=20480
[open] fd=21 path=/data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/launchTime
[open] fd=21 path=/data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/launchTime
[close] fd=21 path=/data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/launchTime
[open] fd=21 path=/data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/crashCounter
[open] fd=21 path=/data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/crashCounter
[read] fd=21 path=/data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/crashCounter bytes=1
[close] fd=21 path=/data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/crashCounter
[open] fd=21 path=/data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/crashCounter
[open] fd=21 path=/data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/crashCounter
[close] fd=21 path=/data/user/0/com.autonavi.minimap/files/boot/15.19.0.2063/crashCounter
[open] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[mmap] ret=0x7f70edb000 len=131072 fd=21 off=0 path=/dev/ashmem
[close] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[mmap] ret=0x7f70a99000 len=131072 fd=21 off=0 path=/dev/ashmem
[close] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[mmap] ret=0x7f56c60000 len=131072 fd=21 off=0 path=/dev/ashmem
[close] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[mmap] ret=0x7f76493000 len=8192 fd=21 off=0 path=/dev/ashmem
[close] fd=21 path=/dev/ashmem
[mmap] ret=0x7f56b5b000 len=1069056 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f7648e000 len=20480 fd=-1 off=0 path=fd:-1
[open] fd=21 path=/data/user/0/com.autonavi.minimap/shared_prefs/AfpSplashEvents.xml
[open] fd=21 path=/data/user/0/com.autonavi.minimap/shared_prefs/AfpSplashEvents.xml
[read] fd=21 path=/data/user/0/com.autonavi.minimap/shared_prefs/AfpSplashEvents.xml bytes=13995
[close] fd=21 path=/data/user/0/com.autonavi.minimap/shared_prefs/AfpSplashEvents.xml
[munmap] addr=0x7f76493000 len=8192
[munmap] addr=0x7f7648e000 len=20480
[open] fd=21 path=/sys/devices/system/cpu
[open] fd=21 path=/sys/devices/system/cpu
[close] fd=21 path=/sys/devices/system/cpu
[open] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[mmap] ret=0x7f76493000 len=8192 fd=21 off=0 path=/dev/ashmem
[close] fd=21 path=/dev/ashmem
[mmap] ret=0x7f56b5b000 len=1069056 fd=-1 off=0 path=fd:-1
[open] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[mmap] ret=0x7f76491000 len=8192 fd=21 off=0 path=/dev/ashmem
[close] fd=21 path=/dev/ashmem
[mmap] ret=0x7f56a36000 len=1069056 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f76364000 len=20480 fd=-1 off=0 path=fd:-1
[open] fd=22 path=/dev/ashmem
[open] fd=22 path=/dev/ashmem
[mmap] ret=0x7f7648f000 len=8192 fd=22 off=0 path=/dev/ashmem
[close] fd=22 path=/dev/ashmem
[mmap] ret=0x7f56911000 len=1069056 fd=-1 off=0 path=fd:-1
[open] fd=22 path=/sys/devices/system/cpu
[open] fd=22 path=/sys/devices/system/cpu
[close] fd=22 path=/sys/devices/system/cpu
[mmap] ret=0x7f7635f000 len=20480 fd=-1 off=0 path=fd:-1
[close] fd=21
[mmap] ret=0x7f76307000 len=20480 fd=-1 off=0 path=fd:-1
[read] fd=10 path=fd:10 bytes=16
[open] fd=21 path=/data/app/com.autonavi.minimap-1/lib/arm64/libmmkv.so
[open] fd=21 path=/data/app/com.autonavi.minimap-1/lib/arm64/libmmkv.so
[close] fd=21 path=/data/app/com.autonavi.minimap-1/lib/arm64/libmmkv.so
[open] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[read] fd=10 path=fd:10 bytes=16
[read] fd=10 path=fd:10 bytes=16
[mmap] ret=0x7f568d1000 len=131072 fd=21 off=0 path=/dev/ashmem
[close] fd=21 path=/dev/ashmem
[read] fd=10 path=fd:10 bytes=16
[read] fd=10 path=fd:10 bytes=16
[read] fd=10 path=fd:10 bytes=16
[open] fd=22 path=/data/app/com.autonavi.minimap-1/lib/arm64/libserverkey.so
[open] fd=22 path=/data/app/com.autonavi.minimap-1/lib/arm64/libserverkey.so
[close] fd=22 path=/data/app/com.autonavi.minimap-1/lib/arm64/libserverkey.so
[open] fd=22 path=/data/user/0/com.autonavi.minimap/files/boot/bootbiz/d3ec0a15fe6c9fc3b985bf0ecff5d529
[open] fd=22 path=/data/user/0/com.autonavi.minimap/files/boot/bootbiz/d3ec0a15fe6c9fc3b985bf0ecff5d529
[mmap] ret=0x7f70ecb000 len=65536 fd=22 off=0 path=/data/user/0/com.autonavi.minimap/files/boot/bootbiz/d3ec0a15fe6c9fc3b985bf0ecff5d529
[open] fd=23 path=/data/user/0/com.autonavi.minimap/files/boot/bootbiz/d3ec0a15fe6c9fc3b985bf0ecff5d529.crc
[open] fd=23 path=/data/user/0/com.autonavi.minimap/files/boot/bootbiz/d3ec0a15fe6c9fc3b985bf0ecff5d529.crc
[mmap] ret=0x7f7749a000 len=4096 fd=23 off=0 path=/data/user/0/com.autonavi.minimap/files/boot/bootbiz/d3ec0a15fe6c9fc3b985bf0ecff5d529.crc
[pread64] fd=20 off=17531430 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=20 off=17531460 path=/data/app/com.autonavi.minimap-1/base.apk bytes=36
[mmap] ret=0x7f771cf000 len=813 fd=20 off=17530880 path=/data/app/com.autonavi.minimap-1/base.apk
[inflateInit2_]
[open] fd=21 path=/dev/ashmem
[open] fd=21 path=/dev/ashmem
[mmap] ret=0x7f76305000 len=8192 fd=21 off=0 path=/dev/ashmem
[close] fd=21 path=/dev/ashmem
[mmap] ret=0x7f56614000 len=1069056 fd=-1 off=0 path=fd:-1
[inflateInit2_] ret=0x0

==================== zlib::inflate ENTER ====================
[inflate] z=0x7fe26d08f0 flush=0
[inflate] LEAVE ret=0x1
[inflateEnd] z=0x7fe26d08f0
[inflateEnd] ret=0x0
[munmap] addr=0x7f771cf000 len=813
[close] fd=21
[read] fd=21 path=fd:21 bytes=12
[fread] FILE*=0x7f7a87bbb8 path=FILE*:0x7f7a87bbb8 bytes=4
[fread] FILE*=0x7f7a87bbb8 path=FILE*:0x7f7a87bbb8 bytes=4
[close] fd=21
[fclose] FILE*=0x7f7a87bbb8
[close] fd=24
[mmap] ret=0x7f76300000 len=20480 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f76305000 len=8192
[munmap] addr=0x7f76300000 len=20480
[open] fd=24 path=/dev/ashmem
[open] fd=24 path=/dev/ashmem
[mmap] ret=0x7f566f9000 len=131072 fd=24 off=0 path=/dev/ashmem
[close] fd=24 path=/dev/ashmem
[open] fd=24 path=/dev/ashmem
[open] fd=24 path=/dev/ashmem
[mmap] ret=0x7f566d9000 len=131072 fd=24 off=0 path=/dev/ashmem
[close] fd=24 path=/dev/ashmem
[pread64] fd=20 off=17529115 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=20 off=17529145 path=/data/app/com.autonavi.minimap-1/base.apk bytes=21
[mmap] ret=0x7f771cf000 len=2576 fd=20 off=17526784 path=/data/app/com.autonavi.minimap-1/base.apk
[inflateInit2_]
[inflateInit2_] ret=0x0

==================== zlib::inflate ENTER ====================
[inflate] z=0x7fe26d08f0 flush=0
[inflate] LEAVE ret=0x1
[inflateEnd] z=0x7fe26d08f0
[inflateEnd] ret=0x0
[munmap] addr=0x7f771cf000 len=2576
[pread64] fd=20 off=17529360 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=20 off=17529390 path=/data/app/com.autonavi.minimap-1/base.apk bytes=22
[mmap] ret=0x7f771cf000 len=2828 fd=20 off=17526784 path=/data/app/com.autonavi.minimap-1/base.apk
[inflateInit2_]
[inflateInit2_] ret=0x0

==================== zlib::inflate ENTER ====================
[inflate] z=0x7fe26d08f0 flush=0
[inflate] LEAVE ret=0x1
[inflateEnd] z=0x7fe26d08f0
[inflateEnd] ret=0x0
[munmap] addr=0x7f771cf000 len=2828
[open] fd=24 path=/dev/ashmem
[open] fd=24 path=/dev/ashmem
[mmap] ret=0x7f566b9000 len=131072 fd=24 off=0 path=/dev/ashmem
[close] fd=24 path=/dev/ashmem
[open] fd=24 path=/dev/ashmem
[open] fd=24 path=/dev/ashmem
[mmap] ret=0x7f76305000 len=8192 fd=24 off=0 path=/dev/ashmem
[close] fd=24 path=/dev/ashmem
[mmap] ret=0x7f565b4000 len=1069056 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f76300000 len=20480 fd=-1 off=0 path=fd:-1
[open] fd=24 path=/data/app/com.autonavi.minimap-1/lib/arm64/libamapcrash.so
[open] fd=24 path=/data/app/com.autonavi.minimap-1/lib/arm64/libamapcrash.so
[close] fd=24 path=/data/app/com.autonavi.minimap-1/lib/arm64/libamapcrash.so
[open] fd=30 path=/dev/ashmem
[open] fd=30 path=/dev/ashmem
[mmap] ret=0x7f762f9000 len=8192 fd=30 off=0 path=/dev/ashmem
[close] fd=30 path=/dev/ashmem
[mmap] ret=0x7f5647f000 len=1069056 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f761f2000 len=20480 fd=-1 off=0 path=fd:-1
[open] fd=30 path=/data/user/0/com.autonavi.minimap/shared_prefs/SharedPreferences.xml
[open] fd=30 path=/data/user/0/com.autonavi.minimap/shared_prefs/SharedPreferences.xml
[read] fd=30 path=/data/user/0/com.autonavi.minimap/shared_prefs/SharedPreferences.xml bytes=11654
[close] fd=30 path=/data/user/0/com.autonavi.minimap/shared_prefs/SharedPreferences.xml
[open] fd=30 path=/dev/ashmem
[open] fd=30 path=/dev/ashmem
[mmap] ret=0x7f762f7000 len=8192 fd=30 off=0 path=/dev/ashmem
[close] fd=30 path=/dev/ashmem
[mmap] ret=0x7f5635a000 len=1069056 fd=-1 off=0 path=fd:-1
[pread64] fd=20 off=12475767 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[munmap] addr=0x7f762f9000 len=8192
[pread64] fd=20 off=12475797 path=/data/app/com.autonavi.minimap-1/base.apk bytes=42
[munmap] addr=0x7f761f2000 len=20480
[mmap] ret=0x7f771cf000 len=3740 fd=20 off=12472320 path=/data/app/com.autonavi.minimap-1/base.apk
[inflateInit2_]
[mmap] ret=0x7f761ed000 len=20480 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f56544000 len=262144 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f56544000 len=262144
[mmap] ret=0x7f56504000 len=262144 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f56504000 len=262144
[mmap] ret=0x7f56505000 len=520192 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f56505000 len=241664
[munmap] addr=0x7f56580000 len=16384
[mmap] ret=0x7f56485000 len=520192 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f56485000 len=241664
[munmap] addr=0x7f56500000 len=16384
[inflateInit2_] ret=0x0

==================== zlib::inflate ENTER ====================
[inflate] z=0x7f566b7710 flush=0
[inflate] LEAVE ret=0x1
[inflateEnd] z=0x7f566b7710
[inflateEnd] ret=0x0
[munmap] addr=0x7f771cf000 len=3740
[pread64] fd=20 off=15072756 path=/data/app/com.autonavi.minimap-1/base.apk bytes=30
[pread64] fd=20 off=15072786 path=/data/app/com.autonavi.minimap-1/base.apk bytes=51
[mmap] ret=0x7f762f9000 len=5026 fd=20 off=15069184 path=/data/app/com.autonavi.minimap-1/base.apk
[munmap] addr=0x7f762f9000 len=5026
[open] fd=30 path=/sys/devices/system/cpu
[open] fd=30 path=/sys/devices/system/cpu
[close] fd=30 path=/sys/devices/system/cpu
[pread64] fd=6 off=6655650 path=fd:6 bytes=30
[pread64] fd=6 off=6655680 path=fd:6 bytes=42
[mmap] ret=0x7f771cf000 len=4065 fd=6 off=6651904 path=fd:6
[inflateInit2_]
[inflateInit2_] ret=0x0

==================== zlib::inflate ENTER ====================
[inflate] z=0x7f566b75f0 flush=0
[inflate] LEAVE ret=0x1
[open] fd=30 path=/sys/devices/system/cpu
[open] fd=30 path=/sys/devices/system/cpu
[close] fd=30 path=/sys/devices/system/cpu
[inflateEnd] z=0x7f566b75f0
[inflateEnd] ret=0x0
[munmap] addr=0x7f771cf000 len=4065
[read] fd=10 path=fd:10 bytes=16
[open] fd=30 path=/dev/ashmem
[open] fd=30 path=/dev/ashmem
[pread64] fd=6 off=6641224 path=fd:6 bytes=30
[pread64] fd=6 off=6641254 path=fd:6 bytes=39
[mmap] ret=0x7f771cf000 len=1887 fd=6 off=6639616 path=fd:6
[inflateInit2_]
[inflateInit2_] ret=0x0

==================== zlib::inflate ENTER ====================
[inflate] z=0x7f566b75c0 flush=0
[inflate] LEAVE ret=0x1
[inflateEnd] z=0x7f566b75c0
[inflateEnd] ret=0x0
[munmap] addr=0x7f771cf000 len=1887
[pread64] fd=6 off=6641519 path=fd:6 bytes=30
[pread64] fd=6 off=6641549 path=fd:6 bytes=43
[mmap] ret=0x7f771cf000 len=2184 fd=6 off=6639616 path=fd:6
[inflateInit2_]
[inflateInit2_] ret=0x0

==================== zlib::inflate ENTER ====================
[inflate] z=0x7f566b75f0 flush=0
[inflate] LEAVE ret=0x1
[inflateEnd] z=0x7f566b75f0
[inflateEnd] ret=0x0
[munmap] addr=0x7f771cf000 len=2184
[mmap] ret=0x7f762f9000 len=8192 fd=30 off=0 path=/dev/ashmem
[close] fd=30 path=/dev/ashmem
[mmap] ret=0x7f56235000 len=1069056 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f761f2000 len=20480 fd=-1 off=0 path=fd:-1
[open] fd=37 path=/data/app/com.autonavi.minimap-1/lib/arm64/libamapnsq.so
[open] fd=37 path=/data/app/com.autonavi.minimap-1/lib/arm64/libamapnsq.so
[close] fd=37 path=/data/app/com.autonavi.minimap-1/lib/arm64/libamapnsq.so
[read] fd=30 path=fd:30 bytes=8
[open] fd=38 path=/data/user/0/com.autonavi.minimap/files/boot/speedup/5d3830e454f6b0d0c3b0106103275177
[open] fd=38 path=/data/user/0/com.autonavi.minimap/files/boot/speedup/5d3830e454f6b0d0c3b0106103275177
[mmap] ret=0x7f56500000 len=131072 fd=38 off=0 path=/data/user/0/com.autonavi.minimap/files/boot/speedup/5d3830e454f6b0d0c3b0106103275177
[open] fd=40 path=/data/user/0/com.autonavi.minimap/files/boot/speedup/5d3830e454f6b0d0c3b0106103275177.crc
[open] fd=40 path=/data/user/0/com.autonavi.minimap/files/boot/speedup/5d3830e454f6b0d0c3b0106103275177.crc
[mmap] ret=0x7f771cf000 len=4096 fd=40 off=0 path=/data/user/0/com.autonavi.minimap/files/boot/speedup/5d3830e454f6b0d0c3b0106103275177.crc
[open] fd=41 path=/dev/ashmem
[open] fd=41 path=/dev/ashmem
[mmap] ret=0x7f761eb000 len=8192 fd=41 off=0 path=/dev/ashmem
[close] fd=41 path=/dev/ashmem
[mmap] ret=0x7f55da2000 len=1069056 fd=-1 off=0 path=fd:-1
[open] fd=41 path=/dev/ashmem
[open] fd=41 path=/dev/ashmem
[mmap] ret=0x7f761e9000 len=8192 fd=41 off=0 path=/dev/ashmem
[close] fd=41 path=/dev/ashmem
[mmap] ret=0x7f55c9d000 len=1069056 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f75910000 len=20480 fd=-1 off=0 path=fd:-1
[open] fd=41 path=/dev/ashmem
[open] fd=41 path=/dev/ashmem
[mmap] ret=0x7f7590e000 len=8192 fd=41 off=0 path=/dev/ashmem
[close] fd=41 path=/dev/ashmem
[mmap] ret=0x7f55b98000 len=1069056 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f728ca000 len=20480 fd=-1 off=0 path=fd:-1
[open] fd=41 path=/dev/ashmem
[open] fd=41 path=/dev/ashmem
[mmap] ret=0x7f728c5000 len=20480 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f7590c000 len=8192 fd=41 off=0 path=/dev/ashmem
[close] fd=41 path=/dev/ashmem
[open] fd=41 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_all_value.xml
[open] fd=41 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_all_value.xml
[read] fd=41 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_all_value.xml bytes=16384
[mmap] ret=0x7f55a52000 len=1069056 fd=-1 off=0 path=fd:-1
[open] fd=42 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_white_list_key_value.xml
[open] fd=42 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_white_list_key_value.xml
[open] fd=43 path=/dev/ashmem
[open] fd=43 path=/dev/ashmem
[read] fd=41 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_all_value.xml bytes=16384
[open] fd=37 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_value.xml
[open] fd=37 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_value.xml
[mmap] ret=0x7f728c3000 len=8192 fd=43 off=0 path=/dev/ashmem
[close] fd=43 path=/dev/ashmem
[mmap] ret=0x7f70ec6000 len=20480 fd=-1 off=0 path=fd:-1
[mmap] ret=0x7f558c4000 len=1069056 fd=-1 off=0 path=fd:-1
[read] fd=42 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_white_list_key_value.xml bytes=16384
[read] fd=37 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_value.xml bytes=16384
[open] fd=43 path=/dev/ashmem
[open] fd=43 path=/dev/ashmem
[mmap] ret=0x7f728c1000 len=8192 fd=43 off=0 path=/dev/ashmem
[close] fd=43 path=/dev/ashmem
[munmap] addr=0x7f7590c000 len=8192
[mmap] ret=0x7f557bf000 len=1069056 fd=-1 off=0 path=fd:-1
[read] fd=41 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_all_value.xml bytes=16384
[read] fd=37 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_value.xml bytes=16384
[read] fd=41 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_all_value.xml bytes=16384
[mmap] ret=0x7f70ec1000 len=20480 fd=-1 off=0 path=fd:-1
[read] fd=42 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_white_list_key_value.xml bytes=16384
[munmap] addr=0x7f728c3000 len=8192
[munmap] addr=0x7f70ec6000 len=20480
[munmap] addr=0x7f70ec1000 len=20480
[read] fd=37 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_value.xml bytes=16384
[mmap] ret=0x7f70ec6000 len=20480 fd=-1 off=0 path=fd:-1

==================== open (OFFLINE CANDIDATE) ====================
[open] fd=44 path=/storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/journal

==================== open (OFFLINE CANDIDATE) ====================
[open] fd=44 path=/storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/journal

==================== read (OFFLINE) ====================
[read] fd=44 path=/storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/journal bytes=8192 ts=1754641887639
             0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
7f564e1000  6c 69 62 63 6f 72 65 2e 69 6f 2e 44 69 73 6b 4c  libcore.io.DiskL
7f564e1010  72 75 43 61 63 68 65 0a 31 0a 31 0a 31 0a 0a 44  ruCache.1.1.1..D
7f564e1020  49 52 54 59 20 35 75 32 7a 37 34 71 6f 69 33 78  IRTY 5u2z74qoi3x
7f564e1030  66 74 64 77 69 35 35 78 7a 34 6b 69 7a 66 0a 43  ftdwi55xz4kizf.C
7f564e1040  4c 45 41 4e 20 35 75 32 7a 37 34 71 6f 69 33 78  LEAN 5u2z74qoi3x
7f564e1050  66 74 64 77 69 35 35 78 7a 34 6b 69 7a 66 20 32  ftdwi55xz4kizf 2
7f564e1060  35 35 34 0a 44 49 52 54 59 20 34 69 66 63 69 32  554.DIRTY 4ifci2
7f564e1070  65 38 74 31 74 34 6b 76 70 37 71 66 30 62 39 76  e8t1t4kvp7qf0b9v

==================== read (OFFLINE) ====================
[read] fd=44 path=/storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/journal bytes=8192 ts=1754641887655
             0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
7f564e1000  77 69 35 35 78 7a 34 6b 69 7a 66 0a 43 4c 45 41  wi55xz4kizf.CLEA
7f564e1010  4e 20 35 75 32 7a 37 34 71 6f 69 33 78 66 74 64  N 5u2z74qoi3xftd
7f564e1020  77 69 35 35 78 7a 34 6b 69 7a 66 20 32 35 35 34  wi55xz4kizf 2554
7f564e1030  0a 52 45 41 44 20 31 70 34 6f 6f 32 69 6f 31 76  .READ 1p4oo2io1v
7f564e1040  6e 6d 78 70 38 35 6b 33 70 77 71 74 34 33 61 0a  nmxp85k3pwqt43a.
7f564e1050  52 45 41 44 20 33 6a 77 67 77 76 67 66 6b 7a 32  READ 3jwgwvgfkz2
7f564e1060  6c 61 72 37 6c 6a 37 75 31 66 76 69 65 6b 0a 44  lar7lj7u1fviek.D
7f564e1070  49 52 54 59 20 34 6a 6d 31 6a 74 6e 33 7a 64 6e  IRTY 4jm1jtn3zdn
[read] fd=42 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_white_list_key_value.xml bytes=16384
[离线追踪] 目标模块已加载: libamapnsq.so, 基址: 0x7f56178000
[read] fd=41 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_all_value.xml bytes=16384

==================== read (OFFLINE) ====================
[read] fd=44 path=/storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/journal bytes=8192 ts=1754641887901
             0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
7f564e1000  52 54 59 20 35 75 32 7a 37 34 71 6f 69 33 78 66  RTY 5u2z74qoi3xf
7f564e1010  74 64 77 69 35 35 78 7a 34 6b 69 7a 66 0a 43 4c  tdwi55xz4kizf.CL
7f564e1020  45 41 4e 20 35 75 32 7a 37 34 71 6f 69 33 78 66  EAN 5u2z74qoi3xf
7f564e1030  74 64 77 69 35 35 78 7a 34 6b 69 7a 66 20 32 35  tdwi55xz4kizf 25
7f564e1040  35 34 0a 52 45 41 44 20 37 69 32 63 32 63 73 67  54.READ 7i2c2csg
7f564e1050  32 6b 71 61 70 34 73 67 6a 61 62 6b 6d 64 37 6e  2kqap4sgjabkmd7n
7f564e1060  79 0a 52 45 41 44 20 31 70 34 6f 6f 32 69 6f 31  y.READ 1p4oo2io1
7f564e1070  76 6e 6d 78 70 38 35 6b 33 70 77 71 74 34 33 61  vnmxp85k3pwqt43a
[open] fd=43 path=/data/app/com.autonavi.minimap-1/lib/arm64/libc++_shared.so
[open] fd=43 path=/data/app/com.autonavi.minimap-1/lib/arm64/libc++_shared.so
[close] fd=43 path=/data/app/com.autonavi.minimap-1/lib/arm64/libc++_shared.so
[read] fd=37 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_value.xml bytes=16384
[read] fd=42 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_white_list_key_value.xml bytes=16384
[read] fd=41 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_all_value.xml bytes=16384

==================== read (OFFLINE) ====================
[read] fd=44 path=/storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/journal bytes=8192 ts=1754641887916
             0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
7f564e1000  6d 34 70 73 66 79 66 77 66 76 6c 63 70 36 62 33  m4psfyfwfvlcp6b3
7f564e1010  61 20 36 31 38 0a 44 49 52 54 59 20 31 63 33 36  a 618.DIRTY 1c36
7f564e1020  30 77 61 7a 71 33 78 62 34 67 68 6a 37 63 35 35  0wazq3xb4ghj7c55
7f564e1030  76 72 62 6a 38 0a 44 49 52 54 59 20 6b 6b 62 64  vrbj8.DIRTY kkbd
7f564e1040  75 39 36 31 6b 38 32 38 70 74 6c 6b 69 76 31 7a  u961k828ptlkiv1z
7f564e1050  34 6d 36 72 0a 43 4c 45 41 4e 20 31 63 33 36 30  4m6r.CLEAN 1c360
7f564e1060  77 61 7a 71 33 78 62 34 67 68 6a 37 63 35 35 76  wazq3xb4ghj7c55v
7f564e1070  72 62 6a 38 20 38 39 35 0a 43 4c 45 41 4e 20 6b  rbj8 895.CLEAN k
[open] fd=43 path=/data/user/0/com.autonavi.minimap/shared_prefs/NAMESPACE_TRIP_BUSINESS.xml
[open] fd=43 path=/data/user/0/com.autonavi.minimap/shared_prefs/NAMESPACE_TRIP_BUSINESS.xml
[open] fd=45 path=/data/app/com.autonavi.minimap-1/lib/arm64/libamapmain.so
[open] fd=45 path=/data/app/com.autonavi.minimap-1/lib/arm64/libamapmain.so

==================== read (OFFLINE) ====================
[read] fd=44 path=/storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/journal bytes=8192 ts=1754641887935
             0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
7f564e1000  35 75 32 7a 37 34 71 6f 69 33 78 66 74 64 77 69  5u2z74qoi3xftdwi
7f564e1010  35 35 78 7a 34 6b 69 7a 66 0a 43 4c 45 41 4e 20  55xz4kizf.CLEAN 
7f564e1020  35 75 32 7a 37 34 71 6f 69 33 78 66 74 64 77 69  5u2z74qoi3xftdwi
7f564e1030  35 35 78 7a 34 6b 69 7a 66 20 32 35 35 34 0a 52  55xz4kizf 2554.R
7f564e1040  45 41 44 20 31 70 34 6f 6f 32 69 6f 31 76 6e 6d  EAD 1p4oo2io1vnm
7f564e1050  78 70 38 35 6b 33 70 77 71 74 34 33 61 0a 44 49  xp85k3pwqt43a.DI
7f564e1060  52 54 59 20 34 6a 6d 31 6a 74 6e 33 7a 64 6e 6a  RTY 4jm1jtn3zdnj
7f564e1070  6f 79 70 79 37 77 75 75 33 6a 35 6f 76 0a 43 4c  oypy7wuu3j5ov.CL
[read] fd=43 path=/data/user/0/com.autonavi.minimap/shared_prefs/NAMESPACE_TRIP_BUSINESS.xml bytes=289
[mmap] ret=0x7f556af000 len=262144 fd=-1 off=0 path=fd:-1

==================== read (OFFLINE) ====================
[read] fd=44 path=/storage/emulated/0/Android/data/com.autonavi.minimap/files/autonavi/httpcache/imageajx/journal bytes=8192 ts=1754641887945
             0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
7f564e1000  45 41 44 20 31 70 34 6f 6f 32 69 6f 31 76 6e 6d  EAD 1p4oo2io1vnm
7f564e1010  78 70 38 35 6b 33 70 77 71 74 34 33 61 0a 44 49  xp85k3pwqt43a.DI
7f564e1020  52 54 59 20 34 6a 6d 31 6a 74 6e 33 7a 64 6e 6a  RTY 4jm1jtn3zdnj
7f564e1030  6f 79 70 79 37 77 75 75 33 6a 35 6f 76 0a 52 45  oypy7wuu3j5ov.RE
7f564e1040  41 44 20 36 67 30 39 37 70 67 65 6c 7a 79 77 30  AD 6g097pgelzyw0
7f564e1050  70 6e 38 67 74 62 6a 67 62 70 39 6d 0a 52 45 41  pn8gtbjgbp9m.REA
7f564e1060  44 20 33 6a 77 67 77 76 67 66 6b 7a 32 6c 61 72  D 3jwgwvgfkz2lar
7f564e1070  37 6c 6a 37 75 31 66 76 69 65 6b 0a 43 4c 45 41  7lj7u1fviek.CLEA
[read] fd=37 path=/data/user/0/com.autonavi.minimap/shared_prefs/cloudconfig_aocs_sp_key_value.xml bytes=16384
[munmap] addr=0x7f556af000 len=262144
[mmap] ret=0x7f55670000 len=520192 fd=-1 off=0 path=fd:-1
[munmap] addr=0x7f55670000 len=65536
[munmap] addr=0x7f556c0000 len=192512
[close] fd=45 path=/data/app/com.autonavi.minimap-1/lib/arm64/libamapmain.so
