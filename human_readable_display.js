/*
 * 高德地图人类可读数据完整展示器
 * 专注于显示完整的可读内容，而非仅仅识别类型
 * 版本: Frida 12.9.7 兼容
 */

console.log("[Human Display] 启动人类可读数据完整展示器...");

var displayCount = 0;
var maxDisplays = 5;

// 将字节数组转换为可读字符串
function bytesToReadableString(bytes, maxLength) {
    var result = "";
    var textPart = "";
    
    for (var i = 0; i < Math.min(maxLength || bytes.length, bytes.length); i++) {
        if (bytes[i] >= 32 && bytes[i] < 127) {
            // 可打印ASCII字符
            textPart += String.fromCharCode(bytes[i]);
        } else if (bytes[i] === 10) {
            // 换行符
            textPart += "\n";
        } else if (bytes[i] === 13) {
            // 回车符
            textPart += "\r";
        } else if (bytes[i] === 9) {
            // Tab符
            textPart += "\t";
        } else {
            // 非可读字符用点表示
            textPart += ".";
        }
    }
    
    return textPart;
}

// 尝试解码UTF-8中文字符
function tryDecodeUTF8Chinese(bytes, startIndex, maxLength) {
    var result = "";
    var i = startIndex;
    
    while (i < Math.min(startIndex + maxLength, bytes.length - 2)) {
        // UTF-8中文字符模式检测
        if (bytes[i] >= 0xE4 && bytes[i] <= 0xE9 && 
            bytes[i+1] >= 0x80 && bytes[i+1] <= 0xBF &&
            bytes[i+2] >= 0x80 && bytes[i+2] <= 0xBF) {
            
            // 找到中文字符，显示其十六进制
            var hex1 = bytes[i].toString(16);
            var hex2 = bytes[i+1].toString(16);
            var hex3 = bytes[i+2].toString(16);
            if (hex1.length === 1) hex1 = "0" + hex1;
            if (hex2.length === 1) hex2 = "0" + hex2;
            if (hex3.length === 1) hex3 = "0" + hex3;
            
            result += "[中文:" + hex1 + hex2 + hex3 + "] ";
            i += 3;
        } else {
            i++;
        }
    }
    
    return result;
}

// 显示完整的人类可读数据
function displayCompleteReadableData(dataPtr, size, source) {
    if (displayCount >= maxDisplays) return;
    
    try {
        // 读取更多数据以获得完整内容
        var readSize = Math.min(2048, size); // 读取最多2KB来获得完整内容
        var data = dataPtr.readByteArray(readSize);
        var bytes = new Uint8Array(data);
        
        console.log("\n" + "=".repeat(60));
        console.log("📋 人类可读数据完整展示 #" + (displayCount + 1));
        console.log("来源: " + source + " | 总大小: " + size + " 字节 | 读取: " + readSize + " 字节");
        console.log("=".repeat(60));
        
        // 检测数据类型并完整显示
        var fullText = bytesToReadableString(bytes, readSize);
        
        // JSON数据完整显示
        if (fullText.indexOf('{"') >= 0 || fullText.indexOf('"res_list"') >= 0) {
            displayCount++;
            console.log("📋 数据类型: JSON配置文件");
            console.log("📄 完整内容:");
            console.log("----------------------------------------");
            
            // 找到JSON起始位置
            var jsonStart = Math.max(fullText.indexOf('{"'), fullText.indexOf('[{'));
            if (jsonStart >= 0) {
                var jsonContent = fullText.substring(jsonStart);
                // 尝试美化JSON显示
                var lines = jsonContent.split('\n');
                for (var i = 0; i < Math.min(lines.length, 20); i++) {
                    console.log("  " + lines[i]);
                }
                if (lines.length > 20) {
                    console.log("  ... (还有 " + (lines.length - 20) + " 行)");
                }
            }
        }
        
        // XML数据完整显示
        else if (fullText.indexOf('<?xml') >= 0) {
            displayCount++;
            console.log("🌐 数据类型: XML配置文件");
            console.log("📄 完整内容:");
            console.log("----------------------------------------");
            
            var xmlLines = fullText.split('\n');
            for (var j = 0; j < Math.min(xmlLines.length, 15); j++) {
                console.log("  " + xmlLines[j]);
            }
            if (xmlLines.length > 15) {
                console.log("  ... (还有 " + (xmlLines.length - 15) + " 行)");
            }
        }
        
        // DICE-AM数据结构展示
        else if (fullText.indexOf("DICE-AM") >= 0) {
            displayCount++;
            console.log("🎯 数据类型: DICE-AM矢量地图数据");
            console.log("📄 数据结构分析:");
            console.log("----------------------------------------");
            console.log("头部标识: " + fullText.substring(0, 8));
            
            // 显示关键字节
            console.log("关键字节序列:");
            for (var k = 0; k < Math.min(64, bytes.length); k += 16) {
                var line = "  ";
                var ascii = "  ";
                for (var l = 0; l < 16 && (k + l) < bytes.length; l++) {
                    var hex = bytes[k + l].toString(16);
                    if (hex.length === 1) hex = "0" + hex;
                    line += hex + " ";
                    
                    if (bytes[k + l] >= 32 && bytes[k + l] < 127) {
                        ascii += String.fromCharCode(bytes[k + l]);
                    } else {
                        ascii += ".";
                    }
                }
                console.log(line + " | " + ascii);
            }
        }
        
        // 中文文本数据展示
        else {
            // 检查是否包含中文
            var chineseFound = false;
            var chineseContent = "";
            
            for (var m = 0; m < Math.min(200, bytes.length); m++) {
                if (bytes[m] >= 0xE4 && bytes[m] <= 0xE9) {
                    chineseFound = true;
                    chineseContent = tryDecodeUTF8Chinese(bytes, m, 100);
                    break;
                }
            }
            
            if (chineseFound) {
                displayCount++;
                console.log("🈲 数据类型: 中文文本/地名数据");
                console.log("📄 文本内容分析:");
                console.log("----------------------------------------");
                console.log("原始文本: " + fullText.substring(0, 100));
                console.log("中文字符: " + chineseContent);
                
                // 显示UTF-8字节序列
                console.log("UTF-8字节序列:");
                var hexLine = "  ";
                for (var n = 0; n < Math.min(50, bytes.length); n++) {
                    if (bytes[n] >= 0xE4 && bytes[n] <= 0xE9) {
                        var hex = bytes[n].toString(16);
                        if (hex.length === 1) hex = "0" + hex;
                        hexLine += hex + " ";
                    }
                }
                console.log(hexLine);
            }
            
            // 如果都不是，显示为通用文本
            else if (fullText.length > 10) {
                displayCount++;
                console.log("📄 数据类型: 通用文本数据");
                console.log("📄 完整内容:");
                console.log("----------------------------------------");
                
                var textLines = fullText.split('\n');
                for (var o = 0; o < Math.min(textLines.length, 25); o++) {
                    if (textLines[o].trim().length > 0) {
                        console.log("  " + textLines[o]);
                    }
                }
                if (textLines.length > 25) {
                    console.log("  ... (还有 " + (textLines.length - 25) + " 行)");
                }
            }
        }
        
        console.log("=".repeat(60) + "\n");
        
    } catch (e) {
        console.log("[显示错误] " + e.message);
    }
}

function isSystemFile(charStr) {
    return charStr.indexOf("VmFlags") >= 0 ||
           charStr.indexOf("kB.") >= 0 ||
           charStr.indexOf("/proc/") >= 0 ||
           charStr.indexOf("/dev/") >= 0 ||
           charStr.indexOf("Referenced") >= 0 ||
           charStr.indexOf("Anonymous") >= 0;
}

function setupCompleteDisplay() {
    setTimeout(function() {
        console.log("[Setup] 设置完整数据展示Hook...");
        
        try {
            var lib = Module.findBaseAddress("libamapnsq.so");
            if (!lib) {
                console.log("[Error] 未找到libamapnsq.so");
                return;
            }
            
            console.log("[Library] 库基址: " + lib);
            
            // Hook文件读取 - 专注于显示完整内容
            var readPtr = Module.findExportByName("libc.so", "read");
            if (readPtr) {
                Interceptor.attach(readPtr, {
                    onEnter: function(args) {
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                        this.isInteresting = (this.size > 100 && this.size < 100000);
                    },
                    onLeave: function(retval) {
                        if (!this.isInteresting || displayCount >= maxDisplays) return;
                        
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 0) {
                            try {
                                // 简单预览判断是否值得显示
                                var preview = this.buffer.readByteArray(Math.min(32, bytesRead));
                                var previewBytes = new Uint8Array(preview);
                                var previewStr = "";
                                
                                for (var i = 0; i < previewBytes.length; i++) {
                                    if (previewBytes[i] >= 32 && previewBytes[i] < 127) {
                                        previewStr += String.fromCharCode(previewBytes[i]);
                                    }
                                }
                                
                                // 只显示非系统文件且包含有意义内容的数据
                                if (!isSystemFile(previewStr) && 
                                    (previewStr.indexOf('{') >= 0 || 
                                     previewStr.indexOf('<') >= 0 || 
                                     previewStr.indexOf('DICE') >= 0 ||
                                     previewBytes[0] >= 0xE4)) {
                                    
                                    displayCompleteReadableData(this.buffer, bytesRead, "文件读取");
                                }
                                
                            } catch (e) {
                                // 忽略预览错误
                            }
                        }
                    }
                });
                console.log("✅ 完整数据展示Hook设置成功");
            }
            
            console.log("[Ready] 完整数据展示器准备就绪");
            console.log("💡 请移动地图到新区域以触发数据加载和完整展示");
            
        } catch (e) {
            console.log("[Setup Error] " + e.message);
        }
    }, 3000);
}

// 启动完整展示器
setupCompleteDisplay();

console.log("[Human Display] 人类可读数据完整展示器已加载"); 