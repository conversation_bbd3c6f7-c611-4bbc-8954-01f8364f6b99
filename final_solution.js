/*
 * 高德地图终极简化解决方案
 * 最小化Hook，最大化稳定性
 * 专注于获取真实可读数据
 */

console.log("[Final Solution] 启动终极解决方案...");

var foundData = [];
var maxFinds = 5;

// 超级简化的数据捕获
function captureData(source, data, size) {
    if (foundData.length >= maxFinds) return;
    
    try {
        console.log("\n🎯 数据捕获 #" + (foundData.length + 1));
        console.log("来源: " + source + ", 大小: " + size);
        
        // 转换为文本
        var text = "";
        for (var i = 0; i < Math.min(data.length, 800); i++) {
            if (data[i] >= 32 && data[i] < 127) {
                text += String.fromCharCode(data[i]);
            } else if (data[i] === 10) {
                text += "\n";
            } else {
                text += (data[i] < 16 ? "0" : "") + data[i].toString(16) + " ";
            }
        }
        
        console.log("=".repeat(50));
        console.log("📄 真实数据内容:");
        console.log(text);
        console.log("=".repeat(50));
        
        foundData.push({
            source: source,
            size: size,
            content: text.substring(0, 500)
        });
        
    } catch (e) {
        console.log("捕获错误: " + e.message);
    }
}

// 只Hook最安全的函数
setTimeout(function() {
    console.log("[Setup] 设置最安全的Hook...");
    
    try {
        // 只Hook write系统调用 - 输出数据时触发
        var writePtr = Module.findExportByName("libc.so", "write");
        if (writePtr) {
            Interceptor.attach(writePtr, {
                onEnter: function(args) {
                    this.fd = args[0].toInt32();
                    this.buffer = args[1];
                    this.size = args[2].toInt32();
                    
                    // 只关注可能的数据文件输出
                    this.isInteresting = (this.size > 200 && this.size < 5000);
                },
                onLeave: function(retval) {
                    if (this.isInteresting && foundData.length < maxFinds) {
                        try {
                            var data = this.buffer.readByteArray(Math.min(this.size, 1000));
                            var bytes = new Uint8Array(data);
                            
                            // 快速检查是否包含目标数据
                            var preview = "";
                            for (var i = 0; i < Math.min(50, bytes.length); i++) {
                                if (bytes[i] >= 32 && bytes[i] < 127) {
                                    preview += String.fromCharCode(bytes[i]);
                                }
                            }
                            
                            if (preview.indexOf("xml") >= 0 || 
                                preview.indexOf("{") >= 0 || 
                                preview.indexOf("DICE") >= 0) {
                                
                                console.log("[Write捕获] 发现目标数据!");
                                captureData("write系统调用", bytes, this.size);
                            }
                            
                        } catch (e) {
                            // 忽略读取错误
                        }
                    }
                }
            });
            console.log("✅ write() Hook设置成功");
        }
        
        console.log("[Ready] 终极解决方案准备就绪");
        console.log("💡 请操作地图应用");
        
    } catch (e) {
        console.log("[Setup Error] " + e.message);
    }
}, 1000);

// 简化报告
function finalReport() {
    console.log("\n📊 === 终极解决方案报告 ===");
    console.log("捕获数据: " + foundData.length + "/" + maxFinds);
    
    if (foundData.length > 0) {
        console.log("🎉 成功捕获数据！");
        for (var i = 0; i < foundData.length; i++) {
            console.log("  " + (i+1) + ". " + foundData[i].source + " (" + foundData[i].size + " 字节)");
        }
    } else {
        console.log("💡 继续操作地图以触发数据捕获");
    }
    console.log("===============================\n");
}

setInterval(finalReport, 20000);

console.log("[Final Solution] 终极解决方案已加载"); 