// 高德地图数据流监控脚本 - 只监控不读取
// 适用于 Frida 12.9.7，使用 ES5 语法
// 基于成功脚本模式，只记录调用统计

(function() {
    'use strict';
    
    // ==================== 全局配置 ====================
    var config = {
        libName: "libz.so",                // 主要监控目标
        debugLevel: 1,                     // 0=仅错误, 1=基本信息, 2=详细信息
        includeBacktrace: false,           // 不记录调用栈以提高稳定性
        maxLogCount: 100,                  // 最大日志数量
        logInterval: 5000                  // 每5秒报告一次统计
    };
    
    // ==================== 全局变量 ====================
    var baseAddr = null;
    var gCallStats = {
        uncompress: 0,
        read: 0,
        total: 0
    };
    var gStartTime = new Date().getTime();
    var gLastReportTime = 0;
    var gHookedFunctions = {};
    
    // ==================== 工具函数 ====================
    function log(message) {
        console.log("[数据流监控] " + message);
    }
    
    function logError(message) {
        console.log("[数据流监控-错误] " + message);
    }
    
    function logDebug(level, message) {
        if (config.debugLevel >= level) {
            console.log("[数据流监控-调试] " + message);
        }
    }
    
    function formatAddress(addr) {
        return "0x" + addr.toString(16);
    }
    
    function getRuntime() {
        var now = new Date().getTime();
        return ((now - gStartTime) / 1000).toFixed(1) + "s";
    }
    
    // ==================== 监控函数 ====================
    function setupZlibMonitor() {
        log("设置 zlib 监控...");
        
        try {
            var zlibModule = Process.findModuleByName("libz.so");
            if (!zlibModule) {
                logError("libz.so 模块未找到");
                return false;
            }
            
            baseAddr = zlibModule.base;
            log("libz.so 基址: " + formatAddress(baseAddr));
            
            var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
            if (!uncompressPtr) {
                logError("uncompress 函数未找到");
                return false;
            }
            
            log("找到 uncompress 函数: " + formatAddress(uncompressPtr));
            
            // 只监控调用，不读取任何数据
            Interceptor.attach(uncompressPtr, {
                onEnter: function(args) {
                    this.sourceLen = 0;
                    try {
                        this.sourceLen = args[3].toInt32();
                    } catch (e) {
                        // 忽略参数读取错误
                    }
                },
                onLeave: function(retval) {
                    try {
                        var result = retval.toInt32();
                        if (result === 0) {
                            gCallStats.uncompress++;
                            gCallStats.total++;
                            
                            // 定期报告但不读取数据内容
                            if (gCallStats.uncompress % 10 === 1) {
                                log("[" + getRuntime() + "] 解压调用 #" + gCallStats.uncompress + 
                                   " (源大小: ~" + this.sourceLen + " 字节)");
                            }
                        }
                    } catch (e) {
                        // 忽略统计错误
                    }
                }
            });
            
            gHookedFunctions["uncompress"] = true;
            log("zlib 监控设置成功");
            return true;
            
        } catch (e) {
            logError("设置 zlib 监控失败: " + e);
            return false;
        }
    }
    
    function setupFileMonitor() {
        log("设置文件操作监控...");
        
        try {
            var readPtr = Module.findExportByName("libc.so", "read");
            if (!readPtr) {
                logError("read 函数未找到");
                return false;
            }
            
            // 只监控大文件读取，不读取内容
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    this.size = 0;
                    try {
                        this.size = args[2].toInt32();
                    } catch (e) {
                        // 忽略参数读取错误
                    }
                },
                onLeave: function(retval) {
                    try {
                        var bytesRead = retval.toInt32();
                        
                        // 只统计较大的文件读取（可能是地图数据）
                        if (bytesRead > 5000 && bytesRead < 200000) {
                            gCallStats.read++;
                            gCallStats.total++;
                            
                            if (gCallStats.read % 20 === 1) {
                                log("[" + getRuntime() + "] 大文件读取 #" + gCallStats.read + 
                                   " (" + bytesRead + " 字节)");
                            }
                        }
                    } catch (e) {
                        // 忽略统计错误
                    }
                }
            });
            
            gHookedFunctions["read"] = true;
            log(" 文件操作监控设置成功");
            return true;
            
        } catch (e) {
            logError("设置文件监控失败: " + e);
            return false;
        }
    }
    
    function setupStatisticsReporter() {
        // 定期报告统计信息，不访问敏感数据
        setInterval(function() {
            try {
                var now = new Date().getTime();
                if (now - gLastReportTime > config.logInterval && gCallStats.total > 0) {
                    log("\n========== 数据流统计报告 ==========");
                    log("运行时间: " + getRuntime());
                    log("zlib解压调用: " + gCallStats.uncompress + " 次");
                    log("大文件读取: " + gCallStats.read + " 次");
                    log("总调用数: " + gCallStats.total + " 次");
                    log("===================================\n");
                    
                    gLastReportTime = now;
                }
            } catch (e) {
                // 忽略报告错误
            }
        }, 2000);
    }
    
    // ==================== 异常处理 ====================
    function setupExceptionHandler() {
        Process.setExceptionHandler(function(exception) {
            try {
                logError("捕获异常: " + exception.type + " @ " + formatAddress(exception.address));
                logError("异常消息: " + (exception.message || "无"));
            } catch (e) {
                // 异常处理中的异常，忽略
            }
            return true;  // 继续执行
        });
    }
    
    // ==================== 主函数 ====================
    function main() {
        log("高德地图数据流监控脚本启动");
        log("模式: 只监控统计，不读取敏感数据");
        log("适用于 Frida 12.9.7，使用 ES5 语法");
        
        // 设置异常处理
        setupExceptionHandler();
        
        try {
            // 延迟设置监控，确保模块加载完成
            setTimeout(function() {
                try {
                    log("开始设置数据流监控...");
                    
                    var zlibSuccess = setupZlibMonitor();
                    var fileSuccess = setupFileMonitor();
                    
                    if (zlibSuccess || fileSuccess) {
                        setupStatisticsReporter();
                        
                        log(" 数据流监控设置完成");
                        log(" 请在地图中移动以观察数据流统计");
                        log(" 脚本将安全监控数据处理过程");
                    } else {
                        logError("监控设置失败");
                    }
                    
                } catch (e) {
                    logError("设置监控失败: " + e);
                }
            }, 3000);
            
        } catch (e) {
            logError("脚本初始化失败: " + e);
        }
        
        log("脚本设置完成，等待初始化...");
    }
    
    // 启动脚本
    main();
})(); 