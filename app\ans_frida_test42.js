// ANS文件处理全链路跟踪脚本 - 专注于nativeAddMapGestureMsg函数
// 简化版本，专门用于跟踪从Java层到Native层的手势消息传递

(function() {
    console.log("[JNI_OnLoad分析-V2] 脚本启动中... (绝对稳定版)");

    // 全局变量
    var libamapr = null;
    var libamapnsq = null;
    var lastGestureTime = 0;
    var nativeAddMapGestureMsgAddr = null;

    // 工具函数 - 兼容ES5的padStart
    function padZero(str, len) {
        str = String(str);
        while (str.length < len) {
            str = '0' + str;
        }
        return str;
    }

    // 等待库加载完成
    console.log("[+] 所有监控已部署。等待应用加载 .so 文件...");

    // 主函数
    setTimeout(function() {
        try {
            setupHooks();
        } catch (e) {
            console.log("[错误] 设置钩子失败: " + e);
        }
    }, 2000);

    function setupHooks() {
        // 查找关键库
        libamapr = Process.findModuleByName("libamapr.so");
        libamapnsq = Process.findModuleByName("libamapnsq.so");

        if (!libamapr || !libamapnsq) {
            console.log("[错误] 未找到所有关键库");
            return;
        }

        console.log("[+] 找到关键库:");
        console.log("- libamapr.so: " + libamapr.base);
        console.log("- libamapnsq.so: " + libamapnsq.base);

        // 设置Java层钩子
        setupJavaHooks();

        // 延迟设置Native钩子
        setTimeout(function() {
            findNativeAddMapGestureMsg();
        }, 1000);
    }

    // 查找nativeAddMapGestureMsg函数
    function findNativeAddMapGestureMsg() {
        console.log("[+] 查找nativeAddMapGestureMsg函数...");

        try {
            // 方法1: 使用JNI_OnLoad函数
            var jniOnLoad = Module.findExportByName("libamapr.so", "JNI_OnLoad");
            if (jniOnLoad) {
                console.log("[+] 找到JNI_OnLoad @ " + jniOnLoad);
                
                // 在JNI_OnLoad周围搜索nativeAddMapGestureMsg字符串
                var searchRange = 0x10000; // 64KB范围
                var pattern = "6e 61 74 69 76 65 41 64 64 4d 61 70 47 65 73 74 75 72 65 4d 73 67"; // "nativeAddMapGestureMsg"的十六进制
                
                Memory.scan(jniOnLoad, searchRange, pattern, {
                    onMatch: function(address, size) {
                        console.log("[+] 找到字符串: nativeAddMapGestureMsg @ " + address);
                        
                        // 查找引用这个字符串的地方
                        var refPattern = address.toString().replace("0x", "");
                        Memory.scan(libamapr.base, libamapr.size, refPattern, {
                            onMatch: function(refAddress, refSize) {
                                console.log("[+] 找到对字符串的引用 @ " + refAddress);
                                
                                // 在引用周围查找函数
                                searchForNativeFunction(refAddress);
                            },
                            onComplete: function() {
                                console.log("[+] 字符串引用搜索完成");
                            }
                        });
                    },
                    onComplete: function() {
                        console.log("[+] 字符串搜索完成");
                        
                        // 如果没找到，尝试方法2
                        if (!nativeAddMapGestureMsgAddr) {
                            console.log("[+] 尝试方法2: 搜索JNI方法表...");
                            searchJNIMethodTable();
                        }
                    }
                });
            } else {
                console.log("[!] 未找到JNI_OnLoad函数");
                searchJNIMethodTable();
            }
        } catch (e) {
            console.log("[错误] 查找nativeAddMapGestureMsg函数失败: " + e);
        }
    }

    // 在引用周围查找函数
    function searchForNativeFunction(refAddress) {
        try {
            // 向上查找可能的函数开始
            var searchStart = refAddress.sub(0x500);
            var searchSize = 0x1000;
            
            console.log("[+] 搜索函数入口点: " + searchStart + " - " + searchStart.add(searchSize));
            
            // 查找常见的ARM64函数序言
            Memory.scan(searchStart, searchSize, "F? 4? BD A9", { // 常见的ARM64函数序言
                onMatch: function(funcAddress, funcSize) {
                    console.log("[+] 可能的函数入口点 @ " + funcAddress);
                    
                    // 设置钩子
                    try {
                        nativeAddMapGestureMsgAddr = funcAddress;
                        hookNativeFunction(funcAddress);
                    } catch (e) {
                        console.log("[错误] 钩住函数失败: " + e);
                    }
                },
                onComplete: function() {
                    console.log("[+] 函数入口点搜索完成");
                }
            });
        } catch (e) {
            console.log("[错误] 搜索函数失败: " + e);
        }
    }

    // 搜索JNI方法表
    function searchJNIMethodTable() {
        try {
            // 在libamapr.so中搜索可能的JNI方法表
            var jniPattern = "00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"; // 可能的空指针序列
            
            Memory.scan(libamapr.base, libamapr.size, jniPattern, {
                onMatch: function(address, size) {
                    // 检查前后是否有函数指针
                    try {
                        var before = Memory.readPointer(address.sub(8));
                        var after = Memory.readPointer(address.add(16));
                        
                        if (before && after) {
                            console.log("[+] 可能的JNI方法表 @ " + address);
                            
                            // 尝试钩住前后的函数
                            hookNativeFunction(before);
                            hookNativeFunction(after);
                        }
                    } catch (e) {
                        // 忽略错误
                    }
                },
                onComplete: function() {
                    console.log("[+] JNI方法表搜索完成");
                }
            });
        } catch (e) {
            console.log("[错误] 搜索JNI方法表失败: " + e);
        }
    }

    // 钩住Native函数
    function hookNativeFunction(address) {
        try {
            Interceptor.attach(address, {
                onEnter: function(args) {
                    // 检查是否在手势事件后调用
                    var timeSinceGesture = Date.now() - lastGestureTime;
                    if (timeSinceGesture < 1000) {
                        console.log("[Native] 可能的nativeAddMapGestureMsg @ " + address);
                        console.log("[Native] 参数: " + 
                            "arg0=" + args[0] + 
                            ", arg1=" + args[1] + 
                            ", arg2=" + args[2] + 
                            ", arg3=" + args[3]);
                        
                        // 记录调用栈
                        console.log("[Native] 调用栈:");
                        var bt = Thread.backtrace(this.context, Backtracer.ACCURATE);
                        bt.forEach(function(addr, i) {
                            console.log("\t" + i + ": " + addr + " " + 
                                DebugSymbol.fromAddress(addr));
                        });
                    }
                },
                onLeave: function(retval) {
                    // 检查是否在手势事件后调用
                    var timeSinceGesture = Date.now() - lastGestureTime;
                    if (timeSinceGesture < 1000) {
                        console.log("[Native] 返回值: " + retval);
                    }
                }
            });
        } catch (e) {
            console.log("[错误] 钩住函数失败: " + e);
        }
    }

    // 设置Java层钩子
    function setupJavaHooks() {
        console.log("[+] 设置Java层钩子...");
        
        Java.perform(function() {
            try {
                // 钩住AMapController类的addGestureMapMessage方法
                var AMapController = Java.use("com.autonavi.ae.gmap.AMapController");
                if (AMapController.addGestureMapMessage) {
                    console.log("[+] 找到AMapController.addGestureMapMessage方法");
                    AMapController.addGestureMapMessage.implementation = function() {
                        console.log("[Java] 调用AMapController.addGestureMapMessage");
                        console.log("[Java] 参数: " + JSON.stringify(arguments));
                        
                        // 打印调用栈
                        console.log("[Java] Java调用栈:");
                        console.log(Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()));
                        
                        lastGestureTime = Date.now();
                        var result = this.addGestureMapMessage.apply(this, arguments);
                        console.log("[Java] AMapController.addGestureMapMessage返回: " + result);
                        return result;
                    };
                } else {
                    console.log("[!] 未找到AMapController.addGestureMapMessage方法");
                }
                
                // 钩住GLMapEngine类的nativeAddMapGestureMsg方法
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine.nativeAddMapGestureMsg) {
                    console.log("[+] 找到GLMapEngine.nativeAddMapGestureMsg方法");
                    
                    // 这是一个native方法，我们不能直接替换它的实现，但可以在调用前后添加日志
                    var nativeAddMapGestureMsg = GLMapEngine.nativeAddMapGestureMsg;
                    GLMapEngine.nativeAddMapGestureMsg.implementation = function() {
                        console.log("[JNI] 调用GLMapEngine.nativeAddMapGestureMsg");
                        console.log("[JNI] 参数: " + JSON.stringify(arguments));
                        
                        // 打印调用栈
                        console.log("[JNI] Java调用栈:");
                        console.log(Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()));
                        
                        lastGestureTime = Date.now();
                        var result = nativeAddMapGestureMsg.apply(this, arguments);
                        console.log("[JNI] GLMapEngine.nativeAddMapGestureMsg返回: " + result);
                        return result;
                    };
                } else {
                    console.log("[!] 未找到GLMapEngine.nativeAddMapGestureMsg方法");
                }
            } catch (e) {
                console.log("[错误] 设置Java钩子失败: " + e);
            }
        });
    }
})(); 