// # Frida脚本：ANS文件解析器 (简化版)

(function() {
    console.log("[ANS解析器-简化版] 启动...");

    var libamapnsq = null;
    var fileDescriptors = {};
    var lastGestureTime = 0;

    // 阶段性执行，降低启动压力
    setTimeout(function() {
        // 仅监控基本文件操作
        setupFileMonitoring();
        
        // 延迟设置更复杂的钩子
        setTimeout(function() {
            setupParserHook();
        }, 3000);
    }, 1000);

    // 设置文件监控
    function setupFileMonitoring() {
        // 监控文件打开
        var openPtr = Module.findExportByName("libc.so", "open");
        if (openPtr) {
            Interceptor.attach(openPtr, {
                onEnter: function(args) {
                    try {
                        this.path = args[0].readUtf8String();
                        if (this.path && (this.path.indexOf("m1.ans") !== -1 || 
                                         this.path.indexOf("m3.ans") !== -1)) {
                            this.isAnsFile = true;
                            console.log("[ANS文件] 打开: " + this.path);
                        }
                    } catch(e) {}
                },
                onLeave: function(result) {
                    if (this.isAnsFile && result.toInt32() > 0) {
                        fileDescriptors[result.toInt32()] = this.path;
                    }
                }
            });
        }
        
        // 简化的读取监控
        var readPtr = Module.findExportByName("libc.so", "read");
        if (readPtr) {
            Interceptor.attach(readPtr, {
                onEnter: function(args) {
                    var fd = args[0].toInt32();
                    if (fileDescriptors[fd]) {
                        this.fd = fd;
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                    }
                },
                onLeave: function(result) {
                    if (this.fd && result.toInt32() > 0) {
                        console.log("[ANS读取] " + fileDescriptors[this.fd].split("/").pop() + 
                                   " 读取 " + result.toInt32() + " 字节到 " + this.buffer);
                    }
                }
            });
        }
        
        console.log("[ANS文件] 基础监控已设置");
    }
    
    // 设置解析函数钩子
    function setupParserHook() {
        libamapnsq = Process.findModuleByName("libamapnsq.so");
        if (!libamapnsq) {
            console.log("[ANS解析器] 未找到libamapnsq.so");
            return;
        }
        
        console.log("[ANS解析器] 已找到libamapnsq.so: " + libamapnsq.base);
        
        // 只钩住主解析函数
        var parserFuncOffset = 0xC654;
        var parserFuncAddr = libamapnsq.base.add(parserFuncOffset);
        
        Interceptor.attach(parserFuncAddr, {
            onEnter: function(args) {
                console.log("[ANS] 调用解析函数: " + parserFuncAddr);
                this.a1 = args[0];  // 源数据
                this.a2 = args[1];  // 目标缓冲区
                this.a3 = args[2];  // 大小指针
            },
            onLeave: function(retval) {
                console.log("[ANS] 解析函数返回: " + retval);
            }
        });
        
        console.log("[ANS解析器] 解析函数钩子设置完成");
    }
    
    console.log("[ANS解析器-简化版] 设置完成");
})();
