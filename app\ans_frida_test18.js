// ans_frida_data_flow.js - 专注于.so层返回Java层的数据流
setTimeout(function() {
  console.log("[ANS数据流监控] 正在准备...");
  
  // 防止崩溃的基础hook
  var libc_exit = Module.findExportByName("libc.so", "exit");
  if (libc_exit) {
    Interceptor.replace(libc_exit, new NativeCallback(function() {
      return 0;
    }, 'void', ['int']));
  }
  
  // 1. 只监控ANS文件打开
  var open_ptr = Module.findExportByName("libc.so", "open");
  if (open_ptr) {
    Interceptor.attach(open_ptr, {
      onEnter: function(args) {
        try {
          var path = args[0].readUtf8String();
          if (path && path.indexOf(".ans") !== -1) {
            this.path = path;
          }
        } catch(e) {}
      },
      onLeave: function(retval) {
        if (this.path) {
          this.fd = retval.toInt32();
          console.log("[ANS文件] " + this.path + " => fd: " + this.fd);
        }
      }
    });
  }
  
  // 2. 监控读取ANS数据 - 仅显示实际数据块
  var read_ptr = Module.findExportByName("libc.so", "read");
  if (read_ptr) {
    Interceptor.attach(read_ptr, {
      onEnter: function(args) {
        this.fd = args[0].toInt32();
        this.buffer = args[1];
        this.size = args[2].toInt32();
      },
      onLeave: function(retval) {
        try {
          var bytesRead = retval.toInt32();
          if (bytesRead > 0 && this.fd > 10) {
            // 检查是否是ANS相关数据
            var data = Memory.readByteArray(this.buffer, Math.min(16, bytesRead));
            var isANSData = false;
            
            // 检查zlib压缩头部
            if (data[0] === 0x78 && (data[1] === 0x9C || data[1] === 0x01 || data[1] === 0xDA)) {
              isANSData = true;
            }
            
            if (isANSData) {
              console.log("[SO → Java] 读取ANS数据: fd=" + this.fd + ", 大小=" + bytesRead + "字节");
              var hexHeader = "";
              for (var i = 0; i < Math.min(data.byteLength, 16); i++) {
                var hex = data[i].toString(16);
                if (hex.length == 1) hex = "0" + hex;
                hexHeader += hex + " ";
              }
              console.log("  头部数据: " + hexHeader);
            }
          }
        } catch(e) {}
      }
    });
  }
  
  // 延迟执行Java层hooks
  setTimeout(function() {
    Java.perform(function() {
      console.log("[Java层] 开始监控SO层向Java层的数据返回");
      
      // 监控SO返回资源加载器
      try {
        var InterfaceAppImpl = Java.use("com.amap.jni.app.InterfaceAppImpl");
        if (InterfaceAppImpl.getNativeResourceLoader) {
          InterfaceAppImpl.getNativeResourceLoader.implementation = function() {
            var result = this.getNativeResourceLoader();
            console.log("[SO → Java] getNativeResourceLoader 返回指针: " + result);
            return result;
          };
        }
      } catch(e) {}
      
      // 监控渲染引擎回调
      try {
        var CoreCallback = Java.use("com.autonavi.ae.gmap.AMapController$ControllerMapCoreCallback");
        
        // 监控资源请求
        if (CoreCallback.OnRequireMapCloudResource) {
          CoreCallback.OnRequireMapCloudResource.implementation = function(engineId, urlPath, localPath, reqId, taskId) {
            if (localPath && localPath.indexOf(".ans") !== -1) {
              console.log("[SO → Java] 请求地图资源: " + localPath);
            }
            return this.OnRequireMapCloudResource(engineId, urlPath, localPath, reqId, taskId);
          };
        }
        
        // 监控回调接口
        if (CoreCallback.OnRenderStatisticsInfo) {
          CoreCallback.OnRenderStatisticsInfo.implementation = function(info) {
            if (info && info.indexOf("ans") !== -1) {
              console.log("[SO → Java] 渲染统计: " + info);
            }
            return this.OnRenderStatisticsInfo(info);
          };
        }
      } catch(e) {}
      
      // 监控Java层文件读取 - 仅ANS文件
      try {
        var FileInputStream = Java.use("java.io.FileInputStream");
        
        // 监控构造函数以识别文件
        FileInputStream.$init.overload("java.lang.String").implementation = function(path) {
          var result = this.$init(path);
          if (path && path.indexOf(".ans") !== -1) {
            console.log("[Java] 打开ANS文件: " + path);
          }
          return result;
        };
        
        // 监控读取方法
        FileInputStream.read.overload("[B").implementation = function(buffer) {
          var result = this.read(buffer);
          try {
            if (result > 0) {
              var str = this.toString();
              if (str && str.indexOf(".ans") !== -1) {
                console.log("[SO → Java] ANS数据读取: " + result + "字节 (" + str + ")");
              }
            }
          } catch(e) {}
          return result;
        };
      } catch(e) {}
    });
  }, 1500);
  
}, 500);
