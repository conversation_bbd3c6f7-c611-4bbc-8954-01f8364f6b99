// 数据流分析脚本 - 基于实际日志验证
// 验证m1.ans和m3.ans文件的处理流程

console.log("[数据流分析] 开始分析离线地图数据处理流程...");

// 数据流统计
var dataFlow = {
    ansFileReads: 0,           // .ans文件读取次数
    zlibDecompressions: 0,     // zlib解压次数
    diceAmData: 0,             // DICE-AM矢量数据
    textData: 0,               // 0x25xx文本数据
    configData: 0,             // bc bc bc bc配置数据
    sqliteBindings: 0,         // SQLite数据绑定
    renderCalls: 0             // 渲染调用
};

// 数据类型识别函数
function identifyDataType(header) {
    var view = new Uint8Array(header);
    
    if (view.length >= 4) {
        // DICE-AM矢量数据
        if (view[0] === 0x44 && view[1] === 0x49 && view[2] === 0x43 && view[3] === 0x45) {
            return "DICE-AM";
        }
        // 配置数据
        if (view[0] === 0xbc && view[1] === 0xbc && view[2] === 0xbc && view[3] === 0xbc) {
            return "CONFIG";
        }
        // 文本/属性数据
        if (view[0] === 0x00 && view[1] === 0x00 && view[2] === 0x25) {
            return "TEXT";
        }
        // zlib压缩头
        if (view[0] === 0x78 && view[1] === 0x9c) {
            return "ZLIB";
        }
        // AM-zlib容器
        if (view[0] === 0x08) {
            return "AM-ZLIB";
        }
    }
    return "UNKNOWN";
}

// 字节转十六进制 (ES5兼容)
function bytesToHex(byteArray, maxLen) {
    var hex = [];
    var len = Math.min(maxLen || 8, byteArray.length);
    for (var i = 0; i < len; i++) {
        var h = byteArray[i].toString(16);
        if (h.length === 1) h = '0' + h;
        hex.push(h);
    }
    return hex.join(' ');
}

// 1. 验证库加载
var libamapnsq = null;
var libz = null;
var libc = null;

try {
    libamapnsq = Process.getModuleByName("libamapnsq.so");
    console.log("[✓] libamapnsq.so 已加载");
} catch (e) {
    console.log("[✗] libamapnsq.so 未找到");
}

try {
    libz = Process.getModuleByName("libz.so");
    console.log("[✓] libz.so 已加载");
} catch (e) {
    console.log("[✗] libz.so 未找到");
}

try {
    libc = Process.getModuleByName("libc.so");
    console.log("[✓] libc.so 已加载");
} catch (e) {
    console.log("[✗] libc.so 未找到");
}

// 2. Hook文件读取 - 专门监控.ans文件
if (libc) {
    var readPtr = libc.getExportByName("read");
    if (readPtr) {
        Interceptor.attach(readPtr, {
            onEnter: function(args) {
                this.fd = args[0].toInt32();
                this.buf = args[1];
                this.count = args[2].toInt32();
            },
            onLeave: function(retval) {
                var bytesRead = retval.toInt32();
                if (bytesRead > 0 && this.count >= 8) {
                    try {
                        var header = this.buf.readByteArray(Math.min(8, bytesRead));
                        if (header) {
                            var dataType = identifyDataType(header);
                            
                            // 检查是否是.ans文件相关数据
                            if (dataType === "AM-ZLIB" || dataType === "ZLIB") {
                                dataFlow.ansFileReads++;
                                console.log("[数据流] .ans文件读取 #" + dataFlow.ansFileReads);
                                console.log("  文件描述符: " + this.fd);
                                console.log("  读取大小: " + bytesRead + " 字节");
                                console.log("  数据类型: " + dataType);
                                console.log("  数据头部: " + bytesToHex(new Uint8Array(header)));
                                
                                // 分析读取模式
                                if (this.count === 4096) {
                                    console.log("  [模式] 标准4KB块读取");
                                } else if (this.count === 8192) {
                                    console.log("  [模式] 标准8KB块读取");
                                } else if (this.count === 131072) {
                                    console.log("  [模式] 大块128KB批量读取");
                                } else if (this.count < 1024) {
                                    console.log("  [模式] 小块压缩数据读取");
                                }
                            }
                        }
                    } catch (e) {
                        // 忽略内存读取错误
                    }
                }
            }
        });
        console.log("[✓] 文件读取Hook设置成功");
    }
}

// 3. Hook zlib解压 - 监控数据解压过程
if (libz) {
    var uncompressPtr = libz.getExportByName("uncompress");
    if (uncompressPtr) {
        Interceptor.attach(uncompressPtr, {
            onEnter: function(args) {
                this.dest = args[0];
                this.destLen = args[1];
                this.source = args[2];
                this.sourceLen = args[3];
                this.srcSize = this.sourceLen.readU32();
                this.dstSize = this.destLen.readU32();
            },
            onLeave: function(retval) {
                if (retval.toInt32() === 0) {
                    dataFlow.zlibDecompressions++;
                    var decompressedSize = this.destLen.readU32();
                    var compressionRatio = (this.srcSize / decompressedSize * 100).toFixed(1);
                    
                    console.log("[数据流] zlib解压 #" + dataFlow.zlibDecompressions);
                    console.log("  压缩前: " + this.srcSize + " 字节");
                    console.log("  解压后: " + decompressedSize + " 字节");
                    console.log("  压缩比: " + compressionRatio + "%");
                    
                    // 检查解压后的数据类型
                    try {
                        var decompressedHeader = this.dest.readByteArray(Math.min(8, decompressedSize));
                        if (decompressedHeader) {
                            var dataType = identifyDataType(decompressedHeader);
                            console.log("  解压数据类型: " + dataType);
                            console.log("  解压数据头部: " + bytesToHex(new Uint8Array(decompressedHeader)));
                            
                            // 统计数据类型
                            if (dataType === "DICE-AM") {
                                dataFlow.diceAmData++;
                                console.log("  [发现] DICE-AM矢量数据 #" + dataFlow.diceAmData);
                            } else if (dataType === "TEXT") {
                                dataFlow.textData++;
                                console.log("  [发现] 文本/属性数据 #" + dataFlow.textData);
                            } else if (dataType === "CONFIG") {
                                dataFlow.configData++;
                                console.log("  [发现] 配置数据 #" + dataFlow.configData);
                            }
                        }
                    } catch (e) {
                        // 忽略内存读取错误
                    }
                }
            }
        });
        console.log("[✓] zlib解压Hook设置成功");
    }
}

// 4. Hook SQLite数据绑定 - 监控数据存储
if (libamapnsq) {
    var bindBlobAddr = libamapnsq.base.add(0x15000);
    try {
        Interceptor.attach(bindBlobAddr, {
            onEnter: function(args) {
                dataFlow.sqliteBindings++;
                console.log("[数据流] SQLite数据绑定 #" + dataFlow.sqliteBindings);
                console.log("  语句: " + args[0]);
                console.log("  索引: " + args[1]);
                console.log("  数据大小: " + args[3] + " 字节");
                
                // 分析绑定的数据类型
                if (args[2] && args[3].toInt32() > 0) {
                    try {
                        var size = Math.min(args[3].toInt32(), 8);
                        var data = args[2].readByteArray(size);
                        if (data) {
                            var dataType = identifyDataType(data);
                            console.log("  绑定数据类型: " + dataType);
                        }
                    } catch (e) {
                        // 忽略内存读取错误
                    }
                }
            }
        });
        console.log("[✓] SQLite绑定Hook设置成功");
    } catch (e) {
        console.log("[警告] SQLite绑定Hook失败: " + e.message);
    }
}

// 5. Hook OpenGL渲染 - 监控最终渲染
try {
    var libGLESv2 = Process.getModuleByName("libGLESv2.so");
    if (libGLESv2) {
        var glDrawArraysPtr = libGLESv2.getExportByName("glDrawArrays");
        if (glDrawArraysPtr) {
            Interceptor.attach(glDrawArraysPtr, {
                onEnter: function(args) {
                    dataFlow.renderCalls++;
                    if (dataFlow.renderCalls % 20 === 1) { // 每20次记录一次
                        console.log("[数据流] OpenGL渲染调用 #" + dataFlow.renderCalls);
                        console.log("  模式: " + args[0] + ", 顶点数: " + args[2]);
                    }
                }
            });
        }
        console.log("[✓] OpenGL渲染Hook设置成功");
    }
} catch (e) {
    console.log("[警告] OpenGL Hook失败: " + e.message);
}

// 6. 定期输出数据流统计
setInterval(function() {
    console.log("\n[数据流统计] ==========================================");
    console.log(".ans文件读取: " + dataFlow.ansFileReads);
    console.log("zlib解压操作: " + dataFlow.zlibDecompressions);
    console.log("DICE-AM矢量数据: " + dataFlow.diceAmData);
    console.log("文本/属性数据: " + dataFlow.textData);
    console.log("配置数据: " + dataFlow.configData);
    console.log("SQLite数据绑定: " + dataFlow.sqliteBindings);
    console.log("OpenGL渲染调用: " + dataFlow.renderCalls);
    
    // 计算数据处理效率
    if (dataFlow.ansFileReads > 0 && dataFlow.zlibDecompressions > 0) {
        var efficiency = (dataFlow.zlibDecompressions / dataFlow.ansFileReads * 100).toFixed(1);
        console.log("数据处理效率: " + efficiency + "% (解压/读取比)");
    }
    console.log("===============================================\n");
}, 12000);

console.log("[数据流分析] 分析脚本已启动，等待地图操作...");
console.log("[提示] 请移动地图以触发m1.ans和m3.ans文件的数据处理流程");
