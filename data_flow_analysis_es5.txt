     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[数据流分析] 开始分析离线地图数据处理流程...
[\u2717] libamapnsq.so 未找到
[\u2713] libz.so 已加载
[\u2713] libc.so 已加载
[\u2713] 文件读取Hook设置成功
[\u2713] zlib解压Hook设置成功
[\u2713] OpenGL渲染Hook设置成功
[数据流分析] 分析脚本已启动，等待地图操作...
[提示] 请移动地图以触发m1.ans和m3.ans文件的数据处理流程
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [数据流] .ans文件读取 #1
  文件描述符: 43
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 04 05 06 04 04 05 04
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #2
  文件描述符: 43
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 07 05 03 04 05 07 08
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #3
  文件描述符: 43
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 03 06 47 36 41 07 03
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #4
  文件描述符: 43
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 23 64 15 06 06 0a 02
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #5
  文件描述符: 114
  读取大小: 531 字节
  数据类型: ZLIB
  数据头部: 78 9c 73 f1 74 76 d5 75
  [模式] 小块压缩数据读取
Error: access violation accessing 0x213
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #1
  压缩前: undefined 字节
  解压后: 8192 字节
  压缩比: NaN%
  解压数据类型: DICE-AM
  解压数据头部: 44 49 43 45 2d 41 4d 00
  [发现] DICE-AM矢量数据 #1
[数据流] .ans文件读取 #6
  文件描述符: 114
  读取大小: 531 字节
  数据类型: ZLIB
  数据头部: 78 9c 73 f1 74 76 d5 75
  [模式] 小块压缩数据读取
Error: access violation accessing 0x213
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #2
  压缩前: undefined 字节
  解压后: 8192 字节
  压缩比: NaN%
  解压数据类型: DICE-AM
  解压数据头部: 44 49 43 45 2d 41 4d 00
  [发现] DICE-AM矢量数据 #2
[数据流] .ans文件读取 #7
  文件描述符: 114
  读取大小: 139 字节
  数据类型: ZLIB
  数据头部: 78 9c ed ca 31 0a c2 30
  [模式] 小块压缩数据读取
Error: access violation accessing 0x8b
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #3
  压缩前: undefined 字节
  解压后: 8192 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: 0d 00 00 00 04 1f a2 00
[数据流] .ans文件读取 #8
  文件描述符: 114
  读取大小: 531 字节
  数据类型: ZLIB
  数据头部: 78 9c 73 f1 74 76 d5 75
  [模式] 小块压缩数据读取
Error: access violation accessing 0x213
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #4
  压缩前: undefined 字节
  解压后: 8192 字节
  压缩比: NaN%
  解压数据类型: DICE-AM
  解压数据头部: 44 49 43 45 2d 41 4d 00
  [发现] DICE-AM矢量数据 #3
Error: access violation accessing 0x9e6
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #5
  压缩前: undefined 字节
  解压后: 4572 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #1
Error: access violation accessing 0x950
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #6
  压缩前: undefined 字节
  解压后: 3920 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #2
Error: access violation accessing 0x64f
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #7
  压缩前: undefined 字节
  解压后: 12136 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
Error: access violation accessing 0xb61
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #8
  压缩前: undefined 字节
  解压后: 4700 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #3
Error: access violation accessing 0x60c
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] OpenGL渲染调用 #1
  模式: 0x5, 顶点数: 0x4
[数据流] zlib解压 #9
  压缩前: undefined 字节
  解压后: 2684 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #4
Error: access violation accessing 0x5e6
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #10
  压缩前: undefined 字节
  解压后: 10408 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
Error: access violation accessing 0xc2d
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #11
  压缩前: undefined 字节
  解压后: 5300 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #5
Error: access violation accessing 0x6f2
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #12
  压缩前: undefined 字节
  解压后: 3096 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #6
Error: access violation accessing 0x86a
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #13
  压缩前: undefined 字节
  解压后: 11712 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
Error: access violation accessing 0xc35
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #14
  压缩前: undefined 字节
  解压后: 5304 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #7
Error: access violation accessing 0x77f
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #15
  压缩前: undefined 字节
  解压后: 3336 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #8
Error: access violation accessing 0x883
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #16
  压缩前: undefined 字节
  解压后: 11760 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
Error: access violation accessing 0xc35
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #17
  压缩前: undefined 字节
  解压后: 5304 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #9
Error: access violation accessing 0x874
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #18
  压缩前: undefined 字节
  解压后: 3712 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #10
Error: access violation accessing 0x8b8
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #19
  压缩前: undefined 字节
  解压后: 12024 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
Error: access violation accessing 0xdc9
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #20
  压缩前: undefined 字节
  解压后: 6304 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #11
Error: access violation accessing 0x800
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #21
  压缩前: undefined 字节
  解压后: 3612 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #12
Error: access violation accessing 0x9af
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #22
  压缩前: undefined 字节
  解压后: 13288 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
Error: access violation accessing 0xd9c
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #23
  压缩前: undefined 字节
  解压后: 5924 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #13
Error: access violation accessing 0x979
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #24
  压缩前: undefined 字节
  解压后: 4372 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #14
Error: access violation accessing 0x910
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #25
  压缩前: undefined 字节
  解压后: 13144 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
Error: access violation accessing 0xc2d
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #26
  压缩前: undefined 字节
  解压后: 5300 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #15
Error: access violation accessing 0x6f2
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #27
  压缩前: undefined 字节
  解压后: 3096 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #16
Error: access violation accessing 0x86a
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #28
  压缩前: undefined 字节
  解压后: 11712 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
Error: access violation accessing 0xb61
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #29
  压缩前: undefined 字节
  解压后: 4700 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #17
Error: access violation accessing 0x60c
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #30
  压缩前: undefined 字节
  解压后: 2684 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #18
Error: access violation accessing 0x5e6
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #31
  压缩前: undefined 字节
  解压后: 10408 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
Error: access violation accessing 0x1747
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #32
  压缩前: undefined 字节
  解压后: 10664 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #19
Error: access violation accessing 0xcc1
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #33
  压缩前: undefined 字节
  解压后: 6340 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #20
Error: access violation accessing 0xe8f
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #34
  压缩前: undefined 字节
  解压后: 17480 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
[数据流] zlib解压 #35
  压缩前: undefined 字节
  解压后: 10200 字节
  压缩比: NaN%
Error: access violation accessing 0x145d
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #21
Error: access violation accessing 0xe81
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #36
  压缩前: undefined 字节
  解压后: 6748 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #22
[数据流] zlib解压 #37
  压缩前: undefined 字节
Error: access violation accessing 0xc80
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
  解压后: 17664 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
Error: access violation accessing 0x869
    at frida/runtime/core.js:144
[数据流] zlib解压 #38
    at /data_flow_analysis_es5.js:143
  压缩前: undefined 字节
  解压后: 3436 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #23
[数据流] zlib解压 #39
  压缩前: undefined 字节
  解压后: 2972 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #24
Error: access violation accessing 0x707
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #40
Error: access violation accessing 0x4d1
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
  压缩前: undefined 字节
  解压后: 9136 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
[数据流] .ans文件读取 #9
  文件描述符: 152
  读取大小: 8192 字节
  数据类型: AM-ZLIB
  数据头部: 08 cf 0d 3c 63 fa 59 bd
  [模式] 标准8KB块读取
[数据流] .ans文件读取 #10
  文件描述符: 148
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 cf 0d 3c 63 fa 59 bd
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #11
  文件描述符: 160
  读取大小: 8192 字节
  数据类型: AM-ZLIB
  数据头部: 08 00 00 00 5b 00 00 00
  [模式] 标准8KB块读取
Error: access violation accessing 0xe32
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #41
  压缩前: undefined 字节
  解压后: 6440 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #25
Error: access violation accessing 0xaba
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #42
  压缩前: undefined 字节
  解压后: 5332 字节
  压缩比: NaN%
  解压数据类型: CONFIG
  解压数据头部: bc bc bc bc 02 00 01 00
  [发现] 配置数据 #26
Error: access violation accessing 0xaa7
    at frida/runtime/core.js:144
    at /data_flow_analysis_es5.js:143
[数据流] zlib解压 #43
  压缩前: undefined 字节
  解压后: 14952 字节
  压缩比: NaN%
  解压数据类型: UNKNOWN
  解压数据头部: ce ca 0b b1 02 00 01 00
[数据流] .ans文件读取 #12
  文件描述符: 172
  读取大小: 8192 字节
  数据类型: AM-ZLIB
  数据头部: 08 00 00 00 1b 00 00 00
  [模式] 标准8KB块读取
[数据流] OpenGL渲染调用 #21
  模式: 0x3, 顶点数: 0x16a
[数据流] .ans文件读取 #13
  文件描述符: 160
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 00 00 00 1b 00 00 00
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #14
  文件描述符: 166
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 00 00 9b 00 e2 5a ce
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #15
  文件描述符: 166
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 00 00 9b 00 e2 5a ce
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #16
  文件描述符: 172
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 fb c4 a8 2e 7b e8 b8
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #17
  文件描述符: 160
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 70 0d 3b cb 67 4b bd
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #18
  文件描述符: 171
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 cf 0d 3c 63 fa 59 bd
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #19
  文件描述符: 169
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 c3 db 4a 34 a4 04 ee
  [模式] 标准4KB块读取
[数据流] OpenGL渲染调用 #41
  模式: 0x3, 顶点数: 0x16a
[数据流] .ans文件读取 #20
  文件描述符: 171
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 00 00 00 1b 00 00 00
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #21
  文件描述符: 98
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 21 a4 fa 66 d8 9b bf
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #22
  文件描述符: 166
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 01 72 65 73 2f 6c 61
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #23
  文件描述符: 166
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 01 72 65 73 2f 6c 61
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #24
  文件描述符: 171
  读取大小: 4096 字节
  数据类型: AM-ZLIB
  数据头部: 08 70 0d 3b cb 67 4b bd
  [模式] 标准4KB块读取
[数据流] .ans文件读取 #25
  文件描述符: 203
  读取大小: 8192 字节
  数据类型: AM-ZLIB
  数据头部: 08 21 96 3d ac 5b 30 3d
  [模式] 标准8KB块读取
[数据流] .ans文件读取 #26
  文件描述符: 203
  读取大小: 8192 字节
  数据类型: AM-ZLIB
  数据头部: 08 df 9e bd 5f d0 22 3e
  [模式] 标准8KB块读取
Server terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=000002F346A37620)

Thread 0x00003148 (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x000061f0 (most recent call first):
<no Python frame>
