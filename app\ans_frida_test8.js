(function() {
  console.log("[高德地图ANS文件分析] 脚本启动 ---");
  
  // 全局变量
  var ansFilePaths = [];
  var hookComplete = false;
  
  // 防止应用崩溃 - 使用ans_frida_test.js的可行方法
  var exit_ptr = Module.findExportByName("libc.so", "exit");
  if (exit_ptr) {
    Interceptor.replace(exit_ptr, new NativeCallback(function() {
      console.log("[+] 拦截exit()调用");
      return 0;
    }, 'void', ['int']));
  }
  
  // 轻量级文件监控 - 只跟踪ANS文件
  var open_ptr = Module.findExportByName("libc.so", "open");
  if (open_ptr) {
    Interceptor.attach(open_ptr, {
      onEnter: function(args) {
        try {
          var path = args[0].readUtf8String();
          if (path && path.endsWith(".ans")) {
            this.isAnsFile = true;
            this.path = path;
          }
        } catch (e) {}
      },
      onLeave: function(retval) {
        if (this.isAnsFile && retval.toInt32() > 0) {
          console.log("[ANS文件] 打开: " + this.path);
          
          // 记录到全局变量
          if (ansFilePaths.indexOf(this.path) === -1) {
            ansFilePaths.push(this.path);
          }
          
          // ANS文件被打开后，延迟启动Java分析
          if (!hookComplete) {
            hookComplete = true;
            setTimeout(setupJavaHooks, 3000);
          }
        }
      }
    });
    console.log("[+] 轻量级监控open()调用成功");
  }
  
  // Java层Hook设置函数 - 在ANS文件被打开后延迟执行
  function setupJavaHooks() {
    Java.perform(function() {
      try {
        console.log("[+] Java层分析开始");
        
        // 1. 监控AmapCompat类
        try {
          var AmapCompat = Java.use("com.autonavi.minimap.offline.nativesupport.AmapCompat");
          if (AmapCompat) {
            console.log("[类] 找到AmapCompat");
            
            // 列出方法
            var methods = AmapCompat.class.getDeclaredMethods();
            console.log("[AmapCompat方法列表]:");
            for (var i = 0; i < methods.length; i++) {
              var methodName = methods[i].getName();
              console.log("  " + methodName);
              
              // 监控关键方法
              if (methodName === "onDatabaseBackup" || 
                  methodName === "onDatabaseCheckAndUpgrade") {
                (function(name) {
                  AmapCompat[name].implementation = function() {
                    console.log("[Java调用] AmapCompat." + name);
                    var result = this[name].apply(this, arguments);
                    console.log("[Java返回] AmapCompat." + name + " 完成");
                    return result;
                  };
                  console.log("[+] 监控" + name + "成功");
                })(methodName);
              }
            }
          }
        } catch(e) {
          console.log("[错误] AmapCompat分析失败: " + e);
        }
        
        // 2. 监控AMapController类
        try {
          var AMapController = Java.use("com.autonavi.ae.gmap.AMapController");
          console.log("[类] 找到AMapController");
          
          // 监控setMapCenter方法
          if (AMapController.setMapCenter) {
            AMapController.setMapCenter.implementation = function() {
              console.log("[Java调用] AMapController.setMapCenter");
              var result = this.setMapCenter.apply(this, arguments);
              return result;
            };
            console.log("[+] 监控setMapCenter成功");
          }
        } catch(e) {
          console.log("[错误] AMapController分析失败: " + e);
        }
        
        // 3. 监控AjxBLFactoryController类
        try {
          var AjxBLFactoryController = Java.use("com.autonavi.jni.ajx3.bl.AjxBLFactoryController");
          console.log("[类] 找到AjxBLFactoryController");
          
          // 监控初始化方法
          var targetMethods = ["init4WarmStart", "nativeInit4WarmStart"];
          for (var i = 0; i < targetMethods.length; i++) {
            var methodName = targetMethods[i];
            try {
              (function(name) {
                if (AjxBLFactoryController[name]) {
                  AjxBLFactoryController[name].implementation = function() {
                    console.log("[Java调用] AjxBLFactoryController." + name + "(" + 
                                JSON.stringify(arguments) + ")");
                    var result = this[name].apply(this, arguments);
                    console.log("[Java返回] AjxBLFactoryController." + name + 
                                " = " + result);
                    return result;
                  };
                  console.log("[+] 监控" + name + "成功");
                }
              })(methodName);
            } catch(e) {
              console.log("[错误] 监控" + methodName + "失败: " + e);
            }
          }
        } catch(e) {
          console.log("[错误] AjxBLFactoryController分析失败: " + e);
        }
        
        // 4. 监控文件操作
        try {
          // FileInputStream
          var FileInputStream = Java.use("java.io.FileInputStream");
          FileInputStream.$init.overload('java.io.File').implementation = function(file) {
            var path = file.getAbsolutePath();
            if (path && path.endsWith(".ans")) {
              console.log("[Java文件] FileInputStream: " + path);
            }
            return this.$init(file);
          };
          console.log("[+] 监控FileInputStream成功");
          
          // RandomAccessFile
          var RandomAccessFile = Java.use("java.io.RandomAccessFile");
          RandomAccessFile.$init.overload('java.lang.String', 'java.lang.String').implementation = function(path, mode) {
            if (path && path.endsWith(".ans")) {
              console.log("[Java文件] RandomAccessFile: " + path + ", 模式: " + mode);
            }
            return this.$init(path, mode);
          };
          console.log("[+] 监控RandomAccessFile成功");
        } catch(e) {
          console.log("[错误] 文件操作监控失败: " + e);
        }
        
        // 5. 监控系统库加载
        try {
          var System = Java.use("java.lang.System");
          System.loadLibrary.implementation = function(libName) {
            console.log("[Java调用] System.loadLibrary(" + libName + ")");
            return this.loadLibrary(libName);
          };
          console.log("[+] 监控System.loadLibrary成功");
        } catch(e) {
          console.log("[错误] System.loadLibrary监控失败: " + e);
        }
        
        console.log("[+] Java层分析完成");
      } catch(e) {
        console.log("[错误] Java层分析异常: " + e);
      }
    });
  }

  // 如果15秒后还没检测到ANS文件，强制启动Java Hook
  setTimeout(function() {
    if (!hookComplete) {
      console.log("[超时] 未检测到ANS文件，强制启动Java层分析");
      setupJavaHooks();
    }
  }, 15000);
  
  console.log("[高德地图ANS文件Java层分析] 脚本设置完成 ---");
})();
