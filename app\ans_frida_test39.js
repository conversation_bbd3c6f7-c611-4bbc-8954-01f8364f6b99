// ANS文件处理流程全链路跟踪脚本 - 稳定版

(function() {
    console.log("[ANS流程分析] 启动...");

    // 全局变量
    var libamapnsq = null;
    var libamapr = null;
    var libamapmain = null;
    var libz = null;
    var fileOperations = {};
    var lastAnsData = null;
    var javaToNativeCalls = [];
    var callDepth = 0;

    // 延迟执行，确保应用完全启动
    setTimeout(function() {
        checkAndSetupHooks();
    }, 3000);

    function checkAndSetupHooks() {
        try {
            // 查找关键库
            libamapnsq = Process.findModuleByName("libamapnsq.so");
            libamapr = Process.findModuleByName("libamapr.so");
            libamapmain = Process.findModuleByName("libamapmain.so");
            libz = Process.findModuleByName("libz.so");
            
            if (!libamapnsq || !libamapr || !libamapmain || !libz) {
                console.log("[ANS流程分析] 未找到所有关键库，稍后重试");
                setTimeout(checkAndSetupHooks, 1000);
                return;
            }
            
            console.log("[ANS流程分析] 已找到关键库:");
            console.log("  libamapnsq.so: " + libamapnsq.base);
            console.log("  libamapr.so: " + libamapr.base);
            console.log("  libamapmain.so: " + libamapmain.base);
            console.log("  libz.so: " + libz.base);
            
            // 设置各种钩子
            setupFileOperationHooks();
            setupANSParserHooks();
            setupJavaHooks();
            
            // 等待一段时间后再设置JNI和渲染钩子，降低启动压力
            setTimeout(function() {
                setupJNIHooks();
                setupRenderHooks();
            }, 5000);
            
            console.log("[ANS流程分析] 钩子设置完成，等待操作...");
        } catch(e) {
            console.log("[ANS流程分析] 设置钩子失败: " + e.message);
        }
    }
    
    function setupFileOperationHooks() {
        console.log("[ANS流程分析] 设置文件操作钩子...");
        
        // 钩住open系统调用
        Interceptor.attach(Module.findExportByName(null, "open"), {
            onEnter: function(args) {
                try {
                    var path = args[0].readUtf8String();
                    if (path && path.indexOf(".ans") !== -1) {
                        this.path = path;
                        this.isAnsFile = true;
                        this.backtrace = Thread.backtrace(this.context, Backtracer.ACCURATE).slice(0, 3);
                        console.log("[文件操作] 打开ANS文件: " + path);
                    }
                } catch(e) {
                    console.log("[文件操作] open onEnter错误: " + e.message);
                }
            },
            onLeave: function(retval) {
                try {
                    if (this.isAnsFile) {
                        var fd = retval.toInt32();
                        if (fd > 0) {
                            fileOperations[fd] = {
                                path: this.path,
                                openTime: new Date().getTime(),
                                size: 0,
                                blocks: []
                            };
                            console.log("[文件操作] ANS文件打开成功，文件描述符: " + fd);
                            
                            // 输出调用栈
                            if (this.backtrace && this.backtrace.length > 0) {
                                console.log("[文件操作] 调用栈:");
                                for (var i = 0; i < this.backtrace.length; i++) {
                                    console.log("  " + i + ": " + this.backtrace[i]);
                                }
                            }
                        }
                    }
                } catch(e) {
                    console.log("[文件操作] open onLeave错误: " + e.message);
                }
            }
        });
        
        // 钩住read系统调用
        Interceptor.attach(Module.findExportByName(null, "read"), {
            onEnter: function(args) {
                try {
                    var fd = args[0].toInt32();
                    this.fd = fd;
                    this.buffer = args[1];
                    this.size = args[2].toInt32();
                    
                    if (fileOperations[fd]) {
                        this.isAnsFile = true;
                    }
                } catch(e) {
                    console.log("[文件操作] read onEnter错误: " + e.message);
                }
            },
            onLeave: function(retval) {
                try {
                    var bytesRead = retval.toInt32();
                    
                    if (this.isAnsFile && bytesRead > 0) {
                        var fileOp = fileOperations[this.fd];
                        if (!fileOp) return; // 防止undefined错误
                        
                        fileOp.size += bytesRead;
                        
                        // 记录数据块
                        var blockInfo = {
                            offset: fileOp.size - bytesRead,
                            size: bytesRead
                        };
                        
                        // 记录数据头部
                        try {
                            if (bytesRead >= 4) {
                                var headerBytes = Memory.readByteArray(this.buffer, Math.min(16, bytesRead));
                                var header = new Uint8Array(headerBytes);
                                var headerHex = "";
                                
                                for (var i = 0; i < header.length; i++) {
                                    var byteHex = header[i].toString(16);
                                    headerHex += (byteHex.length === 1 ? "0" + byteHex : byteHex) + " ";
                                }
                                
                                blockInfo.header = headerHex;
                                
                                // 检测块类型
                                if (header[0] === 0x0A || header[0] === 0x0D || header[0] === 0x05) {
                                    blockInfo.type = header[0];
                                }
                                
                                console.log("[文件操作] 读取ANS文件 fd=" + this.fd + 
                                           ", 大小=" + bytesRead + 
                                           ", 头部=" + headerHex);
                            }
                        } catch(e) {
                            console.log("[文件操作] 读取头部失败: " + e.message);
                        }
                        
                        fileOp.blocks.push(blockInfo);
                    }
                } catch(e) {
                    console.log("[文件操作] read onLeave错误: " + e.message);
                }
            }
        });
        
        // 钩住close系统调用
        Interceptor.attach(Module.findExportByName(null, "close"), {
            onEnter: function(args) {
                try {
                    var fd = args[0].toInt32();
                    this.fd = fd;
                    this.hasFileOp = fileOperations[fd] ? true : false;
                } catch(e) {
                    console.log("[文件操作] close onEnter错误: " + e.message);
                }
            },
            onLeave: function(retval) {
                try {
                    if (this.hasFileOp) {
                        var fileOp = fileOperations[this.fd];
                        if (!fileOp) return; // 防止undefined错误
                        
                        console.log("[文件操作] 关闭ANS文件 fd=" + this.fd);
                        console.log("[文件操作] ANS文件 " + fileOp.path + " 已关闭，总共读取 " + fileOp.size + " 字节");
                        
                        // 分析文件块
                        if (fileOp.blocks.length > 0) {
                            console.log("[文件操作] 文件包含 " + fileOp.blocks.length + " 个数据块");
                        }
                        
                        // 删除文件操作记录，避免内存泄漏
                        delete fileOperations[this.fd];
                    }
                } catch(e) {
                    console.log("[文件操作] close onLeave错误: " + e.message);
                }
            }
        });
    }
    
    function setupANSParserHooks() {
        console.log("[ANS流程分析] 设置ANS解析钩子...");
        
        // 1. 钩住主解析函数 sub_C654
        var parserFuncOffset = 0xC654;
        var parserFuncAddr = libamapnsq.base.add(parserFuncOffset);
        
        console.log("[ANS流程分析] 设置解析函数钩子: " + parserFuncAddr);
        
        Interceptor.attach(parserFuncAddr, {
            onEnter: function(args) {
                try {
                    this.srcData = args[0];
                    this.destBuffer = args[1];
                    this.sizePtr = args[2];
                    this.controlStruct = args[3];
                    this.size = args[4].toInt32();
                    
                    var indent = getIndent(callDepth);
                    callDepth++;
                    
                    console.log(indent + "[ANS解析] ↓ 调用解析函数 sub_C654");
                    console.log(indent + "  源数据地址: " + this.srcData);
                    console.log(indent + "  目标缓冲区: " + this.destBuffer);
                    console.log(indent + "  源数据大小: " + this.size + " 字节");
                    
                    // 检查源数据头部
                    try {
                        if (this.size >= 4) {
                            var headerBytes = Memory.readByteArray(this.srcData, Math.min(16, this.size));
                            var header = new Uint8Array(headerBytes);
                            var headerHex = "";
                            
                            for (var i = 0; i < header.length; i++) {
                                var byteHex = header[i].toString(16);
                                headerHex += (byteHex.length === 1 ? "0" + byteHex : byteHex) + " ";
                            }
                            
                            console.log(indent + "  源数据头部: " + headerHex);
                        }
                    } catch(e) {
                        console.log(indent + "  读取源数据头部失败: " + e.message);
                    }
                } catch(e) {
                    console.log("[ANS解析] sub_C654 onEnter错误: " + e.message);
                }
            },
            onLeave: function(retval) {
                try {
                    callDepth--;
                    var indent = getIndent(callDepth);
                    
                    console.log(indent + "[ANS解析] ↑ 解析函数返回: 0x" + retval.toString(16));
                    
                    if (retval.toInt32() === 0) {
                        try {
                            if (this.sizePtr) {
                                var decompressedSize = Memory.readInt(this.sizePtr);
                                console.log(indent + "  解析成功，解压后大小: " + decompressedSize + " 字节");
                                
                                // 保存解压后的数据引用
                                lastAnsData = {
                                    buffer: this.destBuffer,
                                    size: decompressedSize,
                                    timestamp: new Date().getTime()
                                };
                                
                                // 分析解压后的数据
                                try {
                                    var dataBytes = Memory.readByteArray(this.destBuffer, Math.min(32, decompressedSize));
                                    var data = new Uint8Array(dataBytes);
                                    var dataHex = "";
                                    var dataText = "";
                                    
                                    for (var i = 0; i < data.length; i++) {
                                        var byteHex = data[i].toString(16);
                                        dataHex += (byteHex.length === 1 ? "0" + byteHex : byteHex) + " ";
                                        
                                        // 检查是否是可打印ASCII
                                        if (data[i] >= 32 && data[i] <= 126) {
                                            dataText += String.fromCharCode(data[i]);
                                        } else {
                                            dataText += ".";
                                        }
                                    }
                                    
                                    console.log(indent + "  解压数据头部: " + dataHex);
                                    console.log(indent + "  解压数据文本: " + dataText);
                                    
                                    // 检查是否包含"DICE-AM"头部
                                    if (dataText.indexOf("DICE-AM") !== -1) {
                                        console.log(indent + "  [发现] DICE-AM头部标识");
                                    }
                                    
                                    // 扫描更多数据查找可能的中文文本
                                    if (decompressedSize > 32) {
                                        var foundChinese = false;
                                        
                                        // 每隔1KB采样检查是否有中文字符
                                        for (var offset = 32; offset < decompressedSize && offset < 10240; offset += 1024) {
                                            try {
                                                var sampleBytes = Memory.readByteArray(this.destBuffer.add(offset), 64);
                                                var sample = new Uint8Array(sampleBytes);
                                                
                                                // 简单检查UTF-8编码的中文字符 (0xE4-0xE9开头的三字节序列)
                                                for (var i = 0; i < sample.length - 2; i++) {
                                                    if (sample[i] >= 0xE4 && sample[i] <= 0xE9) {
                                                        console.log(indent + "  [发现] 可能的中文文本在偏移: " + (offset + i));
                                                        foundChinese = true;
                                                        break;
                                                    }
                                                }
                                                
                                                if (foundChinese) break;
                                            } catch(e) {}
                                        }
                                    }
                                } catch(e) {
                                    console.log(indent + "  分析解压数据失败: " + e.message);
                                }
                            }
                        } catch(e) {
                            console.log(indent + "  读取解析数据失败: " + e.message);
                        }
                    } else {
                        console.log(indent + "  解析失败");
                    }
                } catch(e) {
                    console.log("[ANS解析] sub_C654 onLeave错误: " + e.message);
                }
            }
        });
        
        // 2. 钩住预处理函数 sub_C39C
        var preprocessFuncOffset = 0xC39C;
        var preprocessFuncAddr = libamapnsq.base.add(preprocessFuncOffset);
        
        console.log("[ANS流程分析] 设置预处理函数钩子: " + preprocessFuncAddr);
        
        Interceptor.attach(preprocessFuncAddr, {
            onEnter: function(args) {
                try {
                    this.arg1 = args[0];
                    this.arg2 = args[1];
                    this.arg3 = args[2].toInt32();
                    
                    var indent = getIndent(callDepth);
                    callDepth++;
                    
                    console.log(indent + "[ANS解析] ↓ 调用预处理函数 sub_C39C");
                    console.log(indent + "  参数1: " + this.arg1);
                    console.log(indent + "  参数2: " + this.arg2);
                    console.log(indent + "  参数3: " + this.arg3);
                } catch(e) {
                    console.log("[ANS解析] sub_C39C onEnter错误: " + e.message);
                }
            },
            onLeave: function(retval) {
                try {
                    callDepth--;
                    var indent = getIndent(callDepth);
                    
                    console.log(indent + "[ANS解析] ↑ 预处理函数返回: " + retval);
                } catch(e) {
                    console.log("[ANS解析] sub_C39C onLeave错误: " + e.message);
                }
            }
        });
        
        // 3. 钩住uncompress函数
        var uncompressAddr = Module.findExportByName("libz.so", "uncompress");
        
        if (uncompressAddr) {
            console.log("[ANS流程分析] 设置uncompress函数钩子: " + uncompressAddr);
            
            Interceptor.attach(uncompressAddr, {
                onEnter: function(args) {
                    try {
                        this.destBuffer = args[0];
                        this.destLenPtr = args[1];
                        this.srcBuffer = args[2];
                        this.srcLen = args[3].toInt32();
                        
                        var indent = getIndent(callDepth);
                        callDepth++;
                        
                        console.log(indent + "[ANS解压] ↓ 调用uncompress函数");
                        console.log(indent + "  源大小: " + this.srcLen + " 字节");
                        
                        // 检查zlib头部
                        try {
                            if (this.srcLen >= 2) {
                                var headerBytes = Memory.readByteArray(this.srcBuffer, Math.min(4, this.srcLen));
                                var header = new Uint8Array(headerBytes);
                                var headerHex = "";
                                
                                for (var i = 0; i < header.length; i++) {
                                    var byteHex = header[i].toString(16);
                                    headerHex += (byteHex.length === 1 ? "0" + byteHex : byteHex) + " ";
                                }
                                
                                console.log(indent + "  zlib头部: " + headerHex);
                                
                                // 检查是否是标准zlib头部 (78 9C)
                                if (header[0] === 0x78 && header[1] === 0x9C) {
                                    console.log(indent + "  [确认] 标准zlib压缩数据");
                                }
                            }
                        } catch(e) {
                            console.log(indent + "  读取zlib头部失败: " + e.message);
                        }
                    } catch(e) {
                        console.log("[ANS解压] uncompress onEnter错误: " + e.message);
                    }
                },
                onLeave: function(retval) {
                    try {
                        callDepth--;
                        var indent = getIndent(callDepth);
                        
                        console.log(indent + "[ANS解压] ↑ uncompress返回: " + retval.toInt32());
                        
                        // zlib返回值: Z_OK(0), Z_MEM_ERROR(-4), Z_BUF_ERROR(-5), Z_DATA_ERROR(-3)
                        if (retval.toInt32() === 0) {
                            try {
                                var destLen = Memory.readUInt(this.destLenPtr);
                                console.log(indent + "  解压后大小: " + destLen + " 字节");
                            } catch(e) {}
                        }
                    } catch(e) {
                        console.log("[ANS解压] uncompress onLeave错误: " + e.message);
                    }
                }
            });
        }
    }
    
    function setupJavaHooks() {
        console.log("[ANS流程分析] 设置Java层钩子...");
        
        Java.perform(function() {
            try {
                // 钩住GLMapEngine类的关键方法
                var GLMapEngine = Java.use("com.autonavi.ae.gmap.GLMapEngine");
                
                // 钩住loadData方法，可能与加载ANS文件相关
                if (GLMapEngine.loadData) {
                    GLMapEngine.loadData.overloads.forEach(function(overload) {
                        overload.implementation = function() {
                            console.log("[Java] GLMapEngine.loadData调用");
                            var result = this.loadData.apply(this, arguments);
                            console.log("[Java] GLMapEngine.loadData返回: " + result);
                            return result;
                        };
                    });
                }
                
                // 钩住updateMap方法，可能与地图刷新相关
                if (GLMapEngine.updateMap) {
                    GLMapEngine.updateMap.overloads.forEach(function(overload) {
                        overload.implementation = function() {
                            console.log("[Java] GLMapEngine.updateMap调用");
                            var result = this.updateMap.apply(this, arguments);
                            console.log("[Java] GLMapEngine.updateMap返回: " + result);
                            return result;
                        };
                    });
                }
                
                // 钩住可能与手势相关的方法
                var gestureMethodNames = ["onGestureDown", "onGestureUp", "onGestureMove", "onGestureFling", "onGestureScale"];
                
                for (var i = 0; i < gestureMethodNames.length; i++) {
                    var methodName = gestureMethodNames[i];
                    if (GLMapEngine[methodName]) {
                        GLMapEngine[methodName].overloads.forEach(function(overload) {
                            overload.implementation = function() {
                                console.log("[Java] GLMapEngine." + methodName + "调用");
                                var result = this[methodName].apply(this, arguments);
                                console.log("[Java] GLMapEngine." + methodName + "返回: " + result);
                                return result;
                            };
                        });
                    }
                }
            } catch(e) {
                console.log("[ANS流程分析] 设置Java钩子失败: " + e.message);
            }
        });
    }
    
    function setupJNIHooks() {
        console.log("[ANS流程分析] 设置JNI钩子...");
        
        try {
            // 钩住特定的JNI函数
            var jniFunctions = [
                "GLMapEngine_nativeCreateMapModule",
                "GLMapEngine_nativeAttachSurfaceToRenderDevice",
                "GLMapEngine_nativeRenderDeviceChanged",
                "GLMapEngine_nativeDestroyMapModule"
            ];
            
            for (var i = 0; i < jniFunctions.length; i++) {
                var funcName = jniFunctions[i];
                var funcAddr = Module.findExportByName("libamapr.so", funcName);
                
                if (funcAddr) {
                    console.log("[ANS流程分析] 找到JNI函数: " + funcName + " @ " + funcAddr);
                    
                    (function(name) {
                        Interceptor.attach(funcAddr, {
                            onEnter: function(args) {
                                var indent = getIndent(callDepth);
                                callDepth++;
                                
                                console.log(indent + "[JNI] ↓ 调用 " + name);
                                
                                // 记录Java到Native的调用
                                javaToNativeCalls.push({
                                    function: name,
                                    time: new Date().getTime()
                                });
                            },
                            onLeave: function(retval) {
                                callDepth--;
                                var indent = getIndent(callDepth);
                                
                                console.log(indent + "[JNI] ↑ " + name + " 返回: " + retval);
                            }
                        });
                    })(funcName);
                }
            }
        } catch(e) {
            console.log("[ANS流程分析] 设置JNI钩子失败: " + e.message);
        }
    }
    
    function setupRenderHooks() {
        console.log("[ANS流程分析] 设置渲染钩子...");
        
        try {
            // 在libamapr.so中查找可能的渲染函数
            // 注意：这里使用的是十六进制字节模式，而不是字符串
            var renderFuncPattern = "FF 43 01 D1 F3 0B 00 F9 FD 7B 02 A9";
            
            console.log("[ANS流程分析] 搜索渲染函数模式: " + renderFuncPattern);
            
            // 限制搜索范围，避免应用卡顿
            var searchRange = 0x10000; // 64KB
            var searchStart = libamapr.base;
            
            try {
                Memory.scan(searchStart, searchRange, renderFuncPattern, {
                    onMatch: function(address, size) {
                        console.log("[ANS流程分析] 找到可能的渲染函数: " + address);
                        return 'continue';
                    },
                    onComplete: function() {
                        console.log("[ANS流程分析] 渲染函数搜索完成");
                    },
                    onError: function(reason) {
                        console.log("[ANS流程分析] 渲染函数搜索错误: " + reason);
                    }
                });
            } catch(e) {
                console.log("[ANS流程分析] 搜索渲染函数失败: " + e.message);
            }
        } catch(e) {
            console.log("[ANS流程分析] 设置渲染钩子失败: " + e.message);
        }
    }
    
    // 辅助函数：获取缩进
    function getIndent(depth) {
        var indent = "";
        for (var i = 0; i < depth; i++) {
            indent += "  ";
        }
        return indent;
    }
    
    // 定期显示Java到Native的调用统计
    setInterval(function() {
        if (javaToNativeCalls.length > 0) {
            console.log("\n[ANS流程分析] === Java→Native调用统计 ===");
            console.log("最近记录了 " + javaToNativeCalls.length + " 个Java→Native调用");
            
            // 统计调用频率
            var callFreq = {};
            
            for (var i = 0; i < javaToNativeCalls.length; i++) {
                var call = javaToNativeCalls[i];
                
                if (!callFreq[call.function]) {
                    callFreq[call.function] = 1;
                } else {
                    callFreq[call.function]++;
                }
            }
            
            // 显示调用频率
            for (var func in callFreq) {
                console.log("  " + func + ": " + callFreq[func] + " 次");
            }
            
            console.log("[ANS流程分析] === 统计结束 ===\n");
            
            // 清空记录
            javaToNativeCalls = [];
        }
    }, 30000); // 每30秒显示一次
    
    console.log("[ANS流程分析] 脚本设置完成，等待操作...");
})(); 