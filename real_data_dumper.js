/*
 * 高德地图真实数据完整输出器
 * 基于成功模式，专注输出完整的真实数据内容
 * 版本: Frida 12.9.7 兼容
 */

console.log("[Real Data] 启动真实数据完整输出器...");

var realDataCount = 0;
var maxRealData = 3;

// 输出真实的完整数据内容
function dumpRealData(dataPtr, size, source) {
    if (realDataCount >= maxRealData) return;
    
    try {
        console.log("\n" + "=".repeat(100));
        console.log(" 真实数据完整输出 #" + (realDataCount + 1));
        console.log("数据源: " + source + " | 数据大小: " + size + " 字节");
        console.log("=".repeat(100));
        
        // 读取更多数据以获得完整真实内容
        var dumpSize = Math.min(1024, size); // 读取1KB的真实数据
        var data = dataPtr.readByteArray(dumpSize);
        var bytes = new Uint8Array(data);
        
        // 转换为完整的可读文本
        var fullText = "";
        for (var i = 0; i < bytes.length; i++) {
            if (bytes[i] >= 32 && bytes[i] < 127) {
                fullText += String.fromCharCode(bytes[i]);
            } else if (bytes[i] === 10) {
                fullText += "\n";
            } else if (bytes[i] === 13) {
                fullText += "\r";
            } else if (bytes[i] === 9) {
                fullText += "\t";
            } else {
                fullText += ".";
            }
        }
        
        console.log(" 完整真实数据内容:");
        console.log("----------------------------------------");
        
        // 检测数据类型并输出完整内容
        if (fullText.indexOf('<?xml') >= 0) {
            console.log(" 数据类型: XML配置文件 (真实数据)");
            console.log(fullText);
            realDataCount++;
        }
        else if (fullText.indexOf('{"') >= 0 || fullText.indexOf('"res_list"') >= 0) {
            console.log(" 数据类型: JSON配置文件 (真实数据)");
            console.log(fullText);
            realDataCount++;
        }
        else if (fullText.indexOf('DICE-AM') >= 0) {
            console.log(" 数据类型: DICE-AM矢量数据 (真实数据)");
            console.log("ASCII内容: " + fullText.substring(0, 50));
            console.log("原始十六进制数据:");
            
            // 输出完整的十六进制dump
            for (var j = 0; j < Math.min(256, bytes.length); j += 16) {
                var hexLine = "  ";
                var asciiLine = "  ";
                for (var k = 0; k < 16 && (j + k) < bytes.length; k++) {
                    var hex = bytes[j + k].toString(16);
                    if (hex.length === 1) hex = "0" + hex;
                    hexLine += hex + " ";
                    
                    if (bytes[j + k] >= 32 && bytes[j + k] < 127) {
                        asciiLine += String.fromCharCode(bytes[j + k]);
                    } else {
                        asciiLine += ".";
                    }
                }
                console.log(hexLine + "| " + asciiLine);
            }
            realDataCount++;
        }
        else {
            // 检查是否包含中文或其他特殊数据
            var hasChineseBytes = false;
            for (var l = 0; l < Math.min(100, bytes.length); l++) {
                if (bytes[l] >= 0xE4 && bytes[l] <= 0xE9) {
                    hasChineseBytes = true;
                    break;
                }
            }
            
            if (hasChineseBytes) {
                console.log(" 数据类型: 中文文本数据 (真实数据)");
                console.log("原始文本: " + fullText.substring(0, 200));
                
                console.log("UTF-8字节序列:");
                var hexDump = "  ";
                for (var m = 0; m < Math.min(64, bytes.length); m++) {
                    var hex = bytes[m].toString(16);
                    if (hex.length === 1) hex = "0" + hex;
                    hexDump += hex + " ";
                    if ((m + 1) % 16 === 0) {
                        console.log(hexDump);
                        hexDump = "  ";
                    }
                }
                if (hexDump.trim().length > 0) {
                    console.log(hexDump);
                }
                realDataCount++;
            }
            else if (fullText.length > 20) {
                console.log(" 数据类型: 通用文本数据 (真实数据)");
                console.log(fullText);
                realDataCount++;
            }
        }
        
        console.log("----------------------------------------");
        console.log(" 数据提取完成 - 这是运行时的真实数据！");
        console.log("=".repeat(100) + "\n");
        
    } catch (e) {
        console.log("[真实数据输出错误] " + e.message);
    }
}

function isSystemFile(charStr) {
    return charStr.indexOf("VmFlags") >= 0 ||
           charStr.indexOf("kB.") >= 0 ||
           charStr.indexOf("/proc/") >= 0 ||
           charStr.indexOf("/dev/") >= 0 ||
           charStr.indexOf("Referenced") >= 0 ||
           charStr.indexOf("Anonymous") >= 0;
}

function setupRealDataDumper() {
    setTimeout(function() {
        console.log("[Setup] 设置真实数据输出器...");
        
        try {
            var lib = Module.findBaseAddress("libamapnsq.so");
            if (!lib) {
                console.log("[Error] 未找到libamapnsq.so");
                return;
            }
            
            console.log("[Library] 库基址: " + lib);
            
            // Hook文件读取 - 完全基于成功的模式
            var readPtr = Module.findExportByName("libc.so", "read");
            if (readPtr) {
                Interceptor.attach(readPtr, {
                    onEnter: function(args) {
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                        this.isTargetFile = (this.size > 100 && this.size < 10000);
                    },
                    onLeave: function(retval) {
                        if (!this.isTargetFile || realDataCount >= maxRealData) return;
                        
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 0) {
                            try {
                                // 快速预览判断是否为目标数据
                                var preview = this.buffer.readByteArray(Math.min(32, bytesRead));
                                var previewBytes = new Uint8Array(preview);
                                var previewStr = "";
                                
                                for (var i = 0; i < previewBytes.length; i++) {
                                    if (previewBytes[i] >= 32 && previewBytes[i] < 127) {
                                        previewStr += String.fromCharCode(previewBytes[i]);
                                    }
                                }
                                
                                // 只输出非系统文件且包含有意义内容的真实数据
                                if (!isSystemFile(previewStr) && 
                                    (previewStr.indexOf('<?xml') >= 0 || 
                                     previewStr.indexOf('{"') >= 0 || 
                                     previewStr.indexOf('DICE') >= 0 ||
                                     previewBytes[0] >= 0xE4)) {
                                    
                                    console.log("[检测到目标数据] 大小: " + bytesRead + ", 预览: " + previewStr.substring(0, 20));
                                    dumpRealData(this.buffer, bytesRead, "文件读取");
                                }
                                
                            } catch (e) {
                                // 忽略预览错误
                            }
                        }
                    }
                });
                console.log(" 真实数据输出Hook设置成功");
            }
            
            console.log("[Ready] 真实数据输出器准备就绪");
            console.log("请移动地图到新区域以触发真实数据加载和完整输出");
            
        } catch (e) {
            console.log("[Setup Error] " + e.message);
        }
    }, 3000);
}

function generateRealDataReport() {
    console.log("\n=== 真实数据输出报告 ===");
    console.log("已输出真实数据: " + realDataCount + "/" + maxRealData);
    
    if (realDataCount >= maxRealData) {
        console.log(" 真实数据输出完成！");
        console.log(" 以上是从高德地图运行时实际提取的完整数据内容");
    } else if (realDataCount > 0) {
        console.log(" 已获取部分真实数据，继续移动地图获取更多");
    } else {
        console.log(" 请移动地图到新区域触发真实数据加载");
    }
    console.log("================================\n");
}

// 启动真实数据输出器
setupRealDataDumper();
setInterval(generateRealDataReport, 30000);

console.log("[Real Data] 真实数据完整输出器已加载"); 