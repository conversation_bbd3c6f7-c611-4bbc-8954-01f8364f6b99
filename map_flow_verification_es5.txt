     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[流程验证] 开始验证地图数据处理8步骤流程...
[\u2717] libamapnsq.so 未找到
[\u2713] libz.so 已加载
[\u2713] libc.so 已加载
[\u2713] libEGL.so 已加载 (GPU渲染)
[\u2713] 步骤7 OpenGL Hook设置成功
[\u2713] 步骤8 EGL Hook设置成功
[流程验证] 验证脚本已启动，请移动地图以触发数据流程...
[提示] 脚本将追踪完整的8步骤数据处理流程
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [步骤验证] 步骤4-分块读取: 读取 1 字节 (fd:21)
[步骤验证] 步骤4-分块读取: 读取 12 字节 (fd:21)
[步骤验证] 步骤4-分块读取: 读取 8192 字节 (fd:40)
[步骤验证] 步骤4-分块读取: 读取 8192 字节 (fd:40)
[步骤验证] 步骤4-分块读取: 读取 561 字节 (fd:40)
[步骤验证] 步骤4-分块读取: 读取 31 字节 (fd:45)
[步骤验证] 步骤4-分块读取: 读取 443 字节 (fd:45)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
[步骤验证] 步骤4-分块读取: 读取 4096 字节 (fd:46)
Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=0000021552C274B0)

Thread 0x000096ec (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x000071b0 (most recent call first):
<no Python frame>
