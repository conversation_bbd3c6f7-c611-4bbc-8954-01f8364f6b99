// map_location_monitor.js - 捕获经纬度和地址信息
setTimeout(function() {
  console.log("[高德地图数据监控] 开始捕获位置和地址信息");
  
  // 防止崩溃
  var libc_exit = Module.findExportByName("libc.so", "exit");
  if (libc_exit) {
    Interceptor.replace(libc_exit, new NativeCallback(function() {
      return 0;
    }, 'void', ['int']));
  }
  
  // 确保Java VM已加载
  setTimeout(function() {
    Java.perform(function() {
      console.log("[Java层] 开始监控位置和地址数据");
      
      // 1. 监控GeoPoint/经纬度信息
      try {
        var GeoPoint = Java.use("com.autonavi.common.model.GeoPoint");
        if (GeoPoint) {
          // 监控创建点位的构造函数
          GeoPoint.$init.overload('double', 'double').implementation = function(x, y) {
            console.log("[位置数据] 创建GeoPoint - 经度: " + x + ", 纬度: " + y);
            return this.$init(x, y);
          };
          console.log("[+] Hook GeoPoint成功");
          
          // 监控点位转换
          if (GeoPoint.glGeoPoint2GeoPoint) {
            GeoPoint.glGeoPoint2GeoPoint.implementation = function(glPoint) {
              var result = this.glGeoPoint2GeoPoint(glPoint);
              if (result) {
                console.log("[位置转换] GL坐标点 -> GeoPoint: " + 
                           "经度=" + result.x + ", 纬度=" + result.y);
              }
              return result;
            };
          }
        }
      } catch(e) {
        console.log("[-] GeoPoint hook失败: " + e);
      }
      
      // 2. 监控地图中心点设置 - 这通常是用户当前位置
      try {
        var AMapController = Java.use("com.autonavi.ae.gmap.AMapController");
        if (AMapController.setMapCenter) {
          AMapController.setMapCenter.overload('int', 'double', 'double').implementation = function(engineId, lon, lat) {
            console.log("[地图中心] 设置位置 - 经度: " + lon + ", 纬度: " + lat + ", 引擎ID: " + engineId);
            return this.setMapCenter(engineId, lon, lat);
          };
          console.log("[+] Hook setMapCenter成功");
        }
        
        // 获取地图中心点方法
        if (AMapController.getMapCenter) {
          AMapController.getMapCenter.implementation = function(engineId) {
            var result = this.getMapCenter(engineId);
            if (result) {
              console.log("[地图中心] 获取当前位置 - 经度: " + result.x + ", 纬度: " + result.y);
            }
            return result;
          };
          console.log("[+] Hook getMapCenter成功");
        }
      } catch(e) {
        console.log("[-] AMapController hook失败: " + e);
      }
      
      // 3. 监控地址信息和POI数据
      try {
        var POI = Java.use("com.autonavi.common.model.POI");
        if (POI) {
          // 监控POI创建
          POI.$init.overload('java.lang.String', 'com.autonavi.common.model.GeoPoint').implementation = function(name, point) {
            console.log("[POI数据] 创建POI - 名称: " + name);
            if (point) {
              console.log("  位置: 经度=" + point.x + ", 纬度=" + point.y);
            }
            return this.$init(name, point);
          };
          console.log("[+] Hook POI成功");
        }
        
        // 监控POI工厂类
        try {
          var POIFactory = Java.use("com.amap.bundle.datamodel.poi.POIFactory");
          if (POIFactory.createPOI) {
            POIFactory.createPOI.implementation = function(name, point) {
              console.log("[POI工厂] 创建POI - 名称: " + name);
              if (point) {
                console.log("  位置: 经度=" + point.x + ", 纬度=" + point.y);
              }
              var result = this.createPOI(name, point);
              return result;
            };
            console.log("[+] Hook POIFactory成功");
          }
        } catch(e) {
          console.log("[-] POIFactory hook失败: " + e);
        }
      } catch(e) {
        console.log("[-] POI hook失败: " + e);
      }
      
      // 4. 监控地理编码/反编码类
      try {
        var searchClasses = [
          "com.amap.bundle.search.api.search.GeoCodeSearcher",
          "com.amap.bundle.search.api.search.ReGeoCodeSearcher"
        ];
        
        searchClasses.forEach(function(className) {
          try {
            var SearchClass = Java.use(className);
            
            // 监控搜索方法
            var methods = SearchClass.class.getDeclaredMethods();
            for (var i = 0; i < methods.length; i++) {
              var method = methods[i].getName();
              if (method.indexOf("search") !== -1 || method.indexOf("request") !== -1) {
                try {
                  SearchClass[method].implementation = function() {
                    console.log("[地理编码] 调用: " + className + "." + method);
                    console.log("  参数: " + JSON.stringify(arguments));
                    var result = this[method].apply(this, arguments);
                    return result;
                  };
                } catch(e) {}
              }
            }
            
            console.log("[+] Hook " + className + "成功");
          } catch(e) {}
        });
      } catch(e) {
        console.log("[-] 地理编码类hook失败: " + e);
      }
      
      // 5. 监控地址解析回调
      try {
        var callbackClasses = [
          "com.amap.bundle.search.api.search.GeoCodeSearchListener",
          "com.amap.bundle.search.api.search.ReGeoCodeSearchListener"
        ];
        
        callbackClasses.forEach(function(className) {
          try {
            var CallbackClass = Java.use(className);
            
            // 监控回调方法
            var methods = CallbackClass.class.getDeclaredMethods();
            for (var i = 0; i < methods.length; i++) {
              var method = methods[i].getName();
              if (method.indexOf("onResult") !== -1 || method.indexOf("onSearch") !== -1) {
                try {
                  CallbackClass[method].implementation = function() {
                    console.log("[地址回调] 收到结果: " + className + "." + method);
                    var result = JSON.stringify(arguments[0]);
                    console.log("  数据: " + result);
                    return this[method].apply(this, arguments);
                  };
                } catch(e) {}
              }
            }
            
            console.log("[+] Hook " + className + "成功");
          } catch(e) {}
        });
      } catch(e) {
        console.log("[-] 回调类hook失败: " + e);
      }
      
      // 6. 监控定位类
      try {
        var Location = Java.use("com.amap.location.type.location.Location");
        if (Location) {
          // 监控获取经纬度方法
          if (Location.getLatitude) {
            Location.getLatitude.implementation = function() {
              var lat = this.getLatitude();
              console.log("[定位] 获取纬度: " + lat);
              return lat;
            };
          }
          
          if (Location.getLongitude) {
            Location.getLongitude.implementation = function() {
              var lon = this.getLongitude();
              console.log("[定位] 获取经度: " + lon);
              return lon;
            };
          }
          
          if (Location.getAddress) {
            Location.getAddress.implementation = function() {
              var address = this.getAddress();
              console.log("[定位] 获取地址: " + address);
              return address;
            };
          }
          
          console.log("[+] Hook Location成功");
        }
      } catch(e) {
        console.log("[-] Location hook失败: " + e);
      }
      
      console.log("[配置完成] 等待捕获位置和地址数据...");
    });
  }, 3000); // 延长延迟时间确保应用完全初始化
  
}, 500);
