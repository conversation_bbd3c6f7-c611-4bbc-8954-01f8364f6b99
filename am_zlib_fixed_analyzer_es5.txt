     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Spawning `com.autonavi.minimap`...
[AM-zlib修复版] 开始分析AM-zlib处理...
[修复] 解决之前脚本的Hook问题
[1] Hook mmap，寻找AM-zlib文件映射...
[\u2713] mmap Hook设置成功
[2] Hook内存访问，寻找AM-zlib数据读取...
[\u2713] 内存拷贝Hook设置成功
[3] 寻找自定义解压函数...
[修复版分析] 脚本已启动...
[目标] 更准确地分析AM-zlib处理
[提示] 请打开地图并移动以触发数据处理
Spawned `com.autonavi.minimap`. Resuming main thread!
[Remote::com.autonavi.minimap]-> [搜索解压函数] base.odex

[修复版分析报告] ==========================================
运行时间: 25s

mmap文件映射:
  发现的AM-zlib映射: 0 个

内存操作统计:
  压缩数据拷贝: 0 次

解压尝试:
  解压调用: 0 次
===============================================

[大文件映射] fd=20, 大小=68.3MB, 地址=0x7f63887000

[修复版分析报告] ==========================================
运行时间: 50s

mmap文件映射:
  发现的AM-zlib映射: 0 个

内存操作统计:
  压缩数据拷贝: 0 次

解压尝试:
  解压调用: 0 次
===============================================

[大文件映射] fd=97, 大小=74.2MB, 地址=0x7f59a59000

[修复版分析报告] ==========================================
运行时间: 75s

mmap文件映射:
  发现的AM-zlib映射: 0 个

内存操作统计:
  压缩数据拷贝: 0 次

解压尝试:
  解压调用: 0 次
===============================================

[大文件映射] fd=96, 大小=29.9MB, 地址=0x7f57541000
[大文件映射] fd=96, 大小=174.6MB, 地址=0x7f4c6a5000
Process terminated

Thank you for using Frida!
Fatal Python error: could not acquire lock for <_io.BufferedReader name='<stdin>'> at interpreter shutdown, possibly due to daemon threads
Python runtime state: finalizing (tstate=000002A5C5348030)

Thread 0x00001970 (most recent call first):
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 999 in get_input
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\site-packages\frida_tools\repl.py", line 892 in _process_requests
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 870 in run
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 932 in _bootstrap_inner
  File "c:\users\<USER>\.pyenv\pyenv-win\versions\3.8.0\lib\threading.py", line 890 in _bootstrap

Current thread 0x0000a284 (most recent call first):
<no Python frame>
