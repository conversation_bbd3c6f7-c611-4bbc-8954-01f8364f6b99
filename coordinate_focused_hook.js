/*
 * 高德地图坐标生成专项Hook
 * 基于optimized_memory_searcher的发现，专注于坐标数据的生成过程
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[Coordinate Focused Hook] 启动坐标生成专项Hook...");
    
    var CONFIG = {
        INIT_DELAY: 3000,
        MONITOR_ADDRESSES: [
            "0x7f5c59c010", "0x7f5c59c060", "0x7f5c59c0b0", 
            "0x7f5c59c100", "0x7f5c59c150", "0x7f646b0070", "0x7f646b02a0"
        ],
        LOG_COORDINATE_WRITES: true,
        TRACE_FUNCTION_CALLS: true
    };
    
    var monitoringResults = {
        coordinateWrites: [],
        functionCalls: [],
        memoryMappings: [],
        totalEvents: 0
    };
    
    // === 内存写入监控器 ===
    function setupMemoryWriteMonitoring() {
        console.log("[Memory Monitor] 设置内存写入监控...");
        
        try {
            // 监控坐标写入
            for (var i = 0; i < CONFIG.MONITOR_ADDRESSES.length; i++) {
                var addrStr = CONFIG.MONITOR_ADDRESSES[i];
                try {
                    var addr = ptr(addrStr);
                    setupCoordinateWriteHook(addr, "Address_" + i);
                } catch (e) {
                    console.log("[Monitor] 地址 " + addrStr + " 不可访问，跳过");
                }
            }
            
            console.log("[Memory Monitor] 内存监控设置完成");
            
        } catch (e) {
            console.log("[Error] 内存监控设置失败: " + e);
        }
    }
    
    // === 坐标写入Hook ===
    function setupCoordinateWriteHook(address, name) {
        try {
            // 监控32字节范围的写入（8个浮点数）
            for (var offset = 0; offset < 32; offset += 4) {
                var targetAddr = address.add(offset);
                
                // 使用Interceptor监控内存写入
                Interceptor.attach(targetAddr, {
                    onEnter: function(args) {
                        console.log("[Coord Write] " + name + " @ " + this.context.pc + " 写入坐标数据");
                        this.startTime = Date.now();
                    },
                    onLeave: function(retval) {
                        try {
                            // 读取写入后的坐标值
                            var coords = [];
                            for (var j = 0; j < 4; j++) {
                                var coordAddr = address.add(j * 4);
                                var value = coordAddr.readFloat();
                                coords.push(value);
                            }
                            
                            console.log("[Coord Data] " + name + " 新坐标: " + 
                                       coords.map(function(v) { return v.toFixed(6); }).join(", "));
                            
                            monitoringResults.coordinateWrites.push({
                                address: address,
                                name: name,
                                coordinates: coords,
                                timestamp: Date.now(),
                                pc: this.context.pc
                            });
                            
                            monitoringResults.totalEvents++;
                            
                        } catch (e) {
                            console.log("[Coord Error] 坐标读取失败: " + e);
                        }
                    }
                });
            }
            
        } catch (e) {
            console.log("[Hook Error] " + name + " Hook设置失败: " + e);
        }
    }
    
    // === libamapnsq.so 函数Hook ===
    function hookDataProcessingFunctions(libBase) {
        console.log("[Function Hook] 设置数据处理函数Hook...");
        
        try {
            // Hook sub_5C394 (数据调度器)
            var sub5C394 = libBase.add(0x5C394);
            Interceptor.attach(sub5C394, {
                onEnter: function(args) {
                    this.startTime = Date.now();
                    console.log("[sub_5C394] 数据调度器调用");
                    
                    // 检查参数中是否包含坐标数据
                    for (var i = 0; i < 3; i++) {
                        try {
                            if (args[i] && !args[i].isNull()) {
                                var data = args[i].readByteArray(16);
                                var view = new Uint8Array(data);
                                
                                // 检查是否包含浮点数模式
                                if (containsFloatPattern(args[i])) {
                                    console.log("[Float Data] arg[" + i + "] 包含浮点数据:");
                                    logFloatSequence(args[i], 4);
                                }
                            }
                        } catch (e) {
                            // 继续检查下一个参数
                        }
                    }
                },
                
                onLeave: function(retval) {
                    var duration = Date.now() - this.startTime;
                    console.log("[sub_5C394] 调度器完成, 耗时: " + duration + "ms, 返回码: " + retval.toInt32());
                    
                    monitoringResults.functionCalls.push({
                        function: "sub_5C394",
                        duration: duration,
                        returnCode: retval.toInt32(),
                        timestamp: Date.now()
                    });
                }
            });
            
            // Hook sub_10F88 (数据解析器)
            var sub10F88 = libBase.add(0x10F88);
            Interceptor.attach(sub10F88, {
                onEnter: function(args) {
                    this.startTime = Date.now();
                    console.log("[sub_10F88] 数据解析器调用");
                    
                    // 保存参数供后续分析
                    this.args = [];
                    for (var k = 0; k < 3; k++) {
                        this.args.push(args[k]);
                    }
                },
                
                onLeave: function(retval) {
                    var duration = Date.now() - this.startTime;
                    var returnCode = retval.toInt32();
                    
                    console.log("[sub_10F88] 解析器完成, 耗时: " + duration + "ms, 返回码: " + returnCode);
                    
                    // 如果解析成功，搜索新生成的坐标数据
                    if (returnCode === 0) {
                        console.log("[Parse Success] 解析成功，搜索新生成的坐标...");
                        setTimeout(function() {
                            searchForNewCoordinates();
                        }, 100);
                    }
                    
                    monitoringResults.functionCalls.push({
                        function: "sub_10F88", 
                        duration: duration,
                        returnCode: returnCode,
                        timestamp: Date.now()
                    });
                }
            });
            
            console.log("[Function Hook] 函数Hook设置完成");
            
        } catch (e) {
            console.log("[Error] 函数Hook失败: " + e);
        }
    }
    
    // === 搜索新生成的坐标 ===
    function searchForNewCoordinates() {
        try {
            console.log("[Coord Search] 搜索新生成的坐标数据...");
            
            // 搜索我们已知的坐标模式
            var patterns = [
                [28.5, 3.0], // 高度28.5，类型3.0的模式
                [1.5, 284.0], // UI坐标模式
                [1.5, 15.0]   // 小UI元素模式
            ];
            
            for (var p = 0; p < patterns.length; p++) {
                var pattern = patterns[p];
                searchFloatPattern(pattern[0], pattern[1]);
            }
            
        } catch (e) {
            console.log("[Search Error] 坐标搜索失败: " + e);
        }
    }
    
    // === 搜索特定浮点数模式 ===
    function searchFloatPattern(val1, val2) {
        try {
            var ranges = Process.enumerateRanges('rw-');
            var foundCount = 0;
            
            for (var i = 0; i < Math.min(ranges.length, 20); i++) { // 限制搜索范围
                var range = ranges[i];
                if (range.size < 0x1000) continue;
                
                try {
                    var searchSize = Math.min(range.size, 0x10000);
                    for (var offset = 0; offset < searchSize; offset += 16) {
                        try {
                            var addr = range.base.add(offset);
                            var f1 = addr.readFloat();
                            var f2 = addr.add(4).readFloat();
                            
                            if (Math.abs(f1 - val1) < 0.01 && Math.abs(f2 - val2) < 0.01) {
                                console.log("[Pattern Found] 发现模式 " + val1 + "," + val2 + " @ " + addr);
                                logFloatSequence(addr, 4);
                                foundCount++;
                                
                                if (foundCount >= 3) break; // 限制发现数量
                            }
                        } catch (e) {
                            // 继续搜索
                        }
                    }
                } catch (e) {
                    // 继续下一个范围
                }
                
                if (foundCount >= 3) break;
            }
            
        } catch (e) {
            console.log("[Pattern Error] 模式搜索失败: " + e);
        }
    }
    
    // === 辅助函数 ===
    function containsFloatPattern(ptr) {
        try {
            // 检查是否包含合理的浮点数值
            for (var i = 0; i < 4; i++) {
                var value = ptr.add(i * 4).readFloat();
                if (isNaN(value) || !isFinite(value)) return false;
                if (Math.abs(value) > 1e10) return false; // 过滤过大的值
            }
            return true;
        } catch (e) {
            return false;
        }
    }
    
    function logFloatSequence(baseAddr, count) {
        try {
            var values = [];
            for (var i = 0; i < count; i++) {
                var value = baseAddr.add(i * 4).readFloat();
                values.push(value.toFixed(6));
            }
            console.log("[Float Seq] " + baseAddr + ": " + values.join(", "));
        } catch (e) {
            console.log("[Float Error] 浮点数序列读取失败: " + e);
        }
    }
    
    function waitForLibrary(libraryName, callback) {
        var maxAttempts = 30;
        var attempt = 0;
        
        function checkLibrary() {
            try {
                var lib = Module.findBaseAddress(libraryName);
                if (lib) {
                    console.log("[Library] " + libraryName + " 已加载，基址: " + lib);
                    callback(lib);
                    return;
                }
            } catch (e) {
                // 继续等待
            }
            
            attempt++;
            if (attempt < maxAttempts) {
                setTimeout(checkLibrary, 1000);
            } else {
                console.log("[Error] " + libraryName + " 加载超时");
            }
        }
        
        checkLibrary();
    }
    
    // === 结果报告 ===
    function generateCoordinateReport() {
        console.log("\n=== 坐标生成监控报告 ===");
        console.log("坐标写入事件: " + monitoringResults.coordinateWrites.length);
        console.log("函数调用: " + monitoringResults.functionCalls.length);
        console.log("总事件数: " + monitoringResults.totalEvents);
        
        if (monitoringResults.coordinateWrites.length > 0) {
            console.log("\n最近的坐标写入:");
            var recent = monitoringResults.coordinateWrites.slice(-3);
            for (var i = 0; i < recent.length; i++) {
                var write = recent[i];
                console.log("  [" + i + "] " + write.name + " @ " + write.address + 
                           " - " + write.coordinates.slice(0, 2).join(","));
            }
        }
        
        if (monitoringResults.functionCalls.length > 0) {
            console.log("\n函数调用统计:");
            var calls = {};
            for (var j = 0; j < monitoringResults.functionCalls.length; j++) {
                var call = monitoringResults.functionCalls[j];
                if (!calls[call.function]) calls[call.function] = 0;
                calls[call.function]++;
            }
            
            for (var func in calls) {
                console.log("  " + func + ": " + calls[func] + " 次");
            }
        }
        
        console.log("================================\n");
    }
    
    // === 主入口 ===
    function main() {
        console.log("[Main] 等待应用初始化完成...");
        
        setTimeout(function() {
            console.log("[Main] 开始坐标生成监控...");
            
            try {
                // 设置内存监控
                setupMemoryWriteMonitoring();
                
                // 等待并Hook库函数
                waitForLibrary("libamapnsq.so", function(libBase) {
                    hookDataProcessingFunctions(libBase);
                    
                    // 定期生成报告
                    setInterval(function() {
                        if (monitoringResults.totalEvents > 0) {
                            generateCoordinateReport();
                        }
                    }, 15000);
                });
                
                console.log("[Coordinate Focused Hook] 坐标监控已启动!");
                console.log("现在移动地图以触发坐标生成过程...");
                
            } catch (e) {
                console.log("[Error] 监控初始化失败: " + e);
            }
        }, CONFIG.INIT_DELAY);
    }
    
    // === 启动监控 ===
    try {
        Java.perform(function() {
            console.log("[Java] Java环境已准备就绪");
            main();
        });
    } catch (e) {
        console.log("[Error] Java环境初始化失败: " + e);
        main();
    }
    
})(); 