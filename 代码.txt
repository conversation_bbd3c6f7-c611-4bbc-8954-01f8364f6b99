// positive sp value has been detected, the output may be wrong!
__int64 __fastcall sub_5C394(
        __int64 a1,
        __int64 a2,
        __int64 a3,
        __int64 a4,
        __int64 a5,
        __int64 a6,
        __int64 a7,
        __int64 a8,
        unsigned __int8 a9,
        __int64 a10,
        int *a11)
{
  _QWORD *v11; // x0
  unsigned int v12; // w4
  char v13; // w7
  unsigned int v14; // w6
  unsigned int v15; // w5
  unsigned int v16; // w20
  unsigned int v17; // w3
  __int64 v18; // x2
  _QWORD *v19; // x25
  __int64 *v20; // x1
  __int64 *v21; // x28
  __int64 result; // x0
  __int64 v23; // x23
  __int64 v24; // x19
  int v25; // w8
  __int64 v26; // x26
  __int64 v27; // x27
  __int64 v28; // x8
  int v29; // w9
  int v30; // w21
  _QWORD *v31; // x9
  __int64 v32; // x0
  __int64 v33; // x3
  __int64 v34; // x0
  __int64 v35; // x8
  __int64 v36; // x0
  __int64 v37; // x0
  int *v38; // x22
  __int64 v39; // x23
  unsigned __int8 v40; // w8
  __int64 v41; // x24
  unsigned int v42; // w20
  unsigned int v43; // w21
  __int64 v44; // x0
  __int64 v45; // x8
  __int64 v46; // x3
  unsigned int v47; // w24
  int v48; // w22
  int v49; // w21
  __int64 v50; // x20
  unsigned int v51; // w23
  __int64 v52; // x0
  int v53; // w8
  unsigned int v54; // w20
  unsigned int v55; // w21
  __int64 v56; // x0
  __int64 v57; // x0
  __int64 *v59; // x8
  __int64 v60; // x8
  int v61; // w9
  __int64 v62; // x0
  __int64 v63; // x2
  __int64 v64; // x0
  _QWORD *v65; // x8
  __int64 v66; // x0
  __int64 v67; // x21
  __int64 *v68; // x8
  int v69; // w26
  __int64 v70; // x27
  __int64 v71; // x0
  unsigned int v72; // w22
  __int64 v73; // x0
  __int64 v74; // x0
  unsigned int v75; // w22
  unsigned __int64 i; // x25
  int v77; // w8
  __int64 v78; // x0
  __int64 v79; // x0
  __int64 v80; // x0
  __int64 v81; // x0
  int v82; // w8
  __int64 v83; // x0
  __int64 v84; // x0
  unsigned __int64 j; // x20
  __int64 v86; // x0
  unsigned __int64 v87; // x25
  int v88; // w8
  unsigned int v89; // w21
  unsigned int v90; // w24
  unsigned int v91; // w20
  __int64 v92; // x5
  __int64 v93; // x0
  _QWORD *v94; // x8
  __int64 v95; // x2
  __int64 v96; // x0
  _QWORD *v97; // x8
  int v98; // [xsp-F0h] [xbp-F0h]
  int v99; // [xsp-E8h] [xbp-E8h]
  unsigned int v100; // [xsp-E0h] [xbp-E0h]
  int v101; // [xsp-DCh] [xbp-DCh]
  unsigned int v102; // [xsp-D0h] [xbp-D0h]
  int v103; // [xsp-CCh] [xbp-CCh]
  __int64 v104; // [xsp-C8h] [xbp-C8h]
  unsigned int v105; // [xsp-BCh] [xbp-BCh]
  int v106; // [xsp-B4h] [xbp-B4h]
  unsigned int v107; // [xsp-ACh] [xbp-ACh]
  char v108; // [xsp-A8h] [xbp-A8h]
  unsigned int v109; // [xsp-A4h] [xbp-A4h]
  __int64 v110; // [xsp-A0h] [xbp-A0h]
  char v111; // [xsp-98h] [xbp-98h]
  unsigned int v112; // [xsp-94h] [xbp-94h]
  unsigned int v113; // [xsp-8Ch] [xbp-8Ch]
  _QWORD *v114; // [xsp-88h] [xbp-88h]
  __int64 v115; // [xsp-80h] [xbp-80h]
  unsigned int v116; // [xsp-74h] [xbp-74h]
  unsigned int v117; // [xsp-74h] [xbp-74h]
  __int64 v118; // [xsp-70h] [xbp-70h]

  v11 = (_QWORD *)sub_7F26C();
  v108 = v13;
  v109 = v12;
  v112 = v14;
  v16 = v15;
  v102 = v17;
  v110 = v18;
  v19 = v11;
  v21 = v20;
  v104 = *v11;
  result = sub_47774();
  v23 = *((__int16 *)v21 + 33);
  v24 = result;
  if ( (v21[9] & 0x20) != 0 )
  {
    result = ((__int64 (__fastcall *)(__int64 *))sub_37238)(v21);
    v25 = *(unsigned __int16 *)(result + 94);
    v118 = result;
  }
  else
  {
    v118 = 0;
    v25 = 1;
  }
  v105 = v25;
  v116 = v16;
  v26 = 0;
  v27 = 0;
  v113 = v16 + 1;
  while ( v27 < v23 )
  {
    if ( v27 != *((__int16 *)v21 + 32) )
    {
      v28 = v21[1];
      v29 = *(unsigned __int8 *)(v28 + v26 + 40);
      if ( *(_BYTE *)(v28 + v26 + 40) )
      {
        if ( v29 == 10 )
          LOBYTE(v29) = 2;
        if ( a9 != 10 )
          LOBYTE(v29) = a9;
        v30 = (unsigned __int8)v29;
        if ( (unsigned __int8)v29 == 5 )
        {
          if ( *(_QWORD *)(v28 + v26 + 8) )
            v30 = 5;
          else
            v30 = 2;
        }
        switch ( v30 )
        {
          case 1:
          case 3:
            goto LABEL_20;
          case 2:
            v31 = (_QWORD *)v19[56];
            if ( !v31 )
              v31 = v19;
            *((_BYTE *)v31 + 33) = 1;
LABEL_20:
            v32 = ((__int64 (__fastcall *)(__int64, __int64, _QWORD, _QWORD))sub_82D04)(
                    v104,
                    561152,
                    *v21,
                    *(_QWORD *)(v28 + v26));
            v34 = ((__int64 (__fastcall *)(__int64, __int64, __int64, __int64, _QWORD, __int64, __int64))sub_818DC)(
                    v32,
                    23,
                    1299,
                    v33,
                    v16 + (unsigned int)v27 + 1,
                    v32,
                    0xFFFFFFFFLL);
            result = ((__int64 (__fastcall *)(__int64))sub_47858)(v34);
            if ( *(_QWORD *)(v24 + 8) )
            {
              result = ((__int64 (__fastcall *)(__int64))sub_7FAA4)(result);
              *(_BYTE *)(v35 - 21) = 1;
            }
            break;
          case 4:
            result = ((__int64 (__fastcall *)(__int64, __int64))sub_82E70)(v24, 76);
            break;
          default:
            ((void (__fastcall *)(__int64, __int64))sub_80FC8)(v24, 77);
            v36 = ((__int64 (__fastcall *)(_QWORD *, _QWORD, _QWORD))sub_47898)(
                    v19,
                    *(_QWORD *)(v21[1] + v26 + 8),
                    v16 + (unsigned int)v27 + 1);
            v37 = ((__int64 (__fastcall *)(__int64))sub_80214)(v36);
            result = ((__int64 (__fastcall *)(__int64))sub_4782C)(v37);
            break;
        }
      }
    }
    ++v27;
    v26 += 48;
  }
  v38 = (int *)v21[6];
  if ( v38 && (*(_BYTE *)(v104 + 45) & 0x20) == 0 )
  {
    v39 = 0;
    if ( a9 == 10 )
      v40 = 2;
    else
      v40 = a9;
    v41 = 0;
    v42 = v40;
    *((_DWORD *)v19 + 26) = v113;
    while ( v41 < *v38 )
    {
      v43 = sub_82794();
      v44 = sub_81A90();
      ((void (__fastcall *)(__int64, _QWORD, _QWORD))sub_4B680)(v44, *(_QWORD *)(v45 + v39), v43);
      if ( v42 == 4 )
      {
        ((void (__fastcall *)(__int64, __int64))sub_7EF64)(v24, 16);
      }
      else
      {
        v46 = *(_QWORD *)(*((_QWORD *)v38 + 1) + v39 + 8);
        if ( !v46 )
          v46 = *v21;
        if ( v42 == 5 )
          v42 = 2;
        ((void (__fastcall *)(_QWORD *, __int64, _QWORD, __int64, _QWORD, __int64))sub_49D64)(v19, 275, v42, v46, 0, 3);
      }
      result = ((__int64 (__fastcall *)(__int64))sub_81C94)(v24);
      ++v41;
      v39 += 32;
    }
  }
  v47 = 0;
  v48 = 0;
  v49 = 0;
  if ( v108 )
  {
    v50 = v110;
    v51 = v113;
    if ( !v118 )
    {
      v52 = ((__int64 (__fastcall *)(__int64))sub_48FC8)(v24);
      v53 = *((unsigned __int8 *)v21 + 73);
      v54 = v52;
      if ( v53 == 10 )
        LOBYTE(v53) = 2;
      if ( a9 != 10 )
        LOBYTE(v53) = a9;
      v55 = (unsigned __int8)v53;
      if ( v112 )
      {
        v56 = ((__int64 (__fastcall *)(__int64, __int64))sub_80220)(v52, 79);
        v57 = ((__int64 (__fastcall *)(__int64))sub_80F7C)(v56);
        if ( *(_QWORD *)(v24 + 8) )
          ((void (__fastcall *)(__int64))sub_7F750)(v57);
      }
      if ( a9 != 5 && v55 == 5 )
      {
        v59 = v21 + 2;
        while ( 1 )
        {
          v60 = *v59;
          if ( !v60 )
            break;
          v61 = *(unsigned __int8 *)(v60 + 98);
          v59 = (__int64 *)(v60 + 40);
          if ( (unsigned int)(v61 - 3) <= 1 )
          {
            v48 = sub_81108();
            goto LABEL_59;
          }
        }
      }
      v48 = 0;
LABEL_59:
      v62 = ((__int64 (__fastcall *)(__int64, __int64, _QWORD, _QWORD, _QWORD))sub_3924C)(v24, 67, v102, v54, v116);
      if ( v55 - 1 >= 3 )
      {
        if ( v55 == 4 )
        {
          v64 = ((__int64 (__fastcall *)(__int64, __int64))sub_7EF64)(v24, 16);
LABEL_72:
          v49 = 0;
LABEL_73:
          v66 = ((__int64 (__fastcall *)(__int64))sub_7EF88)(v64);
          result = ((__int64 (__fastcall *)(__int64))sub_490F0)(v66);
          if ( v48 )
          {
            v47 = ((__int64 (__fastcall *)(__int64))sub_81108)(result);
            result = ((__int64 (__fastcall *)(__int64))sub_7FE40)(v24);
          }
          else
          {
            v47 = 0;
          }
          v50 = v110;
          goto LABEL_77;
        }
        if ( v55 == 5 )
        {
          if ( (*(_BYTE *)(v104 + 46) & 4) != 0 && (v63 = ((__int64 (__fastcall *)(__int64))sub_808A0)(v62)) != 0
            || (v64 = ((__int64 (__fastcall *)(_QWORD *, __int64 *, __int64))sub_83630)(v19, v21, v63),
                v63 = 0,
                (_DWORD)v64) )
          {
            v65 = (_QWORD *)v19[56];
            v49 = 1;
            if ( !v65 )
              v65 = v19;
            *((_BYTE *)v65 + 32) = 1;
            v64 = ((__int64 (__fastcall *)(_QWORD *, __int64 *, __int64, _QWORD, _QWORD, _QWORD, __int64, _QWORD, char, char))sub_5A294)(
                    v19,
                    v21,
                    v63,
                    v102,
                    v109,
                    v116,
                    1,
                    0,
                    5,
                    1);
          }
          else
          {
            v49 = 1;
            if ( v21[2] )
            {
              v97 = (_QWORD *)v19[56];
              if ( !v97 )
                v97 = v19;
              *((_BYTE *)v97 + 32) = 1;
              v64 = ((__int64 (__fastcall *)(_QWORD *, __int64 *, _QWORD, _QWORD, _QWORD))sub_5AD50)(
                      v19,
                      v21,
                      v102,
                      v109,
                      0);
            }
          }
          goto LABEL_73;
        }
        v55 = 2;
      }
      v64 = ((__int64 (__fastcall *)(_QWORD *, _QWORD, __int64 *))sub_5CE74)(v19, v55, v21);
      goto LABEL_72;
    }
  }
  else
  {
    v50 = v110;
    v51 = v113;
  }
LABEL_77:
  v103 = v49;
  v67 = 0;
  v68 = v21 + 2;
  v69 = -1;
  v100 = v47;
  v101 = v48;
  v111 = 0;
  v114 = v19;
  while ( 1 )
  {
    v70 = *v68;
    if ( !*v68 )
      break;
    if ( !*(_DWORD *)(v50 + 4 * v67) )
      goto LABEL_108;
    if ( !v111 )
    {
      v71 = sub_83D80();
      ((void (__fastcall *)(__int64))sub_5C2A0)(v71);
      v111 = 1;
    }
    v72 = ((__int64 (__fastcall *)(__int64))sub_48FC8)(v24);
    if ( *(_QWORD *)(v70 + 72) )
    {
      v73 = ((__int64 (__fastcall *)(__int64, __int64))sub_7EF64)(v24, 28);
      *((_DWORD *)v19 + 26) = v51;
      v74 = ((__int64 (__fastcall *)(__int64, _QWORD))sub_81A90)(v73, *(_QWORD *)(v70 + 72));
      ((void (__fastcall *)(__int64))sub_49B74)(v74);
      *((_DWORD *)v19 + 26) = 0;
    }
    v117 = v72;
    v115 = v67;
    v75 = ((__int64 (__fastcall *)(_QWORD *, _QWORD))sub_4924C)(v19, *(unsigned __int16 *)(v70 + 96));
    for ( i = 0; i < *(unsigned __int16 *)(v70 + 96); ++i )
    {
      v77 = *(__int16 *)(*(_QWORD *)(v70 + 8) + 2 * i);
      if ( v77 < 0 || *((unsigned __int16 *)v21 + 32) == (unsigned __int16)v77 )
      {
        if ( v75 + (_DWORD)i == v69 )
          continue;
        if ( *(_QWORD *)(v70 + 72) )
          v69 = -1;
        else
          v69 = v75 + i;
      }
      sub_83D6C();
    }
    v78 = sub_827E0();
    v79 = ((__int64 (__fastcall *)(__int64))sub_3924C)(v78);
    v19 = v114;
    v80 = ((__int64 (__fastcall *)(__int64, _QWORD, _QWORD))sub_831A8)(v79, v75, *(unsigned __int16 *)(v70 + 96));
    if ( !v108 && v112 && v118 == v70 )
      goto LABEL_107;
    if ( !*(_BYTE *)(v70 + 98) )
    {
      v80 = ((__int64 (__fastcall *)(__int64))sub_81CAC)(v80);
LABEL_107:
      result = ((__int64 (__fastcall *)(__int64))sub_82DE0)(v80);
      v50 = v110;
      goto LABEL_108;
    }
    v81 = ((__int64 (__fastcall *)(__int64, __int64))sub_80220)(v80, 64);
    v106 = v82;
    ((void (__fastcall *)(__int64))sub_15300)(v81);
    v83 = v75;
    if ( v118 != v70 )
      v83 = ((__int64 (__fastcall *)(_QWORD, _QWORD))sub_83190)(v75, v105);
    v107 = v83;
    if ( v112 || v106 == 5 )
    {
      if ( (v21[9] & 0x20) != 0 )
      {
        if ( v118 != v70 )
        {
          for ( j = 0; j < *(unsigned __int16 *)(v118 + 94); ++j )
          {
            ((void (__fastcall *)(__int64, _QWORD))sub_49F78)(v70, *(unsigned __int16 *)(*(_QWORD *)(v118 + 8) + 2 * j));
            v86 = sub_80220();
            v83 = ((__int64 (__fastcall *)(__int64))sub_3924C)(v86);
          }
        }
        if ( v112 )
        {
          v87 = 0;
          v88 = *(unsigned __int16 *)(v118 + 94);
          v89 = 78;
          v90 = *(_DWORD *)(v24 + 60) + v88;
          if ( (*(_BYTE *)(v70 + 99) & 3) == 2 )
            v91 = v75;
          else
            v91 = v107;
          while ( v87 < (unsigned __int16)v88 )
          {
            v92 = ((__int64 (__fastcall *)(_QWORD *, _QWORD))sub_47C54)(
                    v114,
                    *(_QWORD *)(*(_QWORD *)(v118 + 64) + 8 * v87));
            if ( v87 == *(unsigned __int16 *)(v118 + 94) - 1 )
            {
              v90 = v117;
              v89 = 79;
            }
            v83 = ((__int64 (__fastcall *)(__int64, _QWORD, _QWORD, _QWORD, _QWORD, __int64, __int64))sub_47858)(
                    v24,
                    v89,
                    v112 + 1 + *(__int16 *)(*(_QWORD *)(v118 + 8) + 2 * v87),
                    v90,
                    v91 + (unsigned int)v87,
                    v92,
                    4294967292LL);
            if ( *(_QWORD *)(v24 + 8) )
              v83 = ((__int64 (__fastcall *)(__int64))sub_7F750)(v83);
            v51 = v113;
            ++v87;
            LOWORD(v88) = *(_WORD *)(v118 + 94);
          }
        }
      }
      else
      {
        v84 = ((__int64 (__fastcall *)(__int64, __int64))sub_80220)(v83, 109);
        v83 = ((__int64 (__fastcall *)(__int64))sub_47900)(v84);
        if ( v112 )
        {
          v83 = ((__int64 (__fastcall *)(__int64, __int64, _QWORD, _QWORD, _QWORD))sub_3924C)(v24, 79, v107, v117, v112);
          if ( *(_QWORD *)(v24 + 8) )
            v83 = ((__int64 (__fastcall *)(__int64))sub_7F750)(v83);
        }
      }
    }
    if ( (unsigned int)(v106 - 1) >= 3 )
    {
      if ( v106 == 4 )
      {
        v93 = ((__int64 (__fastcall *)(__int64, __int64))sub_7EF64)(v24, 16);
        v50 = v110;
        v19 = v114;
        v67 = v115;
      }
      else
      {
        v19 = v114;
        v67 = v115;
        v50 = v110;
        v94 = (_QWORD *)v114[56];
        if ( !v94 )
          v94 = v114;
        *((_BYTE *)v94 + 32) = 1;
        if ( (*(_BYTE *)(v104 + 46) & 4) != 0 )
          v95 = ((__int64 (__fastcall *)(__int64))sub_808A0)(v83);
        else
          v95 = 0;
        LOBYTE(v98) = 5;
        LOBYTE(v99) = v118 == v70;
        v93 = ((__int64 (__fastcall *)(_QWORD *, __int64 *, __int64, _QWORD, _QWORD, _QWORD, _QWORD, _QWORD, int, int))sub_5A294)(
                v114,
                v21,
                v95,
                v102,
                v109,
                v107,
                v105,
                0,
                v98,
                v99);
        v103 = 1;
      }
    }
    else
    {
      v19 = v114;
      v93 = ((__int64 (__fastcall *)(_QWORD *))sub_500D4)(v114);
      v50 = v110;
      v67 = v115;
    }
    v96 = ((__int64 (__fastcall *)(__int64))sub_82DE0)(v93);
    result = ((__int64 (__fastcall *)(__int64))sub_81CAC)(v96);
    if ( v107 != v75 )
      result = ((__int64 (__fastcall *)(_QWORD *))sub_494EC)(v19);
LABEL_108:
    v68 = (__int64 *)(v70 + 40);
    ++v67;
  }
  if ( v101 )
  {
    ((void (__fastcall *)(__int64, __int64))sub_7EF64)(v24, 16);
    result = ((__int64 (__fastcall *)(__int64, _QWORD))sub_4782C)(v24, v100);
  }
  *a11 = v103;
  return result;
}