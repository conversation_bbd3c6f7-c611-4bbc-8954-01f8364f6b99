(function() {
  console.log("[高德地图ANS分析器] 启动");
  
  // 全局变量
  var m1_ans_fd = -1;
  var m1_ans_address = null;
  var m1_ans_size = 0;
  var ansFiles = {};
  var maxDataSize = 64;
  
  // 工具函数：打印十六进制字节数据
  function hexdump(data, offset, length) {
    if (!data || !length) return "";
    
    var maxLen = Math.min(length, maxDataSize);
    var result = "";
    for (var i = offset; i < offset + maxLen; i++) {
      var byteVal = data[i] & 0xFF;
      var hex = byteVal.toString(16).toUpperCase();
      if (hex.length == 1) hex = "0" + hex;
      result += hex + " ";
    }
    
    return result;
  }
  
  console.log("[*] 安装Frida检测绕过...");
  
  // 防止应用崩溃 - 拦截exit函数
  var exit_ptr = Module.findExportByName("libc.so", "exit");
  if (exit_ptr) {
    Interceptor.replace(exit_ptr, new NativeCallback(function() {
      console.log("[+] 拦截exit()调用");
      return 0;
    }, 'void', ['int']));
    console.log("[+] 拦截exit()函数成功");
  }
  
  // 拦截/proc/self/maps文件读取
  Interceptor.attach(Module.findExportByName(null, "fopen"), {
    onEnter: function(args) {
      try {
        var path = args[0].readUtf8String();
        if (path === "/proc/self/maps") {
          console.log("[+] 拦截 /proc/self/maps 文件打开");
          this.maps_intercept = true;
        }
      } catch(e) {}
    },
    onLeave: function(retval) {
      if (this.maps_intercept && !retval.isNull()) {
        console.log("[+] 替换 /proc/self/maps 文件句柄");
      }
    }
  });
  
  // 监控库加载
  Interceptor.attach(Module.findExportByName(null, "dlopen"), {
    onEnter: function(args) {
      try {
        this.path = args[0].readUtf8String();
      } catch(e) {}
    },
    onLeave: function(retval) {
      if (this.path && this.path.indexOf("libamaploc.so") !== -1) {
        console.log("[库加载] " + this.path);
      }
    }
  });
  
  // 监控文件打开
  Interceptor.attach(Module.findExportByName("libc.so", "open"), {
    onEnter: function(args) {
      try {
        var path = args[0].readUtf8String();
        if (path && path.indexOf(".ans") !== -1) {
          this.path = path;
          this.isAnsFile = true;
          
          if (path.indexOf("m1.ans") !== -1) {
            this.isM1Ans = true;
            console.log("[重要] m1.ans文件打开: " + path);
          }
        }
      } catch(e) {}
    },
    onLeave: function(retval) {
      if (!this.isAnsFile) return;
      
      var fd = retval.toInt32();
      if (fd > 0) {
        ansFiles[fd] = this.path;
        
        if (this.isM1Ans) {
          m1_ans_fd = fd;
          console.log("[+] m1.ans文件描述符: " + fd);
        }
        
        console.log("[ANS文件] 打开: " + this.path + " -> fd:" + fd);
      }
    }
  });
  
  // 监控mmap内存映射
  Interceptor.attach(Module.findExportByName("libc.so", "mmap"), {
    onEnter: function(args) {
      this.fd = args[4].toInt32();
      this.length = args[1].toInt32();
      this.offset = args[5].toInt32();
      
      if (this.fd === m1_ans_fd) {
        this.isM1Ans = true;
      }
    },
    onLeave: function(retval) {
      if (!this.isM1Ans) return;
      
      var mapped_addr = retval;
      if (!mapped_addr.isNull()) {
        m1_ans_address = mapped_addr;
        m1_ans_size = this.length;
        
        console.log("[M1.ANS映射] 地址: " + mapped_addr + 
                   ", 大小: " + this.length + " 字节" + 
                   ", 偏移: " + this.offset);
        
        try {
          var header = Memory.readByteArray(mapped_addr, Math.min(32, this.length));
          console.log("[M1.ANS头部] " + hexdump(new Uint8Array(header), 0, header.byteLength));
        } catch(e) {}
      }
    }
  });
  
  // Java层绕过与分析 - 使用延迟执行
  setTimeout(function() {
    Java.perform(function() {
      console.log("[*] 开始Hook Java层...");
      
      try {
        // 绕过Frida检测
        var ExecutableMaps = Java.use("com.alipay.security.mobileaspectbundle.probe.security.ExecutableMaps");
        ExecutableMaps.isFridaInject.implementation = function() {
          console.log("[+] 绕过 isFridaInject 检测");
          return false;
        };
        
        // 可能需要绕过ArtHookDetection
        try {
          var ArtHookDetection_MethodInfo = Java.use("com.alipay.security.mobileaspectbundle.probe.security.ArtHookDetection$MethodInfo");
          ArtHookDetection_MethodInfo.isHook.implementation = function() {
            console.log("[+] 绕过 ArtHookDetection.isHook 检测");
            return 0;
          };
        } catch(e) {
          console.log("[!] ArtHookDetection$MethodInfo类hook失败: " + e);
        }
        
        // Hook AMapController
        try {
          var AMapController = Java.use("com.autonavi.ae.gmap.AMapController");
          AMapController.setAppResourceLoader.implementation = function(loader) {
            console.log("[Java] AMapController.setAppResourceLoader调用");
            var result = this.setAppResourceLoader(loader);
            return result;
          };
        } catch(e) {
          console.log("[!] AMapController类hook失败: " + e);
        }
        
        // Hook InterfaceAppImpl
        try {
          var InterfaceAppImpl = Java.use("com.amap.jni.app.InterfaceAppImpl");
          InterfaceAppImpl.getNativeResourceLoader.implementation = function() {
            console.log("[Java] InterfaceAppImpl.getNativeResourceLoader调用");
            var loader = this.getNativeResourceLoader();
            return loader;
          };
        } catch(e) {
          console.log("[!] InterfaceAppImpl类hook失败: " + e);
        }
        
        // Hook AjxBLFactoryController
        try {
          var AjxBLFactoryController = Java.use("com.autonavi.jni.ajx3.bl.AjxBLFactoryController");
          
          if (AjxBLFactoryController.warmInit) {
            AjxBLFactoryController.warmInit.overload('int', 'boolean').implementation = function(arg1, arg2) {
              console.log("[Java] AjxBLFactoryController.warmInit(" + arg1 + ", " + arg2 + ")");
              var result = this.warmInit(arg1, arg2);
              return result;
            };
          }
        } catch(e) {
          console.log("[!] AjxBLFactoryController类hook失败: " + e);
        }
        
        console.log("[+] Java层Hook完成");
      } catch(e) {
        console.log("[!] Java层Hook出错: " + e);
      }
    });
  }, 2000);
})();
