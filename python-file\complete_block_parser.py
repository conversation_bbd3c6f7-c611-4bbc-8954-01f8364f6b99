#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整数据块解析器
严格按照APP代码解析方式，提取完整的数据块内容
不进行片段分析，而是按照真实APP逻辑完整解析数据块
"""

import os
import zlib
import struct
import glob

class CompleteBlockParser:
    """完整数据块解析器 - 按照真实APP方式"""
    
    def __init__(self):
        self.processed_blocks = 0
        
    def parse_all_decompressed_blocks(self):
        """解析所有解压的数据块 - 按照真实APP的完整解析逻辑"""
        print("🎯 完整数据块解析器")
        print("严格按照APP代码解析方式，提取完整数据块内容")
        print("=" * 80)
        
        # 获取所有解压文件
        bin_files = glob.glob("real_decompressed_*.bin")
        
        if not bin_files:
            print("❌ 未找到解压数据文件")
            return
        
        print(f"📁 找到 {len(bin_files)} 个解压数据块")
        
        # 按照真实APP逻辑解析每个数据块
        for i, filename in enumerate(bin_files):
            offset = self.extract_offset_from_filename(filename)
            print(f"\n🔧 解析数据块 #{i+1}: {filename}")
            print(f"    原始偏移: 0x{offset:08X}")
            
            # 按照真实APP的完整解析逻辑
            self.parse_complete_data_block(filename, offset)
            
            self.processed_blocks += 1
            
            # 限制处理数量以查看结果
            if self.processed_blocks >= 20:
                print(f"\n📊 已处理 {self.processed_blocks} 个数据块，查看结果...")
                break
        
        print(f"\n🎉 完整数据块解析完成！处理了 {self.processed_blocks} 个数据块")
    
    def extract_offset_from_filename(self, filename):
        """从文件名提取偏移地址"""
        try:
            # 文件名格式: real_decompressed_000D55A9.bin
            hex_part = filename.split('_')[2].split('.')[0]
            return int(hex_part, 16)
        except:
            return 0
    
    def parse_complete_data_block(self, filename, offset):
        """按照真实APP逻辑完整解析数据块"""
        try:
            with open(filename, 'rb') as f:
                data = f.read()
            
            print(f"    📦 数据块大小: {len(data)} 字节")
            
            # 按照真实APP的解析流程
            # 1. 解析数据块头部
            if len(data) >= 16:
                header_info = self.parse_block_header(data)
                print(f"    🔍 头部信息: {header_info}")
            
            # 2. 按照APP逻辑识别数据类型并完整解析
            self.identify_and_parse_complete_content(data, offset)
            
        except Exception as e:
            print(f"    ❌ 解析失败: {e}")
    
    def parse_block_header(self, data):
        """解析数据块头部 - 按照真实APP结构"""
        if len(data) < 16:
            return "头部数据不足"
        
        # 按照真实APP的数据块头部结构
        try:
            field1 = struct.unpack('<I', data[0:4])[0]
            field2 = struct.unpack('<I', data[4:8])[0] 
            field3 = struct.unpack('<I', data[8:12])[0]
            field4 = struct.unpack('<I', data[12:16])[0]
            
            return f"[{field1:08X}] [{field2:08X}] [{field3:08X}] [{field4:08X}]"
        except:
            return "头部解析失败"
    
    def identify_and_parse_complete_content(self, data, offset):
        """识别并完整解析数据内容 - 按照真实APP逻辑"""
        
        # 按照真实APP的数据类型识别逻辑
        content_type = self.identify_content_type(data)
        print(f"    🎯 数据类型: {content_type}")
        
        # 根据数据类型进行完整解析
        if content_type == "地名道路数据":
            self.parse_complete_road_data(data, offset)
        elif content_type == "矢量坐标数据":
            self.parse_complete_vector_data(data, offset)
        elif content_type == "POI数据":
            self.parse_complete_poi_data(data, offset)
        elif content_type == "索引数据":
            self.parse_complete_index_data(data, offset)
        else:
            self.parse_complete_generic_data(data, offset)
    
    def identify_content_type(self, data):
        """识别数据内容类型 - 按照真实APP逻辑"""
        
        # 检查是否包含中文地名数据
        chinese_count = 0
        for i in range(len(data) - 2):
            if data[i:i+3] in [b'\xe4\xb8\xad', b'\xe5\x9c\xb0', b'\xe8\xb7\xaf', 
                              b'\xe9\x81\x93', b'\xe6\xa1\xa5', b'\xe5\x8c\xba']:
                chinese_count += 1
        
        if chinese_count > 5:
            return "地名道路数据"
        
        # 检查是否为矢量坐标数据
        float_count = 0
        for i in range(0, len(data) - 7, 4):
            try:
                value = struct.unpack('<f', data[i:i+4])[0]
                if -180.0 <= value <= 180.0 and abs(value) > 0.001:
                    float_count += 1
                    if float_count > 20:
                        break
            except:
                continue
        
        if float_count > 20:
            return "矢量坐标数据"
        
        # 检查数据块结构模式
        if len(data) >= 4:
            first_field = struct.unpack('<I', data[0:4])[0]
            if first_field == 0x0D:  # 特定的索引标识
                return "索引数据"
            elif first_field > 0x20000000:
                return "POI数据"
        
        return "未知数据类型"
    
    def parse_complete_road_data(self, data, offset):
        """完整解析地名道路数据 - 按照真实APP逻辑"""
        print(f"    🛣️  完整解析地名道路数据:")
        
        try:
            # 按照真实APP的字符串解析逻辑
            text = data.decode('utf-8', errors='ignore')
            
            # 提取所有中文内容
            chinese_chars = ''.join(c for c in text if '\u4e00' <= c <= '\u9fff')
            
            if len(chinese_chars) > 10:
                print(f"        📄 提取到 {len(chinese_chars)} 个中文字符")
                
                # 按照真实APP逻辑分割道路名称
                road_names = self.extract_road_names(chinese_chars)
                print(f"        🛣️  识别出 {len(road_names)} 个道路/地名")
                
                # 保存完整的解析结果
                output_file = f"complete_road_data_{offset:08X}.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 完整地名道路数据解析结果\n")
                    f.write(f"# 数据块偏移: 0x{offset:08X}\n")
                    f.write(f"# 中文字符总数: {len(chinese_chars)}\n")
                    f.write(f"# 识别道路/地名数: {len(road_names)}\n\n")
                    
                    f.write("=== 完整中文文本 ===\n")
                    f.write(chinese_chars)
                    f.write("\n\n=== 道路/地名列表 ===\n")
                    
                    for i, name in enumerate(road_names, 1):
                        f.write(f"{i:3d}. {name}\n")
                    
                    f.write(f"\n=== 原始数据块 (前1000字节) ===\n")
                    f.write(text[:1000])
                
                print(f"        💾 完整解析保存到: {output_file}")
                print(f"        📋 预览前10个道路名: {road_names[:10]}")
            
        except Exception as e:
            print(f"        ❌ 道路数据解析失败: {e}")
    
    def extract_road_names(self, chinese_text):
        """从中文文本中提取道路名称 - 按照真实APP逻辑"""
        # 常见的道路/地名关键词
        road_keywords = ['高速', '公路', '大道', '路', '街', '桥', '隧道', '立交', '出口', '入口', 
                        '环路', '快速路', '辅路', '支路', '村', '镇', '区', '园', '场', '站',
                        '机场', '港', '码头', '广场', '中心', '小镇', '度假区', '保护区']
        
        road_names = []
        current_name = ""
        
        for char in chinese_text:
            current_name += char
            
            # 检查是否遇到道路关键词
            for keyword in road_keywords:
                if current_name.endswith(keyword):
                    # 提取完整道路名
                    if len(current_name) >= 3:  # 至少3个字符
                        road_names.append(current_name)
                    current_name = ""
                    break
        
        # 去重并保持顺序
        seen = set()
        unique_roads = []
        for road in road_names:
            if road not in seen:
                seen.add(road)
                unique_roads.append(road)
        
        return unique_roads
    
    def parse_complete_vector_data(self, data, offset):
        """完整解析矢量坐标数据 - 按照真实APP逻辑"""
        print(f"    📍 完整解析矢量坐标数据:")
        
        try:
            coordinates = []
            
            # 按照真实APP的坐标解析逻辑
            for i in range(0, len(data) - 7, 4):
                try:
                    value = struct.unpack('<f', data[i:i+4])[0]
                    if -180.0 <= value <= 180.0 and abs(value) > 0.001:
                        coordinates.append(value)
                except:
                    continue
            
            if len(coordinates) >= 4:
                coord_pairs = len(coordinates) // 2
                print(f"        📊 提取到 {coord_pairs} 个坐标点")
                
                # 保存完整的坐标数据
                output_file = f"complete_vector_data_{offset:08X}.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 完整矢量坐标数据解析结果\n")
                    f.write(f"# 数据块偏移: 0x{offset:08X}\n")
                    f.write(f"# 坐标点数: {coord_pairs}\n")
                    f.write(f"# 坐标范围: {min(coordinates):.6f} 到 {max(coordinates):.6f}\n\n")
                    
                    f.write("=== 完整坐标列表 ===\n")
                    for i in range(0, len(coordinates) - 1, 2):
                        if i+1 < len(coordinates):
                            f.write(f"点 {i//2 + 1:4d}: ({coordinates[i]:11.6f}, {coordinates[i+1]:11.6f})\n")
                
                print(f"        💾 完整坐标保存到: {output_file}")
                print(f"        📋 坐标范围: {min(coordinates):.3f} 到 {max(coordinates):.3f}")
            
        except Exception as e:
            print(f"        ❌ 矢量数据解析失败: {e}")
    
    def parse_complete_poi_data(self, data, offset):
        """完整解析POI数据 - 按照真实APP逻辑"""
        print(f"    📍 完整解析POI数据:")
        
        # 按照真实APP的POI解析逻辑
        self.parse_complete_generic_data(data, offset, "POI")
    
    def parse_complete_index_data(self, data, offset):
        """完整解析索引数据 - 按照真实APP逻辑"""
        print(f"    📇 完整解析索引数据:")
        
        try:
            if len(data) >= 16:
                # 按照真实APP的索引结构解析
                index_type = struct.unpack('<I', data[0:4])[0]
                index_count = struct.unpack('<I', data[4:8])[0]
                index_size = struct.unpack('<I', data[8:12])[0]
                index_flags = struct.unpack('<I', data[12:16])[0]
                
                print(f"        📊 索引类型: 0x{index_type:08X}")
                print(f"        📊 索引数量: {index_count}")
                print(f"        📊 索引大小: {index_size}")
                print(f"        📊 索引标志: 0x{index_flags:08X}")
                
                # 保存完整索引信息
                output_file = f"complete_index_data_{offset:08X}.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 完整索引数据解析结果\n")
                    f.write(f"# 数据块偏移: 0x{offset:08X}\n")
                    f.write(f"# 索引类型: 0x{index_type:08X}\n")
                    f.write(f"# 索引数量: {index_count}\n")
                    f.write(f"# 索引大小: {index_size}\n")
                    f.write(f"# 索引标志: 0x{index_flags:08X}\n\n")
                    
                    f.write("=== 完整十六进制数据 ===\n")
                    for i in range(0, min(1024, len(data)), 16):
                        hex_line = " ".join(f"{data[i+j]:02X}" for j in range(min(16, len(data)-i)))
                        ascii_line = "".join(chr(data[i+j]) if 32 <= data[i+j] < 127 else "." for j in range(min(16, len(data)-i)))
                        f.write(f"{i:04X}: {hex_line:<48} |{ascii_line}|\n")
                
                print(f"        💾 完整索引保存到: {output_file}")
        
        except Exception as e:
            print(f"        ❌ 索引数据解析失败: {e}")
    
    def parse_complete_generic_data(self, data, offset, data_type="通用"):
        """完整解析通用数据 - 按照真实APP逻辑"""
        print(f"    🔍 完整解析{data_type}数据:")
        
        try:
            # 保存完整数据的十六进制和可读内容
            output_file = f"complete_{data_type.lower()}_data_{offset:08X}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"# 完整{data_type}数据解析结果\n")
                f.write(f"# 数据块偏移: 0x{offset:08X}\n")
                f.write(f"# 数据块大小: {len(data)} 字节\n\n")
                
                # 尝试UTF-8解码
                try:
                    text = data.decode('utf-8', errors='ignore')
                    f.write("=== UTF-8文本内容 ===\n")
                    f.write(text[:2000])  # 前2000字符
                    f.write("\n\n")
                except:
                    pass
                
                f.write("=== 完整十六进制数据 ===\n")
                for i in range(0, min(2048, len(data)), 16):
                    hex_line = " ".join(f"{data[i+j]:02X}" for j in range(min(16, len(data)-i)))
                    ascii_line = "".join(chr(data[i+j]) if 32 <= data[i+j] < 127 else "." for j in range(min(16, len(data)-i)))
                    f.write(f"{i:04X}: {hex_line:<48} |{ascii_line}|\n")
            
            print(f"        💾 完整{data_type}数据保存到: {output_file}")
            
        except Exception as e:
            print(f"        ❌ {data_type}数据解析失败: {e}")

def main():
    """主函数"""
    parser = CompleteBlockParser()
    parser.parse_all_decompressed_blocks()

if __name__ == "__main__":
    main() 