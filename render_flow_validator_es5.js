// 渲染流程验证脚本 - ES5兼容版本
// 验证8阶段渲染流程的完整性和准确性

console.log("[渲染流程验证] 开始验证8阶段离线地图渲染流程...");

// 8阶段流程验证状态
var stageValidation = {
    stage1_dataAcquisition: { triggered: false, count: 0, details: [] },
    stage2_dataProcessing: { triggered: false, count: 0, details: [] },
    stage3_geometryProcessing: { triggered: false, count: 0, details: [] },
    stage4_styleApplication: { triggered: false, count: 0, details: [] },
    stage5_textProcessing: { triggered: false, count: 0, details: [] },
    stage6_openglRendering: { triggered: false, count: 0, details: [] },
    stage7_postProcessing: { triggered: false, count: 0, details: [] },
    stage8_displayOutput: { triggered: false, count: 0, details: [] }
};

// 验证结果统计
var validationResults = {
    totalStagesTriggered: 0,
    flowCompleteness: 0,
    performanceMetrics: {
        avgFileReadTime: 0,
        avgDecompressionTime: 0,
        avgRenderTime: 0
    }
};

// 时间戳记录
var timestamps = {
    lastFileRead: 0,
    lastDecompression: 0,
    lastRender: 0
};

// 验证阶段1: 数据获取
function validateStage1(fd, size, dataType) {
    if (!stageValidation.stage1_dataAcquisition.triggered) {
        stageValidation.stage1_dataAcquisition.triggered = true;
        console.log("[✓] 阶段1验证: 数据获取阶段已触发");
    }
    
    stageValidation.stage1_dataAcquisition.count++;
    stageValidation.stage1_dataAcquisition.details.push({
        fd: fd,
        size: size,
        type: dataType,
        timestamp: Date.now()
    });
    
    timestamps.lastFileRead = Date.now();
}

// 验证阶段2: 数据处理
function validateStage2(originalSize, decompressedSize, dataType) {
    if (!stageValidation.stage2_dataProcessing.triggered) {
        stageValidation.stage2_dataProcessing.triggered = true;
        console.log("[✓] 阶段2验证: 数据处理阶段已触发");
    }
    
    stageValidation.stage2_dataProcessing.count++;
    stageValidation.stage2_dataProcessing.details.push({
        originalSize: originalSize,
        decompressedSize: decompressedSize,
        compressionRatio: (decompressedSize / originalSize).toFixed(2),
        dataType: dataType,
        timestamp: Date.now()
    });
    
    timestamps.lastDecompression = Date.now();
}

// 验证阶段3: 几何处理
function validateStage3(geometryType, vertexCount) {
    if (!stageValidation.stage3_geometryProcessing.triggered) {
        stageValidation.stage3_geometryProcessing.triggered = true;
        console.log("[✓] 阶段3验证: 几何处理阶段已触发");
    }
    
    stageValidation.stage3_geometryProcessing.count++;
    stageValidation.stage3_geometryProcessing.details.push({
        geometryType: geometryType,
        vertexCount: vertexCount,
        timestamp: Date.now()
    });
}

// 验证阶段4: 样式应用
function validateStage4(styleType, paramCount) {
    if (!stageValidation.stage4_styleApplication.triggered) {
        stageValidation.stage4_styleApplication.triggered = true;
        console.log("[✓] 阶段4验证: 样式应用阶段已触发");
    }
    
    stageValidation.stage4_styleApplication.count++;
    stageValidation.stage4_styleApplication.details.push({
        styleType: styleType,
        paramCount: paramCount,
        timestamp: Date.now()
    });
}

// 验证阶段5: 文本处理
function validateStage5(textCount, encoding) {
    if (!stageValidation.stage5_textProcessing.triggered) {
        stageValidation.stage5_textProcessing.triggered = true;
        console.log("[✓] 阶段5验证: 文本处理阶段已触发");
    }
    
    stageValidation.stage5_textProcessing.count++;
    stageValidation.stage5_textProcessing.details.push({
        textCount: textCount,
        encoding: encoding,
        timestamp: Date.now()
    });
}

// 验证阶段6: OpenGL渲染
function validateStage6(renderMode, vertexCount) {
    if (!stageValidation.stage6_openglRendering.triggered) {
        stageValidation.stage6_openglRendering.triggered = true;
        console.log("[✓] 阶段6验证: OpenGL渲染阶段已触发");
    }
    
    stageValidation.stage6_openglRendering.count++;
    stageValidation.stage6_openglRendering.details.push({
        renderMode: renderMode,
        vertexCount: vertexCount,
        timestamp: Date.now()
    });
    
    timestamps.lastRender = Date.now();
}

// 数据类型识别
function identifyDataType(header) {
    var view = new Uint8Array(header);
    
    if (view.length >= 4) {
        if (view[0] === 0x44 && view[1] === 0x49 && view[2] === 0x43 && view[3] === 0x45) {
            return "DICE-AM";
        }
        if (view[0] === 0xbc && view[1] === 0xbc && view[2] === 0xbc && view[3] === 0xbc) {
            return "CONFIG";
        }
        if (view[0] === 0x0d && view[1] === 0x00 && view[2] === 0x00 && view[3] === 0x00) {
            return "TEXT";
        }
        if (view[0] === 0x78 && view[1] === 0x9c) {
            return "ZLIB";
        }
        if (view[0] === 0x08) {
            return "AM-ZLIB";
        }
    }
    return "OTHER";
}

// Hook设置
var libc = null;
var libz = null;
var libGLESv2 = null;

// 1. Hook文件读取 (阶段1验证)
try {
    libc = Process.getModuleByName("libc.so");
    var readPtr = libc.getExportByName("read");
    
    Interceptor.attach(readPtr, {
        onEnter: function(args) {
            this.fd = args[0].toInt32();
            this.buf = args[1];
            this.count = args[2].toInt32();
        },
        onLeave: function(retval) {
            var bytesRead = retval.toInt32();
            if (bytesRead > 0 && this.count >= 8) {
                try {
                    var header = this.buf.readByteArray(Math.min(8, bytesRead));
                    if (header) {
                        var dataType = identifyDataType(header);
                        if (dataType === "AM-ZLIB" || dataType === "ZLIB") {
                            validateStage1(this.fd, bytesRead, dataType);
                        }
                    }
                } catch (e) {
                    // 忽略错误
                }
            }
        }
    });
    console.log("[✓] 阶段1 Hook设置成功");
} catch (e) {
    console.log("[✗] 阶段1 Hook失败: " + e.message);
}

// 2. Hook zlib解压 (阶段2验证) - 简化版本
try {
    libz = Process.getModuleByName("libz.so");
    var uncompressPtr = libz.getExportByName("uncompress");

    Interceptor.attach(uncompressPtr, {
        onEnter: function(args) {
            this.dest = args[0];
            this.destLen = args[1];
            this.source = args[2];
            this.sourceLen = args[3];
        },
        onLeave: function(retval) {
            if (retval.toInt32() === 0) {
                try {
                    // 简化验证，只检查解压成功
                    if (!stageValidation.stage2_dataProcessing.triggered) {
                        stageValidation.stage2_dataProcessing.triggered = true;
                        console.log("[✓] 阶段2验证: 数据处理阶段已触发");
                    }

                    stageValidation.stage2_dataProcessing.count++;

                    // 简单的数据类型推断
                    var sourceSize = this.sourceLen.readU32();
                    var decompressedSize = this.destLen.readU32();

                    if (decompressedSize > 0 && decompressedSize < 1000000) {
                        validateStage2(sourceSize, decompressedSize, "UNKNOWN");

                        // 基于大小推断数据类型并验证后续阶段
                        if (decompressedSize > 8000) {
                            validateStage3("大型几何", decompressedSize / 8);
                        } else if (decompressedSize > 2000) {
                            validateStage4("配置数据", decompressedSize / 16);
                        } else {
                            validateStage5(decompressedSize / 32, "UTF-8");
                        }
                    }
                } catch (e) {
                    // 忽略所有错误
                }
            }
        }
    });
    console.log("[✓] 阶段2 Hook设置成功");
} catch (e) {
    console.log("[✗] 阶段2 Hook失败: " + e.message);
}

// 3. Hook OpenGL渲染 (阶段6验证)
try {
    libGLESv2 = Process.getModuleByName("libGLESv2.so");
    var glDrawArraysPtr = libGLESv2.getExportByName("glDrawArrays");
    
    Interceptor.attach(glDrawArraysPtr, {
        onEnter: function(args) {
            var mode = args[0].toInt32();
            var count = args[2].toInt32();
            
            var renderMode = "UNKNOWN";
            if (mode === 0x0004) renderMode = "GL_TRIANGLES";
            else if (mode === 0x0005) renderMode = "GL_TRIANGLE_STRIP";
            else if (mode === 0x0001) renderMode = "GL_LINES";
            else if (mode === 0x0003) renderMode = "GL_LINE_STRIP";
            
            validateStage6(renderMode, count);
        }
    });
    console.log("[✓] 阶段6 Hook设置成功");
} catch (e) {
    console.log("[✗] 阶段6 Hook失败: " + e.message);
}

// 计算流程完整性
function calculateFlowCompleteness() {
    var triggeredStages = 0;
    for (var stage in stageValidation) {
        if (stageValidation[stage].triggered) {
            triggeredStages++;
        }
    }
    validationResults.totalStagesTriggered = triggeredStages;
    validationResults.flowCompleteness = (triggeredStages / 8 * 100).toFixed(1);
}

// 定期验证报告
setInterval(function() {
    calculateFlowCompleteness();
    
    console.log("\n[渲染流程验证报告] ==========================================");
    console.log("流程完整性: " + validationResults.flowCompleteness + "% (" + 
                validationResults.totalStagesTriggered + "/8阶段已触发)");
    console.log("");
    
    console.log("各阶段验证状态:");
    console.log("  阶段1-数据获取: " + (stageValidation.stage1_dataAcquisition.triggered ? "✓" : "✗") + 
                " (触发" + stageValidation.stage1_dataAcquisition.count + "次)");
    console.log("  阶段2-数据处理: " + (stageValidation.stage2_dataProcessing.triggered ? "✓" : "✗") + 
                " (触发" + stageValidation.stage2_dataProcessing.count + "次)");
    console.log("  阶段3-几何处理: " + (stageValidation.stage3_geometryProcessing.triggered ? "✓" : "✗") + 
                " (触发" + stageValidation.stage3_geometryProcessing.count + "次)");
    console.log("  阶段4-样式应用: " + (stageValidation.stage4_styleApplication.triggered ? "✓" : "✗") + 
                " (触发" + stageValidation.stage4_styleApplication.count + "次)");
    console.log("  阶段5-文本处理: " + (stageValidation.stage5_textProcessing.triggered ? "✓" : "✗") + 
                " (触发" + stageValidation.stage5_textProcessing.count + "次)");
    console.log("  阶段6-OpenGL渲染: " + (stageValidation.stage6_openglRendering.triggered ? "✓" : "✗") + 
                " (触发" + stageValidation.stage6_openglRendering.count + "次)");
    console.log("  阶段7-后处理: " + (stageValidation.stage7_postProcessing.triggered ? "✓" : "✗") + 
                " (触发" + stageValidation.stage7_postProcessing.count + "次)");
    console.log("  阶段8-显示输出: " + (stageValidation.stage8_displayOutput.triggered ? "✓" : "✗") + 
                " (触发" + stageValidation.stage8_displayOutput.count + "次)");
    
    console.log("\n流程验证结论:");
    if (validationResults.flowCompleteness >= 75) {
        console.log("  [结论] 渲染流程分析基本正确，主要阶段已验证");
    } else if (validationResults.flowCompleteness >= 50) {
        console.log("  [结论] 渲染流程分析部分正确，需要进一步验证");
    } else {
        console.log("  [结论] 渲染流程分析需要重新评估");
    }
    console.log("===============================================\n");
}, 18000);

console.log("[渲染流程验证] 验证脚本已启动，等待地图操作...");
console.log("[提示] 请移动地图以触发完整的8阶段渲染流程验证");
