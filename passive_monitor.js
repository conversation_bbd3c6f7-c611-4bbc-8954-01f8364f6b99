/*
 * 高德地图完全被动监控器
 * 只记录调用，不读取内存，确保零崩溃
 * 版本: Frida 12.9.7 兼容
 */

console.log("[Passive Monitor] 启动完全被动监控器...");

var stats = {
    fileReads: 0,
    sqliteCalls: 0,
    totalStartTime: Date.now()
};

function setupPassiveMonitor() {
    setTimeout(function() {
        console.log("[Setup] 开始设置完全被动监控...");
        
        try {
            var lib = Module.findBaseAddress("libamapnsq.so");
            if (!lib) {
                console.log("[Error] 未找到libamapnsq.so");
                return;
            }
            
            console.log("[Library] 库基址: " + lib);
            
            // 完全被动的文件读取监控 - 只记录大小
            var readPtr = Module.findExportByName("libc.so", "read");
            if (readPtr) {
                Interceptor.attach(readPtr, {
                    onEnter: function(args) {
                        this.size = args[2].toInt32();
                        this.isLargeFile = (this.size > 1000);
                    },
                    onLeave: function(retval) {
                        if (this.isLargeFile) {
                            var bytesRead = retval.toInt32();
                            if (bytesRead > 0) {
                                stats.fileReads++;
                                if (stats.fileReads <= 5) {
                                    console.log("[File] 读取 #" + stats.fileReads + " - 大小: " + bytesRead + " 字节");
                                }
                            }
                        }
                    }
                });
                console.log(" 被动文件监控设置成功");
            }
            
            // 完全被动的SQLite监控 - 只记录调用
            try {
                var sqliteImplAddr = lib.add(0x15000);
                Interceptor.attach(sqliteImplAddr, {
                    onEnter: function(args) {
                        stats.sqliteCalls++;
                        if (stats.sqliteCalls <= 5) {
                            console.log("[SQLite] 调用 #" + stats.sqliteCalls + " - bind_blob");
                            
                            // 只记录参数地址，不读取内容
                            try {
                                var dataPtr = args[2];
                                var sizePtr = args[3];
                                
                                if (dataPtr && !dataPtr.isNull()) {
                                    console.log("[SQLite] 数据指针: " + dataPtr);
                                }
                                if (sizePtr) {
                                    var size = sizePtr.toInt32();
                                    console.log("[SQLite] 数据大小: " + size + " 字节");
                                }
                            } catch (e) {
                                console.log("[SQLite] 参数读取错误: " + e.message);
                            }
                        }
                    }
                });
                console.log(" 被动SQLite监控设置成功");
            } catch (e) {
                console.log("[SQLite] Hook设置失败: " + e.message);
            }
            
            console.log("[Ready] 被动监控设置完成 - 零内存读取模式");
            
        } catch (e) {
            console.log("[Setup Error] " + e.message);
        }
    }, 3000);
}

function generatePassiveReport() {
    var runtime = Math.round((Date.now() - stats.totalStartTime) / 1000);
    
    console.log("\n === 被动监控报告 ===");
    console.log(" 运行时间: " + runtime + " 秒");
    console.log(" 文件读取: " + stats.fileReads + " 次");
    console.log(" SQLite调用: " + stats.sqliteCalls + " 次");
    
    if (stats.fileReads > 0 || stats.sqliteCalls > 0) {
        console.log(" 检测到数据活动，应用正常运行");
    } else {
        console.log(" 未检测到活动，请操作地图");
    }
    
    console.log(" 零崩溃模式 - 只监控不干扰");
    console.log("==========================\n");
}

// 启动被动监控
setupPassiveMonitor();
setInterval(generatePassiveReport, 15000);

console.log("[Passive Monitor] 完全被动监控器已加载 - 保证不崩溃"); 