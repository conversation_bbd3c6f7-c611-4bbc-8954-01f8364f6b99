/*
 * 高德地图解压后数据提取器 - 最简化版本
 * 完全基于成功脚本结构
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[Real Data Extractor] 启动解压后数据提取器...");
    
    var CONFIG = {
        INIT_DELAY: 3000,
        MAX_SAMPLES: 5
    };
    
    var extractorStats = {
        totalExtractions: 0,
        successfulExtractions: 0,
        foundCoordinates: 0,
        foundChineseText: 0
    };
    
    // === 库等待函数 ===
    function waitForLibrary(libraryName, callback) {
        var maxAttempts = 30;
        var attempt = 0;
        
        function checkLibrary() {
            try {
                var lib = Module.findBaseAddress(libraryName);
                if (lib) {
                    console.log("[Library] " + libraryName + " 已加载，基址: " + lib);
                    callback(lib);
                    return;
                }
            } catch (e) {
                // 继续等待
            }
            
            if (++attempt < maxAttempts) {
                setTimeout(checkLibrary, 1000);
            } else {
                console.log("[Error] " + libraryName + " 加载超时");
            }
        }
        
        checkLibrary();
    }
    
    // === 数据提取函数 ===
    function extractRealData(dataPtr, size, source) {
        try {
            if (!dataPtr || dataPtr.isNull() || size <= 0) {
                return null;
            }
            
            var safeSize = Math.min(size, 1024);
            var data = dataPtr.readByteArray(safeSize);
            var view = new Uint8Array(data);
            
            // 构建签名
            var signature = "";
            for (var i = 0; i < Math.min(32, view.length); i++) {
                if (view[i] >= 32 && view[i] < 127) {
                    signature += String.fromCharCode(view[i]);
                } else {
                    signature += ".";
                }
            }
            
            // 检查是否为地图数据
            var isMapData = (
                signature.indexOf('.C.U') !== -1 ||
                signature.indexOf('. .f') !== -1 ||
                signature.indexOf('DICE') !== -1 ||
                view[0] === 0x08
            );
            
            // 特别处理DICE-AM数据
            var isDiceAM = (view.length >= 7 && 
                           view[0] === 0x44 && view[1] === 0x49 && view[2] === 0x43 && 
                           view[3] === 0x45 && view[4] === 0x2D && view[5] === 0x41 && view[6] === 0x4D);
            
            if (isMapData || isDiceAM) {
                console.log("[Map Data Found] 来源: " + source + ", 签名: " + signature.substring(0, 20));
                
                // 如果是DICE-AM，进行专门分析
                if (isDiceAM) {
                    console.log("=== 发现DICE-AM数据块！ ===");
                    console.log(" 版本: " + view[7]);
                    console.log(" 大小: " + size + " 字节");
                    
                    // 显示详细hex数据
                    var hexPreview = "";
                    for (var h = 0; h < Math.min(48, view.length); h++) {
                        var hex = view[h].toString(16).toUpperCase();
                        if (hex.length === 1) hex = '0' + hex;
                        hexPreview += hex + " ";
                        if ((h + 1) % 16 === 0) hexPreview += "\n                ";
                    }
                    console.log(" 详细Hex: " + hexPreview);
                    
                    // 专门的DICE-AM坐标搜索
                    var diceCoords = extractDiceCoordinates(view);
                    if (diceCoords.length > 0) {
                        console.log(" DICE坐标: " + diceCoords.length + " 个");
                        for (var dc = 0; dc < Math.min(3, diceCoords.length); dc++) {
                            console.log("  DICE坐标" + dc + ": " + diceCoords[dc]);
                        }
                        extractorStats.foundCoordinates += diceCoords.length;
                    }
                    
                    // 专门的DICE-AM中文搜索
                    var diceChinese = extractDiceChinese(data);
                    if (diceChinese.length > 0) {
                        console.log(" DICE中文: " + diceChinese.length + " 个");
                        for (var dt = 0; dt < Math.min(3, diceChinese.length); dt++) {
                            console.log("  DICE文本" + dt + ": " + diceChinese[dt]);
                        }
                        extractorStats.foundChineseText += diceChinese.length;
                    }
                    
                    console.log("========================");
                } else {
                    // 输出数据hex预览用于调试
                    var hexPreview = "";
                    for (var h = 0; h < Math.min(32, view.length); h++) {
                        var hex = view[h].toString(16).toUpperCase();
                        if (hex.length === 1) hex = '0' + hex;
                        hexPreview += hex + " ";
                    }
                    console.log("[Hex数据] " + hexPreview);
                    
                    // 提取坐标
                    var coords = extractCoordinates(view);
                    if (coords.length > 0) {
                        console.log("[Coordinates] 找到 " + coords.length + " 个坐标");
                        for (var c = 0; c < Math.min(3, coords.length); c++) {
                            console.log("  坐标" + c + ": (" + coords[c].x.toFixed(6) + ", " + coords[c].y.toFixed(6) + ")");
                        }
                        extractorStats.foundCoordinates += coords.length;
                    } else {
                        console.log("[Coordinates] 在此数据块中未找到坐标");
                    }
                    
                    // 提取中文
                    var chinese = extractChinese(data);
                    if (chinese.length > 0) {
                        console.log("[Chinese Text] 找到 " + chinese.length + " 个中文文本");
                        for (var t = 0; t < Math.min(3, chinese.length); t++) {
                            console.log("  文本" + t + ": " + chinese[t]);
                        }
                        extractorStats.foundChineseText += chinese.length;
                    } else {
                        console.log("[Chinese Text] 在此数据块中未找到中文");
                    }
                }
                
                extractorStats.successfulExtractions++;
                return true;
            }
            
            return false;
            
        } catch (e) {
            console.log("[Extract Error] " + e);
            return false;
        }
    }
    
    // === 坐标提取 ===
    function extractCoordinates(view) {
        var coords = [];
        try {
            var dataView = new DataView(view.buffer, view.byteOffset, view.byteLength);
            for (var i = 0; i < view.length - 8; i += 4) {
                try {
                    var x = dataView.getFloat32(i, true);
                    var y = dataView.getFloat32(i + 4, true);
                    
                    // 扩大坐标范围，包括更多可能的坐标格式
                    if ((x > 10 && x < 180 && y > 10 && y < 90) || 
                        (x > 10 && x < 90 && y > 10 && y < 180)) {
                        console.log("[坐标检测] 发现可能坐标: (" + x.toFixed(6) + ", " + y.toFixed(6) + ") 在偏移 " + i);
                        coords.push({x: x, y: y});
                        if (coords.length >= 5) break;
                    }
                    
                    // 也尝试大端序
                    var x_be = dataView.getFloat32(i, false);
                    var y_be = dataView.getFloat32(i + 4, false);
                    if ((x_be > 10 && x_be < 180 && y_be > 10 && y_be < 90) || 
                        (x_be > 10 && x_be < 90 && y_be > 10 && y_be < 180)) {
                        console.log("[坐标检测] 发现可能坐标(大端): (" + x_be.toFixed(6) + ", " + y_be.toFixed(6) + ") 在偏移 " + i);
                        coords.push({x: x_be, y: y_be});
                        if (coords.length >= 5) break;
                    }
                } catch (e) {
                    continue;
                }
            }
        } catch (e) {
            console.log("[坐标提取错误] " + e);
        }
        return coords;
    }
    
    // === 中文提取 ===
    function extractChinese(data) {
        var texts = [];
        try {
            // 尝试UTF-8解码
            var text = new TextDecoder('utf-8').decode(data);
            var current = "";
            var foundChineseBytes = false;
            
            for (var i = 0; i < text.length; i++) {
                var char = text[i];
                if (char >= '\u4e00' && char <= '\u9fff') {
                    current += char;
                    foundChineseBytes = true;
                } else {
                    if (current.length >= 2) {
                        console.log("[中文检测] 发现中文文本: " + current);
                        texts.push(current);
                        if (texts.length >= 8) break;
                    }
                    current = "";
                }
            }
            if (current.length >= 2) {
                console.log("[中文检测] 发现中文文本: " + current);
                texts.push(current);
            }
            
            // 如果UTF-8失败，尝试直接从字节中查找中文模式
            if (!foundChineseBytes && data.length > 0) {
                var view = new Uint8Array(data);
                for (var j = 0; j < view.length - 2; j++) {
                    // 查找中文UTF-8字节模式 (0xE4-0xE9开头)
                    if (view[j] >= 0xE4 && view[j] <= 0xE9 && 
                        view[j+1] >= 0x80 && view[j+1] <= 0xBF &&
                        view[j+2] >= 0x80 && view[j+2] <= 0xBF) {
                        try {
                            var chineseBytes = data.slice(j, j + 12); // 取更多字节
                            var chineseText = new TextDecoder('utf-8').decode(chineseBytes);
                            var chineseChars = "";
                            for (var k = 0; k < chineseText.length; k++) {
                                var c = chineseText[k];
                                if (c >= '\u4e00' && c <= '\u9fff') {
                                    chineseChars += c;
                                } else if (chineseChars.length > 0) {
                                    break;
                                }
                            }
                            if (chineseChars.length >= 2) {
                                console.log("[字节中文检测] 发现中文: " + chineseChars);
                                texts.push(chineseChars);
                                if (texts.length >= 8) break;
                            }
                        } catch (e) {
                            continue;
                        }
                    }
                }
            }
            
        } catch (e) {
            console.log("[中文提取错误] " + e);
        }
        return texts;
    }
    
    // === DICE专用坐标提取 ===
    function extractDiceCoordinates(view) {
        var results = [];
        try {
            var dataView = new DataView(view.buffer, view.byteOffset, view.byteLength);
            
            for (var i = 16; i < view.length - 8; i += 4) { // 从DICE-AM头部后开始
                try {
                    // 方法1: 标准float32
                    var x1 = dataView.getFloat32(i, true);
                    var y1 = dataView.getFloat32(i + 4, true);
                    if ((x1 >= 70 && x1 <= 140 && y1 >= 15 && y1 <= 55) ||
                        (x1 >= 15 && x1 <= 55 && y1 >= 70 && y1 <= 140)) {
                        results.push("(" + x1.toFixed(6) + ", " + y1.toFixed(6) + ") float32@" + i);
                        if (results.length >= 5) break;
                    }
                    
                    // 方法2: 压缩int32坐标 (除以1000000)
                    var x2 = dataView.getInt32(i, true) / 1000000.0;
                    var y2 = dataView.getInt32(i + 4, true) / 1000000.0;
                    if ((x2 >= 70 && x2 <= 140 && y2 >= 15 && y2 <= 55) ||
                        (x2 >= 15 && x2 <= 55 && y2 >= 70 && y2 <= 140)) {
                        results.push("(" + x2.toFixed(6) + ", " + y2.toFixed(6) + ") int32/1M@" + i);
                        if (results.length >= 5) break;
                    }
                    
                    // 方法3: 压缩int32坐标 (除以100000)
                    var x3 = dataView.getInt32(i, true) / 100000.0;
                    var y3 = dataView.getInt32(i + 4, true) / 100000.0;
                    if ((x3 >= 70 && x3 <= 140 && y3 >= 15 && y3 <= 55) ||
                        (x3 >= 15 && x3 <= 55 && y3 >= 70 && y3 <= 140)) {
                        results.push("(" + x3.toFixed(6) + ", " + y3.toFixed(6) + ") int32/100K@" + i);
                        if (results.length >= 5) break;
                    }
                    
                } catch (e) {
                    continue;
                }
            }
        } catch (e) {}
        return results;
    }
    
    // === DICE专用中文提取 ===
    function extractDiceChinese(data) {
        var results = [];
        try {
            var view = new Uint8Array(data);
            
            for (var i = 16; i < view.length - 3; i++) {
                if (view[i] >= 0xE4 && view[i] <= 0xE9 && 
                    view[i+1] >= 0x80 && view[i+1] <= 0xBF &&
                    view[i+2] >= 0x80 && view[i+2] <= 0xBF) {
                    
                    try {
                        var textBytes = view.slice(i, Math.min(i + 30, view.length));
                        var text = new TextDecoder('utf-8', {fatal: false}).decode(textBytes);
                        var chinese = "";
                        
                        for (var j = 0; j < text.length; j++) {
                            var char = text[j];
                            if (char >= '\u4e00' && char <= '\u9fff') {
                                chinese += char;
                            } else if (chinese.length > 0) {
                                break;
                            }
                        }
                        
                        if (chinese.length >= 2) {
                            results.push("'" + chinese + "' @" + i);
                            i += chinese.length * 3;
                            if (results.length >= 5) break;
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }
        } catch (e) {}
        return results;
    }
    
    // === Hook文件读取 ===
    function setupFileHook() {
        console.log("[File Hook] 设置文件读取Hook...");
        
        try {
            var readPtr = Module.findExportByName("libc.so", "read");
            if (readPtr) {
                Interceptor.attach(readPtr, {
                    onEnter: function(args) {
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                    },
                    onLeave: function(retval) {
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 100 && extractorStats.successfulExtractions < CONFIG.MAX_SAMPLES) {
                            extractorStats.totalExtractions++;
                            extractRealData(this.buffer, bytesRead, "文件读取");
                        }
                    }
                });
                console.log("[File Hook] 文件读取Hook已设置");
            }
        } catch (e) {
            console.log("[File Hook Error] " + e);
        }
    }
    
    // === Hook zlib解压 ===
    function setupZlibHook() {
        console.log("[Zlib Hook] 设置zlib解压Hook...");
        
        try {
            var uncompressPtr = Module.findExportByName("libz.so", "uncompress");
            if (uncompressPtr) {
                Interceptor.attach(uncompressPtr, {
                    onEnter: function(args) {
                        this.destBuffer = args[0];
                        this.destLenPtr = args[1];
                    },
                    onLeave: function(retval) {
                        if (retval.toInt32() === 0 && extractorStats.successfulExtractions < CONFIG.MAX_SAMPLES) {
                            try {
                                var len = this.destLenPtr.readU32();
                                if (len > 100 && len < 1024 * 1024) {
                                    extractorStats.totalExtractions++;
                                    extractRealData(this.destBuffer, len, "zlib解压");
                                }
                            } catch (e) {}
                        }
                    }
                });
                console.log("[Zlib Hook] zlib解压Hook已设置");
            }
        } catch (e) {
            console.log("[Zlib Hook Error] " + e);
        }
    }
    
    // === 生成报告 ===
    function generateReport() {
        console.log("\n=== 解压后数据提取报告 ===");
        console.log("总提取次数: " + extractorStats.totalExtractions);
        console.log("成功提取: " + extractorStats.successfulExtractions);
        console.log("找到坐标: " + extractorStats.foundCoordinates + " 个");
        console.log("找到中文: " + extractorStats.foundChineseText + " 个");
        console.log("==============================\n");
    }
    
    // === 主函数 ===
    function main() {
        console.log("[Main] 开始初始化解压后数据提取器...");
        
        setTimeout(function() {
            setupFileHook();
            setupZlibHook();
            
            setInterval(generateReport, 20000);
            
            console.log("[Real Data Extractor] 解压后数据提取器已启动!");
            console.log("现在移动地图，观察解压后数据提取...");
            
        }, CONFIG.INIT_DELAY);
    }
    
    // === 启动 ===
    try {
        Java.perform(function() {
            console.log("[Java] Java环境已准备就绪");
            main();
        });
    } catch (e) {
        console.log("[Java Error] " + e);
        main();
    }
    
})(); 