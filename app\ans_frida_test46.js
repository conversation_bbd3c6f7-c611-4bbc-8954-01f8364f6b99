// 完全简化版 - 只保留最核心功能，移除所有可能导致崩溃的部分

// 1. 完全禁用异常输出，只记录一次
Process.setExceptionHandler(function(exception) {
    // 静默处理所有异常，不输出日志
    return true;
});

// 2. 修改 hookNativeAddMapGestureMsg 函数，只保留 Java 层钩子
function hookNativeAddMapGestureMsg() {
    log("开始监控 nativeAddMapGestureMsg 执行流程...");
    
    // 只钩住 Java 层的 nativeAddMapGestureMsg 方法
    Java.perform(function() {
        try {
            var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
            if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                GLMapEngine.nativeAddMapGestureMsg.implementation = function(engineId, nativePtr, type, param1, param2, param3, param4) {
                    try {
                        log("\n========== 手势事件 ==========");
                        log("类型: " + getGestureTypeName(type) + " (" + type + ")");
                        log("参数: param1=" + param1.toFixed(2) + 
                           ", param2=" + param2.toFixed(2) + 
                           ", param3=" + param3.toFixed(2) + 
                           ", param4=" + param4);
                        
                        // 调用原始方法
                        var result = this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                        log("========== 手势处理完成 ==========\n");
                        return result;
                    } catch (e) {
                        // 静默处理异常，确保原始方法被调用
                        return this.nativeAddMapGestureMsg(engineId, nativePtr, type, param1, param2, param3, param4);
                    }
                };
                log("成功钩住 Java 层 nativeAddMapGestureMsg 方法");
            } else {
                log("未找到 nativeAddMapGestureMsg 方法");
            }
        } catch (e) {
            log("钩住 Java 层方法失败");
        }
    });
}

// 3. 添加手势类型名称映射函数
function getGestureTypeName(type) {
    var gestureTypes = {
        1: "单指按下",
        2: "单指移动",
        3: "单指抬起",
        4: "双指按下",
        5: "双指移动",
        6: "双指抬起",
        7: "放大",
        8: "缩小",
        9: "旋转",
        10: "长按",
        11: "双击",
        12: "倾斜",
        // 可能的其他类型
        13: "多指手势",
        14: "惯性滑动",
        15: "地图旋转",
        16: "地图倾斜"
    };
    
    return gestureTypes[type] || "未知手势";
}

// 4. 极简主函数
function main() {
    log("高德地图手势分析脚本启动 (超精简版)");
    
    // 延迟执行，确保应用已完全初始化
    setTimeout(function() {
        try {
            // 只钩住 Java 层的 nativeAddMapGestureMsg
            hookNativeAddMapGestureMsg();
        } catch (e) {
            log("脚本设置失败");
        }
    }, 5000);  // 延迟5秒，确保应用已完全初始化
    
    log("脚本设置完成，等待手势事件...");
}

// 5. 简化日志函数
function log(message) {
    console.log("[+] " + message);
}

// 启动脚本
main(); 