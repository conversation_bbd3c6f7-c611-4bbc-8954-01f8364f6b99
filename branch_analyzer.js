/*
 * 高德地图分支逻辑分析器
 * 基于IDA Pro静态分析结果，动态追踪代码分支执行路径
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[Branch Analyzer] 启动分支逻辑分析器...");
    
    var branchHistory = [];
    var conditionResults = {};
    var isLibraryReady = false;
    var CONFIG = {
        INIT_DELAY: 3000,        // 初始化延迟
        SAFE_MODE: true          // 安全模式，减少复杂操作
    };
    
    // === 安全的库检查函数 ===
    function waitForLibrary(libraryName, callback) {
        var maxAttempts = 30;
        var attempt = 0;
        
        function checkLibrary() {
            try {
                var lib = Module.findBaseAddress(libraryName);
                if (lib) {
                    console.log("[Library] " + libraryName + " 已加载，基址: " + lib);
                    callback(lib);
                    return;
                }
            } catch (e) {
                // 继续等待
            }
            
            attempt++;
            if (attempt < maxAttempts) {
                setTimeout(checkLibrary, 1000);
            } else {
                console.log("[Error] " + libraryName + " 加载超时");
            }
        }
        
        checkLibrary();
    }
    
    // === 基于IDA分析的关键分支点 (简化版) ===
    function setupBranchAnalysis(libBase) {
        console.log("[Branch] libamapnsq.so 基址: " + libBase);
        
        try {
            // 只分析主要的函数入口，避免复杂的内部分支
            analyzeSub10F88MainBranch(libBase);
            analyzeSub5C394MainBranch(libBase);
        } catch (e) {
            console.log("[Error] 分支分析设置失败: " + e);
        }
    }
    
    // === 简化的 sub_10F88 分析 ===
    function analyzeSub10F88MainBranch(libBase) {
        try {
            var sub_10F88_base = libBase.add(0x10F88);
            
            console.log("[Branch] 设置 sub_10F88 主分支监控...");
            
            Interceptor.attach(sub_10F88_base, {
                onEnter: function(args) {
                    console.log("[Branch] sub_10F88 函数进入");
                    
                    try {
                        // 尝试安全地分析数据
                        if (args[0] && !args[0].isNull()) {
                            // 检查是否为有效的数据指针
                            var testByte = args[0].readU8();
                            console.log("[Data] 第一个字节: 0x" + testByte.toString(16));
                            
                            // 尝试读取可能的DICE-AM头部
                            try {
                                var headerData = args[0].readByteArray(16);
                                var headerView = new Uint8Array(headerData);
                                
                                var headerStr = "";
                                for (var i = 0; i < Math.min(8, headerView.length); i++) {
                                    if (headerView[i] >= 32 && headerView[i] < 127) {
                                        headerStr += String.fromCharCode(headerView[i]);
                                    } else if (headerView[i] === 0) {
                                        break;
                                    } else {
                                        headerStr += ".";
                                    }
                                }
                                
                                console.log("[Header] 数据头部: '" + headerStr + "'");
                                
                                if (headerStr.indexOf("DICE") !== -1) {
                                    console.log("[Branch] 检测到DICE-AM数据!");
                                    
                                    branchHistory.push({
                                        type: "dice_am_detected",
                                        address: sub_10F88_base,
                                        condition: true,
                                        value: headerStr,
                                        timestamp: Date.now()
                                    });
                                }
                                
                            } catch (e) {
                                console.log("[Debug] 头部读取失败: " + e);
                            }
                        }
                        
                        this.enterTime = Date.now();
                        
                    } catch (e) {
                        console.log("[Error] sub_10F88 分析失败: " + e);
                    }
                },
                
                onLeave: function(retval) {
                    try {
                        var duration = Date.now() - this.enterTime;
                        var returnCode = retval.toInt32();
                        
                        console.log("[Branch] sub_10F88 函数退出, 返回码: " + returnCode + ", 耗时: " + duration + "ms");
                        
                        var success = (returnCode === 0);
                        var errorType = "unknown";
                        
                        switch(returnCode) {
                            case 0: errorType = "success"; break;
                            case 8: errorType = "version_error"; break;
                            case 11: errorType = "validation_error"; break;
                            case 26: errorType = "memory_error"; break;
                        }
                        
                        branchHistory.push({
                            type: "function_result",
                            address: sub_10F88_base,
                            condition: success,
                            value: errorType,
                            timestamp: Date.now()
                        });
                        
                        conditionResults["sub_10F88_success"] = success;
                        
                    } catch (e) {
                        console.log("[Error] sub_10F88 退出处理失败: " + e);
                    }
                }
            });
            
            console.log("[Branch] sub_10F88 hook 已设置");
            
        } catch (e) {
            console.log("[Error] sub_10F88 hook 设置失败: " + e);
        }
    }
    
    // === 简化的 sub_5C394 分析 ===
    function analyzeSub5C394MainBranch(libBase) {
        try {
            var sub_5C394_base = libBase.add(0x5C394);
            
            console.log("[Branch] 设置 sub_5C394 主分支监控...");
            
            Interceptor.attach(sub_5C394_base, {
                onEnter: function(args) {
                    console.log("[Branch] sub_5C394 调度器进入");
                    
                    try {
                        if (args[0] && !args[0].isNull()) {
                            var bufferSize = 0;
                            if (args[1] && !args[1].isNull()) {
                                bufferSize = args[1].toInt32();
                            }
                            
                            console.log("[Dispatch] 缓冲区大小: " + bufferSize + " 字节");
                            
                            // 尝试读取数据类型
                            try {
                                var headerData = args[0].readByteArray(Math.min(16, Math.max(16, bufferSize)));
                                var headerView = new Uint8Array(headerData);
                                
                                var isDiceAm = false;
                                if (headerView.length >= 7) {
                                    isDiceAm = (headerView[0] === 0x44 && // 'D'
                                              headerView[1] === 0x49 && // 'I'  
                                              headerView[2] === 0x43 && // 'C'
                                              headerView[3] === 0x45 && // 'E'
                                              headerView[4] === 0x2D && // '-'
                                              headerView[5] === 0x41 && // 'A'
                                              headerView[6] === 0x4D);  // 'M'
                                }
                                
                                console.log("[TypeCheck] DICE-AM检测: " + (isDiceAm ? "是" : "否"));
                                
                                branchHistory.push({
                                    type: "type_detection",
                                    address: sub_5C394_base,
                                    condition: isDiceAm,
                                    value: isDiceAm ? "DICE-AM" : "Other",
                                    timestamp: Date.now()
                                });
                                
                                conditionResults["is_dice_am"] = isDiceAm;
                                
                            } catch (e) {
                                console.log("[Debug] 类型检测失败: " + e);
                            }
                        }
                        
                        this.enterTime = Date.now();
                        
                    } catch (e) {
                        console.log("[Error] sub_5C394 分析失败: " + e);
                    }
                },
                
                onLeave: function(retval) {
                    try {
                        var duration = Date.now() - this.enterTime;
                        console.log("[Branch] sub_5C394 调度完成, 耗时: " + duration + "ms");
                        
                    } catch (e) {
                        console.log("[Error] sub_5C394 退出处理失败: " + e);
                    }
                }
            });
            
            console.log("[Branch] sub_5C394 hook 已设置");
            
        } catch (e) {
            console.log("[Error] sub_5C394 hook 设置失败: " + e);
        }
    }
    
    // === 分支执行路径分析 ===
    function analyzeBranchPaths() {
        console.log("\n=== 分支执行路径分析 ===");
        
        if (branchHistory.length === 0) {
            console.log("暂无分支执行记录");
            return;
        }
        
        var pathSummary = {};
        var successfulPaths = 0;
        var failedPaths = 0;
        
        for (var i = 0; i < branchHistory.length; i++) {
            var branch = branchHistory[i];
            
            if (!pathSummary[branch.type]) {
                pathSummary[branch.type] = {
                    count: 0,
                    success: 0,
                    failure: 0,
                    lastValue: null
                };
            }
            
            pathSummary[branch.type].count++;
            pathSummary[branch.type].lastValue = branch.value;
            
            if (branch.condition) {
                pathSummary[branch.type].success++;
                successfulPaths++;
            } else {
                pathSummary[branch.type].failure++;
                failedPaths++;
            }
        }
        
        console.log("分支执行统计:");
        for (var type in pathSummary) {
            var stats = pathSummary[type];
            console.log("  " + type + ":");
            console.log("    总执行次数: " + stats.count);
            console.log("    成功次数: " + stats.success);
            console.log("    失败次数: " + stats.failure);
            console.log("    最后值: " + stats.lastValue);
        }
        
        console.log("\n当前条件状态:");
        for (var condition in conditionResults) {
            console.log("  " + condition + ": " + (conditionResults[condition] ? "通过" : "失败"));
        }
        
        console.log("==========================\n");
    }
    
    // === 定期报告 ===
    function setupPeriodicReporting() {
        setInterval(function() {
            if (branchHistory.length > 0) {
                analyzeBranchPaths();
            }
        }, 20000);
    }
    
    // === 安全的主入口 ===
    function main() {
        console.log("[Main] 等待应用初始化完成...");
        
        setTimeout(function() {
            console.log("[Main] 开始初始化分支分析器...");
            
            try {
                waitForLibrary("libamapnsq.so", function(libBase) {
                    try {
                        setupBranchAnalysis(libBase);
                        setupPeriodicReporting();
                        
                        console.log("[Branch Analyzer] 分支逻辑分析器已启动!");
                        console.log("现在移动地图以触发分支执行，观察代码分支逻辑...");
                        
                        isLibraryReady = true;
                        
                    } catch (e) {
                        console.log("[Error] 分支分析器初始化失败: " + e);
                    }
                });
                
            } catch (e) {
                console.log("[Error] 主初始化失败: " + e);
            }
        }, CONFIG.INIT_DELAY);
    }
    
    // === 启动分析器 ===
    try {
        Java.perform(function() {
            console.log("[Java] Java环境已准备就绪");
            main();
        });
    } catch (e) {
        console.log("[Error] Java环境初始化失败: " + e);
        main();
    }
    
})(); 