// ANS文件深度解析与数据提取脚本

(function() {
    console.log("[ANS深度解析] 启动...");

    var libamapnsq = null;
    var libamapr = null;
    var lastAnsData = null;
    var textBlocks = [];
    var coordBlocks = [];
    var hookInterval = null;

    // 阶段性执行，降低启动压力
    setTimeout(function() {
        // 持续检查库是否加载
        hookInterval = setInterval(checkAndSetupHooks, 1000);
    }, 1000);

    function checkAndSetupHooks() {
        // 检查库是否已加载
        libamapnsq = Process.findModuleByName("libamapnsq.so");
        libamapr = Process.findModuleByName("libamapr.so");
        
        if (!libamapnsq) {
            console.log("[ANS深度解析] 等待libamapnsq.so加载...");
            return;
        }
        
        if (!libamapr) {
            console.log("[ANS深度解析] 等待libamapr.so加载...");
            return;
        }
        
        // 两个库都已加载，清除定时器并设置钩子
        clearInterval(hookInterval);
        console.log("[ANS深度解析] 已找到关键库:");
        console.log("  libamapnsq.so: " + libamapnsq.base);
        console.log("  libamapr.so: " + libamapr.base);
        
        // 设置钩子
        setupHooks();
    }

    function setupHooks() {
        try {
            // 1. 钩住ANS文件解析函数
            var parserFuncOffset = 0xC654;  // sub_C654
            var parserFuncAddr = libamapnsq.base.add(parserFuncOffset);
            
            console.log("[ANS深度解析] 设置解析函数钩子: " + parserFuncAddr);
            
            Interceptor.attach(parserFuncAddr, {
                onEnter: function(args) {
                    this.srcData = args[0];
                    this.destBuffer = args[1];
                    this.sizePtr = args[2];
                    this.controlStruct = args[3];
                    this.size = args[4].toInt32();
                    
                    // 只记录较大的块，避免日志过多
                    if (this.size > 1000) {
                        console.log("[ANS解析] 调用解析函数 sub_C654");
                        console.log("  源数据: " + this.srcData);
                        console.log("  目标缓冲区: " + this.destBuffer);
                        console.log("  大小: " + this.size + " 字节");
                    }
                },
                onLeave: function(retval) {
                    if (retval.toInt32() === 0) {
                        try {
                            // 保存解析后数据的引用
                            if (this.sizePtr) {
                                var size = Memory.readInt(this.sizePtr);
                                lastAnsData = {
                                    buffer: this.destBuffer,
                                    size: size,
                                    timestamp: Date.now()
                                };
                                
                                // 只处理较大的块
                                if (this.size > 1000) {
                                    console.log("[ANS解析] 解析成功，解压后大小: " + size);
                                    
                                    // 分析解压后的数据
                                    try {
                                        analyzeDecompressedData(this.destBuffer, size);
                                    } catch(e) {
                                        console.log("[ANS解析] 分析数据失败: " + e);
                                    }
                                }
                            }
                        } catch(e) {
                            console.log("[ANS解析] 读取解析数据失败: " + e);
                        }
                    } else if (this.size > 1000) {
                        console.log("[ANS解析] 解析失败，返回值: " + retval);
                    }
                }
            });
            
            // 2. 钩住文本处理函数 (0x7f5eac3730)
            try {
                // 这个地址在每次运行时可能不同，需要动态计算
                // 从日志中我们知道这个函数处理Unicode字符
                var textFuncPattern = "FF 43 01 D1 F3 0B 00 F9 FD 7B 02 A9 FD 03 00 91";
                
                Memory.scan(libamapr.base, libamapr.size, textFuncPattern, {
                    onMatch: function(address, size) {
                        console.log("[ANS深度解析] 找到疑似文本处理函数: " + address);
                        
                        Interceptor.attach(address, {
                            onEnter: function(args) {
                                // 第一个参数通常是Unicode码点
                                var charCode = args[0].toInt32();
                                
                                // 只处理可能是中文的字符
                                if (charCode > 0x4E00 && charCode < 0x9FFF) {
                                    var char = String.fromCharCode(charCode);
                                    console.log("[ANS文本] Unicode字符: " + char + " (0x" + charCode.toString(16) + ")");
                                    
                                    // 收集文本数据
                                    collectTextData(charCode);
                                }
                            }
                        });
                        
                        return 'stop'; // 找到一个匹配就停止
                    },
                    onComplete: function() {}
                });
            } catch(e) {
                console.log("[ANS深度解析] 搜索文本处理函数失败: " + e);
            }
            
            // 3. 搜索特定渲染函数 (0x7f5e57e0a8)
            try {
                var renderFuncPattern = "F? 0F 1D F8 F? 07 41 F9 F? 03 00 AA";
                
                Memory.scan(libamapr.base, libamapr.size, renderFuncPattern, {
                    onMatch: function(address, size) {
                        console.log("[ANS深度解析] 找到疑似主渲染函数: " + address);
                        
                        Interceptor.attach(address, {
                            onEnter: function(args) {
                                // 只在有新解析数据时记录
                                if (lastAnsData && (Date.now() - lastAnsData.timestamp < 500)) {
                                    this.hasRecentData = true;
                                    console.log("[ANS渲染] 主渲染函数被调用，可能正在渲染新解析的数据");
                                    
                                    // 尝试从最近解析的数据中提取坐标
                                    extractCoordinates(lastAnsData.buffer, lastAnsData.size);
                                }
                            }
                        });
                        
                        return 'stop';
                    },
                    onComplete: function() {}
                });
            } catch(e) {
                console.log("[ANS深度解析] 搜索主渲染函数失败: " + e);
            }
            
            console.log("[ANS深度解析] 钩子设置完成，请操作地图...");
        } catch(e) {
            console.log("[ANS深度解析] 设置钩子时出错: " + e);
        }
    }
    
    // 分析解压后的数据，识别不同类型的块
    function analyzeDecompressedData(bufferPtr, size) {
        try {
            // 读取前32字节进行分析
            var headerBytes = Memory.readByteArray(bufferPtr, Math.min(32, size));
            var header = new Uint8Array(headerBytes);
            
            // 转换为十六进制字符串便于分析
            var headerHex = "";
            for (var i = 0; i < Math.min(16, header.length); i++) {
                headerHex += padZero(header[i].toString(16)) + " ";
            }
            
            console.log("[ANS数据] 块头部: " + headerHex);
            
            // 识别块类型
            if (header[0] === 0x44 && header[1] === 0x49 && header[2] === 0x43 && header[3] === 0x45) {
                // "DICE-AM" 文件头
                console.log("[ANS数据] 识别为文件头块");
                analyzeFileHeader(bufferPtr, size);
            } 
            else if (header[0] === 0x05 && header[1] === 0x00) {
                // 可能是坐标数据块
                console.log("[ANS数据] 识别为坐标/索引块");
                analyzeCoordinateBlock(bufferPtr, size);
            }
            else if (header[0] === 0x0D && header[1] === 0x00) {
                // 索引块
                console.log("[ANS数据] 识别为索引块");
                analyzeIndexBlock(bufferPtr, size);
            }
            else {
                // 检查是否包含UTF-8文本
                var hasText = false;
                for (var i = 0; i < Math.min(size - 3, 32); i++) {
                    // 检查UTF-8中文字符模式
                    if (header[i] >= 0xE0 && header[i] <= 0xEF && 
                        i+1 < header.length && (header[i+1] & 0xC0) === 0x80 &&
                        i+2 < header.length && (header[i+2] & 0xC0) === 0x80) {
                        hasText = true;
                        console.log("[ANS数据] 识别为文本数据块");
                        analyzeTextBlock(bufferPtr, size);
                        break;
                    }
                }
                
                if (!hasText) {
                    // 检查是否可能包含浮点数坐标
                    var view = new DataView(headerBytes);
                    var possibleCoord = false;
                    
                    for (var i = 0; i < Math.min(size - 8, 24); i += 4) {
                        try {
                            var val = view.getFloat32(i, true); // 小端序
                            if (!isNaN(val) && Math.abs(val) > 1 && Math.abs(val) < 180) {
                                possibleCoord = true;
                                console.log("[ANS数据] 可能的坐标值: " + val);
                            }
                        } catch(e) {}
                    }
                    
                    if (possibleCoord) {
                        console.log("[ANS数据] 识别为可能的坐标数据块");
                        analyzeCoordinateBlock(bufferPtr, size);
                    } else {
                        console.log("[ANS数据] 未识别的数据块类型");
                    }
                }
            }
        } catch(e) {
            console.log("[ANS数据] 分析数据块失败: " + e);
        }
    }
    
    // 分析文件头块
    function analyzeFileHeader(bufferPtr, size) {
        try {
            var headerBytes = Memory.readByteArray(bufferPtr, Math.min(64, size));
            var header = new Uint8Array(headerBytes);
            
            // 提取文件头信息
            var magic = "";
            for (var i = 0; i < 8; i++) {
                magic += String.fromCharCode(header[i]);
            }
            
            console.log("[ANS文件头] 魔数: " + magic);
            console.log("[ANS文件头] 版本标识: 0x" + padZero(header[8].toString(16)) + padZero(header[9].toString(16)));
            
            // 提取更多文件头信息
            var view = new DataView(headerBytes);
            try {
                var val1 = view.getUint32(10, true);
                var val2 = view.getUint32(14, true);
                console.log("[ANS文件头] 控制值1: 0x" + val1.toString(16));
                console.log("[ANS文件头] 控制值2: 0x" + val2.toString(16));
            } catch(e) {}
        } catch(e) {
            console.log("[ANS文件头] 分析失败: " + e);
        }
    }
    
    // 分析文本块
    function analyzeTextBlock(bufferPtr, size) {
        try {
            var dataBytes = Memory.readByteArray(bufferPtr, Math.min(size, 4096));
            var data = new Uint8Array(dataBytes);
            
            var textFound = "";
            var i = 0;
            
            while (i < data.length - 2) {
                // 检查UTF-8中文字符模式
                if (data[i] >= 0xE0 && data[i] <= 0xEF && 
                    (data[i+1] & 0xC0) === 0x80 &&
                    (data[i+2] & 0xC0) === 0x80) {
                    
                    // 解码UTF-8字符
                    var charCode = ((data[i] & 0x0F) << 12) | 
                                  ((data[i+1] & 0x3F) << 6) | 
                                   (data[i+2] & 0x3F);
                    
                    if (charCode >= 0x4E00 && charCode <= 0x9FFF) {
                        var char = String.fromCharCode(charCode);
                        textFound += char;
                    }
                    
                    i += 3;
                } else {
                    i++;
                }
            }
            
            if (textFound.length > 0) {
                console.log("[ANS文本块] 提取文本: " + textFound);
                
                // 存储提取的文本
                if (textBlocks.indexOf(textFound) === -1) {
                    textBlocks.push(textFound);
                    
                    // 定期显示所有收集的文本
                    if (textBlocks.length % 5 === 0) {
                        showCollectedText();
                    }
                }
            }
        } catch(e) {
            console.log("[ANS文本块] 分析失败: " + e);
        }
    }
    
    // 分析坐标块
    function analyzeCoordinateBlock(bufferPtr, size) {
        try {
            var dataBytes = Memory.readByteArray(bufferPtr, Math.min(size, 1024));
            var view = new DataView(dataBytes);
            
            // 尝试提取可能的坐标对
            var coords = [];
            
            for (var i = 4; i < dataBytes.byteLength - 8; i += 4) {
                try {
                    var val1 = view.getFloat32(i, true);
                    var val2 = view.getFloat32(i + 4, true);
                    
                    // 检查是否在合理的经纬度范围内
                    if (!isNaN(val1) && !isNaN(val2) && 
                        Math.abs(val1) > 1 && Math.abs(val1) < 180 && 
                        Math.abs(val2) > 1 && Math.abs(val2) < 90) {
                        
                        coords.push({lon: val1, lat: val2});
                        
                        // 只记录前几个坐标，避免日志过多
                        if (coords.length >= 3) break;
                    }
                } catch(e) {}
            }
            
            if (coords.length > 0) {
                console.log("[ANS坐标块] 可能的坐标数据:");
                for (var i = 0; i < coords.length; i++) {
                    console.log("  经度: " + coords[i].lon + ", 纬度: " + coords[i].lat);
                }
                
                // 存储坐标数据
                coordBlocks.push(coords);
                
                // 定期显示所有收集的坐标
                if (coordBlocks.length % 5 === 0) {
                    showCollectedCoords();
                }
            }
        } catch(e) {
            console.log("[ANS坐标块] 分析失败: " + e);
        }
    }
    
    // 分析索引块
    function analyzeIndexBlock(bufferPtr, size) {
        try {
            var dataBytes = Memory.readByteArray(bufferPtr, Math.min(size, 128));
            var view = new DataView(dataBytes);
            
            // 提取索引信息
            var blockType = view.getUint32(0, true);
            var indexCount = view.getUint32(4, true);
            
            console.log("[ANS索引块] 类型: 0x" + blockType.toString(16));
            console.log("[ANS索引块] 索引数量: " + indexCount);
            
            // 提取一些索引项
            var indices = [];
            for (var i = 8; i < Math.min(dataBytes.byteLength, 40); i += 4) {
                try {
                    var idx = view.getUint32(i, true);
                    indices.push("0x" + idx.toString(16));
                } catch(e) {}
            }
            
            if (indices.length > 0) {
                console.log("[ANS索引块] 索引项: " + indices.join(", "));
            }
        } catch(e) {
            console.log("[ANS索引块] 分析失败: " + e);
        }
    }
    
    // 从缓冲区中提取可能的坐标
    function extractCoordinates(bufferPtr, size) {
        try {
            // 只分析前1KB数据，避免处理过多
            var dataBytes = Memory.readByteArray(bufferPtr, Math.min(size, 1024));
            var view = new DataView(dataBytes);
            
            for (var i = 0; i < dataBytes.byteLength - 8; i += 4) {
                try {
                    var val1 = view.getFloat32(i, true);
                    var val2 = view.getFloat32(i + 4, true);
                    
                    // 检查是否在合理的经纬度范围内
                    if (!isNaN(val1) && !isNaN(val2) && 
                        Math.abs(val1) > 73 && Math.abs(val1) < 135 && 
                        Math.abs(val2) > 3 && Math.abs(val2) < 54) {
                        
                        // 这个范围更符合中国的经纬度
                        console.log("[ANS坐标] 发现可能的中国坐标 - 经度: " + val1 + ", 纬度: " + val2);
                    }
                } catch(e) {}
            }
        } catch(e) {
            console.log("[ANS坐标] 提取坐标失败: " + e);
        }
    }
    
    // 收集文本数据
    function collectTextData(charCode) {
        // 这个函数收集单个Unicode字符，尝试组合成有意义的文本
        // 简化实现，实际应用可能需要更复杂的逻辑
    }
    
    // 显示收集的所有文本
    function showCollectedText() {
        if (textBlocks.length > 0) {
            console.log("\n[ANS数据汇总] 已收集的文本数据:");
            for (var i = 0; i < textBlocks.length; i++) {
                console.log("  " + (i+1) + ". " + textBlocks[i]);
            }
            console.log("");
        }
    }
    
    // 显示收集的所有坐标
    function showCollectedCoords() {
        if (coordBlocks.length > 0) {
            console.log("\n[ANS数据汇总] 已收集的坐标数据:");
            for (var i = 0; i < Math.min(coordBlocks.length, 10); i++) {
                var coords = coordBlocks[i];
                for (var j = 0; j < coords.length; j++) {
                    console.log("  坐标组 " + (i+1) + "." + (j+1) + ": " + 
                               coords[j].lon + ", " + coords[j].lat);
                }
            }
            console.log("");
        }
    }
    
    // 辅助函数：补零
    function padZero(str) {
        return str.length < 2 ? "0" + str : str;
    }
    
    // 设置定期显示收集的数据
    setInterval(function() {
        showCollectedText();
        showCollectedCoords();
    }, 30000);  // 每30秒显示一次汇总数据
})();
