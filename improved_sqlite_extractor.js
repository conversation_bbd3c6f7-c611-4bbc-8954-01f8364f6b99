/*
 * 高德地图改进版SQLite BLOB数据提取器
 * 基于sqlite_data_extractor的观察结果优化
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[Improved SQLite Extractor] 启动改进版SQLite数据提取器...");
    
    var extractorStats = {
        totalCalls: 0,
        validSizes: 0,
        invalidSizes: 0,
        blobFound: 0,
        successfulExtractions: 0,
        extractedSamples: [],
        sizeCounts: {}
    };
    
    var CONFIG = {
        SAMPLE_RATE: 5,              // 更频繁的采样：每5次调用一次
        MAX_SAMPLES: 5,              // 收集5个样本
        MIN_BLOB_SIZE: 10,           // 降低最小大小到10字节
        MAX_BLOB_SIZE: 1024 * 10,    // 降低最大大小到10KB
        INTERESTING_SIZES: [16, 32, 48, 53, 141], // 关注的特定大小
        EXTRACT_SMALL_DATA: true     // 提取小数据进行分析
    };
    
    // === 改进的SQLite BLOB Hook ===
    function setupImprovedSQLiteBlobHook(libBase) {
        console.log("[Improved SQLite] 设置改进版SQLite BLOB Hook...");
        
        try {
            var sqliteBlobFunc = libBase.add(0x15000);
            
            Interceptor.attach(sqliteBlobFunc, {
                onEnter: function(args) {
                    extractorStats.totalCalls++;
                    
                    // 采样机制
                    if (extractorStats.totalCalls % CONFIG.SAMPLE_RATE !== 0) {
                        return;
                    }
                    
                    this.shouldAnalyze = true;
                    this.startTime = Date.now();
                    
                    // 安全地解析参数
                    try {
                        this.sqliteStmt = args[0];
                        this.paramIndex = args[1];
                        this.blobData = args[2];
                        this.blobSize = args[3];
                        this.destructor = args[4];
                        
                        var sampleNum = Math.floor(extractorStats.totalCalls / CONFIG.SAMPLE_RATE);
                        
                        // 安全地获取大小
                        var blobSizeInt = 0;
                        var paramIndexInt = 0;
                        var sizeValid = false;
                        
                        try {
                            blobSizeInt = this.blobSize.toInt32();
                            paramIndexInt = this.paramIndex.toInt32();
                            
                            // 检查大小是否合理
                            if (blobSizeInt >= 0 && blobSizeInt <= CONFIG.MAX_BLOB_SIZE) {
                                sizeValid = true;
                                extractorStats.validSizes++;
                                
                                // 统计大小分布
                                if (!extractorStats.sizeCounts[blobSizeInt]) {
                                    extractorStats.sizeCounts[blobSizeInt] = 0;
                                }
                                extractorStats.sizeCounts[blobSizeInt]++;
                                
                            } else {
                                extractorStats.invalidSizes++;
                            }
                            
                        } catch (e) {
                            console.log("[SQLite Parse Error] 参数解析失败: " + e);
                            extractorStats.invalidSizes++;
                            return;
                        }
                        
                        console.log("[SQLite Sample " + sampleNum + "] 调用 (第" + extractorStats.totalCalls + "次)");
                        console.log("[SQLite Params] 语句: " + this.sqliteStmt + ", 参数: " + paramIndexInt + ", 大小: " + blobSizeInt + " 字节, 有效: " + sizeValid);
                        
                        // 放宽条件：处理更多数据
                        if (sizeValid && blobSizeInt >= CONFIG.MIN_BLOB_SIZE) {
                            
                            this.validBlob = true;
                            this.blobSizeInt = blobSizeInt;
                            
                            // 检查数据指针是否有效
                            try {
                                if (!this.blobData.isNull()) {
                                    
                                    // 尝试读取数据头部
                                    var headerSize = Math.min(32, blobSizeInt);
                                    var quickHeader = this.blobData.readByteArray(headerSize);
                                    var headerView = new Uint8Array(quickHeader);
                                    
                                    // 生成可读字符串
                                    var headerStr = "";
                                    var hexStr = "";
                                    for (var i = 0; i < Math.min(16, headerView.length); i++) {
                                        var hex = headerView[i].toString(16).toUpperCase();
                                        if (hex.length === 1) hex = '0' + hex;
                                        hexStr += hex + " ";
                                        
                                        if (headerView[i] >= 32 && headerView[i] < 127) {
                                            headerStr += String.fromCharCode(headerView[i]);
                                        } else {
                                            headerStr += ".";
                                        }
                                    }
                                    
                                    console.log("[SQLite Data] 头部Hex: " + hexStr);
                                    console.log("[SQLite Data] 头部字符: '" + headerStr + "'");
                                    
                                    // 检查是否包含有趣的数据模式
                                    var isInteresting = false;
                                    var dataType = "unknown";
                                    
                                    // 检查已知格式
                                    if (headerStr.indexOf('ANS') !== -1) {
                                        isInteresting = true;
                                        dataType = "ANS_Container";
                                    } else if (headerStr.indexOf('DIC') !== -1) {
                                        isInteresting = true;
                                        dataType = "DICE_Block";
                                    } else if (headerStr.indexOf('.!9') !== -1) {
                                        isInteresting = true;
                                        dataType = "Type1_Vector";
                                    } else if (headerStr.indexOf('.C.') !== -1) {
                                        isInteresting = true;
                                        dataType = "Type2_POI";
                                    } else if (headerView[0] === 0x08) {
                                        isInteresting = true;
                                        dataType = "Compressed_Data";
                                    }
                                    
                                    // 也检查特定大小的数据
                                    if (!isInteresting) {
                                        for (var j = 0; j < CONFIG.INTERESTING_SIZES.length; j++) {
                                            if (blobSizeInt === CONFIG.INTERESTING_SIZES[j]) {
                                                isInteresting = true;
                                                dataType = "Size_" + blobSizeInt + "_Data";
                                                break;
                                            }
                                        }
                                    }
                                    
                                    // 如果有特殊字节模式也认为有趣
                                    if (!isInteresting && headerView.length >= 4) {
                                        // 检查是否有非零数据
                                        var nonZeroCount = 0;
                                        for (var k = 0; k < headerView.length; k++) {
                                            if (headerView[k] !== 0) nonZeroCount++;
                                        }
                                        
                                        if (nonZeroCount > headerView.length / 2) {
                                            isInteresting = true;
                                            dataType = "Binary_Data";
                                        }
                                    }
                                    
                                    if (isInteresting) {
                                        console.log("[INTERESTING DATA] 发现" + dataType + "数据！");
                                        extractorStats.blobFound++;
                                        this.containsMapData = true;
                                        this.dataType = dataType;
                                        
                                        // 提取样本
                                        if (extractorStats.successfulExtractions < CONFIG.MAX_SAMPLES) {
                                            this.needExtraction = true;
                                            console.log("[Extract Flag] 将提取数据样本");
                                        }
                                    }
                                    
                                } else {
                                    console.log("[SQLite Warning] BLOB数据指针为空");
                                }
                                
                            } catch (e) {
                                console.log("[SQLite Data Error] 数据读取失败: " + e);
                            }
                            
                        } else if (sizeValid) {
                            console.log("[SQLite Skip] 数据太小: " + blobSizeInt + " 字节");
                        } else {
                            console.log("[SQLite Skip] 数据大小无效: " + blobSizeInt + " 字节");
                        }
                        
                    } catch (e) {
                        console.log("[SQLite Critical Error] Hook处理失败: " + e);
                    }
                },
                
                onLeave: function(retval) {
                    if (!this.shouldAnalyze) {
                        return;
                    }
                    
                    var duration = Date.now() - this.startTime;
                    var returnCode = retval.toInt32();
                    
                    console.log("[SQLite Result] 绑定完成, 耗时: " + duration + "ms, 返回码: " + returnCode);
                    
                    // 执行数据提取
                    if (this.needExtraction && this.containsMapData && this.validBlob) {
                        console.log("[Extract Start] 提取" + this.dataType + "数据...");
                        extractImprovedBlobData(this.blobData, this.blobSizeInt, this.dataType, "sample_" + extractorStats.successfulExtractions);
                    }
                }
            });
            
            console.log("[Improved SQLite] Hook已设置");
            
        } catch (e) {
            console.log("[Error] 改进版SQLite Hook设置失败: " + e);
        }
    }
    
    // === 改进的数据提取函数 ===
    function extractImprovedBlobData(blobPtr, blobSize, dataType, sampleName) {
        try {
            console.log("\n=== 开始提取 " + sampleName + " ===");
            console.log("数据类型: " + dataType);
            console.log("数据大小: " + blobSize + " 字节");
            console.log("数据地址: " + blobPtr);
            
            // 读取完整数据
            var fullData = blobPtr.readByteArray(blobSize);
            var dataView = new Uint8Array(fullData);
            
            var sample = {
                name: sampleName,
                type: dataType,
                address: blobPtr,
                size: blobSize,
                timestamp: Date.now(),
                hexHeader: "",
                signature: "",
                isCompressed: false,
                analysis: ""
            };
            
            // 生成hex头部（前16字节）
            var headerBytes = Math.min(16, dataView.length);
            for (var i = 0; i < headerBytes; i++) {
                var hex = dataView[i].toString(16).toUpperCase();
                if (hex.length === 1) hex = '0' + hex;
                sample.hexHeader += hex + " ";
            }
            
            // 生成可读签名（前32字节）
            var sigBytes = Math.min(32, dataView.length);
            for (var j = 0; j < sigBytes; j++) {
                if (dataView[j] >= 32 && dataView[j] < 127) {
                    sample.signature += String.fromCharCode(dataView[j]);
                } else if (dataView[j] === 0) {
                    sample.signature += "\\0";
                } else {
                    sample.signature += ".";
                }
            }
            
            // 检查压缩
            if (dataView[0] === 0x08) {
                sample.isCompressed = true;
            }
            
            // 简单分析
            var zeroCount = 0;
            var printableCount = 0;
            for (var k = 0; k < dataView.length; k++) {
                if (dataView[k] === 0) zeroCount++;
                if (dataView[k] >= 32 && dataView[k] < 127) printableCount++;
            }
            
            sample.analysis = "零字节: " + zeroCount + "/" + dataView.length + 
                            ", 可打印: " + printableCount + "/" + dataView.length;
            
            console.log("=== 数据样本信息 ===");
            console.log("样本: " + sample.name);
            console.log("类型: " + sample.type);
            console.log("大小: " + sample.size + " 字节");
            console.log("压缩: " + (sample.isCompressed ? "是" : "否"));
            console.log("头部: " + sample.hexHeader);
            console.log("签名: '" + sample.signature.substring(0, 40) + "'");
            console.log("分析: " + sample.analysis);
            
            // 显示完整hex dump
            console.log("\n=== 完整数据Hex Dump ===");
            console.log(hexdump(fullData, {length: blobSize, ansi: false}));
            console.log("========================\n");
            
            extractorStats.extractedSamples.push(sample);
            extractorStats.successfulExtractions++;
            
            console.log("[Extract Success] 样本提取完成！总样本数: " + extractorStats.successfulExtractions);
            
        } catch (e) {
            console.log("[Extract Error] " + sampleName + " 提取失败: " + e);
            console.log("[Extract Error] 错误详情: " + e.stack);
        }
    }
    
    // === 改进的报告函数 ===
    function generateImprovedReport() {
        console.log("\n=== 改进版SQLite数据提取报告 ===");
        console.log("总调用: " + extractorStats.totalCalls);
        console.log("采样: " + Math.floor(extractorStats.totalCalls / CONFIG.SAMPLE_RATE));
        console.log("有效大小: " + extractorStats.validSizes);
        console.log("无效大小: " + extractorStats.invalidSizes);
        console.log("发现数据: " + extractorStats.blobFound + " 次");
        console.log("成功提取: " + extractorStats.successfulExtractions + " 个");
        
        // 显示大小分布（前10个最常见的）
        var sizes = Object.keys(extractorStats.sizeCounts).map(function(size) {
            return {size: parseInt(size), count: extractorStats.sizeCounts[size]};
        }).sort(function(a, b) { return b.count - a.count; });
        
        if (sizes.length > 0) {
            console.log("\n最常见的数据大小:");
            for (var i = 0; i < Math.min(10, sizes.length); i++) {
                console.log("  " + sizes[i].size + " 字节: " + sizes[i].count + " 次");
            }
        }
        
        if (extractorStats.extractedSamples.length > 0) {
            console.log("\n已提取样本:");
            for (var j = 0; j < extractorStats.extractedSamples.length; j++) {
                var sample = extractorStats.extractedSamples[j];
                console.log("  [" + j + "] " + sample.name + " - " + sample.type + " (" + sample.size + " 字节)");
            }
        }
        
        console.log("=======================================\n");
    }
    
    // === 库等待函数 ===
    function waitForLibrary(name, callback) {
        var attempts = 0;
        function check() {
            try {
                var lib = Module.findBaseAddress(name);
                if (lib) {
                    console.log("[Library] " + name + " 基址: " + lib);
                    callback(lib);
                    return;
                }
            } catch (e) {}
            
            if (++attempts < 30) {
                setTimeout(check, 1000);
            } else {
                console.log("[Error] " + name + " 加载超时");
            }
        }
        check();
    }
    
    // === 主函数 ===
    function main() {
        console.log("[Main] 初始化改进版SQLite数据提取器...");
        
        setTimeout(function() {
            waitForLibrary("libamapnsq.so", function(libBase) {
                setupImprovedSQLiteBlobHook(libBase);
                
                // 更频繁的报告
                setInterval(generateImprovedReport, 15000);
            });
            
            console.log("[Improved SQLite Extractor] 改进版提取器已启动!");
            console.log("配置: 采样率1/" + CONFIG.SAMPLE_RATE + ", 大小范围" + CONFIG.MIN_BLOB_SIZE + "-" + CONFIG.MAX_BLOB_SIZE + "字节");
            console.log("现在移动地图，观察改进的数据提取...");
            
        }, 3000);
    }
    
    // === 启动 ===
    main();
    
})(); 