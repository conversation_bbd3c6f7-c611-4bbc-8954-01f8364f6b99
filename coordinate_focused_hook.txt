     ____
    / _  |   Frida 12.9.7 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://www.frida.re/docs/home/
Attaching...
[Coordinate Focused Hook] 启动坐标生成专项Hook...
[Java] Java环境已准备就绪
[Main] 等待应用初始化完成...
[Remote::com.autonavi.minimap]-> [Main] 开始坐标生成监控...
[Memory Monitor] 设置内存写入监控...
[Hook Error] Address_0 Hook设置失败: Error: unable to intercept function at 0x7f5c59c010; please file a bug
[Hook Error] Address_1 Hook设置失败: Error: unable to intercept function at 0x7f5c59c060; please file a bug
[Hook Error] Address_2 Hook设置失败: Error: unable to intercept function at 0x7f5c59c0b0; please file a bug
[Hook Error] Address_3 Hook设置失败: Error: unable to intercept function at 0x7f5c59c100; please file a bug
[Hook Error] Address_4 Hook设置失败: Error: unable to intercept function at 0x7f5c59c150; please file a bug
[Hook Error] Address_5 Hook设置失败: Error: unable to intercept function at 0x7f646b0070; please file a bug
[Hook Error] Address_6 Hook设置失败: Error: unable to intercept function at 0x7f646b02a0; please file a bug
[Memory Monitor] 内存监控设置完成
[Library] libamapnsq.so 已加载，基址: 0x7f762be000
[Function Hook] 设置数据处理函数Hook...
[Function Hook] 函数Hook设置完成
[Coordinate Focused Hook] 坐标监控已启动!
现在移动地图以触发坐标生成过程...
[sub_10F88] 数据解析器调用
[sub_10F88] 解析器完成, 耗时: 23ms, 返回码: 0
[Parse Success] 解析成功，搜索新生成的坐标...
[sub_10F88] 数据解析器调用
[sub_10F88] 解析器完成, 耗时: 1ms, 返回码: 0
[Parse Success] 解析成功，搜索新生成的坐标...
[sub_10F88] 数据解析器调用
[sub_10F88] 解析器完成, 耗时: 9ms, 返回码: 0
[Parse Success] 解析成功，搜索新生成的坐标...
[sub_10F88] 数据解析器调用
[sub_10F88] 解析器完成, 耗时: 1ms, 返回码: 0
[Parse Success] 解析成功，搜索新生成的坐标...
[sub_10F88] 数据解析器调用
[sub_10F88] 解析器完成, 耗时: 1ms, 返回码: 0
[Parse Success] 解析成功，搜索新生成的坐标...
[Coord Search] 搜索新生成的坐标数据...
[sub_10F88] 数据解析器调用
[Coord Search] 搜索新生成的坐标数据...
[Coord Search] 搜索新生成的坐标数据...
[Coord Search] 搜索新生成的坐标数据...

[Remote::com.autonavi.minimap]-> 
[Remote::com.autonavi.minimap]-> exit

Thank you for using Frida!
