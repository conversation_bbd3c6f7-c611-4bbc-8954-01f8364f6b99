setTimeout(function() {
  console.log("[最小化ANS分析] 启动");
  
  // 只监控文件打开 - 专注于ANS文件
  var open_ptr = Module.findExportByName("libc.so", "open");
  if (open_ptr) {
    Interceptor.attach(open_ptr, {
      onEnter: function(args) {
        try {
          var path = args[0].readUtf8String();
          if (path && path.indexOf(".ans") !== -1) {
            this.path = path;
            
            // 只对m1.ans输出详细日志，其他ANS文件只简单记录
            if (path.indexOf("m1.ans") !== -1) {
              console.log("[重要] m1.ans文件: " + path);
            }
          }
        } catch(e) {}
      },
      onLeave: function(retval) {
        if (this.path && retval.toInt32() > 0) {
          // 简化输出，减少日志量
          if (this.path.indexOf("m1.ans") !== -1) {
            console.log("[m1.ans] fd: " + retval.toInt32());
          }
        }
      }
    });
  }
  
  // 延迟10秒后才执行Java Hook，让应用有足够时间初始化
  setTimeout(function() {
    // 仅Hook最关键的一个方法
    Java.perform(function() {
      try {
        var NewMapActivity = Java.use("com.autonavi.map.activity.NewMapActivity");
        if (NewMapActivity.J) {
          NewMapActivity.J.implementation = function() {
            console.log("[地图初始化] 开始");
            var result = this.J();
            console.log("[地图初始化] 完成");
            return result;
          };
        }
      } catch(e) {}
    });
  }, 1000);
  
}, 1000);
