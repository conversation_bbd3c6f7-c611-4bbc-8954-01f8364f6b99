[{"filepath": "F:\\baidu\\decode\\gaodeApp\\app\\file\\autonavi\\data\\navi\\compile_v3\\chn\\a0\\m1.ans", "file_size": 44040192, "magic": "AM-zlib\u0000", "version": 170, "flags": 0, "geometry_type": 137, "geometry_flags": 141, "reserved": "cf8d0000", "compressed_size": 44040176, "compressed_header": "00 01 00 00 2e d7 00 00 00 00 fe fe 00 00 2e d7 00 00 00 01 00 00 00 04 00 00 00 00 00 00 00 00", "decompression_results": [{"method": "zlib", "success": false, "error": "Error -3 while decompressing data: incorrect header check"}, {"method": "gzip", "success": false, "error": "Not a gzipped file (b'\\x00\\x01')"}, {"method": "lzma", "success": false, "error": "Input format not supported by decoder"}, {"method": "raw_deflate", "success": false, "error": "Error -3 while decompressing data: invalid stored block lengths"}], "pattern_analysis": {"entropy": 5.841094551980474, "most_common_bytes": [[0, 3220], [128, 630], [5, 230], [6, 223], [8, 138]], "common_patterns": [["00000000", 114], ["00000037", 15], ["0000002c", 13], ["00000038", 13], ["00000043", 13]]}}, {"filepath": "F:\\baidu\\decode\\gaodeApp\\app\\file\\autonavi\\data\\navi\\compile_v3\\chn\\a3\\m1.ans", "file_size": 54968320, "magic": "AM-zlib\u0000", "version": 170, "flags": 0, "geometry_type": 137, "geometry_flags": 141, "reserved": "cf8d0000", "compressed_size": 54968304, "compressed_header": "00 01 00 00 2c ad 00 00 00 00 fe fe 00 00 2c ad 00 00 00 01 00 00 00 04 00 00 00 00 00 00 00 00", "decompression_results": [{"method": "zlib", "success": false, "error": "Error -3 while decompressing data: incorrect header check"}, {"method": "gzip", "success": false, "error": "Not a gzipped file (b'\\x00\\x01')"}, {"method": "lzma", "success": false, "error": "Input format not supported by decoder"}, {"method": "raw_deflate", "success": false, "error": "Error -3 while decompressing data: invalid stored block lengths"}], "pattern_analysis": {"entropy": 5.81147361749888, "most_common_bytes": [[0, 3193], [128, 644], [9, 275], [8, 248], [7, 220]], "common_patterns": [["00000000", 117], ["80000009", 13], ["80000005", 12], ["8000000c", 12], ["00000004", 11]]}}, {"filepath": "F:\\baidu\\decode\\gaodeApp\\app\\file\\autonavi\\data\\navi\\compile_v3\\chn\\a3\\m2.ans", "file_size": 133881856, "magic": "AM-zlib\u0000", "version": 170, "flags": 0, "geometry_type": 137, "geometry_flags": 141, "reserved": "cf8d0000", "compressed_size": 133881840, "compressed_header": "00 01 00 00 7b 05 00 00 00 00 fe fe 00 00 7b 05 00 00 00 01 00 00 00 04 00 00 00 00 00 00 00 00", "decompression_results": [{"method": "zlib", "success": false, "error": "Error -3 while decompressing data: incorrect header check"}, {"method": "gzip", "success": false, "error": "Not a gzipped file (b'\\x00\\x01')"}, {"method": "lzma", "success": false, "error": "Input format not supported by decoder"}, {"method": "raw_deflate", "success": false, "error": "Error -3 while decompressing data: invalid stored block lengths"}], "pattern_analysis": {"entropy": 5.858595271534507, "most_common_bytes": [[0, 3221], [128, 598], [4, 168], [2, 143], [1, 140]], "common_patterns": [["00000000", 112], ["00000039", 18], ["0000001d", 17], ["0000002a", 17], ["0000001a", 15]]}}]