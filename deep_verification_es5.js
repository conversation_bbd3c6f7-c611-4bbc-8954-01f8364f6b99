// 深度验证脚本 - 严格验证高德地图数据的真实性
// 目标: 质疑之前的发现，进行更严格的验证

console.log("[深度验证] 开始严格验证高德地图数据的真实性...");
console.log("[质疑] 重新审视之前的853次'关键发现'");

// 严格验证状态
var deepVerification = {
    fileDescriptors: {},  // fd -> 文件路径映射
    dataBlocks: [],       // 详细的数据块分析
    falsePositives: [],   // 可能的假阳性
    realMapData: [],      // 确认的真实地图数据
    startTime: Date.now()
};

// 更严格的数据类型验证
function strictDataTypeCheck(header, size, context) {
    var analysis = {
        type: "UNKNOWN",
        confidence: 0,
        reasons: [],
        warnings: []
    };
    
    if (header.length < 16) {
        analysis.warnings.push("数据太短，无法可靠识别");
        return analysis;
    }
    
    // DICE-AM严格验证
    if (header[0] === 0x44 && header[1] === 0x49 && header[2] === 0x43 && header[3] === 0x45) {
        analysis.type = "DICE-AM";
        analysis.confidence = 50; // 初始置信度
        analysis.reasons.push("魔数匹配DICE");
        
        // 验证完整魔数
        if (header[4] === 0x2d && header[5] === 0x41 && header[6] === 0x4d && header[7] === 0x00) {
            analysis.confidence += 30;
            analysis.reasons.push("完整魔数DICE-AM\\0匹配");
        } else {
            analysis.warnings.push("DICE后缀不匹配，可能是假阳性");
        }
        
        // 验证版本号合理性
        if (header[8] === 0xaa) {
            analysis.confidence += 10;
            analysis.reasons.push("版本号170合理");
        } else {
            analysis.warnings.push("版本号异常: " + header[8]);
        }
        
        // 验证几何类型
        var geoType = header[10];
        if (geoType === 0x89 || geoType === 0x8d || geoType === 0xcf) {
            analysis.confidence += 10;
            analysis.reasons.push("几何类型合理: 0x" + geoType.toString(16));
        } else {
            analysis.warnings.push("几何类型异常: 0x" + geoType.toString(16));
        }
        
    }
    // TEXT严格验证
    else if (header[0] === 0x0d && header[1] === 0x00 && header[2] === 0x00 && header[3] === 0x00) {
        analysis.type = "TEXT";
        analysis.confidence = 40;
        analysis.reasons.push("TEXT魔数匹配");
        
        // 验证后续数据是否像文本
        var hasTextLike = false;
        for (var i = 8; i < Math.min(header.length, 16); i++) {
            var byte = header[i];
            // 检查是否有可打印ASCII或UTF-8字符
            if ((byte >= 0x20 && byte <= 0x7e) || byte >= 0x80) {
                hasTextLike = true;
                break;
            }
        }
        
        if (hasTextLike) {
            analysis.confidence += 20;
            analysis.reasons.push("包含类似文本的字节");
        } else {
            analysis.warnings.push("后续数据不像文本");
        }
    }
    // CONFIG严格验证
    else if (header[0] === 0xbc && header[1] === 0xbc && header[2] === 0xbc && header[3] === 0xbc) {
        analysis.type = "CONFIG";
        analysis.confidence = 60;
        analysis.reasons.push("CONFIG魔数匹配");
        
        // 验证是否有合理的配置结构
        if (size >= 20 && size <= 100000) {
            analysis.confidence += 20;
            analysis.reasons.push("大小合理用于配置数据");
        }
    }
    
    return analysis;
}

// 文件描述符跟踪
function trackFileDescriptor(fd, operation, filename) {
    if (!deepVerification.fileDescriptors[fd]) {
        deepVerification.fileDescriptors[fd] = {
            fd: fd,
            filename: filename || "unknown",
            operations: [],
            dataTypes: {},
            totalBytes: 0
        };
    }
    
    deepVerification.fileDescriptors[fd].operations.push({
        operation: operation,
        timestamp: Date.now()
    });
}

// 1. Hook文件打开 - 建立fd到文件的映射
console.log("[1] 建立文件描述符映射...");

try {
    var libc = Process.getModuleByName("libc.so");
    var openPtr = libc.getExportByName("open");
    
    Interceptor.attach(openPtr, {
        onEnter: function(args) {
            this.filename = args[0].readCString();
            this.flags = args[1].toInt32();
        },
        onLeave: function(retval) {
            var fd = retval.toInt32();
            if (fd > 0) {
                trackFileDescriptor(fd, "open", this.filename);
                console.log("[文件映射] fd=" + fd + " -> " + this.filename);
            }
        }
    });
    
    console.log("[✓] 文件打开Hook设置成功");
} catch (e) {
    console.log("[✗] 文件打开Hook失败: " + e.message);
}

// 2. Hook文件读取 - 严格验证数据
try {
    var readPtr = libc.getExportByName("read");
    
    Interceptor.attach(readPtr, {
        onEnter: function(args) {
            this.fd = args[0].toInt32();
            this.buf = args[1];
            this.count = args[2].toInt32();
        },
        onLeave: function(retval) {
            var bytesRead = retval.toInt32();
            if (bytesRead > 16) {
                try {
                    var data = this.buf.readByteArray(Math.min(64, bytesRead));
                    var header = new Uint8Array(data);

                    // 安全的数组处理 - 避免slice问题
                    var headerArray = [];
                    for (var i = 0; i < Math.min(16, header.length); i++) {
                        headerArray.push(header[i]);
                    }

                    // 严格验证数据类型
                    var analysis = strictDataTypeCheck(headerArray, bytesRead, "file_read");
                    
                    if (analysis.confidence >= 60) {
                        // 高置信度数据
                        var dataBlock = {
                            source: "file_read",
                            fd: this.fd,
                            filename: deepVerification.fileDescriptors[this.fd] ? 
                                     deepVerification.fileDescriptors[this.fd].filename : "unknown",
                            size: bytesRead,
                            type: analysis.type,
                            confidence: analysis.confidence,
                            reasons: analysis.reasons,
                            warnings: analysis.warnings,
                            headerHex: headerArray.map(function(b) {
                                return ('0' + b.toString(16)).slice(-2);
                            }).join(' '),
                            timestamp: Date.now()
                        };
                        
                        deepVerification.realMapData.push(dataBlock);
                        
                        console.log("[真实地图数据] " + analysis.type + " (置信度: " + analysis.confidence + "%)");
                        console.log("  文件: " + dataBlock.filename);
                        console.log("  大小: " + bytesRead + " 字节");
                        console.log("  原因: " + analysis.reasons.join(", "));
                        if (analysis.warnings.length > 0) {
                            console.log("  警告: " + analysis.warnings.join(", "));
                        }
                        
                    } else if (analysis.confidence >= 30) {
                        // 可疑数据
                        deepVerification.falsePositives.push({
                            source: "file_read",
                            fd: this.fd,
                            type: analysis.type,
                            confidence: analysis.confidence,
                            warnings: analysis.warnings,
                            timestamp: Date.now()
                        });
                        
                        console.log("[可疑数据] " + analysis.type + " (置信度: " + analysis.confidence + "%)");
                        console.log("  警告: " + analysis.warnings.join(", "));
                    }
                    
                    // 更新fd统计
                    if (deepVerification.fileDescriptors[this.fd]) {
                        var fdInfo = deepVerification.fileDescriptors[this.fd];
                        fdInfo.totalBytes += bytesRead;
                        fdInfo.dataTypes[analysis.type] = (fdInfo.dataTypes[analysis.type] || 0) + 1;
                    }
                    
                } catch (e) {
                    console.log("[错误] 数据分析失败: " + e.message);
                }
            }
        }
    });
    
    console.log("[✓] 文件读取Hook设置成功");
} catch (e) {
    console.log("[✗] 文件读取Hook失败: " + e.message);
}

// 3. Hook zlib解压 - 验证解压数据的真实性
console.log("[3] 验证zlib解压数据...");

try {
    var libz = Process.getModuleByName("libz.so");
    var uncompressPtr = libz.getExportByName("uncompress");
    
    Interceptor.attach(uncompressPtr, {
        onEnter: function(args) {
            this.dest = args[0];
            this.destLen = args[1];
            this.source = args[2];
            this.sourceLen = args[3];
        },
        onLeave: function(retval) {
            if (retval.toInt32() === 0) {
                try {
                    var sourceSize = this.sourceLen.readU32();
                    var decompressedSize = this.destLen.readU32();
                    
                    if (decompressedSize > 16 && decompressedSize < 1000000) {
                        var data = this.dest.readByteArray(Math.min(64, decompressedSize));
                        var header = new Uint8Array(data);

                        // 安全的数组处理
                        var headerArray = [];
                        for (var i = 0; i < Math.min(16, header.length); i++) {
                            headerArray.push(header[i]);
                        }

                        // 严格验证解压数据
                        var analysis = strictDataTypeCheck(headerArray, decompressedSize, "zlib_decompress");
                        
                        if (analysis.confidence >= 60) {
                            var dataBlock = {
                                source: "zlib_decompress",
                                compressedSize: sourceSize,
                                decompressedSize: decompressedSize,
                                compressionRatio: (decompressedSize / sourceSize).toFixed(2),
                                type: analysis.type,
                                confidence: analysis.confidence,
                                reasons: analysis.reasons,
                                warnings: analysis.warnings,
                                headerHex: headerArray.map(function(b) {
                                    return ('0' + b.toString(16)).slice(-2);
                                }).join(' '),
                                timestamp: Date.now()
                            };
                            
                            deepVerification.realMapData.push(dataBlock);
                            
                            console.log("[解压地图数据] " + analysis.type + " (置信度: " + analysis.confidence + "%)");
                            console.log("  压缩: " + sourceSize + " → " + decompressedSize + " 字节 (比率: " + dataBlock.compressionRatio + ":1)");
                            console.log("  原因: " + analysis.reasons.join(", "));
                        }
                    }
                } catch (e) {
                    // 忽略解压分析错误
                }
            }
        }
    });
    
    console.log("[✓] zlib解压Hook设置成功");
} catch (e) {
    console.log("[✗] zlib解压Hook失败: " + e.message);
}

// 4. 定期输出严格验证报告
setInterval(function() {
    var runtime = Math.floor((Date.now() - deepVerification.startTime) / 1000);
    
    console.log("\n[深度验证报告] ==========================================");
    console.log("运行时间: " + runtime + "s");
    console.log("");
    
    console.log("文件描述符映射:");
    var fdCount = Object.keys(deepVerification.fileDescriptors).length;
    console.log("  映射的fd数量: " + fdCount);
    
    for (var fd in deepVerification.fileDescriptors) {
        var fdInfo = deepVerification.fileDescriptors[fd];
        if (fdInfo.totalBytes > 0) {
            console.log("  fd=" + fd + ": " + fdInfo.filename + " (" + fdInfo.totalBytes + " 字节)");
            
            var typeCount = Object.keys(fdInfo.dataTypes).length;
            if (typeCount > 0) {
                var types = [];
                for (var type in fdInfo.dataTypes) {
                    types.push(type + ":" + fdInfo.dataTypes[type]);
                }
                console.log("    数据类型: " + types.join(", "));
            }
        }
    }
    
    console.log("");
    console.log("数据验证结果:");
    console.log("  高置信度地图数据: " + deepVerification.realMapData.length + " 个");
    console.log("  可疑/假阳性数据: " + deepVerification.falsePositives.length + " 个");
    
    // 按类型统计真实数据
    var realDataTypes = {};
    for (var i = 0; i < deepVerification.realMapData.length; i++) {
        var data = deepVerification.realMapData[i];
        realDataTypes[data.type] = (realDataTypes[data.type] || 0) + 1;
    }
    
    console.log("");
    console.log("确认的真实地图数据分布:");
    for (var type in realDataTypes) {
        console.log("  " + type + ": " + realDataTypes[type] + " 个");
    }
    
    // 计算总体置信度
    var totalConfidence = 0;
    var count = 0;
    for (var i = 0; i < deepVerification.realMapData.length; i++) {
        totalConfidence += deepVerification.realMapData[i].confidence;
        count++;
    }
    
    if (count > 0) {
        var avgConfidence = (totalConfidence / count).toFixed(1);
        console.log("");
        console.log("总体验证置信度: " + avgConfidence + "%");
        
        if (avgConfidence >= 80) {
            console.log("结论: 高置信度确认存在真实地图数据");
        } else if (avgConfidence >= 60) {
            console.log("结论: 中等置信度，需要进一步验证");
        } else {
            console.log("结论: 低置信度，可能存在大量假阳性");
        }
    }
    
    console.log("===============================================\n");
}, 30000);

console.log("[深度验证] 严格验证脚本已启动...");
console.log("[目标] 质疑并严格验证之前的发现");
console.log("[提示] 请移动地图以触发数据加载，我们将进行严格的真实性验证");
