// 地图数据流程验证脚本 - ES5兼容版本
// 验证8步骤数据处理流程

console.log("[流程验证] 开始验证地图数据处理8步骤流程...");

// 流程步骤计数器
var flowSteps = {
    step1_viewport_change: 0,      // 视口变化检测
    step2_missing_data_calc: 0,    // 缺失数据区域计算
    step3_ans_file_locate: 0,      // .ans文件定位
    step4_chunk_read: 0,           // 分块读取
    step5_zlib_decompress: 0,      // zlib解压
    step6_data_dispatch: 0,        // 数据类型分发
    step7_render_pipeline: 0,      // 渲染管道处理
    step8_gpu_draw: 0              // GPU绘制更新
};

// 工具函数
function logStep(stepName, details) {
    console.log("[步骤验证] " + stepName + ": " + details);
}

// 1. 验证库加载
var libamapnsq = null;
var libz = null;
var libc = null;
var libEGL = null;

try {
    libamapnsq = Process.getModuleByName("libamapnsq.so");
    console.log("[✓] libamapnsq.so 已加载");
} catch (e) {
    console.log("[✗] libamapnsq.so 未找到");
}

try {
    libz = Process.getModuleByName("libz.so");
    console.log("[✓] libz.so 已加载");
} catch (e) {
    console.log("[✗] libz.so 未找到");
}

try {
    libc = Process.getModuleByName("libc.so");
    console.log("[✓] libc.so 已加载");
} catch (e) {
    console.log("[✗] libc.so 未找到");
}

try {
    libEGL = Process.getModuleByName("libEGL.so");
    console.log("[✓] libEGL.so 已加载 (GPU渲染)");
} catch (e) {
    console.log("[✗] libEGL.so 未找到");
}

// 2. Hook步骤1: 视口变化检测 (通过模块系统函数)
if (libamapnsq) {
    // 基于IDA Pro分析的模块管理函数
    var moduleAddr = libamapnsq.base.add(0x908c4);
    try {
        Interceptor.attach(moduleAddr, {
            onEnter: function(args) {
                flowSteps.step1_viewport_change++;
                logStep("步骤1-视口变化检测", "模块处理被调用 #" + flowSteps.step1_viewport_change);
                console.log("  参数1: " + args[0] + ", 参数2: " + args[1]);
            }
        });
        console.log("[✓] 步骤1 Hook设置成功");
    } catch (e) {
        console.log("[警告] 步骤1 Hook失败: " + e.message);
    }
}

// 3. Hook步骤2&3: 数据需求计算和文件定位 (通过文件打开操作)
if (libc) {
    var openPtr = libc.getExportByName("open");
    var openatPtr = libc.getExportByName("openat");
    
    if (openPtr) {
        Interceptor.attach(openPtr, {
            onEnter: function(args) {
                this.filename = args[0].readCString();
            },
            onLeave: function(retval) {
                if (this.filename && this.filename.indexOf(".ans") !== -1) {
                    flowSteps.step3_ans_file_locate++;
                    logStep("步骤3-.ans文件定位", "打开文件: " + this.filename + " (fd:" + retval + ")");
                }
            }
        });
    }
    
    if (openatPtr) {
        Interceptor.attach(openatPtr, {
            onEnter: function(args) {
                this.filename = args[1].readCString();
            },
            onLeave: function(retval) {
                if (this.filename && this.filename.indexOf(".ans") !== -1) {
                    flowSteps.step3_ans_file_locate++;
                    logStep("步骤3-.ans文件定位", "打开文件: " + this.filename + " (fd:" + retval + ")");
                }
            }
        });
    }
}

// 4. Hook步骤4: 分块读取
if (libc) {
    var readPtr = libc.getExportByName("read");
    if (readPtr) {
        Interceptor.attach(readPtr, {
            onEnter: function(args) {
                this.fd = args[0].toInt32();
                this.count = args[2].toInt32();
            },
            onLeave: function(retval) {
                var bytesRead = retval.toInt32();
                if (bytesRead > 0 && (this.count === 4096 || this.count === 8192 || this.count === 131072)) {
                    flowSteps.step4_chunk_read++;
                    logStep("步骤4-分块读取", "读取 " + bytesRead + " 字节 (fd:" + this.fd + ")");
                }
            }
        });
    }
}

// 5. Hook步骤5: zlib解压
if (libz) {
    var uncompressPtr = libz.getExportByName("uncompress");
    if (uncompressPtr) {
        Interceptor.attach(uncompressPtr, {
            onEnter: function(args) {
                this.destLen = args[1];
                this.sourceLen = args[3];
            },
            onLeave: function(retval) {
                if (retval.toInt32() === 0) {
                    flowSteps.step5_zlib_decompress++;
                    var decompressedSize = this.destLen.readU32();
                    var sourceSize = this.sourceLen.readU32();
                    logStep("步骤5-zlib解压", "解压成功: " + sourceSize + " → " + decompressedSize + " 字节");
                }
            }
        });
    }
}

// 6. Hook步骤6: 数据类型分发 (基于IDA Pro分析的函数)
if (libamapnsq) {
    var dataDispatchAddr = libamapnsq.base.add(0x5c060);
    try {
        Interceptor.attach(dataDispatchAddr, {
            onEnter: function(args) {
                flowSteps.step6_data_dispatch++;
                logStep("步骤6-数据类型分发", "数据分发函数被调用 #" + flowSteps.step6_data_dispatch);
                console.log("  参数1: " + args[0] + ", 参数2: " + args[1]);
            }
        });
        console.log("[✓] 步骤6 Hook设置成功");
    } catch (e) {
        console.log("[警告] 步骤6 Hook失败: " + e.message);
    }
}

// 7. Hook步骤7: 渲染管道处理 (OpenGL相关函数)
try {
    var libGLESv2 = Process.getModuleByName("libGLESv2.so");
    if (libGLESv2) {
        var glDrawArraysPtr = libGLESv2.getExportByName("glDrawArrays");
        var glDrawElementsPtr = libGLESv2.getExportByName("glDrawElements");
        
        if (glDrawArraysPtr) {
            Interceptor.attach(glDrawArraysPtr, {
                onEnter: function(args) {
                    flowSteps.step7_render_pipeline++;
                    if (flowSteps.step7_render_pipeline % 10 === 1) { // 每10次记录一次，避免日志过多
                        logStep("步骤7-渲染管道", "glDrawArrays调用 #" + flowSteps.step7_render_pipeline);
                    }
                }
            });
        }
        
        if (glDrawElementsPtr) {
            Interceptor.attach(glDrawElementsPtr, {
                onEnter: function(args) {
                    flowSteps.step7_render_pipeline++;
                    if (flowSteps.step7_render_pipeline % 10 === 1) {
                        logStep("步骤7-渲染管道", "glDrawElements调用 #" + flowSteps.step7_render_pipeline);
                    }
                }
            });
        }
        console.log("[✓] 步骤7 OpenGL Hook设置成功");
    }
} catch (e) {
    console.log("[警告] OpenGL库未找到: " + e.message);
}

// 8. Hook步骤8: GPU绘制更新 (EGL交换缓冲区)
if (libEGL) {
    var eglSwapBuffersPtr = libEGL.getExportByName("eglSwapBuffers");
    if (eglSwapBuffersPtr) {
        Interceptor.attach(eglSwapBuffersPtr, {
            onEnter: function(args) {
                flowSteps.step8_gpu_draw++;
                logStep("步骤8-GPU绘制更新", "eglSwapBuffers调用 #" + flowSteps.step8_gpu_draw);
            }
        });
        console.log("[✓] 步骤8 EGL Hook设置成功");
    }
}

// 9. 定期输出流程统计
setInterval(function() {
    console.log("\n[流程统计] ==========================================");
    console.log("步骤1-视口变化检测: " + flowSteps.step1_viewport_change);
    console.log("步骤2-缺失数据计算: " + flowSteps.step2_missing_data_calc + " (通过步骤3推断)");
    console.log("步骤3-.ans文件定位: " + flowSteps.step3_ans_file_locate);
    console.log("步骤4-分块读取: " + flowSteps.step4_chunk_read);
    console.log("步骤5-zlib解压: " + flowSteps.step5_zlib_decompress);
    console.log("步骤6-数据类型分发: " + flowSteps.step6_data_dispatch);
    console.log("步骤7-渲染管道处理: " + flowSteps.step7_render_pipeline);
    console.log("步骤8-GPU绘制更新: " + flowSteps.step8_gpu_draw);
    console.log("===============================================\n");
}, 15000);

console.log("[流程验证] 验证脚本已启动，请移动地图以触发数据流程...");
console.log("[提示] 脚本将追踪完整的8步骤数据处理流程");
