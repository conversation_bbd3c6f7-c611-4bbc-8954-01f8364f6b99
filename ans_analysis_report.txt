# 高德地图.ans文件分析报告

文件: gb_v6.ans
大小: 3,252,224 字节 (3.1 MB)
发现数据块: 16 个

## 数据块 #1
类型: DICE_AM_header
偏移: 0x00000000 (0)
文件: extracted_DICE_AM_header_00000000.txt
预览:
```
DICE-AM........................"...
.....................................................-..........
```

## 数据块 #2
类型: JSON_data
偏移: 0x0009F225 (651813)
文件: extracted_JSON_data_0009F225.txt
预览:
```
\v.$T....1$@.."z......}S.8......$-.........qE..x..{"..P....$/........ZB......{".......$-.....@...qE.
```

## 数据块 #3
类型: JSON_data
偏移: 0x0009F240 (651840)
文件: extracted_JSON_data_0009F240.txt
预览:
```
.....$-.........qE..x..{"..P....$/........ZB......{".......$-.....@...qE..x..z...`.......qE..x..B.. 
```

## 数据块 #4
类型: JSON_data
偏移: 0x000A487A (673914)
文件: extracted_JSON_data_000A487A.txt
预览:
```
.q.q c....$-.........qE..x..{.b(..9\.......qE..x..{".(8~..4....$..........qE..x..y.;0. .....@.(...0~
```

## 数据块 #5
类型: JSON_data
偏移: 0x000A4A81 (674433)
文件: extracted_JSON_data_000A4A81.txt
预览:
```
..xr.8|..0....1.....ZB......}..(H...1.....ZB......{".8H...1.....ZB......s#q0.`..8.$A....3..........Z
```

## 数据块 #6
类型: JSON_data
偏移: 0x0010A888 (1091720)
文件: extracted_JSON_data_0010A888.txt
预览:
```
.0...T^.G....`....p...g.x.O}A..G....`....p...w....{"d.R....y..	.x...#...)z"d.R....y..	.x...8A....=\>
```

## 数据块 #7
类型: XML_tag
偏移: 0x0000006F (111)
文件: extracted_XML_tag_0000006F.txt
预览:
```
............................-..............
......<...t.....I...x...................................
```

## 数据块 #8
类型: XML_tag
偏移: 0x00001CB8 (7352)
文件: extracted_XML_tag_00001CB8.txt
预览:
```
.....k.H.[9ys..$s.E|.E.........,.c.;4..4Y..]..+s;p<.....p..J...]....... ...wJ..4.O.....O...Be.G.....
```

## 数据块 #9
类型: XML_tag
偏移: 0x00001D38 (7480)
文件: extracted_XML_tag_00001D38.txt
预览:
```
..Pb......A.Q?.%....l|.....R*q.w.._5.0...cH..=../ <.%i.}..o....I.,.a.G.....'........+Q9=....7.L.Q.B.
```

## 数据块 #10
类型: XML_tag
偏移: 0x00001F1C (7964)
文件: extracted_XML_tag_00001F1C.txt
预览:
```
... ..:%.....O...kwn.Wq...n	...jpG...K7s&.W....f..<..\....V..S.........$..7.AX<. .`.G.....'.........
```

## 数据块 #11
类型: XML_tag
偏移: 0x00001F38 (7992)
文件: extracted_XML_tag_00001F38.txt
预览:
```
...jpG...K7s&.W....f..<..\....V..S.........$..7.AX<. .`.G.....'..........CQ...h...........U.qw..L$.x
```

## 数据块 #12
类型: Chinese_UTF8
偏移: 0x00030C29 (199721)
文件: extracted_Chinese_UTF8_00030C29.txt
预览:
```
...0..........0..........0..........0..........0.T.......................0..Bhutan...)......0.......
```

## 数据块 #13
类型: Chinese_UTF8
偏移: 0x00045C7B (285819)
文件: extracted_Chinese_UTF8_00045C7B.txt
预览:
```
zh-Hans'.0.....................................0.l.............................0.<.................0
```

## 数据块 #14
类型: Chinese_UTF8
偏移: 0x00045CBE (285886)
文件: extracted_Chinese_UTF8_00045CBE.txt
预览:
```
............0.<.................0.T.......................0.l.............................0.........
```

## 数据块 #15
类型: Chinese_UTF8
偏移: 0x00045D4F (286031)
文件: extracted_Chinese_UTF8_00045D4F.txt
预览:
```
............0.$...........0.l.............................0.l.............................0.l.......
```

## 数据块 #16
类型: Chinese_UTF8
偏移: 0x00045D5A (286042)
文件: extracted_Chinese_UTF8_00045D5A.txt
预览:
```
.0.$...........0.l.............................0.l.............................0.l..................
```

