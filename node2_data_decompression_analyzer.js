/*
 * 节点2: 数据解压流程深度分析器
 * 目标: 分析AM-zlib格式的解压算法和zlib块处理
 * 方法: IDA Pro静态分析 + Frida动态追踪
 * 作者: 高级Frida程序员 + IDA Pro分析师
 */

console.log("=== 节点2: 数据解压流程深度分析器 ===");
console.log("目标: 深入分析AM-zlib解压和zlib块处理流程");

// 节点2分析状态管理
var node2Analysis = {
    // 解压操作追踪
    decompressionOps: {
        zlibCalls: [],           // zlib解压调用
        amZlibProcessing: [],    // AM-zlib处理
        blockProcessing: [],     // 数据块处理
        errorHandling: []        // 错误处理记录
    },
    
    // 数据格式分析
    dataFormatAnalysis: {
        amZlibHeaders: [],       // AM-zlib头部分析
        zlibBlocks: [],          // zlib块分析
        compressionRatios: [],   // 压缩比分析
        blockSizes: []           // 块大小统计
    },
    
    // 解压后数据分析
    decompressedData: {
        diceAmBlocks: [],        // DICE-AM数据块
        textBlocks: [],          // 文本数据块
        configBlocks: [],        // 配置数据块
        unknownBlocks: []        // 未知数据块
    },
    
    // 性能分析
    performanceMetrics: {
        decompressionTimes: [],  // 解压时间
        throughput: [],          // 吞吐量
        memoryUsage: []          // 内存使用
    },
    
    // 统计信息
    statistics: {
        totalDecompressions: 0,
        totalCompressedBytes: 0,
        totalDecompressedBytes: 0,
        averageCompressionRatio: 0,
        startTime: Date.now()
    }
};

// 数据类型识别函数
function identifyDataType(data) {
    if (!data || data.length < 4) return "UNKNOWN";
    
    var view = new Uint8Array(data);
    
    // DICE-AM矢量数据
    if (view[0] === 0x44 && view[1] === 0x49 && view[2] === 0x43 && view[3] === 0x45) {
        return "DICE-AM";
    }
    
    // 配置数据标识
    if (view[0] === 0xbc && view[1] === 0xbc && view[2] === 0xbc && view[3] === 0xbc) {
        return "CONFIG";
    }
    
    // 文本数据标识
    if (view[0] === 0x00 && view[1] === 0x00 && view[2] === 0x25) {
        return "TEXT";
    }
    
    // zlib压缩头
    if (view[0] === 0x78 && (view[1] === 0x9c || view[1] === 0xda || view[1] === 0x01)) {
        return "ZLIB";
    }
    
    // AM-zlib容器
    if (view[0] === 0x41 && view[1] === 0x4d && view[2] === 0x2d) {
        return "AM-ZLIB";
    }
    
    return "UNKNOWN";
}

// 安全的字节数组转十六进制
function safeByteArrayToHex(byteArray, maxLen) {
    if (!byteArray) return "";
    
    var hexBytes = [];
    var len = Math.min(maxLen || 16, byteArray.length);
    
    for (var i = 0; i < len; i++) {
        var byte = byteArray[i];
        if (typeof byte === 'number') {
            hexBytes.push(('0' + byte.toString(16)).slice(-2));
        }
    }
    return hexBytes.join(' ');
}

// 分析AM-zlib头部结构
function analyzeAMZlibHeader(data) {
    if (!data || data.length < 64) return null;
    
    var view = new Uint8Array(data);
    var header = {
        magic: String.fromCharCode.apply(null, view.slice(0, 8)),
        version: view.slice(8, 16),
        indexCount: new DataView(data).getUint32(16, true),
        indexOffset: new DataView(data).getUint32(20, true),
        dataOffset: new DataView(data).getUint32(24, true),
        totalSize: new DataView(data).getUint32(28, true),
        checksum: new DataView(data).getUint32(32, true),
        reserved: view.slice(36, 64)
    };
    
    return header;
}

// 计算压缩比
function calculateCompressionRatio(compressedSize, decompressedSize) {
    if (decompressedSize === 0) return 0;
    return (compressedSize / decompressedSize * 100).toFixed(2);
}

console.log("初始化节点2分析器...");

// 1. Hook libz.so 的解压函数
try {
    var libz = Process.getModuleByName("libz.so");
    console.log("[✓] libz.so 模块已找到");
    
    // Hook uncompress() - 标准zlib解压
    var uncompressPtr = libz.getExportByName("uncompress");
    if (uncompressPtr) {
        Interceptor.attach(uncompressPtr, {
            onEnter: function(args) {
                this.dest = args[0];
                this.destLen = args[1];
                this.source = args[2];
                this.sourceLen = args[3].toInt32();
                this.startTime = Date.now();
                
                console.log("[zlib解压] 开始 - 压缩大小:", this.sourceLen);
                
                // 分析压缩数据
                try {
                    var compressedData = this.source.readByteArray(Math.min(32, this.sourceLen));
                    if (compressedData) {
                        var hexData = safeByteArrayToHex(new Uint8Array(compressedData));
                        console.log("[压缩数据预览]", hexData);
                        
                        var dataType = identifyDataType(compressedData);
                        console.log("[数据类型]", dataType);
                    }
                } catch (e) {
                    console.log("[压缩数据分析错误]", e.message);
                }
            },
            onLeave: function(retval) {
                var result = retval.toInt32();
                var endTime = Date.now();
                var duration = endTime - this.startTime;
                
                if (result === 0) { // Z_OK
                    try {
                        var destLenValue = this.destLen.readU32();
                        console.log("[zlib解压成功] 解压大小:", destLenValue, "耗时:", duration, "ms");
                        
                        // 分析解压后的数据
                        var decompressedData = this.dest.readByteArray(Math.min(64, destLenValue));
                        if (decompressedData) {
                            var hexData = safeByteArrayToHex(new Uint8Array(decompressedData), 32);
                            console.log("[解压数据预览]", hexData);
                            
                            var dataType = identifyDataType(decompressedData);
                            console.log("[解压数据类型]", dataType);
                            
                            // 记录解压操作
                            node2Analysis.decompressionOps.zlibCalls.push({
                                timestamp: Date.now(),
                                compressedSize: this.sourceLen,
                                decompressedSize: destLenValue,
                                duration: duration,
                                compressionRatio: calculateCompressionRatio(this.sourceLen, destLenValue),
                                dataType: dataType,
                                success: true
                            });
                            
                            // 根据数据类型分类存储
                            var blockInfo = {
                                timestamp: Date.now(),
                                size: destLenValue,
                                dataPreview: hexData,
                                dataType: dataType
                            };
                            
                            switch (dataType) {
                                case "DICE-AM":
                                    node2Analysis.decompressedData.diceAmBlocks.push(blockInfo);
                                    break;
                                case "TEXT":
                                    node2Analysis.decompressedData.textBlocks.push(blockInfo);
                                    break;
                                case "CONFIG":
                                    node2Analysis.decompressedData.configBlocks.push(blockInfo);
                                    break;
                                default:
                                    node2Analysis.decompressedData.unknownBlocks.push(blockInfo);
                            }
                            
                            // 更新统计
                            node2Analysis.statistics.totalDecompressions++;
                            node2Analysis.statistics.totalCompressedBytes += this.sourceLen;
                            node2Analysis.statistics.totalDecompressedBytes += destLenValue;
                        }
                    } catch (e) {
                        console.log("[解压数据分析错误]", e.message);
                    }
                } else {
                    console.log("[zlib解压失败] 错误码:", result);
                    
                    // 记录失败的解压操作
                    node2Analysis.decompressionOps.errorHandling.push({
                        timestamp: Date.now(),
                        compressedSize: this.sourceLen,
                        errorCode: result,
                        duration: duration
                    });
                }
            }
        });
        console.log("[✓] uncompress() Hook 已设置");
    }
    
} catch (e) {
    console.log("[✗] libz.so Hook 设置失败:", e.message);
}

// 2. Hook libamapnsq.so 的AM-zlib处理函数
try {
    var libamapnsq = Process.getModuleByName("libamapnsq.so");
    console.log("[✓] libamapnsq.so 模块已找到");

    // Hook sub_10F88 - zlib解压处理函数
    var sub10F88Addr = libamapnsq.base.add(0x10F88);
    Interceptor.attach(sub10F88Addr, {
        onEnter: function(args) {
            console.log("[sub_10F88] AM-zlib解压函数调用");
            this.startTime = Date.now();

            // 尝试分析参数
            try {
                if (args[0] && !args[0].isNull()) {
                    var data = args[0].readByteArray(32);
                    if (data) {
                        var hexData = safeByteArrayToHex(new Uint8Array(data));
                        console.log("[sub_10F88] 输入数据预览:", hexData);

                        // 检查是否是AM-zlib头部
                        var header = analyzeAMZlibHeader(data);
                        if (header && header.magic.indexOf("AM-zlib") === 0) {
                            console.log("[sub_10F88] 检测到AM-zlib头部");
                            console.log("  索引数量:", header.indexCount);
                            console.log("  索引偏移:", header.indexOffset);
                            console.log("  数据偏移:", header.dataOffset);

                            node2Analysis.dataFormatAnalysis.amZlibHeaders.push({
                                timestamp: Date.now(),
                                header: header,
                                function: "sub_10F88"
                            });
                        }
                    }
                }
            } catch (e) {
                console.log("[sub_10F88] 参数分析错误:", e.message);
            }
        },
        onLeave: function(retval) {
            var duration = Date.now() - this.startTime;
            console.log("[sub_10F88] 函数返回:", retval, "耗时:", duration, "ms");

            // 记录AM-zlib处理
            node2Analysis.decompressionOps.amZlibProcessing.push({
                timestamp: Date.now(),
                function: "sub_10F88",
                duration: duration,
                result: retval.toString()
            });
        }
    });
    console.log("[✓] sub_10F88 Hook 已设置");

    // Hook sub_5C394 - 数据分发函数
    var sub5C394Addr = libamapnsq.base.add(0x5C394);
    Interceptor.attach(sub5C394Addr, {
        onEnter: function(args) {
            console.log("[sub_5C394] 数据分发函数调用");
            this.startTime = Date.now();

            // 分析数据块
            try {
                if (args[0] && !args[0].isNull()) {
                    var blockData = args[0].readByteArray(64);
                    if (blockData) {
                        var hexData = safeByteArrayToHex(new Uint8Array(blockData), 32);
                        console.log("[sub_5C394] 数据块预览:", hexData);

                        var dataType = identifyDataType(blockData);
                        console.log("[sub_5C394] 数据类型:", dataType);

                        // 记录数据块处理
                        node2Analysis.decompressionOps.blockProcessing.push({
                            timestamp: Date.now(),
                            function: "sub_5C394",
                            dataType: dataType,
                            dataPreview: hexData,
                            blockSize: args[1] ? args[1].toInt32() : 0
                        });
                    }
                }
            } catch (e) {
                console.log("[sub_5C394] 数据分析错误:", e.message);
            }
        },
        onLeave: function(retval) {
            var duration = Date.now() - this.startTime;
            console.log("[sub_5C394] 函数返回:", retval, "耗时:", duration, "ms");
        }
    });
    console.log("[✓] sub_5C394 Hook 已设置");

} catch (e) {
    console.log("[✗] libamapnsq.so Hook 设置失败:", e.message);
}

// 3. 生成节点2分析报告
function generateNode2Report() {
    console.log("\n=== 节点2分析报告 ===");
    console.log("运行时间:", (Date.now() - node2Analysis.statistics.startTime) / 1000, "秒");

    console.log("\n--- 解压统计 ---");
    console.log("总解压次数:", node2Analysis.statistics.totalDecompressions);
    console.log("总压缩字节:", node2Analysis.statistics.totalCompressedBytes);
    console.log("总解压字节:", node2Analysis.statistics.totalDecompressedBytes);

    if (node2Analysis.statistics.totalDecompressedBytes > 0) {
        var avgRatio = (node2Analysis.statistics.totalCompressedBytes / node2Analysis.statistics.totalDecompressedBytes * 100).toFixed(2);
        console.log("平均压缩比:", avgRatio + "%");
    }

    console.log("\n--- 数据类型分布 ---");
    console.log("DICE-AM块:", node2Analysis.decompressedData.diceAmBlocks.length);
    console.log("文本块:", node2Analysis.decompressedData.textBlocks.length);
    console.log("配置块:", node2Analysis.decompressedData.configBlocks.length);
    console.log("未知块:", node2Analysis.decompressedData.unknownBlocks.length);

    console.log("\n--- 函数调用统计 ---");
    console.log("zlib调用:", node2Analysis.decompressionOps.zlibCalls.length);
    console.log("AM-zlib处理:", node2Analysis.decompressionOps.amZlibProcessing.length);
    console.log("数据块处理:", node2Analysis.decompressionOps.blockProcessing.length);
    console.log("错误处理:", node2Analysis.decompressionOps.errorHandling.length);

    if (node2Analysis.dataFormatAnalysis.amZlibHeaders.length > 0) {
        console.log("\n--- AM-zlib头部分析 ---");
        node2Analysis.dataFormatAnalysis.amZlibHeaders.forEach(function(header, index) {
            console.log("  头部" + (index + 1) + ":");
            console.log("    魔数:", header.header.magic);
            console.log("    索引数量:", header.header.indexCount);
            console.log("    数据偏移:", header.header.dataOffset);
        });
    }

    console.log("=== 节点2分析报告结束 ===\n");
}

// 4. 数据导出功能
function exportNode2Data() {
    var exportData = {
        metadata: {
            analysisType: "Node2_DataDecompression",
            timestamp: new Date().toISOString(),
            duration: Date.now() - node2Analysis.statistics.startTime,
            version: "1.0"
        },
        statistics: node2Analysis.statistics,
        decompressionOps: node2Analysis.decompressionOps,
        dataFormatAnalysis: node2Analysis.dataFormatAnalysis,
        decompressedData: node2Analysis.decompressedData,
        performanceMetrics: node2Analysis.performanceMetrics
    };

    console.log("\n=== 节点2数据导出 ===");
    console.log(JSON.stringify(exportData, null, 2));
    console.log("=== 导出结束 ===\n");

    return exportData;
}

// 设置定期报告
setInterval(generateNode2Report, 30000); // 每30秒报告一次

console.log("节点2分析器初始化完成，等待解压操作...");
console.log("提示: 请在高德地图中进行操作以触发数据解压");

// 导出全局函数
global.generateNode2Report = generateNode2Report;
global.exportNode2Data = exportNode2Data;
global.node2Analysis = node2Analysis;
