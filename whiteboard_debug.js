/*
 * 高德地图白屏问题调试器
 * 专注于找出数据加载中断的原因
 * 版本: Frida 12.9.7 (ES5 compatible)
 */

(function() {
    'use strict';
    
    console.log("[Whiteboard Debug] 启动白屏问题调试器...");
    
    var debugResults = {
        gestureEvents: 0,
        fileReads: 0,
        dataProcessing: 0,
        renderCalls: 0,
        lastActivity: 0
    };
    
    // === 手势处理监控 ===
    function setupGestureMonitoring() {
        console.log("[Gesture] 设置手势监控...");
        
        try {
            // 监控手势处理的关键函数
            Java.perform(function() {
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                
                GLMapEngine.nativeAddMapGestureMsg.implementation = function(a, b, c, d, e, f, g) {
                    console.log("[Gesture] 手势事件: type=" + c + ", x=" + d + ", y=" + e);
                    debugResults.gestureEvents++;
                    debugResults.lastActivity = Date.now();
                    
                    // 调用原函数
                    return this.nativeAddMapGestureMsg(a, b, c, d, e, f, g);
                };
            });
            
            console.log("[Gesture] 手势监控设置完成");
            
        } catch (e) {
            console.log("[Error] 手势监控设置失败: " + e);
        }
    }
    
    // === 文件读取监控 ===
    function setupFileMonitoring() {
        console.log("[File] 设置文件监控...");
        
        try {
            var readPtr = Module.getExportByName("libc.so", "read");
            if (readPtr) {
                Interceptor.attach(readPtr, {
                    onEnter: function(args) {
                        this.size = args[2].toInt32();
                    },
                    onLeave: function(retval) {
                        var bytesRead = retval.toInt32();
                        if (bytesRead > 1000) {
                            console.log("[File] 读取大文件: " + bytesRead + " 字节");
                            debugResults.fileReads++;
                            debugResults.lastActivity = Date.now();
                        }
                    }
                });
            }
            
            console.log("[File] 文件监控设置完成");
            
        } catch (e) {
            console.log("[Error] 文件监控设置失败: " + e);
        }
    }
    
    // === 数据处理监控 ===
    function setupDataProcessingMonitoring(libBase) {
        console.log("[Data] 设置数据处理监控...");
        
        try {
            // 监控关键数据处理函数
            var sub5C394 = libBase.add(0x5C394);
            Interceptor.attach(sub5C394, {
                onEnter: function(args) {
                    console.log("[Data] sub_5C394 调用 - 数据调度");
                    debugResults.dataProcessing++;
                    debugResults.lastActivity = Date.now();
                }
            });
            
            var sub10F88 = libBase.add(0x10F88);
            Interceptor.attach(sub10F88, {
                onEnter: function(args) {
                    console.log("[Data] sub_10F88 调用 - 数据解析");
                },
                onLeave: function(retval) {
                    var code = retval.toInt32();
                    console.log("[Data] sub_10F88 完成 - 返回码: " + code);
                    if (code !== 0) {
                        console.log("⚠️  数据解析失败！返回码: " + code);
                    }
                }
            });
            
            console.log("[Data] 数据处理监控设置完成");
            
        } catch (e) {
            console.log("[Error] 数据处理监控设置失败: " + e);
        }
    }
    
    // === 渲染监控 ===
    function setupRenderMonitoring() {
        console.log("[Render] 设置渲染监控...");
        
        try {
            var glDrawPtr = Module.findExportByName("libGLESv2.so", "glDrawElements");
            if (glDrawPtr) {
                Interceptor.attach(glDrawPtr, {
                    onEnter: function(args) {
                        var count = args[1].toInt32();
                        if (count > 50) {
                            console.log("[Render] glDrawElements: " + count + " 顶点");
                            debugResults.renderCalls++;
                            debugResults.lastActivity = Date.now();
                        }
                    }
                });
            }
            
            console.log("[Render] 渲染监控设置完成");
            
        } catch (e) {
            console.log("[Error] 渲染监控设置失败: " + e);
        }
    }
    
    // === 诊断报告 ===
    function generateDiagnosticReport() {
        var now = Date.now();
        var timeSinceLastActivity = now - debugResults.lastActivity;
        
        console.log("\n=== 白屏诊断报告 ===");
        console.log("手势事件: " + debugResults.gestureEvents);
        console.log("文件读取: " + debugResults.fileReads);
        console.log("数据处理: " + debugResults.dataProcessing);
        console.log("渲染调用: " + debugResults.renderCalls);
        console.log("最后活动: " + (timeSinceLastActivity/1000).toFixed(1) + "秒前");
        
        // 诊断分析
        if (debugResults.gestureEvents === 0) {
            console.log("❌ 没有手势事件 - 触摸可能未被识别");
        } else if (debugResults.dataProcessing === 0) {
            console.log("❌ 手势未触发数据处理 - 可能缓存命中或逻辑错误");
        } else if (debugResults.fileReads === 0) {
            console.log("❌ 没有文件读取 - 数据可能已缓存或路径错误");
        } else if (debugResults.renderCalls === 0) {
            console.log("❌ 没有渲染调用 - 渲染管道可能堵塞");
        } else {
            console.log("✅ 所有环节正常 - 问题可能在数据内容");
        }
        
        console.log("========================\n");
    }
    
    // === 简化的库等待函数 ===
    function waitForLibrary(name, callback) {
        var attempts = 0;
        function check() {
            try {
                var lib = Module.findBaseAddress(name);
                if (lib) {
                    console.log("[Library] " + name + " 加载完成: " + lib);
                    callback(lib);
                    return;
                }
            } catch (e) {}
            
            if (++attempts < 30) {
                setTimeout(check, 1000);
            } else {
                console.log("[Error] " + name + " 加载超时");
            }
        }
        check();
    }
    
    // === 主函数 ===
    function main() {
        console.log("[Main] 初始化白屏调试器...");
        
        setTimeout(function() {
            // 设置基础监控
            setupGestureMonitoring();
            setupFileMonitoring();
            setupRenderMonitoring();
            
            // 等待数据处理库
            waitForLibrary("libamapnsq.so", function(libBase) {
                setupDataProcessingMonitoring(libBase);
                
                // 定期诊断
                setInterval(generateDiagnosticReport, 10000);
            });
            
            console.log("[Whiteboard Debug] 调试器已启动!");
            console.log("请移动地图到新区域，观察数据流...");
            
        }, 3000);
    }
    
    // === 启动 ===
    main();
    
})(); 