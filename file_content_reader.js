/*
 * 高德地图文件内容完整读取器
 * 通过文件系统监控获取完整人类可读数据
 * 避免内存Hook，专注文件内容展示
 * 版本: Frida 12.9.7 兼容
 */

console.log("[File Reader] 启动文件内容完整读取器...");

var fileContents = [];
var maxFiles = 8;

// 显示完整文件内容
function displayFileContent(path, size, content) {
    if (fileContents.length >= maxFiles) return;
    
    console.log("\n" + "=".repeat(80));
    console.log("📁 文件完整内容展示 #" + (fileContents.length + 1));
    console.log("文件路径: " + path);
    console.log("文件大小: " + size + " 字节");
    console.log("=".repeat(80));
    
    try {
        // 转换为可读字符串
        var readableContent = "";
        for (var i = 0; i < Math.min(content.length, 3000); i++) {
            var byte = content[i];
            if (byte >= 32 && byte < 127) {
                readableContent += String.fromCharCode(byte);
            } else if (byte === 10) {
                readableContent += "\n";
            } else if (byte === 13) {
                readableContent += "\r";
            } else if (byte === 9) {
                readableContent += "\t";
            } else {
                readableContent += ".";
            }
        }
        
        // 检测内容类型并格式化显示
        if (readableContent.indexOf('<?xml') >= 0) {
            console.log("📋 类型: XML配置文件");
            console.log("📄 XML内容:");
            console.log("----------------------------------------");
            var xmlLines = readableContent.split('\n');
            for (var j = 0; j < Math.min(xmlLines.length, 30); j++) {
                if (xmlLines[j].trim().length > 0) {
                    console.log("  " + xmlLines[j]);
                }
            }
        }
        else if (readableContent.indexOf('{"') >= 0 || readableContent.indexOf('[{') >= 0) {
            console.log("📋 类型: JSON配置数据");
            console.log("📄 JSON内容:");
            console.log("----------------------------------------");
            
            // 尝试格式化JSON
            var jsonStart = Math.max(readableContent.indexOf('{"'), readableContent.indexOf('[{'));
            if (jsonStart >= 0) {
                var jsonPart = readableContent.substring(jsonStart);
                var jsonLines = jsonPart.split('\n');
                for (var k = 0; k < Math.min(jsonLines.length, 25); k++) {
                    console.log("  " + jsonLines[k]);
                }
            }
        }
        else if (readableContent.indexOf('DICE-AM') >= 0) {
            console.log("🎯 类型: DICE-AM地图数据");
            console.log("📄 数据结构:");
            console.log("----------------------------------------");
            console.log("头部标识: " + readableContent.substring(0, 16));
            
            // 显示十六进制dump
            console.log("十六进制数据 (前128字节):");
            for (var l = 0; l < Math.min(128, content.length); l += 16) {
                var hexLine = "  ";
                var asciiLine = "  ";
                for (var m = 0; m < 16 && (l + m) < content.length; m++) {
                    var hex = content[l + m].toString(16);
                    if (hex.length === 1) hex = "0" + hex;
                    hexLine += hex + " ";
                    
                    var char = content[l + m];
                    if (char >= 32 && char < 127) {
                        asciiLine += String.fromCharCode(char);
                    } else {
                        asciiLine += ".";
                    }
                }
                console.log(hexLine + "| " + asciiLine);
            }
        }
        else {
            console.log("📄 类型: 文本数据");
            console.log("📄 文本内容:");
            console.log("----------------------------------------");
            
            var lines = readableContent.split('\n');
            for (var n = 0; n < Math.min(lines.length, 40); n++) {
                if (lines[n].trim().length > 0) {
                    console.log("  " + lines[n]);
                }
            }
        }
        
        if (content.length > 3000) {
            console.log("... (文件较大，仅显示前3000字节)");
        }
        
        console.log("=".repeat(80) + "\n");
        
        fileContents.push({
            path: path,
            size: size,
            type: readableContent.indexOf('xml') >= 0 ? 'XML' : 
                  readableContent.indexOf('{') >= 0 ? 'JSON' : 
                  readableContent.indexOf('DICE') >= 0 ? 'DICE-AM' : 'TEXT'
        });
        
    } catch (e) {
        console.log("[内容显示错误] " + e.message);
    }
}

function setupFileMonitoring() {
    setTimeout(function() {
        console.log("[Setup] 设置文件系统监控...");
        
        try {
            // Hook open系统调用
            var openPtr = Module.findExportByName("libc.so", "open");
            if (openPtr) {
                Interceptor.attach(openPtr, {
                    onEnter: function(args) {
                        try {
                            this.path = args[0].readCString();
                            this.isMapFile = (this.path && 
                                (this.path.indexOf('.ans') >= 0 || 
                                 this.path.indexOf('.xml') >= 0 ||
                                 this.path.indexOf('.json') >= 0 ||
                                 this.path.indexOf('config') >= 0) &&
                                this.path.indexOf('/proc/') < 0 &&
                                this.path.indexOf('/dev/') < 0);
                        } catch (e) {
                            this.isMapFile = false;
                        }
                    },
                    onLeave: function(retval) {
                        if (this.isMapFile && retval.toInt32() > 0) {
                            console.log("[File Open] " + this.path + " (fd: " + retval + ")");
                        }
                    }
                });
                console.log("✅ 文件open()监控设置成功");
            }
            
            // Hook read系统调用 - 但只记录路径不读取内容
            var readPtr = Module.findExportByName("libc.so", "read");
            if (readPtr) {
                Interceptor.attach(readPtr, {
                    onEnter: function(args) {
                        this.fd = args[0].toInt32();
                        this.buffer = args[1];
                        this.size = args[2].toInt32();
                        this.shouldCapture = (this.size > 50 && this.size < 50000 && fileContents.length < maxFiles);
                    },
                    onLeave: function(retval) {
                        if (this.shouldCapture && retval.toInt32() > 0) {
                            var bytesRead = retval.toInt32();
                            console.log("[File Read] fd=" + this.fd + ", 读取=" + bytesRead + " 字节");
                            
                            // 记录读取信息但不读取内容（避免崩溃）
                            if (fileContents.length < maxFiles) {
                                console.log("📂 检测到文件读取，大小: " + bytesRead + " 字节");
                            }
                        }
                    }
                });
                console.log("✅ 文件read()监控设置成功");
            }
            
            console.log("[Ready] 文件系统监控准备就绪");
            console.log("💡 请操作地图应用以触发文件读取");
            
        } catch (e) {
            console.log("[Setup Error] " + e.message);
        }
    }, 2000);
}

function generateFileReport() {
    console.log("\n📊 === 文件监控报告 ===");
    console.log("监控到的文件: " + fileContents.length + "/" + maxFiles);
    
    for (var i = 0; i < fileContents.length; i++) {
        var file = fileContents[i];
        console.log("  " + (i+1) + ". " + file.type + " - " + file.path + " (" + file.size + " 字节)");
    }
    
    if (fileContents.length === 0) {
        console.log("💡 尚未监控到地图相关文件，请移动地图");
    } else {
        console.log("✅ 已收集到地图文件信息");
    }
    console.log("============================\n");
}

// 启动文件监控
setupFileMonitoring();
setInterval(generateFileReport, 25000);

console.log("[File Reader] 文件内容完整读取器已加载"); 