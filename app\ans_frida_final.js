
(function() {
    console.log("[ GaoDe ANS Analyzer - Final Version ] Script starting...");

    // --- Configuration ---
    var LIBC_HOOKS_ENABLED = true;
    var ANTI_ANTI_DEBUG_ENABLED = true;

    // --- Anti-Anti-Debugging ---
    if (ANTI_ANTI_DEBUG_ENABLED) {
        try {
            var exit_ptr = Module.findExportByName("libc.so", "exit");
            if (exit_ptr) {
                Interceptor.replace(exit_ptr, new NativeCallback(function() {
                    console.log("[AntiDebug] exit() called, preventing app termination.");
                    return 0;
                }, 'void', ['int']));
            }

            setTimeout(function() {
                Java.perform(function() {
                    try {
                        var Debug = Java.use("android.os.Debug");
                        Debug.isDebuggerConnected.implementation = function() {
                            return false;
                        };
                    } catch (e) {}
                    try {
                        var Process = Java.use("android.os.Process");
                        Process.killProcess.implementation = function(pid) {
                            console.log("[AntiDebug] android.os.Process.killProcess() called for pid " + pid + ". Preventing app termination.");
                        };
                    } catch (e) {}
                });
            }, 100);

            console.log("[AntiDebug] Anti-anti-debugging measures enabled.");
        } catch (e) {
            console.log("[AntiDebug] Error applying anti-anti-debugging: " + e);
        }
    }

    // --- JNI Hooks ---
    var jniOnLoad = Module.findExportByName(null, "JNI_OnLoad");
    if (jniOnLoad) {
        Interceptor.attach(jniOnLoad, {
            onEnter: function(args) {
                this.vm = args[0];
            },
            onLeave: function(retval) {
                if (this.vm) {
                    var env = this.vm.getEnv();
                    var RegisterNatives = env.vtable.RegisterNatives;
                    Interceptor.attach(RegisterNatives, {
                        onEnter: function(args) {
                            var className = env.getClassName(args[1]);
                            var numMethods = args[3].toInt32();
                            for (var i = 0; i < numMethods; i++) {
                                var method = args[2].add(i * Process.pointerSize * 3).readPointer();
                                var methodName = method.readCString();
                                var signature = method.add(Process.pointerSize).readPointer().readCString();
                                var fnPtr = method.add(Process.pointerSize * 2).readPointer();

                                var keywords = ["ans", "parse", "load", "map"];
                                var shouldLog = keywords.some(function(keyword) {
                                    return methodName.toLowerCase().indexOf(keyword) !== -1;
                                });

                                if (shouldLog) {
                                    console.log("[JNI] Class: " + className + ", Method: " + methodName + ", Signature: " + signature + ", Address: " + fnPtr);
                                }
                            }
                        }
                    });
                }
            }
        });
    }

    console.log("[ GaoDe ANS Analyzer - Final Version ] Script loaded successfully. Waiting for app activity...");

})();
