(function() {
    'use strict';
    
    // 配置
    var config = {
        libName: "libamapr.so",
        debug: true,
        includeBacktrace: true
    };
    
    // 全局变量
    var baseAddr = null;
    var hookHandles = [];
    
    // 工具函数
    function log(message) {
        console.log("[高德地图分析] " + message);
    }
    
    function logError(message) {
        console.log("[高德地图分析-错误] " + message);
    }
    
    function getBacktrace() {
        if (!config.includeBacktrace) return "";
        try {
            return Thread.backtrace(this.context, Backtracer.ACCURATE)
                .map(DebugSymbol.fromAddress)
                .join("\n");
        } catch (e) {
            return "获取调用栈失败: " + e;
        }
    }
    
    function hexdump(address, size) {
        if (!address || address.isNull()) return "[NULL]";
        try {
            return hexdump(address, {
                length: size || 64,
                header: true,
                ansi: false
            });
        } catch (e) {
            return "[无法读取内存]";
        }
    }
    
    // 初始化
    function init() {
        var modules = Process.enumerateModules();
        for (var i = 0; i < modules.length; i++) {
            if (modules[i].name === config.libName) {
                baseAddr = modules[i].base;
                break;
            }
        }
        
        if (!baseAddr) {
            logError(config.libName + " 未找到!");
            return false;
        }
        
        log(config.libName + " 基址: " + baseAddr);
        return true;
    }
    
    // 查找关键函数
    function findRenderFunctions() {
        log("开始查找渲染相关函数...");
        
        // 搜索OpenGL相关函数
        var glFunctions = [
            "glClear",
            "glDrawElements",
            "glDrawArrays",
            "glBindTexture",
            "glTexImage2D"
        ];
        
        for (var i = 0; i < glFunctions.length; i++) {
            var funcName = glFunctions[i];
            var matches = Module.findExportByName(null, funcName);
            if (matches) {
                log("找到OpenGL函数: " + funcName + " @ " + matches);
                hookOpenGLFunction(funcName, matches);
            }
        }
        
        // 搜索可能的渲染函数
        var renderFunctionPatterns = [
            "render",
            "draw",
            "display",
            "update",
            "frame"
        ];
        
        // 遍历模块的导出函数
        var exports = Module.enumerateExports(config.libName);
        for (var i = 0; i < exports.length; i++) {
            var exp = exports[i];
            for (var j = 0; j < renderFunctionPatterns.length; j++) {
                var pattern = renderFunctionPatterns[j];
                if (exp.name.toLowerCase().indexOf(pattern) !== -1) {
                    log("找到可能的渲染函数: " + exp.name + " @ " + exp.address);
                    hookPotentialRenderFunction(exp.name, exp.address);
                }
            }
        }
        
        // 搜索JNI函数
        var jniFunctionPatterns = [
            "nativeRender",
            "nativeDraw",
            "nativeUpdate",
            "nativeAddMapGesture"
        ];
        
        var javaVm = Java.vm;
        if (javaVm) {
            Java.perform(function() {
                try {
                    var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                    if (GLMapEngine) {
                        var methods = GLMapEngine.class.getDeclaredMethods();
                        for (var i = 0; i < methods.length; i++) {
                            var method = methods[i];
                            var methodName = method.getName();
                            for (var j = 0; j < jniFunctionPatterns.length; j++) {
                                var pattern = jniFunctionPatterns[j];
                                if (methodName.indexOf(pattern) !== -1) {
                                    log("找到JNI方法: " + methodName);
                                    hookJNIMethod(GLMapEngine, methodName);
                                }
                            }
                        }
                    }
                } catch (e) {
                    logError("搜索JNI函数失败: " + e);
                }
            });
        }
        
        // 搜索nativeAddMapGestureMsg函数
        try {
            Java.perform(function() {
                var GLMapEngine = Java.use("com.autonavi.jni.ae.gmap.GLMapEngine");
                if (GLMapEngine && GLMapEngine.nativeAddMapGestureMsg) {
                    log("找到nativeAddMapGestureMsg JNI方法");
                    hookJNIMethod(GLMapEngine, "nativeAddMapGestureMsg");
                }
            });
        } catch (e) {
            logError("搜索nativeAddMapGestureMsg失败: " + e);
        }
        
        return true;
    }
    
    // 钩住OpenGL函数
    function hookOpenGLFunction(funcName, address) {
        try {
            var hook = Interceptor.attach(address, {
                onEnter: function(args) {
                    this.funcName = funcName;
                    this.startTime = new Date().getTime();
                    
                    if (funcName === "glDrawElements" || funcName === "glDrawArrays") {
                        log("调用OpenGL渲染函数: " + funcName);
                        
                        if (funcName === "glDrawElements") {
                            var mode = args[0].toInt32();
                            var count = args[1].toInt32();
                            var type = args[2].toInt32();
                            log("  模式: " + getGLConstantName(mode) + 
                                ", 顶点数: " + count + 
                                ", 索引类型: " + getGLConstantName(type));
                        } else if (funcName === "glDrawArrays") {
                            var mode = args[0].toInt32();
                            var first = args[1].toInt32();
                            var count = args[2].toInt32();
                            log("  模式: " + getGLConstantName(mode) + 
                                ", 起始顶点: " + first + 
                                ", 顶点数: " + count);
                        }
                        
                        if (config.includeBacktrace) {
                            log("调用栈:\n" + getBacktrace.call(this));
                        }
                    }
                },
                onLeave: function(retval) {
                    if (this.funcName === "glDrawElements" || this.funcName === "glDrawArrays") {
                        var duration = new Date().getTime() - this.startTime;
                        log("OpenGL渲染函数 " + this.funcName + " 执行时间: " + duration + "ms");
                    }
                }
            });
            hookHandles.push(hook);
        } catch (e) {
            logError("钩住OpenGL函数 " + funcName + " 失败: " + e);
        }
    }
    
    // 钩住潜在的渲染函数
    function hookPotentialRenderFunction(funcName, address) {
        try {
            var hook = Interceptor.attach(address, {
                onEnter: function(args) {
                    this.funcName = funcName;
                    this.startTime = new Date().getTime();
                    
                    log("调用潜在渲染函数: " + funcName);
                    log("  参数1: " + args[0]);
                    log("  参数2: " + args[1]);
                    
                    if (config.includeBacktrace) {
                        log("调用栈:\n" + getBacktrace.call(this));
                    }
                },
                onLeave: function(retval) {
                    var duration = new Date().getTime() - this.startTime;
                    log("潜在渲染函数 " + this.funcName + " 执行时间: " + duration + "ms");
                    log("  返回值: " + retval);
                }
            });
            hookHandles.push(hook);
        } catch (e) {
            logError("钩住潜在渲染函数 " + funcName + " 失败: " + e);
        }
    }
    
    // 钩住JNI方法
    function hookJNIMethod(clazz, methodName) {
        try {
            var originalMethod = clazz[methodName];
            clazz[methodName].implementation = function() {
                var startTime = new Date().getTime();
                
                log("调用JNI方法: " + methodName);
                log("  参数: " + JSON.stringify(arguments));
                
                var result = originalMethod.apply(this, arguments);
                
                var duration = new Date().getTime() - startTime;
                log("JNI方法 " + methodName + " 执行时间: " + duration + "ms");
                log("  返回值: " + result);
                
                return result;
            };
        } catch (e) {
            logError("钩住JNI方法 " + methodName + " 失败: " + e);
        }
    }
    
    // 获取OpenGL常量名称
    function getGLConstantName(value) {
        var constants = {
            0x0000: "GL_POINTS",
            0x0001: "GL_LINES",
            0x0002: "GL_LINE_LOOP",
            0x0003: "GL_LINE_STRIP",
            0x0004: "GL_TRIANGLES",
            0x0005: "GL_TRIANGLE_STRIP",
            0x0006: "GL_TRIANGLE_FAN",
            0x1400: "GL_BYTE",
            0x1401: "GL_UNSIGNED_BYTE",
            0x1402: "GL_SHORT",
            0x1403: "GL_UNSIGNED_SHORT",
            0x1404: "GL_INT",
            0x1405: "GL_UNSIGNED_INT",
            0x1406: "GL_FLOAT"
        };
        
        return constants[value] || "0x" + value.toString(16);
    }
    
    // 主函数
    function main() {
        log("高德地图渲染流程分析脚本启动");
        
        if (!init()) {
            logError("初始化失败，退出");
            return;
        }
        
        if (!findRenderFunctions()) {
            logError("查找渲染函数失败，退出");
            return;
        }
        
        log("脚本初始化完成，等待渲染事件...");
        log("请在地图上进行缩放或平移操作以触发分析流程");
    }
    
    // 执行主函数
    main();
})();
